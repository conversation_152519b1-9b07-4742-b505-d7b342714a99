# Sensors Focus AI

### 本地编译方法
```shell
# conda 切换到该项目的虚拟环境
conda activate xxx
pip install uv
uv sync

# 开始构建包
bash build.sh

# 将包安装导本地
uv pip install dist/*.whl

# 启动 http openapi
http_server
```

### docker 启动
docker 启动需要先按照上述方式编译打包。

然后执行以下命令：
```shell
cd docker
docker compose up -d
```

如果需要看管理后台前端，则需要进入 `focus-ai-web` 容器内部执行：
```shell
st_server
```

如果要运行 grpc 服务，则进入 `focus-ai-web` 容器内部执行：
```shell
rpc_server
```