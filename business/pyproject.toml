[project]
name = "business"
version = "1.0.0"
classifiers = ["Private :: Do Not Upload"]
description = ""
authors = [
    { name = "<EMAIL>" }
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "apis",
    "common",
    "framework",
    "impyla>=0.21.0",
]

[tool.uv.sources]
apis = { workspace = true }
common = { workspace = true }
framework = { workspace = true }

[tool.setuptools]
packages = ["business"]

[tool.setuptools.package-dir]
business = "src/business"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
