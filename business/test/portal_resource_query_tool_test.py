import asyncio

from google.protobuf import json_format

from apis.definition.portal.portal_bot_pb2 import InsightParams
from business.config import DEMO_SENSORS_PROJECT_INFO
from business.portal.tools import PortalResourceQueryTool
from framework.agents.react import ReActAgent


async def test():
    insight_params = {
        "start_time": "2025-07-14 02:22:27",
        "end_time": "2025-07-21 02:22:27",
        "level": "SCENE",
        "scene_id": 1,
        "strategies": [
            {
                "type": "CANVAS",
                "ids": [
                    "1",
                    "2",
                    "3"
                ]
            }
        ]
    }
    tool = PortalResourceQueryTool(
        tenant_project_info=DEMO_SENSORS_PROJECT_INFO,
        insight_params=json_format.ParseDict(insight_params, InsightParams())
    )
    agent = ReActAgent(tools=[tool])
    result = await agent.achat("请查询所有资源")
    print(f"result={result}")


asyncio.run(test())
