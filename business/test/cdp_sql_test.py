import asyncio

from business.config import DEMO_SENSORS_PROJECT_INFO
from framework.agents.react import ReActAgent

from business.portal.tools import CDPSqlExecutorTool


async def test():
    tool = CDPSqlExecutorTool(
        tenant_project_info=DEMO_SENSORS_PROJECT_INFO
    )
    agent = ReActAgent(tools=[tool])
    result = await agent.achat("请执行 SQL 语句统计所有用户总数")
    print(f"result={result}")


asyncio.run(test())
