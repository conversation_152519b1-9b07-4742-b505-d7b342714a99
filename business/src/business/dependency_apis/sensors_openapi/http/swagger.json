{"openapi": "3.0.1", "info": {"title": "OpenAPI 文档", "description": "Generate by openapi-generator-cli", "version": "1.0.0"}, "servers": [{"url": "/api/v3/analytics/v1"}, {"url": "/api/v3/horizon/v1"}], "tags": [{"name": "SaEventMeta", "description": "事件相关基础元数据", "externalDocs": {"url": "https://manual.sensorsdata.cn/openapi/api/v1/openapi_download?file_name=2.5.5-analytics-EventMeta-v1-swagger.json"}}, {"name": "SaPropertyMeta", "description": "属性相关基础元数据", "externalDocs": {"url": "https://manual.sensorsdata.cn/openapi/api/v1/openapi_download?file_name=2.5.5-analytics-PropertyMeta-v1-swagger.json"}}, {"name": "SdhSchema", "description": "元数据管理服务", "externalDocs": {"url": "https://manual.sensorsdata.cn/openapi/api/v1/openapi_download?file_name=1.3.1-horizon-Schema-v1-swagger.json"}}, {"name": "SdhSegment", "description": "分群管理服务", "externalDocs": {"url": "https://manual.sensorsdata.cn/openapi/api/v1/openapi_download?file_name=1.3.1-horizon-Segment-v1-swagger.json"}}, {"name": "SdhTag", "description": "标签管理服务", "externalDocs": {"url": "https://manual.sensorsdata.cn/openapi/api/v1/openapi_download?file_name=1.3.1-horizon-Tag-v1-swagger.json"}}], "paths": {"/api/v3/analytics/v1/event-meta/events/all": {"get": {"description": "获取事件列表", "operationId": "ListEventsAll", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"events": [{}]}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.ListEventAllResponseHttpApiResult"}}}, "description": "sensorsdata.analytics.v1.ListEventAllResponse"}}, "summary": "获取事件列表", "x-sd-openapi": {"responseClass": "com.sensorsdata.analytics.v1.ListEventAllResponse", "isServerStreaming": false, "grpcServiceBase": "com.sensorsdata.analytics.v1.EventMetaServiceGrpc.EventMetaServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "tags": ["SaEventMeta"]}}, "/api/v3/analytics/v1/event-meta/events/tags": {"get": {"description": "获取事件标签列表", "operationId": "ListEventTags", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"tag_infos": [{}]}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.ListEventTagsResponseHttpApiResult"}}}, "description": "sensorsdata.analytics.v1.ListEventTagsResponse"}}, "summary": "获取事件标签列表", "x-sd-openapi": {"responseClass": "com.sensorsdata.analytics.v1.ListEventTagsResponse", "isServerStreaming": false, "grpcServiceBase": "com.sensorsdata.analytics.v1.EventMetaServiceGrpc.EventMetaServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "tags": ["SaEventMeta"]}}, "/api/v3/analytics/v1/property-meta/event-properties/all": {"get": {"description": "获取所有事件属性列表", "operationId": "ListAllEventProperties", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"properties": [{}]}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.EventPropertyAllResponseHttpApiResult"}}}, "description": "sensorsdata.analytics.v1.EventPropertyAllResponse"}}, "summary": "获取所有事件属性", "x-sd-openapi": {"responseClass": "com.sensorsdata.analytics.v1.EventPropertyAllResponse", "isServerStreaming": false, "grpcServiceBase": "com.sensorsdata.analytics.v1.PropertyMetaServiceGrpc.PropertyMetaServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "tags": ["SaPropertyMeta"]}}, "/api/v3/analytics/v1/property-meta/event-properties": {"post": {"description": "查询指定事件和属性列表", "operationId": "ListEventProperties", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.EventPropertyRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"event_properties": [{"event_define": {}, "properties": [{}]}]}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.EventPropertyResponseHttpApiResult"}}}, "description": "sensorsdata.analytics.v1.EventPropertyResponse"}}, "summary": "获取指定的事件和相关属性", "x-sd-openapi": {"responseClass": "com.sensorsdata.analytics.v1.EventPropertyResponse", "isServerStreaming": false, "requestClass": "com.sensorsdata.analytics.v1.EventPropertyRequest", "grpcServiceBase": "com.sensorsdata.analytics.v1.PropertyMetaServiceGrpc.PropertyMetaServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "EventPropertyRequest", "tags": ["SaPropertyMeta"]}}, "/api/v3/analytics/v1/property-meta/user-properties/all": {"get": {"description": "获取所有用户属性列表", "operationId": "ListAllUserProperties", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"user_properties": [{}]}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.UserPropertyAllResponseHttpApiResult"}}}, "description": "sensorsdata.analytics.v1.UserPropertyAllResponse"}}, "summary": "获取所有用户属性列表", "x-sd-openapi": {"responseClass": "com.sensorsdata.analytics.v1.UserPropertyAllResponse", "isServerStreaming": false, "grpcServiceBase": "com.sensorsdata.analytics.v1.PropertyMetaServiceGrpc.PropertyMetaServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "tags": ["SaPropertyMeta"]}}, "/api/v3/analytics/v1/property-meta/user-groups/all": {"get": {"description": "获取所有用户分群列表", "operationId": "ListUserGroups", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"user_groups": [{}]}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.UserGroupAllResponseHttpApiResult"}}}, "description": "sensorsdata.analytics.v1.UserGroupAllResponse"}}, "summary": "获取所有用户分群列表", "x-sd-openapi": {"responseClass": "com.sensorsdata.analytics.v1.UserGroupAllResponse", "isServerStreaming": false, "grpcServiceBase": "com.sensorsdata.analytics.v1.PropertyMetaServiceGrpc.PropertyMetaServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "tags": ["SaPropertyMeta"]}}, "/api/v3/analytics/v1/property-meta/user-tags/dir": {"get": {"description": "获取带有目录结构的用户标签信息", "operationId": "ListUserTagsWithDir", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"user_tags": [{}]}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.UserTagDirResponseHttpApiResult"}}}, "description": "sensorsdata.analytics.v1.UserTagDirResponse"}}, "summary": "获取带有目录结构的用户标签列表", "x-sd-openapi": {"responseClass": "com.sensorsdata.analytics.v1.UserTagDirResponse", "isServerStreaming": false, "grpcServiceBase": "com.sensorsdata.analytics.v1.PropertyMetaServiceGrpc.PropertyMetaServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "tags": ["SaPropertyMeta"]}}, "/api/v3/analytics/v1/property-meta/property/values": {"post": {"description": "获取属性候选值", "operationId": "GetPropertyV<PERSON>ues", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.GetPropertyValueRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.GetPropertyValueResponseHttpApiResult"}}}, "description": "sensorsdata.analytics.v1.GetPropertyValueResponse"}}, "summary": "获取属性候选值", "x-sd-openapi": {"responseClass": "com.sensorsdata.analytics.v1.GetPropertyValueResponse", "isServerStreaming": false, "requestClass": "com.sensorsdata.analytics.v1.GetPropertyValueRequest", "grpcServiceBase": "com.sensorsdata.analytics.v1.PropertyMetaServiceGrpc.PropertyMetaServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "GetPropertyValueRequest", "tags": ["SaPropertyMeta"]}}, "/api/v3/horizon/v1/schema/event/list": {"post": {"description": "获取事件定义列表", "operationId": "ListEventSchemas", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListLogicalSchemasRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"schemas": [{"mapping": {"condition": {"index": 10, "conditions": [{"function": "EQUAL", "index": 10, "params": [{"expression": "user.p1/user.p2+user.p3", "field": "#ResourceId", "time_point": {"relative_time": {"time_interval": {}}}, "variable": "$ENTRY_TIME", "value": {}, "param_type": "FIELD"}]}], "operator": "AND"}}, "data_mapping": {"view_table": {}, "extended_tables": [{"access_info": {}}]}, "fields": [{"unit": {}, "data_mapping": {"extended_column": {}}, "data_type": {"type": "STRING"}, "view_data_source": {"inline_expression": {}, "view_column_source_data": {}, "complex_expression": {}}, "object_fields": [{}]}], "statistics": {"track_platforms": [{}]}}]}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListLogicalSchemasResponseHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.ListLogicalSchemasResponse"}}, "summary": "获取事件定义列表", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.ListLogicalSchemasResponse", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.ListLogicalSchemasRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SchemaServiceGrpc.SchemaServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "ListLogicalSchemasRequest", "tags": ["SdhSchema"]}}, "/api/v3/horizon/v1/schema/event/get": {"post": {"description": "获取事件定义", "operationId": "GetEvent", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.GetSchemaByOriginalNameRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"mapping": {"condition": {"index": 10, "conditions": [{"function": "EQUAL", "index": 10, "params": [{"expression": "user.p1/user.p2+user.p3", "field": "#ResourceId", "time_point": {"relative_time": {"time_interval": {}}}, "variable": "$ENTRY_TIME", "value": {}, "param_type": "FIELD"}]}], "operator": "AND"}}, "data_mapping": {"view_table": {}, "extended_tables": [{"access_info": {}}]}, "fields": [{"unit": {}, "data_mapping": {"extended_column": {}}, "data_type": {"type": "STRING"}, "view_data_source": {"inline_expression": {}, "view_column_source_data": {}, "complex_expression": {}}, "object_fields": [{}]}], "statistics": {"track_platforms": [{}]}}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SchemaHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.<PERSON><PERSON>a"}}, "summary": "获取事件定义", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.Schema", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.GetSchemaByOriginalNameRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SchemaServiceGrpc.SchemaServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "GetSchemaByOriginalNameRequest", "tags": ["SdhSchema"]}}, "/api/v3/horizon/v1/schema/event/create": {"post": {"description": "创建事件定义", "operationId": "CreateEvent", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.BatchCreateLogicalSchemasRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.common.HttpApiResult"}}}, "description": ""}}, "summary": "创建事件定义", "x-sd-openapi": {"responseClass": "com.google.protobuf.Empty", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.BatchCreateLogicalSchemasRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SchemaServiceGrpc.SchemaServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "BatchCreateLogicalSchemasRequest", "tags": ["SdhSchema"]}}, "/api/v3/horizon/v1/schema/event/update": {"post": {"description": "更新事件定义", "operationId": "UpdateEvent", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.UpdateSchemaRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"mapping": {"condition": {"index": 10, "conditions": [{"function": "EQUAL", "index": 10, "params": [{"expression": "user.p1/user.p2+user.p3", "field": "#ResourceId", "time_point": {"relative_time": {"time_interval": {}}}, "variable": "$ENTRY_TIME", "value": {}, "param_type": "FIELD"}]}], "operator": "AND"}}, "data_mapping": {"view_table": {}, "extended_tables": [{"access_info": {}}]}, "fields": [{"unit": {}, "data_mapping": {"extended_column": {}}, "data_type": {"type": "STRING"}, "view_data_source": {"inline_expression": {}, "view_column_source_data": {}, "complex_expression": {}}, "object_fields": [{}]}], "statistics": {"track_platforms": [{}]}}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SchemaHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.<PERSON><PERSON>a"}}, "summary": "更新事件定义", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.Schema", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.UpdateSchemaRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SchemaServiceGrpc.SchemaServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "UpdateSchemaRequest", "tags": ["SdhSchema"]}}, "/api/v3/horizon/v1/schema/event/field/list": {"post": {"description": "获取事件属性", "operationId": "ListEventFields", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListSchemaFieldsRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"fields": [{"unit": {}, "data_mapping": {"extended_column": {}}, "data_type": {"type": "STRING"}, "view_data_source": {"inline_expression": {}, "view_column_source_data": {}, "complex_expression": {}}, "object_fields": [{}]}]}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListSchemaFieldsResponseHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.ListSchemaFieldsResponse"}}, "summary": "获取事件属性", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.ListSchemaFieldsResponse", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.ListSchemaFieldsRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SchemaServiceGrpc.SchemaServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "ListSchemaFieldsRequest", "tags": ["SdhSchema"]}}, "/api/v3/horizon/v1/schema/field/list": {"post": {"description": "获取属性列表", "operationId": "ListSchemaFields", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListSchemaFieldsRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"fields": [{"unit": {}, "data_mapping": {"extended_column": {}}, "data_type": {"type": "STRING"}, "view_data_source": {"inline_expression": {}, "view_column_source_data": {}, "complex_expression": {}}, "object_fields": [{}]}]}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListSchemaFieldsResponseHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.ListSchemaFieldsResponse"}}, "summary": "获取属性列表", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.ListSchemaFieldsResponse", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.ListSchemaFieldsRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SchemaServiceGrpc.SchemaServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "ListSchemaFieldsRequest", "tags": ["SdhSchema"]}}, "/api/v3/horizon/v1/schema/field/get": {"post": {"description": "获取属性信息", "operationId": "GetSchemaField", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.GetSchemaFieldRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"unit": {}, "data_mapping": {"extended_column": {}}, "data_type": {"type": "STRING"}, "view_data_source": {"inline_expression": {}, "view_column_source_data": {}, "complex_expression": {}}, "object_fields": [{}]}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.FieldHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.Field"}}, "summary": "获取属性信息", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.Field", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.GetSchemaFieldRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SchemaServiceGrpc.SchemaServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "GetSchemaFieldRequest", "tags": ["SdhSchema"]}}, "/api/v3/horizon/v1/schema/field/batch-create": {"post": {"description": "批量创建属性\n 命名规则 `^[a-zA-Z_$][a-zA-Z\\d_$]{0,99}$`\n 不允许创建以下保留字段及包含保留前缀的字段\n - 保留字段\n   - 公共保留字段\n     - distinct_id, original_id, time, properties, id, first_id, second_id, users, events, event, user_id, date, datetime\n   - SchemaClass = EVENT\n     - 公共保留字段以及 event_id, event_bucket, day, week_id, month_id, _offset, sampling_group\n   - SchemaClass = USER\n     - 公共保留字段以及 _offset, first_id_type, second_id_type, generated_from, merged_to\n   - SchemaClass = ITEM\n     - 公共保留字段以及 item_type, item_id\n - 保留前缀\n   - user_tag, user_group, $identity_, identity_, segment_ (可以创建 $ 开头的属性)", "operationId": "BatchCreateSchemaFields", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.BatchCreateSchemaFieldsRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.BatchCreateSchemaFieldsResponseHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.BatchCreateSchemaFieldsResponse"}}, "summary": "批量创建属性", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.BatchCreateSchemaFieldsResponse", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.BatchCreateSchemaFieldsRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SchemaServiceGrpc.SchemaServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "BatchCreateSchemaFieldsRequest", "tags": ["SdhSchema"]}}, "/api/v3/horizon/v1/schema/field/update": {"post": {"description": "更新属性\n 更新属性\n 支持修改 display_name,unit,custom_param,visible,enable,has_data,required,data_type,view_data_source,dict\n 支持更新维度字典", "operationId": "UpdateSchemaField", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.UpdateSchemaFieldRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"unit": {}, "data_mapping": {"extended_column": {}}, "data_type": {"type": "STRING"}, "view_data_source": {"inline_expression": {}, "view_column_source_data": {}, "complex_expression": {}}, "object_fields": [{}]}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.FieldHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.Field"}}, "summary": "更新属性", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.Field", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.UpdateSchemaFieldRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SchemaServiceGrpc.SchemaServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "UpdateSchemaFieldRequest", "tags": ["SdhSchema"]}}, "/api/v3/horizon/v1/schema/extended-table/list": {"post": {"description": "查询 Schema 关联的 Table", "operationId": "ListSchemaExtendedTables", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListSchemaExtendedTablesRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"extended_tables": [{"access_info": {}}]}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListSchemaExtendedTablesResponseHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.ListSchemaExtendedTablesResponse"}}, "summary": "查询 Schema 关联的 Table", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.ListSchemaExtendedTablesResponse", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.ListSchemaExtendedTablesRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SchemaServiceGrpc.SchemaServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "ListSchemaExtendedTablesRequest", "tags": ["SdhSchema"]}}, "/api/v3/horizon/v1/schema/extended-table/batch-create": {"post": {"description": "创建维度表关联关系", "operationId": "BatchCreateSchemaExtendedTables", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.BatchCreateSchemaExtendedTablesRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.common.HttpApiResult"}}}, "description": ""}}, "summary": "创建维度表关联关系", "x-sd-openapi": {"responseClass": "com.google.protobuf.Empty", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.BatchCreateSchemaExtendedTablesRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SchemaServiceGrpc.SchemaServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "BatchCreateSchemaExtendedTablesRequest", "tags": ["SdhSchema"]}}, "/api/v3/horizon/v1/schema/extended-table/update": {"post": {"description": "更新 SchemaExtendedTable", "operationId": "UpdateSchemaExtendedTable", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.UpdateSchemaExtendedTableRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"access_info": {}}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SchemaExtendedTableHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.SchemaExtendedTable"}}, "summary": "更新 SchemaExtendedTable", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.SchemaExtendedTable", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.UpdateSchemaExtendedTableRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SchemaServiceGrpc.SchemaServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "UpdateSchemaExtendedTableRequest", "tags": ["SdhSchema"]}}, "/api/v3/horizon/v1/schema/get": {"post": {"description": "获取用户表/事件表元信息", "operationId": "GetSchema", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.GetSchemaRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"mapping": {"condition": {"index": 10, "conditions": [{"function": "EQUAL", "index": 10, "params": [{"expression": "user.p1/user.p2+user.p3", "field": "#ResourceId", "time_point": {"relative_time": {"time_interval": {}}}, "variable": "$ENTRY_TIME", "value": {}, "param_type": "FIELD"}]}], "operator": "AND"}}, "data_mapping": {"view_table": {}, "extended_tables": [{"access_info": {}}]}, "fields": [{"unit": {}, "data_mapping": {"extended_column": {}}, "data_type": {"type": "STRING"}, "view_data_source": {"inline_expression": {}, "view_column_source_data": {}, "complex_expression": {}}, "object_fields": [{}]}], "statistics": {"track_platforms": [{}]}}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SchemaHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.<PERSON><PERSON>a"}}, "summary": "获取用户表/事件表元信息", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.Schema", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.GetSchemaRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SchemaServiceGrpc.SchemaServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "GetSchemaRequest", "tags": ["SdhSchema"]}}, "/api/v3/horizon/v1/segment/definition/list": {"post": {"description": "获取分群列表", "operationId": "ListSegmentDefinitions", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListSegmentDefinitionsRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"segment_definitions": [{"segment_rule_ref": {"expression": {"eql_segment_rule": {}, "entity_set_rules": [{}], "sql_segment_rule": {"query_options": [{}]}, "loading_segment_rule": {}, "custom_rule": {}, "event_sequence_rule": {"simple_event_sequences": [{"time_window": {}, "time_range": {}, "event_steps": [{"condition": {"index": 10, "conditions": [{"function": "EQUAL", "index": 10, "params": [{"expression": "user.p1/user.p2+user.p3", "field": "#ResourceId", "time_point": {"relative_time": {"time_interval": {}}}, "variable": "$ENTRY_TIME", "value": {}, "param_type": "FIELD"}]}], "operator": "AND"}}]}]}, "tailor": {"rank_field": {"orders": [{"measure": {"aggregator_field": {}}}]}}, "group_expression_rule": {}}}, "alarm_config": {"alarm_rules": [{}]}, "trigger": {"cron_trigger": {}, "periodic_trigger": {}, "static_trigger": {}}, "source": {}, "storage_settings": {}, "notify_params": {"subscribe_param": {"contents": [{}]}}}]}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListSegmentDefinitionsResponseHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.ListSegmentDefinitionsResponse"}}, "summary": "获取分群列表", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.ListSegmentDefinitionsResponse", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.ListSegmentDefinitionsRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SegmentServiceGrpc.SegmentServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "ListSegmentDefinitionsRequest", "tags": ["SdhSegment"]}}, "/api/v3/horizon/v1/segment/definition/get": {"post": {"description": "获取分群信息", "operationId": "GetSegmentDefinition", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.GetSegmentDefinitionRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"segment_rule_ref": {"expression": {"eql_segment_rule": {}, "entity_set_rules": [{}], "sql_segment_rule": {"query_options": [{}]}, "loading_segment_rule": {}, "custom_rule": {}, "event_sequence_rule": {"simple_event_sequences": [{"time_window": {}, "time_range": {}, "event_steps": [{"condition": {"index": 10, "conditions": [{"function": "EQUAL", "index": 10, "params": [{"expression": "user.p1/user.p2+user.p3", "field": "#ResourceId", "time_point": {"relative_time": {"time_interval": {}}}, "variable": "$ENTRY_TIME", "value": {}, "param_type": "FIELD"}]}], "operator": "AND"}}]}]}, "tailor": {"rank_field": {"orders": [{"measure": {"aggregator_field": {}}}]}}, "group_expression_rule": {}}}, "alarm_config": {"alarm_rules": [{}]}, "trigger": {"cron_trigger": {}, "periodic_trigger": {}, "static_trigger": {}}, "source": {}, "storage_settings": {}, "notify_params": {"subscribe_param": {"contents": [{}]}}}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentDefinitionHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.SegmentDefinition"}}, "summary": "获取分群信息", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.SegmentDefinition", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.GetSegmentDefinitionRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SegmentServiceGrpc.SegmentServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "GetSegmentDefinitionRequest", "tags": ["SdhSegment"]}}, "/api/v3/horizon/v1/segment/definition/create-with-rule": {"post": {"description": "创建分群", "operationId": "CreateSegmentDefinitionWithRule", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.CreateSegmentDefinitionWithRuleRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"segment_rule_ref": {"expression": {"eql_segment_rule": {}, "entity_set_rules": [{}], "sql_segment_rule": {"query_options": [{}]}, "loading_segment_rule": {}, "custom_rule": {}, "event_sequence_rule": {"simple_event_sequences": [{"time_window": {}, "time_range": {}, "event_steps": [{"condition": {"index": 10, "conditions": [{"function": "EQUAL", "index": 10, "params": [{"expression": "user.p1/user.p2+user.p3", "field": "#ResourceId", "time_point": {"relative_time": {"time_interval": {}}}, "variable": "$ENTRY_TIME", "value": {}, "param_type": "FIELD"}]}], "operator": "AND"}}]}]}, "tailor": {"rank_field": {"orders": [{"measure": {"aggregator_field": {}}}]}}, "group_expression_rule": {}}}, "alarm_config": {"alarm_rules": [{}]}, "trigger": {"cron_trigger": {}, "periodic_trigger": {}, "static_trigger": {}}, "source": {}, "storage_settings": {}, "notify_params": {"subscribe_param": {"contents": [{}]}}}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentDefinitionHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.SegmentDefinition"}}, "summary": "创建分群", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.SegmentDefinition", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.CreateSegmentDefinitionWithRuleRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SegmentServiceGrpc.SegmentServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "CreateSegmentDefinitionWithRuleRequest", "tags": ["SdhSegment"]}}, "/api/v3/horizon/v1/segment/definition/update-with-rule": {"post": {"description": "编辑分群", "operationId": "UpdateSegmentDefinitionWithRule", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.UpdateSegmentDefinitionWithRuleRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"segment_rule_ref": {"expression": {"eql_segment_rule": {}, "entity_set_rules": [{}], "sql_segment_rule": {"query_options": [{}]}, "loading_segment_rule": {}, "custom_rule": {}, "event_sequence_rule": {"simple_event_sequences": [{"time_window": {}, "time_range": {}, "event_steps": [{"condition": {"index": 10, "conditions": [{"function": "EQUAL", "index": 10, "params": [{"expression": "user.p1/user.p2+user.p3", "field": "#ResourceId", "time_point": {"relative_time": {"time_interval": {}}}, "variable": "$ENTRY_TIME", "value": {}, "param_type": "FIELD"}]}], "operator": "AND"}}]}]}, "tailor": {"rank_field": {"orders": [{"measure": {"aggregator_field": {}}}]}}, "group_expression_rule": {}}}, "alarm_config": {"alarm_rules": [{}]}, "trigger": {"cron_trigger": {}, "periodic_trigger": {}, "static_trigger": {}}, "source": {}, "storage_settings": {}, "notify_params": {"subscribe_param": {"contents": [{}]}}}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentDefinitionHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.SegmentDefinition"}}, "summary": "编辑分群", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.SegmentDefinition", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.UpdateSegmentDefinitionWithRuleRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SegmentServiceGrpc.SegmentServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "UpdateSegmentDefinitionWithRuleRequest", "tags": ["SdhSegment"]}}, "/api/v3/horizon/v1/segment/definition/status/update": {"post": {"description": "更新调度状态", "operationId": "UpdateSegmentSchedulerStatus", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.UpdateSegmentSchedulerStatusRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.common.HttpApiResult"}}}, "description": ""}}, "summary": "更新调度状态", "x-sd-openapi": {"responseClass": "com.google.protobuf.Empty", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.UpdateSegmentSchedulerStatusRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SegmentServiceGrpc.SegmentServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "UpdateSegmentSchedulerStatusRequest", "tags": ["SdhSegment"]}}, "/api/v3/horizon/v1/segment/definition/evaluate": {"post": {"description": "触发分群计算", "operationId": "EvaluateSegment", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.EvaluateSegmentRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.common.HttpApiResult"}}}, "description": ""}}, "summary": "触发分群计算", "x-sd-openapi": {"responseClass": "com.google.protobuf.Empty", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.EvaluateSegmentRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SegmentServiceGrpc.SegmentServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "EvaluateSegmentRequest", "tags": ["SdhSegment"]}}, "/api/v3/horizon/v1/segment/item/list": {"post": {"description": "获取分群定义关联的分群包元素", "operationId": "ListSegmentItems", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListSegmentItemsRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"items": [{}]}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListSegmentItemsResponseHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.ListSegmentItemsResponse"}}, "summary": "获取分群定义关联的分群包元素", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.ListSegmentItemsResponse", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.ListSegmentItemsRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SegmentServiceGrpc.SegmentServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "ListSegmentItemsRequest", "tags": ["SdhSegment"]}}, "/api/v3/horizon/v1/segment/task/get": {"post": {"description": "获取分群任务状态", "operationId": "GetSegmentTask", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.GetSegmentTaskRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"tasks": [{"failure_detail": {}, "segment_task_report": {"segment_loading_report": {}}, "rule_ref": {"expression": {"eql_segment_rule": {}, "entity_set_rules": [{}], "sql_segment_rule": {"query_options": [{}]}, "loading_segment_rule": {}, "custom_rule": {}, "event_sequence_rule": {"simple_event_sequences": [{"time_window": {}, "time_range": {}, "event_steps": [{"condition": {"index": 10, "conditions": [{"function": "EQUAL", "index": 10, "params": [{"expression": "user.p1/user.p2+user.p3", "field": "#ResourceId", "time_point": {"relative_time": {"time_interval": {}}}, "variable": "$ENTRY_TIME", "value": {}, "param_type": "FIELD"}]}], "operator": "AND"}}]}]}, "tailor": {"rank_field": {"orders": [{"measure": {"aggregator_field": {}}}]}}, "group_expression_rule": {}}}}]}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.GetSegmentTaskResponseHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.GetSegmentTaskResponse"}}, "summary": "获取分群任务状态", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.GetSegmentTaskResponse", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.GetSegmentTaskRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SegmentServiceGrpc.SegmentServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "GetSegmentTaskRequest", "tags": ["SdhSegment"]}}, "/api/v3/horizon/v1/segment/task/cancel": {"post": {"description": "停止分群任务", "operationId": "CancelSegmentTask", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.CancelSegmentTaskRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.common.HttpApiResult"}}}, "description": ""}}, "summary": "停止分群任务", "x-sd-openapi": {"responseClass": "com.google.protobuf.Empty", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.CancelSegmentTaskRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.SegmentServiceGrpc.SegmentServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "CancelSegmentTaskRequest", "tags": ["SdhSegment"]}}, "/api/v3/horizon/v1/tag/definition/list": {"post": {"description": "查询标签列表", "operationId": "ListTagDefinitions", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListTagDefinitionsRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"tags": [{"expression": {"eql_based_rule": {}, "segment_based_rule": {"groups": [{"segment": {"eql_segment_rule": {}, "entity_set_rules": [{}], "sql_segment_rule": {"query_options": [{}]}, "loading_segment_rule": {}, "custom_rule": {}, "event_sequence_rule": {"simple_event_sequences": [{"time_window": {}, "time_range": {}, "event_steps": [{"condition": {"index": 10, "conditions": [{"function": "EQUAL", "index": 10, "params": [{"expression": "user.p1/user.p2+user.p3", "field": "#ResourceId", "time_point": {"relative_time": {"time_interval": {}}}, "variable": "$ENTRY_TIME", "value": {}, "param_type": "FIELD"}]}], "operator": "AND"}}]}]}, "tailor": {"rank_field": {"orders": [{"measure": {"aggregator_field": {}}}]}}, "group_expression_rule": {}}}]}, "basic_measure_based_rule": {}, "rfm_based_rule": {"r_factor": {"factor_group_rules": [{}]}}, "sql_based_rule": {"query_options": [{}]}, "loading_based_rule": {}, "custom_based_rule": {}}, "alarm_config": {"alarm_rules": [{}]}, "tag_post_actions": [{}], "trigger": {"cron_trigger": {}, "periodic_trigger": {}, "static_trigger": {}}, "source": {}, "storage_settings": {}, "notify_params": {"subscribe_param": {"contents": [{}]}}}]}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListTagDefinitionsResponseHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.ListTagDefinitionsResponse"}}, "summary": "查询标签列表", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.ListTagDefinitionsResponse", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.ListTagDefinitionsRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.TagServiceGrpc.TagServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "ListTagDefinitionsRequest", "tags": ["SdhTag"]}}, "/api/v3/horizon/v1/tag/definition/get": {"post": {"description": "查询单个标签信息", "operationId": "GetTagDefinition", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.GetTagDefinitionRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"expression": {"eql_based_rule": {}, "segment_based_rule": {"groups": [{"segment": {"eql_segment_rule": {}, "entity_set_rules": [{}], "sql_segment_rule": {"query_options": [{}]}, "loading_segment_rule": {}, "custom_rule": {}, "event_sequence_rule": {"simple_event_sequences": [{"time_window": {}, "time_range": {}, "event_steps": [{"condition": {"index": 10, "conditions": [{"function": "EQUAL", "index": 10, "params": [{"expression": "user.p1/user.p2+user.p3", "field": "#ResourceId", "time_point": {"relative_time": {"time_interval": {}}}, "variable": "$ENTRY_TIME", "value": {}, "param_type": "FIELD"}]}], "operator": "AND"}}]}]}, "tailor": {"rank_field": {"orders": [{"measure": {"aggregator_field": {}}}]}}, "group_expression_rule": {}}}]}, "basic_measure_based_rule": {}, "rfm_based_rule": {"r_factor": {"factor_group_rules": [{}]}}, "sql_based_rule": {"query_options": [{}]}, "loading_based_rule": {}, "custom_based_rule": {}}, "alarm_config": {"alarm_rules": [{}]}, "tag_post_actions": [{}], "trigger": {"cron_trigger": {}, "periodic_trigger": {}, "static_trigger": {}}, "source": {}, "storage_settings": {}, "notify_params": {"subscribe_param": {"contents": [{}]}}}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagDefinitionHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.TagDefinition"}}, "summary": "查询单个标签信息", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.TagDefinition", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.GetTagDefinitionRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.TagServiceGrpc.TagServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "GetTagDefinitionRequest", "tags": ["SdhTag"]}}, "/api/v3/horizon/v1/tag/definition/create": {"post": {"description": "创建标签定义", "operationId": "CreateTagDefinition", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.CreateTagDefinitionRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"expression": {"eql_based_rule": {}, "segment_based_rule": {"groups": [{"segment": {"eql_segment_rule": {}, "entity_set_rules": [{}], "sql_segment_rule": {"query_options": [{}]}, "loading_segment_rule": {}, "custom_rule": {}, "event_sequence_rule": {"simple_event_sequences": [{"time_window": {}, "time_range": {}, "event_steps": [{"condition": {"index": 10, "conditions": [{"function": "EQUAL", "index": 10, "params": [{"expression": "user.p1/user.p2+user.p3", "field": "#ResourceId", "time_point": {"relative_time": {"time_interval": {}}}, "variable": "$ENTRY_TIME", "value": {}, "param_type": "FIELD"}]}], "operator": "AND"}}]}]}, "tailor": {"rank_field": {"orders": [{"measure": {"aggregator_field": {}}}]}}, "group_expression_rule": {}}}]}, "basic_measure_based_rule": {}, "rfm_based_rule": {"r_factor": {"factor_group_rules": [{}]}}, "sql_based_rule": {"query_options": [{}]}, "loading_based_rule": {}, "custom_based_rule": {}}, "alarm_config": {"alarm_rules": [{}]}, "tag_post_actions": [{}], "trigger": {"cron_trigger": {}, "periodic_trigger": {}, "static_trigger": {}}, "source": {}, "storage_settings": {}, "notify_params": {"subscribe_param": {"contents": [{}]}}}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagDefinitionHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.TagDefinition"}}, "summary": "创建标签定义", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.TagDefinition", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.CreateTagDefinitionRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.TagServiceGrpc.TagServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "CreateTagDefinitionRequest", "tags": ["SdhTag"]}}, "/api/v3/horizon/v1/tag/definition/update": {"post": {"description": "修改标签定义", "operationId": "UpdateTagDefinition", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.UpdateTagDefinitionRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"expression": {"eql_based_rule": {}, "segment_based_rule": {"groups": [{"segment": {"eql_segment_rule": {}, "entity_set_rules": [{}], "sql_segment_rule": {"query_options": [{}]}, "loading_segment_rule": {}, "custom_rule": {}, "event_sequence_rule": {"simple_event_sequences": [{"time_window": {}, "time_range": {}, "event_steps": [{"condition": {"index": 10, "conditions": [{"function": "EQUAL", "index": 10, "params": [{"expression": "user.p1/user.p2+user.p3", "field": "#ResourceId", "time_point": {"relative_time": {"time_interval": {}}}, "variable": "$ENTRY_TIME", "value": {}, "param_type": "FIELD"}]}], "operator": "AND"}}]}]}, "tailor": {"rank_field": {"orders": [{"measure": {"aggregator_field": {}}}]}}, "group_expression_rule": {}}}]}, "basic_measure_based_rule": {}, "rfm_based_rule": {"r_factor": {"factor_group_rules": [{}]}}, "sql_based_rule": {"query_options": [{}]}, "loading_based_rule": {}, "custom_based_rule": {}}, "alarm_config": {"alarm_rules": [{}]}, "tag_post_actions": [{}], "trigger": {"cron_trigger": {}, "periodic_trigger": {}, "static_trigger": {}}, "source": {}, "storage_settings": {}, "notify_params": {"subscribe_param": {"contents": [{}]}}}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagDefinitionHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.TagDefinition"}}, "summary": "修改标签定义", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.TagDefinition", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.UpdateTagDefinitionRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.TagServiceGrpc.TagServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "UpdateTagDefinitionRequest", "tags": ["SdhTag"]}}, "/api/v3/horizon/v1/tag/definition/evaluate": {"post": {"description": "执行标签计算", "operationId": "EvaluateTag", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.EvaluateTagRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.common.HttpApiResult"}}}, "description": ""}}, "summary": "执行标签计算", "x-sd-openapi": {"responseClass": "com.google.protobuf.Empty", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.EvaluateTagRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.TagServiceGrpc.TagServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "EvaluateTagRequest", "tags": ["SdhTag"]}}, "/api/v3/horizon/v1/tag/definition/status/update": {"post": {"description": "更改调度状态", "operationId": "UpdateTagSchedulerStatus", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.UpdateTagSchedulerStatusRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.common.HttpApiResult"}}}, "description": ""}}, "summary": "更改调度状态", "x-sd-openapi": {"responseClass": "com.google.protobuf.Empty", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.UpdateTagSchedulerStatusRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.TagServiceGrpc.TagServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "UpdateTagSchedulerStatusRequest", "tags": ["SdhTag"]}}, "/api/v3/horizon/v1/tag/partition/get": {"post": {"description": "获取标签的 partition 列表", "operationId": "ListTagPartitions", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListTagPartitionsRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"tag_partitions": [{}]}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListTagPartitionsResponseHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.ListTagPartitionsResponse"}}, "summary": "获取标签的 partition 列表", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.ListTagPartitionsResponse", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.ListTagPartitionsRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.TagServiceGrpc.TagServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "ListTagPartitionsRequest", "tags": ["SdhTag"]}}, "/api/v3/horizon/v1/tag/latest-partition/get": {"post": {"description": "获取最新的 partition 信息", "operationId": "GetLastedTagPartition", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.GetLastedTagPartitionRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.GetLastedTagPartitionResponseHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.GetLastedTagPartitionResponse"}}, "summary": "获取最新的 partition 信息", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.GetLastedTagPartitionResponse", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.GetLastedTagPartitionRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.TagServiceGrpc.TagServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "GetLastedTagPartitionRequest", "tags": ["SdhTag"]}}, "/api/v3/horizon/v1/tag/task/get": {"post": {"description": "获取标签任务信息", "operationId": "GetTagTask", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.GetTagTaskRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {"tasks": [{"failure_detail": {}, "rule": {"eql_based_rule": {}, "segment_based_rule": {"groups": [{"segment": {"eql_segment_rule": {}, "entity_set_rules": [{}], "sql_segment_rule": {"query_options": [{}]}, "loading_segment_rule": {}, "custom_rule": {}, "event_sequence_rule": {"simple_event_sequences": [{"time_window": {}, "time_range": {}, "event_steps": [{"condition": {"index": 10, "conditions": [{"function": "EQUAL", "index": 10, "params": [{"expression": "user.p1/user.p2+user.p3", "field": "#ResourceId", "time_point": {"relative_time": {"time_interval": {}}}, "variable": "$ENTRY_TIME", "value": {}, "param_type": "FIELD"}]}], "operator": "AND"}}]}]}, "tailor": {"rank_field": {"orders": [{"measure": {"aggregator_field": {}}}]}}, "group_expression_rule": {}}}]}, "basic_measure_based_rule": {}, "rfm_based_rule": {"r_factor": {"factor_group_rules": [{}]}}, "sql_based_rule": {"query_options": [{}]}, "loading_based_rule": {}, "custom_based_rule": {}}, "tag_task_report": {"tag_loading_report": {}}}]}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.GetTagTaskResponseHttpApiResult"}}}, "description": "sensorsdata.horizon.v1.GetTagTaskResponse"}}, "summary": "获取标签任务信息", "x-sd-openapi": {"responseClass": "com.sensorsdata.horizon.v1.GetTagTaskResponse", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.GetTagTaskRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.TagServiceGrpc.TagServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "GetTagTaskRequest", "tags": ["SdhTag"]}}, "/api/v3/horizon/v1/tag/task/cancel": {"post": {"description": "停止计算任务", "operationId": "CancelTagTask", "parameters": [{"description": "全局唯一的密钥，用于验证和授权访问 API 接口", "in": "header", "name": "api-key", "required": true, "schema": {"type": "string"}}, {"description": "项目名, 指定请求所属项目", "in": "header", "name": "sensorsdata-project", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.CancelTagTaskRequest"}}}, "required": false}, "responses": {"2XX": {"content": {"application/json": {"example": {"code": "SUCCESS", "data": {}, "request_id": "1"}, "schema": {"$ref": "#/components/schemas/sensorsdata.common.HttpApiResult"}}}, "description": ""}}, "summary": "停止计算任务", "x-sd-openapi": {"responseClass": "com.google.protobuf.Empty", "isServerStreaming": false, "requestClass": "com.sensorsdata.horizon.v1.CancelTagTaskRequest", "grpcServiceBase": "com.sensorsdata.horizon.v1.TagServiceGrpc.TagServiceImplBase", "internalDesc": "classes in template for grpc service dynamic invoking", "isClientStreaming": false}, "x-codegen-request-body-name": "CancelTagTaskRequest", "tags": ["SdhTag"]}}}, "components": {"schemas": {"sensorsdata.analytics.v1.EventDefine": {"description": "", "properties": {"id": {"description": "", "format": "int32", "type": "integer"}, "name": {"description": "", "type": "string"}, "cname": {"description": "", "type": "string"}, "is_virtual": {"description": "", "type": "boolean"}, "tags": {"description": "", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "comment": {"description": "", "type": "string"}, "total_count": {"description": "", "format": "int64", "type": "integer"}, "platforms": {"description": "", "items": {"type": "string"}, "type": "array"}, "trigger_opportunity": {"description": "", "type": "string"}, "create_time": {"description": "", "format": "date-time", "type": "string"}, "update_time": {"description": "", "format": "date-time", "type": "string"}}, "title": "EventDefine", "type": "object"}, "sensorsdata.common.HttpApiResult": {"properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"properties": {}, "type": "object"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "HttpApiResult", "type": "object"}, "sensorsdata.analytics.v1.ListEventAllResponse": {"description": "", "properties": {"events": {"description": "事件列表", "items": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.EventDefine"}, "type": "array"}}, "title": "ListEventAllResponse", "type": "object"}, "sensorsdata.analytics.v1.ListEventTagsResponse": {"description": "", "properties": {"tag_infos": {"description": "事件列表", "items": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.TagInfo"}, "type": "array"}}, "title": "ListEventTagsResponse", "type": "object"}, "sensorsdata.analytics.v1.TagInfo": {"description": "", "properties": {"id": {"description": "", "format": "int32", "type": "integer"}, "name": {"description": "", "type": "string"}, "color": {"description": "", "type": "string"}}, "title": "TagInfo", "type": "object"}, "sensorsdata.common.ErrorCause": {"properties": {"error_cause": {"description": "致错原因1", "example": "参数异常1", "type": "string"}, "action_suggestion": {"description": "操作建议，针对该原因，建议的解决方式", "example": "检测入参的数据类型1", "type": "string"}}, "title": "Error<PERSON><PERSON><PERSON>", "type": "object"}, "sensorsdata.common.ErrorContext": {"description": "发生错误时，系统快照信息", "properties": {"origin_stack": {"description": "上游异常的 stack，由系统截获", "type": "string"}, "origin_cause": {"description": "上游异常的 cause by，由系统截获", "type": "string"}, "origin_code": {"description": "上游异常的错误简码", "type": "string"}, "origin_complete_code": {"description": "上游异常的完整错误码", "type": "string"}, "error_extend_desc": {"description": "本次异常的辅助说明，可传入", "type": "string"}}, "title": "ErrorContext", "type": "object"}, "sensorsdata.common.ErrorInfo": {"properties": {"code": {"description": "具体的错误码", "example": "XX-D-F-2-1", "type": "string"}, "description": {"description": "错误描述", "example": "前端参数校验异常,PARAMETER_FORMAT_ERROR(quantiles=[10, -90] is not include [0, 100])", "type": "string"}, "system_response": {"description": "致错的可能原因列表", "example": "系统终止了查询处理", "type": "string"}, "error_causes": {"items": {"$ref": "#/components/schemas/sensorsdata.common.ErrorCause"}, "type": "array"}, "context": {"$ref": "#/components/schemas/sensorsdata.common.ErrorContext"}}, "title": "ErrorInfo", "type": "object"}, "sensorsdata.analytics.v1.ListEventAllResponseHttpApiResult": {"description": "sensorsdata.analytics.v1.ListEventAllResponse", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.ListEventAllResponse"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.analytics.v1.ListEventAllResponseHttpApiResult", "type": "object"}, "sensorsdata.analytics.v1.ListEventTagsResponseHttpApiResult": {"description": "sensorsdata.analytics.v1.ListEventTagsResponse", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.ListEventTagsResponse"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.analytics.v1.ListEventTagsResponseHttpApiResult", "type": "object"}, "sensorsdata.analytics.v1.EventWithProperty": {"description": "", "properties": {"event_define": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.EventDefine"}, "properties": {"description": "事件下的属性信息", "items": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.PropertyDefine"}, "type": "array"}}, "title": "EventWithProperty", "type": "object"}, "sensorsdata.analytics.v1.EventPropertyAllResponse": {"description": "", "properties": {"properties": {"description": "事件属性列表", "items": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.PropertyDefine"}, "type": "array"}}, "title": "EventPropertyAllResponse", "type": "object"}, "sensorsdata.analytics.v1.UserTagDirDefine": {"description": "", "properties": {"name": {"description": "", "type": "string"}, "cname": {"description": "", "type": "string"}, "data_type": {"description": "", "type": "string"}, "type": {"description": "", "enum": ["USER_TAG_TYPE_UNSPECIFIED", "DIR", "TAG"], "type": "string"}, "sub_nodes": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.UserTagDirDefine"}, "type": "array"}}, "title": "UserTagDirDefine", "type": "object"}, "sensorsdata.analytics.v1.UserTagDirResponse": {"description": "", "properties": {"user_tags": {"description": "用户标签列表", "items": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.UserTagDirDefine"}, "type": "array"}}, "title": "UserTagDirResponse", "type": "object"}, "sensorsdata.analytics.v1.PropertyDefine": {"description": "", "properties": {"id": {"description": "", "format": "int32", "type": "integer"}, "name": {"description": "", "type": "string"}, "cname": {"description": "", "type": "string"}, "data_type": {"description": "", "type": "string"}, "is_virtual": {"description": "", "type": "boolean"}, "has_dict": {"description": "", "type": "boolean"}, "unit": {"description": "", "type": "string"}, "platforms": {"description": "", "items": {"type": "string"}, "type": "array"}, "trigger_opportunity": {"description": "", "type": "string"}, "create_time": {"description": "", "format": "date-time", "type": "string"}, "update_time": {"description": "", "format": "date-time", "type": "string"}}, "title": "PropertyDefine", "type": "object"}, "sensorsdata.analytics.v1.EventPropertyRequest": {"description": "", "properties": {"events": {"description": "指定事件列表", "items": {"type": "string"}, "type": "array"}}, "title": "EventPropertyRequest", "type": "object"}, "sensorsdata.analytics.v1.GetPropertyValueRequest": {"description": "", "properties": {"table_type": {"description": "属性类型", "enum": ["TABLE_TYPE_UNSPECIFIED", "EVENT", "USER"], "type": "string"}, "property_name": {"description": "属性名称", "type": "string"}, "limit": {"description": "限制返回值个数", "format": "int32", "type": "integer"}}, "title": "GetPropertyValueRequest", "type": "object"}, "sensorsdata.analytics.v1.UserGroupDefine": {"description": "", "properties": {"id": {"description": "", "format": "int32", "type": "integer"}, "name": {"description": "", "type": "string"}, "cname": {"description": "", "type": "string"}, "data_type": {"description": "", "type": "string"}, "create_time": {"description": "", "format": "date-time", "type": "string"}, "update_time": {"description": "", "format": "date-time", "type": "string"}}, "title": "UserGroupDefine", "type": "object"}, "sensorsdata.analytics.v1.UserGroupAllResponse": {"description": "", "properties": {"user_groups": {"description": "用户分群列表", "items": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.UserGroupDefine"}, "type": "array"}}, "title": "UserGroupAllResponse", "type": "object"}, "sensorsdata.analytics.v1.UserPropertyAllResponse": {"description": "", "properties": {"user_properties": {"description": "用户属性列表", "items": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.PropertyDefine"}, "type": "array"}}, "title": "UserPropertyAllResponse", "type": "object"}, "sensorsdata.analytics.v1.EventPropertyResponse": {"description": "", "properties": {"event_properties": {"description": "事件的属性列表", "items": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.EventWithProperty"}, "type": "array"}}, "title": "EventPropertyResponse", "type": "object"}, "sensorsdata.analytics.v1.GetPropertyValueResponse": {"description": "", "properties": {"values": {"description": "候选值", "items": {"type": "string"}, "type": "array"}}, "title": "GetPropertyValueResponse", "type": "object"}, "sensorsdata.analytics.v1.EventPropertyAllResponseHttpApiResult": {"description": "sensorsdata.analytics.v1.EventPropertyAllResponse", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.EventPropertyAllResponse"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.analytics.v1.EventPropertyAllResponseHttpApiResult", "type": "object"}, "sensorsdata.analytics.v1.EventPropertyResponseHttpApiResult": {"description": "sensorsdata.analytics.v1.EventPropertyResponse", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.EventPropertyResponse"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.analytics.v1.EventPropertyResponseHttpApiResult", "type": "object"}, "sensorsdata.analytics.v1.UserPropertyAllResponseHttpApiResult": {"description": "sensorsdata.analytics.v1.UserPropertyAllResponse", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.UserPropertyAllResponse"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.analytics.v1.UserPropertyAllResponseHttpApiResult", "type": "object"}, "sensorsdata.analytics.v1.UserGroupAllResponseHttpApiResult": {"description": "sensorsdata.analytics.v1.UserGroupAllResponse", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.UserGroupAllResponse"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.analytics.v1.UserGroupAllResponseHttpApiResult", "type": "object"}, "sensorsdata.analytics.v1.UserTagDirResponseHttpApiResult": {"description": "sensorsdata.analytics.v1.UserTagDirResponse", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.UserTagDirResponse"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.analytics.v1.UserTagDirResponseHttpApiResult", "type": "object"}, "sensorsdata.analytics.v1.GetPropertyValueResponseHttpApiResult": {"description": "sensorsdata.analytics.v1.GetPropertyValueResponse", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.analytics.v1.GetPropertyValueResponse"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.analytics.v1.GetPropertyValueResponseHttpApiResult", "type": "object"}, "sensorsdata.horizon.v1.GetSchemaByOriginalNameRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "physical_schema_name": {"description": "", "type": "string"}, "original_name": {"description": "", "type": "string"}, "expand": {"description": "", "type": "string"}}, "required": ["original_name", "physical_schema_name", "project_id"], "title": "GetSchemaByOriginalNameRequest", "type": "object"}, "sensorsdata.horizon.v1.Schema.SchemaMapping": {"description": "", "properties": {"target_schema_name": {"description": "", "type": "string"}, "condition": {"$ref": "#/components/schemas/sensorsdata.common.CompoundFilterCondition"}, "raw_condition": {"$ref": "#/components/schemas/sensorsdata.common.CompoundFilterCondition"}}, "required": ["target_schema_name"], "title": "<PERSON><PERSON>aMapping", "type": "object"}, "sensorsdata.horizon.v1.SchemaDataMapping.SchemaSourceTable": {"description": "", "properties": {"db_name": {"description": "", "type": "string"}, "table_name": {"description": "", "type": "string"}, "condition": {"description": "", "type": "string"}}, "title": "SchemaSourceTable", "type": "object"}, "sensorsdata.horizon.v1.UpdateSchemaFieldRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "schema_name": {"description": "", "type": "string"}, "field": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.Field"}, "dimension_dict": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SchemaFieldDimensionDict"}, "update_mask": {"description": "", "type": "string"}}, "required": ["field", "project_id", "schema_name", "update_mask"], "title": "UpdateSchemaFieldRequest", "type": "object"}, "sensorsdata.horizon.v1.SchemaExtendedTable": {"description": "", "properties": {"extended_table_id": {"description": "", "type": "string"}, "schema_name": {"description": "", "type": "string"}, "db_name": {"description": "", "type": "string"}, "table_name": {"description": "", "type": "string"}, "table_display_type": {"description": "", "type": "string"}, "display_name": {"description": "", "type": "string"}, "join_on_expression": {"description": "", "type": "string"}, "join_key_columns": {"description": "", "items": {"type": "string"}, "type": "array"}, "join_mode": {"description": "", "enum": ["SCHEMA_TABLE_JOIN_MODE_UNSPECIFIED", "INNER_JOIN", "LEFT_JOIN", "RIGHT_JOIN", "LEFT_ANTI_JOIN", "LEFT_SEMI_JOIN", "FULL_OUTER_JOIN"], "type": "string"}, "access_info": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.AccessInfo"}}, "required": ["db_name", "extended_table_id", "join_on_expression", "schema_name", "table_name"], "title": "SchemaExtendedTable", "type": "object"}, "sensorsdata.horizon.v1.Field.CustomParamsEntry": {"description": "", "properties": {"key": {"description": "", "type": "string"}, "value": {"description": "", "type": "string"}}, "title": "CustomParamsEntry", "type": "object"}, "sensorsdata.horizon.v1.BatchCreateSchemaFieldsRequest.FieldCreateDescriptor": {"description": "", "properties": {"schema_name": {"description": "", "type": "string"}, "field": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.Field"}, "dimension_dict": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SchemaFieldDimensionDict"}}, "required": ["field", "schema_name"], "title": "FieldCreateDescriptor", "type": "object"}, "sensorsdata.horizon.v1.SchemaFieldDimensionDict": {"description": "", "properties": {"dict": {"additionalProperties": {"description": "", "type": "string"}, "description": "", "type": "object"}, "upload_mode": {"description": "", "enum": ["UPLOAD_TYPE_UNSPECIFIED", "OVERWRITE", "INCREMENT_IGNORE_DUPLICATE", "INCREMENT_OVERWRITE_DUPLICATE"], "type": "string"}}, "required": ["dict"], "title": "SchemaFieldDimensionDict", "type": "object"}, "sensorsdata.horizon.v1.Field.ObjectField": {"description": "", "properties": {"name": {"description": "", "type": "string"}, "display_name": {"description": "", "type": "string"}, "data_type": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.DataType"}, "description": {"description": "", "type": "string"}}, "title": "ObjectField", "type": "object"}, "sensorsdata.horizon.v1.SchemaStatistics.SchemaTrackPlatform": {"description": "", "properties": {"platform": {"description": "", "type": "string"}, "has_data": {"description": "", "type": "boolean"}}, "required": ["platform"], "title": "SchemaTrackPlatform", "type": "object"}, "sensorsdata.horizon.v1.UpdateSchemaRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "schema": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.Schema"}, "update_mask": {"description": "", "type": "string"}}, "required": ["project_id", "schema", "update_mask"], "title": "UpdateSchemaRequest", "type": "object"}, "sensorsdata.horizon.v1.BatchCreateSchemaFieldsResponse": {"description": "", "title": "BatchCreateSchemaFieldsResponse", "type": "object"}, "sensorsdata.horizon.v1.DataType": {"description": "数据类型", "properties": {"type": {"description": "数据类型", "enum": ["DATA_TYPE_UNSPECIFIED", "BOOL", "INT", "NUMBER", "BIGINT", "DECIMAL", "STRING", "DATETIME", "DATE", "TIMESTAMP", "LIST", "OBJECT_ARRAY", "UNKNOWN_DATA_TYPE"], "example": "STRING", "type": "string"}, "number_decimal_precision": {"description": "精度\n 例如，数字 123.45 的精度是 5", "format": "int32", "type": "integer"}, "number_decimal_scale": {"description": "小数位数\n 例如，数字 123.45 的小数位数是 2", "format": "int32", "type": "integer"}}, "required": ["type"], "title": "DataType", "type": "object"}, "sensorsdata.common.TimePoint": {"description": "", "properties": {"type": {"description": "", "enum": ["TIME_POINT_TYPE_UNSPECIFIED", "STATIC", "RELATIVE", "EXPRESSION"], "type": "string"}, "trunc_unit": {"description": "", "enum": ["TRUNC_UNIT_UNSPECIFIED", "TRUNC_HOUR", "TRUNC_DAY", "TRUNC_WEEK", "TRUNC_MONTH", "TRUNC_YEAR", "TRUNC_BIGBANG"], "type": "string"}, "static_time": {"description": "", "format": "date-time", "type": "string"}, "relative_time": {"$ref": "#/components/schemas/sensorsdata.common.TimePoint.RelativeTimePoint"}, "expression": {"description": "", "type": "string"}}, "title": "TimePoint", "type": "object"}, "sensorsdata.horizon.v1.ListLogicalSchemasResponse": {"description": "", "properties": {"schemas": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.Schema"}, "type": "array"}, "total_size": {"description": "", "format": "int32", "type": "integer"}, "has_next": {"description": "", "type": "boolean"}}, "title": "ListLogicalSchemasResponse", "type": "object"}, "sensorsdata.horizon.v1.FieldViewDataSource.ViewColumnSourceData": {"description": "", "properties": {"extended_table_id": {"description": "", "type": "string"}, "extended_table_condition": {"description": "", "type": "string"}, "column_name": {"description": "", "type": "string"}, "alias_view_name": {"description": "", "type": "string"}}, "required": ["extended_table_id"], "title": "ViewColumnSourceData", "type": "object"}, "sensorsdata.horizon.v1.SchemaDataMapping": {"description": "", "properties": {"view_table": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SchemaDataMapping.SchemaSourceTable"}, "main_table": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SchemaDataMapping.SchemaSourceTable"}, "extended_tables": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SchemaExtendedTable"}, "type": "array"}}, "title": "SchemaDataMapping", "type": "object"}, "sensorsdata.horizon.v1.ListSchemaFieldsRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "schema_name": {"description": "", "type": "string"}, "field_names": {"description": "", "items": {"type": "string"}, "type": "array"}, "field_display_names": {"description": "", "items": {"type": "string"}, "type": "array"}, "expand": {"description": "", "type": "string"}}, "required": ["project_id", "schema_name"], "title": "ListSchemaFieldsRequest", "type": "object"}, "sensorsdata.horizon.v1.FieldUnit": {"description": "", "properties": {"name": {"description": "", "type": "string"}, "display_name": {"description": "", "type": "string"}, "builtin": {"description": "", "type": "boolean"}, "data_type": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.DataType"}, "base": {"description": "", "format": "int32", "type": "integer"}, "unit_group_name": {"description": "", "type": "string"}, "unit_group_display_name": {"description": "", "type": "string"}, "access_info": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.AccessInfo"}}, "required": ["display_name"], "title": "FieldUnit", "type": "object"}, "sensorsdata.common.FilterCondition": {"description": "单个过滤条件", "properties": {"function": {"description": "过滤条件的判断函数", "enum": ["PREDICATE_FUNCTION_TYPE_UNSPECIFIED", "IS_SET", "IS_NOT_SET", "EQUAL", "NOT_EQUAL", "IN", "NOT_IN", "LESS", "LESS_EQUAL", "GREATER", "GREATER_EQUAL", "BETWEEN", "RIGHT_OPEN_BETWEEN", "HASH_BETWEEN", "LEFT_OPEN_BETWEEN", "OPEN_BETWEEN", "IS_TRUE", "IS_FALSE", "CONTAIN", "NOT_CONTAIN", "IS_EMPTY", "IS_NOT_EMPTY", "RLIKE", "NOT_RLIKE", "INCLUDE", "NOT_INCLUDE", "ABSOLUTE_BETWEEN", "RELATIVE_WITHIN", "RELATIVE_BETWEEN", "RELATIVE_EVENT_TIME", "RELATIVE_BEFORE", "INTERSECTS", "NOT_INTERSECTS", "SUBSET_OF", "NOT_SUBSET_OF", "SUPERSET_OF", "NOT_SUPERSET_OF"], "example": "EQUAL", "type": "string"}, "params": {"description": "函数的参数", "items": {"$ref": "#/components/schemas/sensorsdata.common.PredicateFunctionParam"}, "type": "array"}, "index": {"description": "当前筛选条件所处的位置下标，主要为了保证前端展示顺序的稳定", "example": 10, "format": "int32", "type": "integer"}}, "title": "FilterCondition", "type": "object"}, "sensorsdata.horizon.v1.ListLogicalSchemasRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "physical_schema_name": {"description": "", "type": "string"}, "schema_type": {"description": "", "enum": ["SCHEMA_TYPE_UNSPECIFIED", "PHYSICAL", "LOGICAL", "VIRTUAL"], "type": "string"}, "contain_field_names": {"description": "", "items": {"type": "string"}, "type": "array"}, "display_names": {"description": "", "items": {"type": "string"}, "type": "array"}, "expand": {"description": "", "type": "string"}, "page": {"description": "", "format": "int32", "type": "integer"}, "page_size": {"description": "", "format": "int32", "type": "integer"}}, "required": ["physical_schema_name", "project_id"], "title": "ListLogicalSchemasRequest", "type": "object"}, "sensorsdata.horizon.v1.GetSchemaFieldRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "schema_name": {"description": "", "type": "string"}, "field_name": {"description": "", "type": "string"}, "expand": {"description": "", "type": "string"}}, "required": ["field_name", "project_id", "schema_name"], "title": "GetSchemaFieldRequest", "type": "object"}, "sensorsdata.common.PredicateFunctionParam": {"description": "```\n 函数的参数，有几种可能\n 1. 一个 field, 如 events.$Anything.$city\n 2. 一个 Literal 的常量\n 3. 一个变量, 如 $ENTRY_TIME, 需要在执行上下文里进行替换\n 4. 一个 TimePoint, 代表一个相对或绝对的时间点, 用于时间类型的条件\n 5. 一个 expression user.p1/user.p2+user.p3\n ```", "properties": {"param_type": {"description": "函数的参数类型", "enum": ["FUNCTION_PARAM_TYPE_UNSPECIFIED", "FIELD", "VALUE", "VARIABLE", "TIME_POINT", "EXPRESSION"], "example": "FIELD", "type": "string"}, "field": {"description": "字段名", "example": "#ResourceId", "type": "string"}, "value": {"$ref": "#/components/schemas/sensorsdata.common.Literal"}, "variable": {"description": "变量，需要在执行上下文里进行替换", "example": "$ENTRY_TIME", "type": "string"}, "time_point": {"$ref": "#/components/schemas/sensorsdata.common.TimePoint"}, "expression": {"description": "自定义表达式", "example": "user.p1/user.p2+user.p3", "type": "string"}}, "title": "PredicateFunctionParam", "type": "object"}, "sensorsdata.horizon.v1.UpdateSchemaExtendedTableRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "extended_table": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SchemaExtendedTable"}, "update_mask": {"description": "", "type": "string"}}, "required": ["extended_table", "project_id", "update_mask"], "title": "UpdateSchemaExtendedTableRequest", "type": "object"}, "sensorsdata.common.CompoundFilterCondition": {"description": "复合过滤条件，用于描述 condition 或 compound_filter 之间的关系", "properties": {"operator": {"description": "逻辑运算法", "enum": ["LOGICAL_OPERATOR_UNSPECIFIED", "AND", "OR"], "example": "AND", "type": "string"}, "conditions": {"description": "过滤条件列表，只有最后一级才有", "items": {"$ref": "#/components/schemas/sensorsdata.common.FilterCondition"}, "type": "array"}, "compound_conditions": {"description": "嵌套的下一级复合过滤条件", "items": {"$ref": "#/components/schemas/sensorsdata.common.CompoundFilterCondition"}, "type": "array"}, "index": {"description": "当前筛选条件所处的位置下标，主要为了保证前端展示顺序的稳定", "example": 10, "format": "int32", "type": "integer"}}, "title": "CompoundFilterCondition", "type": "object"}, "sensorsdata.horizon.v1.FieldViewDataSource.ComplexExpression": {"description": "", "properties": {"extended_table_ids": {"description": "", "items": {"type": "string"}, "type": "array"}, "expression": {"description": "", "type": "string"}, "related_mode": {"description": "", "enum": ["RELATED_MODE_UNSPECIFIED", "CONTAINS_ALL", "CONTAINS_ANY"], "type": "string"}}, "required": ["expression"], "title": "ComplexExpression", "type": "object"}, "sensorsdata.horizon.v1.BatchCreateSchemaExtendedTablesRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "extended_tables": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SchemaExtendedTable"}, "type": "array"}}, "required": ["extended_tables", "project_id"], "title": "BatchCreateSchemaExtendedTablesRequest", "type": "object"}, "sensorsdata.horizon.v1.SchemaFieldDimensionDict.DictEntry": {"description": "", "properties": {"key": {"description": "", "type": "string"}, "value": {"description": "", "type": "string"}}, "title": "DictEntry", "type": "object"}, "sensorsdata.horizon.v1.BatchCreateSchemaFieldsRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "fields": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.BatchCreateSchemaFieldsRequest.FieldCreateDescriptor"}, "type": "array"}}, "required": ["fields", "project_id"], "title": "BatchCreateSchemaFieldsRequest", "type": "object"}, "sensorsdata.horizon.v1.Schema": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "name": {"description": "", "type": "string"}, "original_name": {"description": "", "type": "string"}, "display_name": {"description": "", "type": "string"}, "type": {"description": "", "enum": ["SCHEMA_TYPE_UNSPECIFIED", "PHYSICAL", "LOGICAL", "VIRTUAL"], "type": "string"}, "schema_class": {"description": "", "enum": ["SCHEMA_CLASS_UNSPECIFIED", "EVENT", "USER", "ITEM", "DETAIL"], "type": "string"}, "schema_display_type": {"description": "", "type": "string"}, "builtin": {"description": "", "type": "boolean"}, "has_data": {"description": "", "type": "boolean"}, "visible": {"description": "", "type": "boolean"}, "enable": {"description": "", "type": "boolean"}, "mapping": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.Schema.SchemaMapping"}, "data_mapping": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SchemaDataMapping"}, "schema_data_mapping_type": {"description": "", "enum": ["SCHEMA_DATA_MAPPING_TYPE_UNSPECIFIED", "BUILTIN_TABLE", "REFERENCE_TABLE"], "type": "string"}, "custom_params": {"additionalProperties": {"description": "", "type": "string"}, "description": "", "type": "object"}, "fields": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.Field"}, "type": "array"}, "version": {"description": "", "format": "int32", "type": "integer"}, "access_info": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.AccessInfo"}, "statistics": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SchemaStatistics"}, "legacy_id": {"description": "", "format": "int32", "type": "integer"}}, "required": ["original_name", "schema_class", "type"], "title": "<PERSON><PERSON><PERSON>", "type": "object"}, "sensorsdata.horizon.v1.ListSchemaFieldsResponse": {"description": "", "properties": {"fields": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.Field"}, "type": "array"}}, "title": "ListSchemaFieldsResponse", "type": "object"}, "sensorsdata.horizon.v1.Field": {"description": "", "properties": {"name": {"description": "", "type": "string"}, "display_name": {"description": "", "type": "string"}, "data_type": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.DataType"}, "field_display_type": {"description": "", "type": "string"}, "created_by": {"description": "", "type": "string"}, "builtin": {"description": "", "type": "boolean"}, "has_data": {"description": "", "type": "boolean"}, "has_dict": {"description": "", "type": "boolean"}, "visible": {"description": "", "type": "boolean"}, "enable": {"description": "", "type": "boolean"}, "subscribable": {"description": "", "type": "boolean"}, "primary_identity": {"description": "", "type": "boolean"}, "identity": {"description": "", "type": "boolean"}, "required": {"description": "", "type": "boolean"}, "data_versioned": {"description": "", "type": "boolean"}, "data_version_form": {"description": "", "enum": ["FIELD_VERSION_LEVEL_UNSPECIFIED", "HOUR", "DAY", "WEEK", "MONTH", "YEAR", "CUSTOM"], "type": "string"}, "data_mapping": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.FieldDataMapping"}, "view_data_source": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.FieldViewDataSource"}, "custom_params": {"additionalProperties": {"description": "", "type": "string"}, "description": "", "type": "object"}, "unit": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.FieldUnit"}, "object_fields": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.Field.ObjectField"}, "type": "array"}, "version": {"description": "", "format": "int32", "type": "integer"}, "access_info": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.AccessInfo"}, "legacy_id": {"description": "", "format": "int32", "type": "integer"}}, "required": ["created_by", "data_type", "name"], "title": "Field", "type": "object"}, "sensorsdata.horizon.v1.FieldViewDataSource": {"description": "", "properties": {"field_name": {"description": "", "type": "string"}, "view_column_source_data": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.FieldViewDataSource.ViewColumnSourceData"}, "complex_expression": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.FieldViewDataSource.ComplexExpression"}, "inline_expression": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.FieldViewDataSource.InlineExpression"}, "version": {"description": "", "format": "int32", "type": "integer"}}, "required": ["field_name"], "title": "FieldViewDataSource", "type": "object"}, "sensorsdata.common.TimePoint.RelativeTimePoint": {"description": "", "properties": {"base_time": {"description": "", "type": "string"}, "time_interval": {"$ref": "#/components/schemas/sensorsdata.common.TimeInterval"}}, "title": "RelativeTimePoint", "type": "object"}, "sensorsdata.common.Literal": {"description": "", "properties": {"data_type": {"description": "", "enum": ["DATA_TYPE_UNSPECIFIED", "BOOL", "INT", "NUMBER", "STRING", "LIST", "DATETIME", "DATE", "BIGINT", "DECIMAL"], "type": "string"}, "bool_value": {"description": "", "type": "boolean"}, "int_value": {"description": "", "format": "int64", "type": "integer"}, "number_value": {"description": "", "format": "double", "type": "number"}, "string_value": {"description": "", "type": "string"}, "list_value": {"description": "", "items": {"type": "string"}, "type": "array"}, "datetime_value": {"description": "", "format": "date-time", "type": "string"}, "date_value": {"description": "", "type": "string"}, "bigint_value": {"description": "", "format": "int64", "type": "integer"}, "decimal_value": {"description": "", "type": "string"}}, "title": "Literal", "type": "object"}, "sensorsdata.horizon.v1.FieldDataMapping.ExtendedColumn": {"description": "", "properties": {"extended_table_id": {"description": "", "type": "string"}, "column_name": {"description": "", "type": "string"}, "version_column": {"description": "", "type": "string"}}, "required": ["extended_table_id"], "title": "ExtendedColumn", "type": "object"}, "sensorsdata.horizon.v1.BatchCreateLogicalSchemasRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "physical_schema_name": {"description": "", "type": "string"}, "schemas": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.Schema"}, "type": "array"}}, "required": ["physical_schema_name", "project_id", "schemas"], "title": "BatchCreateLogicalSchemasRequest", "type": "object"}, "sensorsdata.horizon.v1.SchemaStatistics": {"description": "", "properties": {"last_30_day_count": {"description": "", "format": "int64", "type": "integer"}, "track_platforms": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SchemaStatistics.SchemaTrackPlatform"}, "type": "array"}, "access_info": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.AccessInfo"}}, "title": "SchemaStatistics", "type": "object"}, "sensorsdata.horizon.v1.ListSchemaExtendedTablesRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "schema_names": {"description": "", "items": {"type": "string"}, "type": "array"}, "table_display_types": {"description": "", "items": {"type": "string"}, "type": "array"}, "expand": {"description": "", "type": "string"}}, "required": ["project_id", "schema_names"], "title": "ListSchemaExtendedTablesRequest", "type": "object"}, "sensorsdata.common.TimeInterval": {"description": "", "properties": {"size": {"description": "", "format": "int32", "type": "integer"}, "unit": {"description": "", "enum": ["DATE_TIME_UNIT_UNSPECIFIED", "MILLISECOND", "SECOND", "MINUTE", "HOUR", "DAY", "WEEK", "MONTH", "YEAR"], "type": "string"}}, "title": "TimeInterval", "type": "object"}, "sensorsdata.horizon.v1.GetSchemaRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "name": {"description": "", "type": "string"}, "expand": {"description": "", "type": "string"}}, "required": ["name", "project_id"], "title": "GetSchemaRequest", "type": "object"}, "sensorsdata.horizon.v1.Schema.CustomParamsEntry": {"description": "", "properties": {"key": {"description": "", "type": "string"}, "value": {"description": "", "type": "string"}}, "title": "CustomParamsEntry", "type": "object"}, "sensorsdata.horizon.v1.FieldViewDataSource.InlineExpression": {"description": "", "properties": {"expression": {"description": "", "type": "string"}, "related_mode": {"description": "", "enum": ["RELATED_MODE_UNSPECIFIED", "CONTAINS_ALL", "CONTAINS_ANY"], "type": "string"}}, "required": ["expression", "related_mode"], "title": "InlineExpression", "type": "object"}, "sensorsdata.horizon.v1.ListSchemaExtendedTablesResponse": {"description": "", "properties": {"extended_tables": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SchemaExtendedTable"}, "type": "array"}}, "title": "ListSchemaExtendedTablesResponse", "type": "object"}, "sensorsdata.horizon.v1.FieldDataMapping": {"description": "", "properties": {"view_column_name": {"description": "", "type": "string"}, "source_type": {"description": "", "enum": ["FIELD_SOURCE_TYPE_UNSPECIFIED", "MAIN_TABLE_COLUMN", "EXTENDED_TABLE_COLUMN", "COMPLEX_EXPRESSION", "INLINE_EXPRESSION"], "type": "string"}, "extended_column": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.FieldDataMapping.ExtendedColumn"}}, "title": "FieldDataMapping", "type": "object"}, "sensorsdata.horizon.v1.AccessInfo": {"description": "数据访问信息", "properties": {"creator_id": {"description": "创建人 ID", "type": "string"}, "modifier_id": {"description": "最近一次修改人 ID", "type": "string"}, "create_time": {"description": "创建时间", "format": "date-time", "type": "string"}, "update_time": {"description": "最近一次修改时间", "format": "date-time", "type": "string"}}, "title": "AccessInfo", "type": "object"}, "sensorsdata.horizon.v1.ListLogicalSchemasResponseHttpApiResult": {"description": "sensorsdata.horizon.v1.ListLogicalSchemasResponse", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListLogicalSchemasResponse"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.horizon.v1.ListLogicalSchemasResponseHttpApiResult", "type": "object"}, "sensorsdata.horizon.v1.SchemaHttpApiResult": {"description": "sensorsdata.horizon.v1.<PERSON><PERSON>a", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.Schema"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.horizon.v1.SchemaHttpApiResult", "type": "object"}, "sensorsdata.horizon.v1.ListSchemaFieldsResponseHttpApiResult": {"description": "sensorsdata.horizon.v1.ListSchemaFieldsResponse", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListSchemaFieldsResponse"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.horizon.v1.ListSchemaFieldsResponseHttpApiResult", "type": "object"}, "sensorsdata.horizon.v1.FieldHttpApiResult": {"description": "sensorsdata.horizon.v1.Field", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.Field"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.horizon.v1.FieldHttpApiResult", "type": "object"}, "sensorsdata.horizon.v1.BatchCreateSchemaFieldsResponseHttpApiResult": {"description": "sensorsdata.horizon.v1.BatchCreateSchemaFieldsResponse", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.BatchCreateSchemaFieldsResponse"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.horizon.v1.BatchCreateSchemaFieldsResponseHttpApiResult", "type": "object"}, "sensorsdata.horizon.v1.ListSchemaExtendedTablesResponseHttpApiResult": {"description": "sensorsdata.horizon.v1.ListSchemaExtendedTablesResponse", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListSchemaExtendedTablesResponse"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.horizon.v1.ListSchemaExtendedTablesResponseHttpApiResult", "type": "object"}, "sensorsdata.horizon.v1.SchemaExtendedTableHttpApiResult": {"description": "sensorsdata.horizon.v1.SchemaExtendedTable", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SchemaExtendedTable"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.horizon.v1.SchemaExtendedTableHttpApiResult", "type": "object"}, "sensorsdata.horizon.v1.SegmentAlarmConfig.AlarmRule.AlarmParamsEntry": {"description": "", "properties": {"key": {"description": "", "type": "string"}, "value": {"description": "", "type": "string"}}, "title": "AlarmParamsEntry", "type": "object"}, "sensorsdata.horizon.v1.CustomSegmentRuleExpression": {"description": "", "properties": {"component_name": {"description": "", "type": "string"}, "rule": {"description": "", "properties": {}, "type": "object"}, "rule_str": {"description": "", "type": "string"}}, "title": "CustomSegmentRuleExpression", "type": "object"}, "sensorsdata.horizon.v1.GroupExpressionSegmentRuleExpression": {"description": "", "properties": {"groups": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentRuleExpression"}, "type": "array"}, "group_expression": {"description": "", "type": "string"}}, "title": "GroupExpressionSegmentRuleExpression", "type": "object"}, "sensorsdata.horizon.v1.Order": {"description": "", "properties": {"measure": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.OrderMeasure"}, "is_asc": {"description": "", "type": "boolean"}}, "title": "Order", "type": "object"}, "sensorsdata.horizon.v1.EventSequenceSegmentRuleExpression.SimpleEventSequenceSegmentRuleExpression": {"description": "", "properties": {"event_steps": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.EventSequenceSegmentRuleExpression.SimpleEventSequenceSegmentRuleExpression.EventStep"}, "type": "array"}, "time_range": {"$ref": "#/components/schemas/sensorsdata.common.TimeRange"}, "time_window": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.EventSequenceSegmentRuleExpression.SimpleEventSequenceSegmentRuleExpression.TimeWindow"}}, "title": "SimpleEventSequenceSegmentRuleExpression", "type": "object"}, "sensorsdata.common.Trigger": {"description": "", "properties": {"trigger_type": {"description": "", "enum": ["TRIGGER_TYPE_UNSPECIFIED", "MANUAL", "PERIODIC", "CRON", "STATIC", "DYNAMIC"], "type": "string"}, "periodic_trigger": {"$ref": "#/components/schemas/sensorsdata.common.PeriodicTrigger"}, "cron_trigger": {"$ref": "#/components/schemas/sensorsdata.common.CronTrigger"}, "static_trigger": {"$ref": "#/components/schemas/sensorsdata.common.StaticTrigger"}}, "title": "<PERSON><PERSON>", "type": "object"}, "sensorsdata.horizon.v1.SegmentNotifyParams.NotifyParam": {"description": "", "properties": {"enable": {"description": "", "type": "boolean"}, "contents": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentNotifyParams.Content"}, "type": "array"}}, "title": "NotifyParam", "type": "object"}, "sensorsdata.horizon.v1.SegmentDefinitionSource": {"description": "", "properties": {"app": {"description": "", "type": "string"}, "category": {"description": "", "type": "string"}, "sub_category": {"description": "", "type": "string"}}, "title": "SegmentDefinitionSource", "type": "object"}, "sensorsdata.horizon.v1.SegmentTask": {"description": "", "properties": {"id": {"description": "", "format": "int32", "type": "integer"}, "segment_task_status": {"description": "", "enum": ["SEGMENT_TASK_STATUS_UNSPECIFIED", "SEGMENT_TASK_PREPARING", "SEGMENT_TASK_WAITING_DATA", "SEGMENT_TASK_COMPUTING", "SEGMENT_TASK_SUCCESS", "SEGMENT_TASK_FAILED", "SEGMENT_TASK_CANCELLED"], "type": "string"}, "entity_name": {"description": "", "type": "string"}, "segment_definition_name": {"description": "", "type": "string"}, "rule_ref": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentRuleRef"}, "base_time": {"description": "", "format": "date-time", "type": "string"}, "segment_task_params": {"additionalProperties": {"$ref": "#/components/schemas/sensorsdata.common.Literal"}, "description": "", "type": "object"}, "custom_params": {"additionalProperties": {"$ref": "#/components/schemas/sensorsdata.common.Literal"}, "description": "", "type": "object"}, "segment_name": {"description": "", "type": "string"}, "segment_task_report": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentTaskReport"}, "failure_detail": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentTask.SegmentTaskFailureDetail"}, "start_time": {"description": "", "format": "date-time", "type": "string"}, "end_time": {"description": "", "format": "date-time", "type": "string"}, "failed_times": {"description": "", "format": "int32", "type": "integer"}, "creator_id": {"description": "", "type": "string"}, "modifier_id": {"description": "", "type": "string"}, "create_time": {"description": "", "format": "date-time", "type": "string"}, "update_time": {"description": "", "format": "date-time", "type": "string"}}, "required": ["entity_name"], "title": "SegmentTask", "type": "object"}, "sensorsdata.horizon.v1.EventSequenceSegmentRuleExpression": {"description": "", "properties": {"operator": {"description": "", "enum": ["SET_OPERATOR_UNSPECIFIED", "UNION", "INTERSECT"], "type": "string"}, "simple_event_sequences": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.EventSequenceSegmentRuleExpression.SimpleEventSequenceSegmentRuleExpression"}, "type": "array"}}, "title": "EventSequenceSegmentRuleExpression", "type": "object"}, "sensorsdata.horizon.v1.SegmentTask.CustomParamsEntry": {"description": "", "properties": {"key": {"description": "", "type": "string"}, "value": {"$ref": "#/components/schemas/sensorsdata.common.Literal"}}, "title": "CustomParamsEntry", "type": "object"}, "sensorsdata.horizon.v1.SqlSegmentRuleExpression.QueryOption": {"description": "", "properties": {"option": {"description": "", "type": "string"}, "value": {"description": "", "type": "string"}}, "title": "QueryOption", "type": "object"}, "sensorsdata.horizon.v1.ListSegmentItemsResponse": {"description": "", "properties": {"items": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentItem"}, "type": "array"}}, "required": ["items"], "title": "ListSegmentItemsResponse", "type": "object"}, "sensorsdata.horizon.v1.SegmentItem": {"description": "", "properties": {"base_time": {"description": "", "format": "date-time", "type": "string"}, "segment_name": {"description": "", "type": "string"}, "create_time": {"description": "", "format": "date-time", "type": "string"}, "segment_ready": {"description": "", "type": "boolean"}, "segment_ready_time": {"description": "", "format": "date-time", "type": "string"}, "count": {"description": "", "format": "int64", "type": "integer"}}, "title": "SegmentItem", "type": "object"}, "sensorsdata.common.PeriodicTrigger": {"description": "", "properties": {"start_time": {"description": "", "format": "date-time", "type": "string"}, "end_time": {"description": "", "format": "date-time", "type": "string"}, "interval_size": {"description": "", "format": "int32", "type": "integer"}, "interval_unit": {"description": "", "enum": ["DATE_TIME_UNIT_UNSPECIFIED", "MILLISECOND", "SECOND", "MINUTE", "HOUR", "DAY", "WEEK", "MONTH", "YEAR"], "type": "string"}, "trunc_unit": {"description": "", "enum": ["TRUNC_UNIT_UNSPECIFIED", "TRUNC_HOUR", "TRUNC_DAY", "TRUNC_WEEK", "TRUNC_MONTH", "TRUNC_YEAR", "TRUNC_BIGBANG"], "type": "string"}}, "title": "PeriodicTrigger", "type": "object"}, "sensorsdata.common.StaticTrigger": {"description": "", "properties": {"time": {"description": "", "format": "date-time", "type": "string"}}, "title": "StaticTrigger", "type": "object"}, "sensorsdata.horizon.v1.GetSegmentTaskRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "entity_name": {"description": "", "type": "string"}, "definition_name": {"description": "", "type": "string"}, "fetch_all": {"description": "", "type": "boolean"}, "field_mask": {"description": "", "type": "string"}, "base_time_list": {"description": "", "items": {"description": "google.protobuf.Timestamp 按照 rfc 3399", "format": "date-time", "type": "string"}, "type": "array"}}, "required": ["base_time_list", "definition_name", "entity_name", "project_id"], "title": "GetSegmentTaskRequest", "type": "object"}, "sensorsdata.horizon.v1.SegmentNotifyParams": {"description": "", "properties": {"subscribe_param": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentNotifyParams.NotifyParam"}, "push_param": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentNotifyParams.NotifyParam"}}, "title": "SegmentNotifyParams", "type": "object"}, "sensorsdata.horizon.v1.UpdateSegmentSchedulerStatusRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "entity_name": {"description": "", "type": "string"}, "name": {"description": "", "type": "string"}, "status": {"description": "", "enum": ["STATUS_UNSPECIFIED", "ACTIVE", "SUSPENDED", "DELETE_REQUESTED"], "type": "string"}}, "required": ["entity_name", "name", "project_id"], "title": "UpdateSegmentSchedulerStatusRequest", "type": "object"}, "sensorsdata.horizon.v1.SegmentDefinition": {"description": "", "properties": {"entity_name": {"description": "", "type": "string"}, "name": {"description": "", "type": "string"}, "display_name": {"description": "", "type": "string"}, "comment": {"description": "", "type": "string"}, "segment_rule_ref": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentRuleRef"}, "trigger": {"$ref": "#/components/schemas/sensorsdata.common.Trigger"}, "custom_params": {"additionalProperties": {"$ref": "#/components/schemas/sensorsdata.common.Literal"}, "description": "", "type": "object"}, "visible": {"description": "", "type": "boolean"}, "managed": {"description": "", "type": "boolean"}, "status": {"description": "", "enum": ["STATUS_UNSPECIFIED", "ACTIVE", "SUSPENDED", "DELETE_REQUESTED"], "type": "string"}, "created_by": {"description": "", "type": "string"}, "create_type": {"description": "", "type": "string"}, "storage_settings": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentStorageSettings"}, "alarm_config": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentAlarmConfig"}, "creator_id": {"description": "", "type": "string"}, "modifier_id": {"description": "", "type": "string"}, "create_time": {"description": "", "format": "date-time", "type": "string"}, "update_time": {"description": "", "format": "date-time", "type": "string"}, "notify_params": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentNotifyParams"}, "delete_time": {"description": "", "format": "date-time", "type": "string"}, "delete_account_id": {"description": "", "type": "string"}, "source": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentDefinitionSource"}, "legacy_id": {"description": "", "format": "int32", "type": "integer"}}, "required": ["entity_name", "name"], "title": "SegmentDefinition", "type": "object"}, "sensorsdata.horizon.v1.SegmentTaskReport": {"description": "", "properties": {"count": {"description": "", "format": "int64", "type": "integer"}, "segment_loading_report": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentTaskReport.SegmentLoadingReport"}}, "title": "SegmentTaskReport", "type": "object"}, "sensorsdata.horizon.v1.EventSequenceSegmentRuleExpression.SimpleEventSequenceSegmentRuleExpression.EventStep": {"description": "", "properties": {"event_name": {"description": "", "type": "string"}, "condition": {"$ref": "#/components/schemas/sensorsdata.common.CompoundFilterCondition"}}, "title": "EventStep", "type": "object"}, "sensorsdata.horizon.v1.ListSegmentItemsRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "entity_name": {"description": "", "type": "string"}, "definition_name": {"description": "", "type": "string"}, "segment_ready": {"description": "", "type": "boolean"}, "base_time_list": {"description": "", "items": {"description": "google.protobuf.Timestamp 按照 rfc 3399", "format": "date-time", "type": "string"}, "type": "array"}, "limit": {"description": "", "format": "int32", "type": "integer"}}, "required": ["definition_name", "entity_name", "project_id"], "title": "ListSegmentItemsRequest", "type": "object"}, "sensorsdata.horizon.v1.SqlSegmentRuleExpression": {"description": "", "properties": {"sql": {"description": "", "type": "string"}, "query_options": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SqlSegmentRuleExpression.QueryOption"}, "type": "array"}}, "title": "SqlSegmentRuleExpression", "type": "object"}, "sensorsdata.horizon.v1.SegmentRuleExpression": {"description": "", "properties": {"type": {"description": "", "enum": ["SIMPLE_USER_GROUP_TYPE_UNSPECIFIED", "SQL_BASED", "EQL_BASED", "LOADING_BASED", "CUSTOM_BASED", "EVENT_SEQUENCE_BASED", "GROUP_EXPRESSION_BASED", "ALL"], "type": "string"}, "sql_segment_rule": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SqlSegmentRuleExpression"}, "eql_segment_rule": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.EqlSegmentRuleExpression"}, "loading_segment_rule": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.LoadingSegmentRuleExpression"}, "custom_rule": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.CustomSegmentRuleExpression"}, "event_sequence_rule": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.EventSequenceSegmentRuleExpression"}, "group_expression_rule": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.GroupExpressionSegmentRuleExpression"}, "virtual": {"description": "", "type": "boolean"}, "tailor": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.Tailor"}, "entity_set_rules": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentRule"}, "type": "array"}, "time_zone": {"description": "", "type": "string"}, "version": {"description": "", "format": "int32", "type": "integer"}}, "title": "SegmentRuleExpression", "type": "object"}, "sensorsdata.horizon.v1.SegmentAlarmConfig.AlarmRule": {"description": "", "properties": {"name": {"description": "", "type": "string"}, "alarm_params": {"additionalProperties": {"description": "", "type": "string"}, "description": "", "type": "object"}}, "title": "AlarmRule", "type": "object"}, "sensorsdata.horizon.v1.ListSegmentDefinitionsResponse": {"description": "", "properties": {"segment_definitions": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentDefinition"}, "type": "array"}, "total": {"description": "", "format": "int32", "type": "integer"}}, "title": "ListSegmentDefinitionsResponse", "type": "object"}, "sensorsdata.horizon.v1.SegmentNotifyParams.Content": {"description": "", "properties": {"config_id": {"description": "", "format": "int32", "type": "integer"}, "strategy_name": {"description": "", "type": "string"}, "extension_point_id": {"description": "", "type": "string"}, "ext_params": {"description": "", "type": "string"}}, "title": "Content", "type": "object"}, "sensorsdata.horizon.v1.SegmentRule": {"description": "", "properties": {"entity_name": {"description": "", "type": "string"}, "name": {"description": "", "type": "string"}, "display_name": {"description": "", "type": "string"}, "comment": {"description": "", "type": "string"}, "id": {"description": "", "format": "int32", "type": "integer"}, "status": {"description": "", "enum": ["STATUS_UNSPECIFIED", "ACTIVE", "INACTIVE", "DELETE_REQUESTED"], "type": "string"}, "expression": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentRuleExpression"}, "version": {"description": "", "type": "string"}, "rule_available": {"description": "", "type": "boolean"}, "created_by": {"description": "", "type": "string"}, "custom_params": {"additionalProperties": {"$ref": "#/components/schemas/sensorsdata.common.Literal"}, "description": "", "type": "object"}, "creator_id": {"description": "", "type": "string"}, "modifier_id": {"description": "", "type": "string"}, "create_time": {"description": "", "format": "date-time", "type": "string"}, "update_time": {"description": "", "format": "date-time", "type": "string"}, "type": {"description": "", "enum": ["TYPE_UNSPECIFIED", "SEGMENT_DEFINITION_RULE", "ENTITY_SET_RULE"], "type": "string"}, "read_only": {"description": "", "type": "boolean"}, "delete_flag": {"description": "", "format": "int64", "type": "integer"}}, "required": ["entity_name", "expression"], "title": "SegmentRule", "type": "object"}, "sensorsdata.horizon.v1.SegmentStorageSettings": {"description": "", "properties": {"version_count": {"description": "", "format": "int32", "type": "integer"}}, "title": "SegmentStorageSettings", "type": "object"}, "sensorsdata.horizon.v1.UpdateSegmentDefinitionWithRuleRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "segment_definition": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentDefinition"}, "update_mask": {"description": "", "type": "string"}, "dry_run": {"description": "", "type": "boolean"}}, "required": ["project_id", "segment_definition"], "title": "UpdateSegmentDefinitionWithRuleRequest", "type": "object"}, "sensorsdata.horizon.v1.EvaluateSegmentRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "entity_name": {"description": "", "type": "string"}, "evaluate_params": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.EvaluateSegmentRequest.EvaluateSegmentParam"}, "type": "array"}}, "required": ["entity_name"], "title": "EvaluateSegmentRequest", "type": "object"}, "sensorsdata.horizon.v1.SegmentTask.SegmentTaskFailureDetail": {"description": "", "properties": {"code": {"description": "", "type": "string"}, "error_message": {"description": "", "type": "string"}}, "title": "SegmentTaskFailureDetail", "type": "object"}, "sensorsdata.horizon.v1.Tailor": {"description": "", "properties": {"rank_field": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.RankField"}, "function": {"description": "", "enum": ["PREDICATE_FUNCTION_TYPE_UNSPECIFIED", "IS_SET", "IS_NOT_SET", "EQUAL", "NOT_EQUAL", "IN", "NOT_IN", "LESS", "LESS_EQUAL", "GREATER", "GREATER_EQUAL", "BETWEEN", "RIGHT_OPEN_BETWEEN", "HASH_BETWEEN", "LEFT_OPEN_BETWEEN", "OPEN_BETWEEN", "IS_TRUE", "IS_FALSE", "CONTAIN", "NOT_CONTAIN", "IS_EMPTY", "IS_NOT_EMPTY", "RLIKE", "NOT_RLIKE", "INCLUDE", "NOT_INCLUDE", "ABSOLUTE_BETWEEN", "RELATIVE_WITHIN", "RELATIVE_BETWEEN", "RELATIVE_EVENT_TIME", "RELATIVE_BEFORE", "INTERSECTS", "NOT_INTERSECTS", "SUBSET_OF", "NOT_SUBSET_OF", "SUPERSET_OF", "NOT_SUPERSET_OF"], "type": "string"}, "params": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.common.PredicateFunctionParam"}, "type": "array"}}, "title": "<PERSON><PERSON>", "type": "object"}, "sensorsdata.horizon.v1.GetSegmentTaskResponse": {"description": "", "properties": {"tasks": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentTask"}, "type": "array"}}, "title": "GetSegmentTaskResponse", "type": "object"}, "sensorsdata.horizon.v1.EventSequenceSegmentRuleExpression.SimpleEventSequenceSegmentRuleExpression.TimeWindow": {"description": "", "properties": {"size": {"description": "", "format": "int32", "type": "integer"}, "unit": {"description": "", "enum": ["TIME_UNIT_UNSPECIFIED", "DAY", "HOUR", "MINUTE"], "type": "string"}}, "title": "TimeWindow", "type": "object"}, "sensorsdata.common.CronTrigger": {"description": "", "properties": {"crontab_exp": {"description": "", "type": "string"}, "start_time": {"description": "", "format": "date-time", "type": "string"}, "end_time": {"description": "", "format": "date-time", "type": "string"}}, "title": "CronTrigger", "type": "object"}, "sensorsdata.horizon.v1.CreateSegmentDefinitionWithRuleRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "segment_definition": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentDefinition"}, "dry_run": {"description": "", "type": "boolean"}}, "required": ["project_id", "segment_definition"], "title": "CreateSegmentDefinitionWithRuleRequest", "type": "object"}, "sensorsdata.horizon.v1.RankField": {"description": "", "properties": {"type": {"description": "", "enum": ["RANK_TYPE_UNSPECIFIED", "SHUFFLE_RANK", "PERCENT_SHUFFLE_RANK", "ORDER_RANK", "PERCENT_ORDER_RANK"], "type": "string"}, "orders": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.Order"}, "type": "array"}}, "title": "Rank<PERSON>ield", "type": "object"}, "sensorsdata.horizon.v1.SegmentRuleRef": {"description": "", "properties": {"segment_rule_name": {"description": "", "type": "string"}, "version": {"description": "", "type": "string"}, "expression": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentRuleExpression"}}, "title": "SegmentRuleRef", "type": "object"}, "sensorsdata.horizon.v1.EvaluateSegmentRequest.EvaluateSegmentParam": {"description": "", "properties": {"segment_definition_name": {"description": "", "type": "string"}, "segment_rule_name": {"description": "", "type": "string"}, "segment_rule_version": {"description": "", "type": "string"}, "segment_task_params": {"additionalProperties": {"$ref": "#/components/schemas/sensorsdata.common.Literal"}, "description": "", "type": "object"}, "base_time": {"description": "", "format": "date-time", "type": "string"}}, "required": ["base_time", "segment_definition_name", "segment_rule_name"], "title": "EvaluateSegmentParam", "type": "object"}, "sensorsdata.horizon.v1.OrderMeasure": {"description": "", "properties": {"type": {"description": "", "enum": ["FIELD_TYPE_UNSPECIFIED", "PLAIN_FIELD", "AGGREGATOR_FIELD", "EQL_MEASURE_FIELD"], "type": "string"}, "plain_field": {"description": "", "type": "string"}, "aggregator_field": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.AggregatorField"}, "condition": {"$ref": "#/components/schemas/sensorsdata.common.CompoundFilterCondition"}, "eql_measure": {"description": "", "type": "string"}}, "title": "OrderMeasure", "type": "object"}, "sensorsdata.horizon.v1.SegmentRule.CustomParamsEntry": {"description": "", "properties": {"key": {"description": "", "type": "string"}, "value": {"$ref": "#/components/schemas/sensorsdata.common.Literal"}}, "title": "CustomParamsEntry", "type": "object"}, "sensorsdata.horizon.v1.GetSegmentDefinitionRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "entity_name": {"description": "", "type": "string"}, "name": {"description": "", "type": "string"}, "field_mask": {"description": "", "type": "string"}, "show_deleted": {"description": "", "type": "boolean"}}, "required": ["entity_name", "name", "project_id"], "title": "GetSegmentDefinitionRequest", "type": "object"}, "sensorsdata.horizon.v1.ListSegmentDefinitionsRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "entity_name": {"description": "", "type": "string"}, "segment_definition_names": {"description": "", "items": {"type": "string"}, "type": "array"}, "field_mask": {"description": "", "type": "string"}, "has_valid_segment": {"description": "", "type": "boolean"}, "show_invisible": {"description": "", "type": "boolean"}, "page_size": {"description": "", "format": "int32", "type": "integer"}, "page": {"description": "", "format": "int32", "type": "integer"}, "show_deleted": {"description": "", "type": "boolean"}, "only_brief": {"description": "", "type": "boolean"}}, "required": ["entity_name", "project_id"], "title": "ListSegmentDefinitionsRequest", "type": "object"}, "sensorsdata.horizon.v1.LoadingSegmentRuleExpression": {"description": "", "properties": {"matched_field": {"description": "", "type": "string"}, "sync_flag": {"description": "", "type": "boolean"}, "loading_type": {"description": "", "enum": ["LOADING_TYPE_UNSPECIFIED", "LOADING_TYPE_OVERWRITE", "LOADING_TYPE_INCREMENT"], "type": "string"}}, "title": "LoadingSegmentRuleExpression", "type": "object"}, "sensorsdata.horizon.v1.SegmentDefinition.CustomParamsEntry": {"description": "", "properties": {"key": {"description": "", "type": "string"}, "value": {"$ref": "#/components/schemas/sensorsdata.common.Literal"}}, "title": "CustomParamsEntry", "type": "object"}, "sensorsdata.horizon.v1.EvaluateSegmentRequest.EvaluateSegmentParam.SegmentTaskParamsEntry": {"description": "", "properties": {"key": {"description": "", "type": "string"}, "value": {"$ref": "#/components/schemas/sensorsdata.common.Literal"}}, "title": "SegmentTaskParamsEntry", "type": "object"}, "sensorsdata.horizon.v1.AggregatorField": {"description": "", "properties": {"aggregator": {"description": "", "enum": ["AGGREGATOR_TYPE_UNSPECIFIED", "COUNT", "AVG", "MAX", "MIN", "SUM", "UNIQUE_COUNT", "UNIQUE_AVG", "BOUNCE_RATE", "EXIT_RATE", "UNIQUE_COUNT_APPROX", "COUNT_PERCENT", "RANK_ASC", "RANK_DESC", "PERCENT_RANK_ASC", "PERCENT_RANK_DESC", "SESSION_COUNT", "FIRST_TIME", "LAST_TIME", "FIRST_TIME_INTERVAL", "LAST_TIME_INTERVAL", "GROUP_CONCAT", "UNIQUE_COUNT_BITMAP", "UNIQUE_COUNT_APPROX_ORDINARY", "COUNT_DISTINCT", "LTV", "LTV_AVG", "COUNT_RANK", "AVG_RANK", "MAX_RANK", "MIN_RANK", "SUM_RANK", "COUNT_RANK_ASC", "COUNT_RANK_DESC", "COUNT_PERCENT_RANK_ASC", "COUNT_PERCENT_RANK_DESC", "AVG_RANK_ASC", "AVG_RANK_DESC", "AVG_PERCENT_RANK_ASC", "AVG_PERCENT_RANK_DESC", "MAX_RANK_ASC", "MAX_RANK_DESC", "MAX_PERCENT_RANK_ASC", "MAX_PERCENT_RANK_DESC", "MIN_RANK_ASC", "MIN_RANK_DESC", "MIN_PERCENT_RANK_ASC", "MIN_PERCENT_RANK_DESC", "SUM_RANK_ASC", "SUM_RANK_DESC", "SUM_PERCENT_RANK_ASC", "SUM_PERCENT_RANK_DESC"], "type": "string"}, "field": {"description": "", "type": "string"}}, "title": "AggregatorField", "type": "object"}, "sensorsdata.horizon.v1.SegmentTaskReport.SegmentLoadingReport": {"description": "", "properties": {"total_line_count": {"description": "", "format": "int64", "type": "integer"}, "valid_line_count": {"description": "", "format": "int64", "type": "integer"}, "distinct_line_count": {"description": "", "format": "int64", "type": "integer"}, "matched_property_count": {"description": "", "format": "int64", "type": "integer"}, "unmatched_property_count": {"description": "", "format": "int64", "type": "integer"}, "matched_entity_count": {"description": "", "format": "int64", "type": "integer"}}, "title": "SegmentLoadingReport", "type": "object"}, "sensorsdata.horizon.v1.SegmentTask.SegmentTaskParamsEntry": {"description": "", "properties": {"key": {"description": "", "type": "string"}, "value": {"$ref": "#/components/schemas/sensorsdata.common.Literal"}}, "title": "SegmentTaskParamsEntry", "type": "object"}, "sensorsdata.horizon.v1.CancelSegmentTaskRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "task_ids": {"description": "", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "sync": {"description": "", "type": "boolean"}, "sync_timeout_seconds": {"description": "", "format": "int32", "type": "integer"}}, "title": "CancelSegmentTaskRequest", "type": "object"}, "sensorsdata.horizon.v1.EqlSegmentRuleExpression": {"description": "", "properties": {"eql": {"description": "", "type": "string"}}, "title": "EqlSegmentRuleExpression", "type": "object"}, "sensorsdata.common.TimeRange": {"description": "", "properties": {"start_time": {"$ref": "#/components/schemas/sensorsdata.common.TimePoint"}, "end_time": {"$ref": "#/components/schemas/sensorsdata.common.TimePoint"}}, "title": "TimeRange", "type": "object"}, "sensorsdata.horizon.v1.SegmentAlarmConfig": {"description": "", "properties": {"enable": {"description": "", "type": "boolean"}, "alarm_rules": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentAlarmConfig.AlarmRule"}, "type": "array"}}, "title": "SegmentAlarmConfig", "type": "object"}, "sensorsdata.horizon.v1.ListSegmentDefinitionsResponseHttpApiResult": {"description": "sensorsdata.horizon.v1.ListSegmentDefinitionsResponse", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListSegmentDefinitionsResponse"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.horizon.v1.ListSegmentDefinitionsResponseHttpApiResult", "type": "object"}, "sensorsdata.horizon.v1.SegmentDefinitionHttpApiResult": {"description": "sensorsdata.horizon.v1.SegmentDefinition", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentDefinition"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.horizon.v1.SegmentDefinitionHttpApiResult", "type": "object"}, "sensorsdata.horizon.v1.ListSegmentItemsResponseHttpApiResult": {"description": "sensorsdata.horizon.v1.ListSegmentItemsResponse", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListSegmentItemsResponse"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.horizon.v1.ListSegmentItemsResponseHttpApiResult", "type": "object"}, "sensorsdata.horizon.v1.GetSegmentTaskResponseHttpApiResult": {"description": "sensorsdata.horizon.v1.GetSegmentTaskResponse", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.GetSegmentTaskResponse"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.horizon.v1.GetSegmentTaskResponseHttpApiResult", "type": "object"}, "sensorsdata.horizon.v1.EqlBasedRuleExpression": {"description": "", "properties": {"value_expression": {"description": "", "type": "string"}, "filter_expression": {"description": "", "type": "string"}}, "title": "EqlBasedRuleExpression", "type": "object"}, "sensorsdata.horizon.v1.SegmentBasedRuleExpression.SegmentTagEntry": {"description": "", "properties": {"segment": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentRuleExpression"}, "tag": {"description": "", "type": "string"}}, "title": "SegmentTagEntry", "type": "object"}, "sensorsdata.horizon.v1.TagPartition.CustomParamsEntry": {"description": "", "properties": {"key": {"description": "", "type": "string"}, "value": {"description": "", "type": "string"}}, "title": "CustomParamsEntry", "type": "object"}, "sensorsdata.horizon.v1.CancelTagTaskRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "task_ids": {"description": "", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "sync": {"description": "", "type": "boolean"}, "sync_timeout_seconds": {"description": "", "format": "int32", "type": "integer"}}, "required": ["project_id", "task_ids"], "title": "CancelTagTaskRequest", "type": "object"}, "sensorsdata.horizon.v1.GetTagTaskResponse": {"description": "", "properties": {"tasks": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagTask"}, "type": "array"}}, "title": "GetTagTaskResponse", "type": "object"}, "sensorsdata.horizon.v1.SegmentBasedRuleExpression": {"description": "", "properties": {"groups": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentBasedRuleExpression.SegmentTagEntry"}, "type": "array"}}, "title": "SegmentBasedRuleExpression", "type": "object"}, "sensorsdata.horizon.v1.TagPostAction.CustomParamEntry": {"description": "", "properties": {"key": {"description": "", "type": "string"}, "value": {"description": "", "type": "string"}}, "title": "CustomParamEntry", "type": "object"}, "sensorsdata.horizon.v1.TagTaskReport.ExtendReportInfoEntry": {"description": "", "properties": {"key": {"description": "", "type": "string"}, "value": {"description": "", "type": "string"}}, "title": "ExtendReportInfoEntry", "type": "object"}, "sensorsdata.horizon.v1.RfmBasedRuleExpression.RfmFactor.RfmFactorGroupRule": {"description": "", "properties": {"aggregator": {"description": "", "enum": ["AGGREGATOR_TYPE_UNSPECIFIED", "COUNT", "AVG", "MAX", "MIN", "SUM", "UNIQUE_COUNT", "UNIQUE_AVG", "BOUNCE_RATE", "EXIT_RATE", "UNIQUE_COUNT_APPROX", "COUNT_PERCENT", "RANK_ASC", "RANK_DESC", "PERCENT_RANK_ASC", "PERCENT_RANK_DESC", "SESSION_COUNT", "FIRST_TIME", "LAST_TIME", "FIRST_TIME_INTERVAL", "LAST_TIME_INTERVAL", "GROUP_CONCAT", "UNIQUE_COUNT_BITMAP", "UNIQUE_COUNT_APPROX_ORDINARY", "COUNT_DISTINCT", "LTV", "LTV_AVG", "COUNT_RANK", "AVG_RANK", "MAX_RANK", "MIN_RANK", "SUM_RANK", "COUNT_RANK_ASC", "COUNT_RANK_DESC", "COUNT_PERCENT_RANK_ASC", "COUNT_PERCENT_RANK_DESC", "AVG_RANK_ASC", "AVG_RANK_DESC", "AVG_PERCENT_RANK_ASC", "AVG_PERCENT_RANK_DESC", "MAX_RANK_ASC", "MAX_RANK_DESC", "MAX_PERCENT_RANK_ASC", "MAX_PERCENT_RANK_DESC", "MIN_RANK_ASC", "MIN_RANK_DESC", "MIN_PERCENT_RANK_ASC", "MIN_PERCENT_RANK_DESC", "SUM_RANK_ASC", "SUM_RANK_DESC", "SUM_PERCENT_RANK_ASC", "SUM_PERCENT_RANK_DESC"], "type": "string"}, "function": {"description": "", "enum": ["PREDICATE_FUNCTION_TYPE_UNSPECIFIED", "IS_SET", "IS_NOT_SET", "EQUAL", "NOT_EQUAL", "IN", "NOT_IN", "LESS", "LESS_EQUAL", "GREATER", "GREATER_EQUAL", "BETWEEN", "RIGHT_OPEN_BETWEEN", "HASH_BETWEEN", "LEFT_OPEN_BETWEEN", "OPEN_BETWEEN", "IS_TRUE", "IS_FALSE", "CONTAIN", "NOT_CONTAIN", "IS_EMPTY", "IS_NOT_EMPTY", "RLIKE", "NOT_RLIKE", "INCLUDE", "NOT_INCLUDE", "ABSOLUTE_BETWEEN", "RELATIVE_WITHIN", "RELATIVE_BETWEEN", "RELATIVE_EVENT_TIME", "RELATIVE_BEFORE", "INTERSECTS", "NOT_INTERSECTS", "SUBSET_OF", "NOT_SUBSET_OF", "SUPERSET_OF", "NOT_SUPERSET_OF"], "type": "string"}, "params": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.common.PredicateFunctionParam"}, "type": "array"}, "tag": {"description": "", "type": "string"}}, "title": "RfmFactorGroupRule", "type": "object"}, "sensorsdata.horizon.v1.UpdateTagDefinitionRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "definition": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagDefinition"}, "update_mask": {"description": "", "type": "string"}}, "required": ["definition", "project_id"], "title": "UpdateTagDefinitionRequest", "type": "object"}, "sensorsdata.horizon.v1.ListTagPartitionsResponse": {"description": "", "properties": {"tag_partitions": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagPartition"}, "type": "array"}}, "title": "ListTagPartitionsResponse", "type": "object"}, "sensorsdata.horizon.v1.TagNotifyParams": {"description": "", "properties": {"subscribe_param": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagNotifyParams.NotifyParam"}, "push_param": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagNotifyParams.NotifyParam"}}, "title": "TagNotifyParams", "type": "object"}, "sensorsdata.horizon.v1.ListTagDefinitionsResponse": {"description": "", "properties": {"tags": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagDefinition"}, "type": "array"}}, "title": "ListTagDefinitionsResponse", "type": "object"}, "sensorsdata.horizon.v1.GetLastedTagPartitionResponse": {"description": "", "properties": {"lasted_tag_partition_map": {"additionalProperties": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagPartition"}, "description": "", "type": "object"}}, "title": "GetLastedTagPartitionResponse", "type": "object"}, "sensorsdata.horizon.v1.BasicMeasureBasedRuleExpression": {"description": "", "properties": {"bucket_type": {"description": "", "enum": ["BUCKET_TYPE_UNSPECIFIED", "DISCRETE", "PERCENT", "NUMBER"], "type": "string"}, "measure_rule": {"description": "", "type": "string"}, "values": {"description": "", "items": {"type": "string"}, "type": "array"}, "buckets": {"description": "", "items": {"format": "double", "type": "number"}, "type": "array"}}, "title": "BasicMeasureBasedRuleExpression", "type": "object"}, "sensorsdata.horizon.v1.CreateTagDefinitionRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "definition": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagDefinition"}, "dry_run": {"description": "", "type": "boolean"}}, "required": ["definition", "project_id"], "title": "CreateTagDefinitionRequest", "type": "object"}, "sensorsdata.horizon.v1.TagAlarmConfig.AlarmRule.AlarmParamsEntry": {"description": "", "properties": {"key": {"description": "", "type": "string"}, "value": {"description": "", "type": "string"}}, "title": "AlarmParamsEntry", "type": "object"}, "sensorsdata.horizon.v1.TagNotifyParams.NotifyParam": {"description": "", "properties": {"enable": {"description": "", "type": "boolean"}, "contents": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagNotifyParams.Content"}, "type": "array"}}, "title": "NotifyParam", "type": "object"}, "sensorsdata.horizon.v1.TagTask": {"description": "", "properties": {"id": {"description": "", "format": "int32", "type": "integer"}, "status": {"description": "", "enum": ["TAG_TASK_STATUS_UNSPECIFIED", "TAG_PREPARING", "TAG_WAITING_DATA", "TAG_COMPUTING", "TAG_SUCCESS", "TAG_FAILED", "TAG_TASK_CANCELLED"], "type": "string"}, "entity_name": {"description": "", "type": "string"}, "tag_definition_name": {"description": "", "type": "string"}, "rule": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagRuleExpression"}, "rule_version": {"description": "", "format": "int32", "type": "integer"}, "base_time": {"description": "", "format": "date-time", "type": "string"}, "tag_task_params": {"additionalProperties": {"$ref": "#/components/schemas/sensorsdata.common.Literal"}, "description": "", "type": "object"}, "failure_detail": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagTask.TagTaskFailureDetail"}, "tag_task_report": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagTaskReport"}, "start_time": {"description": "", "format": "date-time", "type": "string"}, "end_time": {"description": "", "format": "date-time", "type": "string"}, "failed_times": {"description": "", "format": "int32", "type": "integer"}, "creator_id": {"description": "", "type": "string"}, "modifier_id": {"description": "", "type": "string"}, "create_time": {"description": "", "format": "date-time", "type": "string"}, "update_time": {"description": "", "format": "date-time", "type": "string"}}, "required": ["entity_name"], "title": "TagTask", "type": "object"}, "sensorsdata.horizon.v1.TagDefinition": {"description": "", "properties": {"entity_name": {"description": "", "type": "string"}, "name": {"description": "", "type": "string"}, "display_name": {"description": "", "type": "string"}, "comment": {"description": "", "type": "string"}, "data_type": {"description": "", "enum": ["DATA_TYPE_UNSPECIFIED", "BOOL", "INT", "NUMBER", "STRING", "LIST", "DATETIME", "DATE", "BIGINT", "DECIMAL"], "type": "string"}, "folder": {"description": "", "type": "string"}, "expression": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagRuleExpression"}, "current_rule_available": {"description": "", "type": "boolean"}, "rule_version": {"description": "", "format": "int32", "type": "integer"}, "visible": {"description": "", "type": "boolean"}, "managed": {"description": "", "type": "boolean"}, "trigger": {"$ref": "#/components/schemas/sensorsdata.common.Trigger"}, "custom_params": {"additionalProperties": {"$ref": "#/components/schemas/sensorsdata.common.Literal"}, "description": "", "type": "object"}, "status": {"description": "", "enum": ["TAG_STATUS_UNSPECIFIED", "TAG_NEW", "TAG_ACTIVE", "TAG_SUSPENDED", "TAG_DELETE_REQUESTED"], "type": "string"}, "created_by": {"description": "", "type": "string"}, "create_type": {"description": "", "type": "string"}, "storage_settings": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagStorageSettings"}, "tag_post_actions": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagPostAction"}, "type": "array"}, "alarm_config": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagAlarmConfig"}, "creator_id": {"description": "", "type": "string"}, "modifier_id": {"description": "", "type": "string"}, "create_time": {"description": "", "format": "date-time", "type": "string"}, "update_time": {"description": "", "format": "date-time", "type": "string"}, "delete_time": {"description": "", "format": "date-time", "type": "string"}, "delete_account_id": {"description": "", "type": "string"}, "notify_params": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagNotifyParams"}, "source": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagDefinitionSource"}}, "required": ["create_type", "entity_name", "name"], "title": "TagDefinition", "type": "object"}, "sensorsdata.horizon.v1.TagNotifyParams.Content": {"description": "", "properties": {"config_id": {"description": "", "format": "int32", "type": "integer"}, "plugin_name": {"description": "", "type": "string"}, "ext_params": {"additionalProperties": {"$ref": "#/components/schemas/sensorsdata.common.Literal"}, "description": "", "type": "object"}}, "title": "Content", "type": "object"}, "sensorsdata.horizon.v1.CustomBasedRuleExpression": {"description": "", "properties": {"component_name": {"description": "", "type": "string"}, "rule": {"description": "", "properties": {}, "type": "object"}, "rule_str": {"description": "", "type": "string"}}, "title": "CustomBasedRuleExpression", "type": "object"}, "sensorsdata.horizon.v1.TagTask.TagTaskFailureDetail": {"description": "", "properties": {"code": {"description": "", "type": "string"}, "error_message": {"description": "", "type": "string"}}, "title": "TagTaskFailureDetail", "type": "object"}, "sensorsdata.horizon.v1.RfmBasedRuleExpression.RfmFactor": {"description": "", "properties": {"factor_eql": {"description": "", "type": "string"}, "factor_group_rules": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.RfmBasedRuleExpression.RfmFactor.RfmFactorGroupRule"}, "type": "array"}}, "title": "RfmFactor", "type": "object"}, "sensorsdata.horizon.v1.LoadingBasedRuleExpression": {"description": "", "properties": {"matched_field": {"description": "", "type": "string"}, "sync_flag": {"description": "", "type": "boolean"}, "loading_type": {"description": "", "enum": ["TAG_LOADING_TYPE_UNSPECIFIED", "TAG_LOADING_TYPE_OVERWRITE", "TAG_LOADING_TYPE_INCREMENT", "TAG_LOADING_TYPE_INCREMENT_AND_OVERWRITE"], "type": "string"}}, "title": "LoadingBasedRuleExpression", "type": "object"}, "sensorsdata.horizon.v1.TagTaskReport": {"description": "", "properties": {"count": {"description": "", "format": "int64", "type": "integer"}, "tag_loading_report": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagTaskReport.TagLoadingReport"}, "extend_report_info": {"additionalProperties": {"description": "", "type": "string"}, "description": "", "type": "object"}}, "title": "TagTaskReport", "type": "object"}, "sensorsdata.horizon.v1.RfmBasedRuleExpression": {"description": "", "properties": {"segment": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentRuleExpression"}, "r_factor": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.RfmBasedRuleExpression.RfmFactor"}, "f_factor": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.RfmBasedRuleExpression.RfmFactor"}, "m_factor": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.RfmBasedRuleExpression.RfmFactor"}, "layer_params": {"additionalProperties": {"description": "", "type": "string"}, "description": "", "type": "object"}}, "title": "RfmBasedRuleExpression", "type": "object"}, "sensorsdata.horizon.v1.TagPostAction": {"description": "", "properties": {"post_action_type": {"description": "", "enum": ["TAG_NOTIFY_TYPE_UNSPECIFIED", "TAG_SUBSCRIBE", "TAG_PUSH"], "type": "string"}, "name": {"description": "", "type": "string"}, "custom_param": {"additionalProperties": {"description": "", "type": "string"}, "description": "", "type": "object"}}, "title": "TagPostAction", "type": "object"}, "sensorsdata.horizon.v1.SqlBasedRuleExpression.QueryOption": {"description": "", "properties": {"option": {"description": "", "type": "string"}, "value": {"description": "", "type": "string"}}, "title": "QueryOption", "type": "object"}, "sensorsdata.horizon.v1.GetLastedTagPartitionRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "entity_name": {"description": "", "type": "string"}, "tag_names": {"description": "", "items": {"type": "string"}, "type": "array"}, "status": {"description": "", "enum": ["TAG_PARTITION_STATUS_UNSPECIFIED", "TAG_PARTITION_AVAILABLE", "TAG_PARTITION_UNAVAILABLE"], "type": "string"}}, "required": ["entity_name", "project_id", "tag_names"], "title": "GetLastedTagPartitionRequest", "type": "object"}, "sensorsdata.horizon.v1.TagNotifyParams.Content.ExtParamsEntry": {"description": "", "properties": {"key": {"description": "", "type": "string"}, "value": {"$ref": "#/components/schemas/sensorsdata.common.Literal"}}, "title": "ExtParamsEntry", "type": "object"}, "sensorsdata.horizon.v1.TagAlarmConfig.AlarmRule": {"description": "", "properties": {"name": {"description": "", "type": "string"}, "alarm_params": {"additionalProperties": {"description": "", "type": "string"}, "description": "", "type": "object"}}, "title": "AlarmRule", "type": "object"}, "sensorsdata.horizon.v1.UpdateTagSchedulerStatusRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "entity_name": {"description": "", "type": "string"}, "tag_name": {"description": "", "type": "string"}, "status": {"description": "", "enum": ["TAG_STATUS_UNSPECIFIED", "TAG_NEW", "TAG_ACTIVE", "TAG_SUSPENDED", "TAG_DELETE_REQUESTED"], "type": "string"}}, "required": ["entity_name", "project_id", "tag_name"], "title": "UpdateTagSchedulerStatusRequest", "type": "object"}, "sensorsdata.horizon.v1.TagRuleExpression": {"description": "", "properties": {"tag_type": {"description": "", "enum": ["TAG_TYPE_UNSPECIFIED", "SQL_BASED", "LOADING_BASED", "EQL_BASED", "CUSTOM_BASED", "SEGMENT_BASED", "BASIC_MEASURE_BASED", "RFM_BASED"], "type": "string"}, "sql_based_rule": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SqlBasedRuleExpression"}, "eql_based_rule": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.EqlBasedRuleExpression"}, "custom_based_rule": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.CustomBasedRuleExpression"}, "loading_based_rule": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.LoadingBasedRuleExpression"}, "segment_based_rule": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentBasedRuleExpression"}, "basic_measure_based_rule": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.BasicMeasureBasedRuleExpression"}, "rfm_based_rule": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.RfmBasedRuleExpression"}, "virtual": {"description": "", "type": "boolean"}, "time_zone": {"description": "", "type": "string"}, "segment_filter": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentRuleExpression"}, "filter_segment_at_last": {"description": "", "type": "boolean"}, "entity_set_rules": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SegmentRule"}, "type": "array"}, "version": {"description": "", "format": "int32", "type": "integer"}}, "title": "TagRuleExpression", "type": "object"}, "sensorsdata.horizon.v1.TagStorageSettings": {"description": "", "properties": {"version_count": {"description": "", "format": "int32", "type": "integer"}}, "title": "TagStorageSettings", "type": "object"}, "sensorsdata.horizon.v1.ListTagDefinitionsRequest": {"description": "", "properties": {"page_size": {"description": "", "format": "int32", "type": "integer"}, "page": {"description": "", "format": "int32", "type": "integer"}, "project_id": {"description": "", "format": "int32", "type": "integer"}, "entity_name": {"description": "", "type": "string"}, "tag_names": {"description": "", "items": {"type": "string"}, "type": "array"}, "show_deleted": {"description": "", "type": "boolean"}, "show_invisible": {"description": "", "type": "boolean"}, "field_mask": {"description": "", "type": "string"}, "only_brief": {"description": "", "type": "boolean"}}, "required": ["entity_name", "project_id"], "title": "ListTagDefinitionsRequest", "type": "object"}, "sensorsdata.horizon.v1.EvaluateTagRequest.EvaluateTagParam.TagTaskParamsEntry": {"description": "", "properties": {"key": {"description": "", "type": "string"}, "value": {"$ref": "#/components/schemas/sensorsdata.common.Literal"}}, "title": "TagTaskParamsEntry", "type": "object"}, "sensorsdata.horizon.v1.EvaluateTagRequest.EvaluateTagParam": {"description": "", "properties": {"tag_definition_name": {"description": "", "type": "string"}, "tag_task_params": {"additionalProperties": {"$ref": "#/components/schemas/sensorsdata.common.Literal"}, "description": "", "type": "object"}, "tag_rule": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagRuleExpression"}, "base_time": {"description": "", "format": "date-time", "type": "string"}}, "required": ["base_time", "tag_definition_name"], "title": "EvaluateTagParam", "type": "object"}, "sensorsdata.horizon.v1.RfmBasedRuleExpression.LayerParamsEntry": {"description": "", "properties": {"key": {"description": "", "type": "string"}, "value": {"description": "", "type": "string"}}, "title": "LayerParamsEntry", "type": "object"}, "sensorsdata.horizon.v1.TagTaskReport.TagLoadingReport": {"description": "", "properties": {"total_line_count": {"description": "", "format": "int64", "type": "integer"}, "valid_line_count": {"description": "", "format": "int64", "type": "integer"}, "distinct_line_count": {"description": "", "format": "int64", "type": "integer"}, "matched_property_count": {"description": "", "format": "int64", "type": "integer"}, "unmatched_property_count": {"description": "", "format": "int64", "type": "integer"}, "matched_entity_count": {"description": "", "format": "int64", "type": "integer"}}, "title": "TagLoadingReport", "type": "object"}, "sensorsdata.horizon.v1.ListTagPartitionsRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "entity_name": {"description": "", "type": "string"}, "tag_name": {"description": "", "type": "string"}, "start_time": {"description": "", "format": "date-time", "type": "string"}, "end_time": {"description": "", "format": "date-time", "type": "string"}, "base_time_list": {"description": "", "items": {"description": "google.protobuf.Timestamp 按照 rfc 3399", "format": "date-time", "type": "string"}, "type": "array"}, "field_mask": {"description": "", "type": "string"}, "limit": {"description": "", "format": "int32", "type": "integer"}}, "title": "ListTagPartitionsRequest", "type": "object"}, "sensorsdata.horizon.v1.SqlBasedRuleExpression": {"description": "", "properties": {"sql": {"description": "", "type": "string"}, "query_options": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.SqlBasedRuleExpression.QueryOption"}, "type": "array"}}, "title": "SqlBasedRuleExpression", "type": "object"}, "sensorsdata.horizon.v1.TagTask.TagTaskParamsEntry": {"description": "", "properties": {"key": {"description": "", "type": "string"}, "value": {"$ref": "#/components/schemas/sensorsdata.common.Literal"}}, "title": "TagTaskParamsEntry", "type": "object"}, "sensorsdata.horizon.v1.GetTagDefinitionRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "entity_name": {"description": "", "type": "string"}, "name": {"description": "", "type": "string"}, "show_deleted": {"description": "", "type": "boolean"}, "field_mask": {"description": "", "type": "string"}}, "required": ["entity_name", "name", "project_id"], "title": "GetTagDefinitionRequest", "type": "object"}, "sensorsdata.horizon.v1.GetTagTaskRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "entity_name": {"description": "", "type": "string"}, "tag_name": {"description": "", "type": "string"}, "fetch_all": {"description": "", "type": "boolean"}, "field_mask": {"description": "", "type": "string"}, "start_time": {"description": "", "format": "date-time", "type": "string"}, "end_time": {"description": "", "format": "date-time", "type": "string"}, "base_time_list": {"description": "", "items": {"description": "google.protobuf.Timestamp 按照 rfc 3399", "format": "date-time", "type": "string"}, "type": "array"}}, "required": ["end_time", "entity_name", "project_id", "start_time", "tag_name"], "title": "GetTagTaskRequest", "type": "object"}, "sensorsdata.horizon.v1.TagDefinition.CustomParamsEntry": {"description": "", "properties": {"key": {"description": "", "type": "string"}, "value": {"$ref": "#/components/schemas/sensorsdata.common.Literal"}}, "title": "CustomParamsEntry", "type": "object"}, "sensorsdata.horizon.v1.TagAlarmConfig": {"description": "", "properties": {"enable": {"description": "", "type": "boolean"}, "alarm_rules": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagAlarmConfig.AlarmRule"}, "type": "array"}}, "title": "TagAlarmConfig", "type": "object"}, "sensorsdata.horizon.v1.TagDefinitionSource": {"description": "", "properties": {"app": {"description": "", "type": "string"}, "category": {"description": "", "type": "string"}, "sub_category": {"description": "", "type": "string"}}, "title": "TagDefinitionSource", "type": "object"}, "sensorsdata.horizon.v1.GetLastedTagPartitionResponse.LastedTagPartitionMapEntry": {"description": "", "properties": {"key": {"description": "", "type": "string"}, "value": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagPartition"}}, "title": "LastedTagPartitionMapEntry", "type": "object"}, "sensorsdata.horizon.v1.TagPartition": {"description": "", "properties": {"entity_name": {"description": "", "type": "string"}, "tag_definition_name": {"description": "", "type": "string"}, "count": {"description": "", "format": "int64", "type": "integer"}, "base_time": {"description": "", "format": "date-time", "type": "string"}, "completed_time": {"description": "", "format": "date-time", "type": "string"}, "status": {"description": "", "enum": ["TAG_PARTITION_STATUS_UNSPECIFIED", "TAG_PARTITION_AVAILABLE", "TAG_PARTITION_UNAVAILABLE"], "type": "string"}, "custom_params": {"additionalProperties": {"description": "", "type": "string"}, "description": "", "type": "object"}}, "required": ["entity_name"], "title": "TagPartition", "type": "object"}, "sensorsdata.horizon.v1.EvaluateTagRequest": {"description": "", "properties": {"project_id": {"description": "", "format": "int32", "type": "integer"}, "entity_name": {"description": "", "type": "string"}, "evaluate_params": {"description": "", "items": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.EvaluateTagRequest.EvaluateTagParam"}, "type": "array"}}, "required": ["entity_name", "project_id"], "title": "EvaluateTagRequest", "type": "object"}, "sensorsdata.horizon.v1.ListTagDefinitionsResponseHttpApiResult": {"description": "sensorsdata.horizon.v1.ListTagDefinitionsResponse", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListTagDefinitionsResponse"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.horizon.v1.ListTagDefinitionsResponseHttpApiResult", "type": "object"}, "sensorsdata.horizon.v1.TagDefinitionHttpApiResult": {"description": "sensorsdata.horizon.v1.TagDefinition", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.TagDefinition"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.horizon.v1.TagDefinitionHttpApiResult", "type": "object"}, "sensorsdata.horizon.v1.ListTagPartitionsResponseHttpApiResult": {"description": "sensorsdata.horizon.v1.ListTagPartitionsResponse", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.ListTagPartitionsResponse"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.horizon.v1.ListTagPartitionsResponseHttpApiResult", "type": "object"}, "sensorsdata.horizon.v1.GetLastedTagPartitionResponseHttpApiResult": {"description": "sensorsdata.horizon.v1.GetLastedTagPartitionResponse", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.GetLastedTagPartitionResponse"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.horizon.v1.GetLastedTagPartitionResponseHttpApiResult", "type": "object"}, "sensorsdata.horizon.v1.GetTagTaskResponseHttpApiResult": {"description": "sensorsdata.horizon.v1.GetTagTaskResponse", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "request_id": {"type": "string"}, "data": {"$ref": "#/components/schemas/sensorsdata.horizon.v1.GetTagTaskResponse"}, "error_info": {"$ref": "#/components/schemas/sensorsdata.common.ErrorInfo"}}, "title": "sensorsdata.horizon.v1.GetTagTaskResponseHttpApiResult", "type": "object"}}}}