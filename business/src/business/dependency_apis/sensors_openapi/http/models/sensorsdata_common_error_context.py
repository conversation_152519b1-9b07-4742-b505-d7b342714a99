# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataCommonErrorContext(BaseModel):
    """
    发生错误时，系统快照信息
    """ # noqa: E501
    origin_stack: Optional[StrictStr] = Field(default=None, description="上游异常的 stack，由系统截获")
    origin_cause: Optional[StrictStr] = Field(default=None, description="上游异常的 cause by，由系统截获")
    origin_code: Optional[StrictStr] = Field(default=None, description="上游异常的错误简码")
    origin_complete_code: Optional[StrictStr] = Field(default=None, description="上游异常的完整错误码")
    error_extend_desc: Optional[StrictStr] = Field(default=None, description="本次异常的辅助说明，可传入")
    __properties: ClassVar[List[str]] = ["origin_stack", "origin_cause", "origin_code", "origin_complete_code", "error_extend_desc"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataCommonErrorContext from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataCommonErrorContext from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "origin_stack": obj.get("origin_stack"),
            "origin_cause": obj.get("origin_cause"),
            "origin_code": obj.get("origin_code"),
            "origin_complete_code": obj.get("origin_complete_code"),
            "error_extend_desc": obj.get("error_extend_desc")
        })
        return _obj


