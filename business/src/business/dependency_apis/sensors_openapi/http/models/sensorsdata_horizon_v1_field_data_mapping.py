# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field_data_mapping_extended_column import SensorsdataHorizonV1FieldDataMappingExtendedColumn
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1FieldDataMapping(BaseModel):
    """
    
    """ # noqa: E501
    view_column_name: Optional[StrictStr] = None
    source_type: Optional[StrictStr] = None
    extended_column: Optional[SensorsdataHorizonV1FieldDataMappingExtendedColumn] = None
    __properties: ClassVar[List[str]] = ["view_column_name", "source_type", "extended_column"]

    @field_validator('source_type')
    def source_type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['FIELD_SOURCE_TYPE_UNSPECIFIED', 'MAIN_TABLE_COLUMN', 'EXTENDED_TABLE_COLUMN', 'COMPLEX_EXPRESSION', 'INLINE_EXPRESSION']):
            raise ValueError("must be one of enum values ('FIELD_SOURCE_TYPE_UNSPECIFIED', 'MAIN_TABLE_COLUMN', 'EXTENDED_TABLE_COLUMN', 'COMPLEX_EXPRESSION', 'INLINE_EXPRESSION')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1FieldDataMapping from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of extended_column
        if self.extended_column:
            _dict['extended_column'] = self.extended_column.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1FieldDataMapping from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "view_column_name": obj.get("view_column_name"),
            "source_type": obj.get("source_type"),
            "extended_column": SensorsdataHorizonV1FieldDataMappingExtendedColumn.from_dict(obj["extended_column"]) if obj.get("extended_column") is not None else None
        })
        return _obj


