# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictBool, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_basic_measure_based_rule_expression import SensorsdataHorizonV1BasicMeasureBasedRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_custom_based_rule_expression import SensorsdataHorizonV1CustomBasedRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_eql_based_rule_expression import SensorsdataHorizonV1EqlBasedRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_loading_based_rule_expression import SensorsdataHorizonV1LoadingBasedRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_rfm_based_rule_expression import SensorsdataHorizonV1RfmBasedRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_based_rule_expression import SensorsdataHorizonV1SegmentBasedRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_rule import SensorsdataHorizonV1SegmentRule
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_rule_expression import SensorsdataHorizonV1SegmentRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_sql_based_rule_expression import SensorsdataHorizonV1SqlBasedRuleExpression
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1TagRuleExpression(BaseModel):
    """
    
    """ # noqa: E501
    tag_type: Optional[StrictStr] = None
    sql_based_rule: Optional[SensorsdataHorizonV1SqlBasedRuleExpression] = None
    eql_based_rule: Optional[SensorsdataHorizonV1EqlBasedRuleExpression] = None
    custom_based_rule: Optional[SensorsdataHorizonV1CustomBasedRuleExpression] = None
    loading_based_rule: Optional[SensorsdataHorizonV1LoadingBasedRuleExpression] = None
    segment_based_rule: Optional[SensorsdataHorizonV1SegmentBasedRuleExpression] = None
    basic_measure_based_rule: Optional[SensorsdataHorizonV1BasicMeasureBasedRuleExpression] = None
    rfm_based_rule: Optional[SensorsdataHorizonV1RfmBasedRuleExpression] = None
    virtual: Optional[StrictBool] = None
    time_zone: Optional[StrictStr] = None
    segment_filter: Optional[SensorsdataHorizonV1SegmentRuleExpression] = None
    filter_segment_at_last: Optional[StrictBool] = None
    entity_set_rules: Optional[List[SensorsdataHorizonV1SegmentRule]] = None
    version: Optional[StrictInt] = None
    __properties: ClassVar[List[str]] = ["tag_type", "sql_based_rule", "eql_based_rule", "custom_based_rule", "loading_based_rule", "segment_based_rule", "basic_measure_based_rule", "rfm_based_rule", "virtual", "time_zone", "segment_filter", "filter_segment_at_last", "entity_set_rules", "version"]

    @field_validator('tag_type')
    def tag_type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['TAG_TYPE_UNSPECIFIED', 'SQL_BASED', 'LOADING_BASED', 'EQL_BASED', 'CUSTOM_BASED', 'SEGMENT_BASED', 'BASIC_MEASURE_BASED', 'RFM_BASED']):
            raise ValueError("must be one of enum values ('TAG_TYPE_UNSPECIFIED', 'SQL_BASED', 'LOADING_BASED', 'EQL_BASED', 'CUSTOM_BASED', 'SEGMENT_BASED', 'BASIC_MEASURE_BASED', 'RFM_BASED')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1TagRuleExpression from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of sql_based_rule
        if self.sql_based_rule:
            _dict['sql_based_rule'] = self.sql_based_rule.to_dict()
        # override the default output from pydantic by calling `to_dict()` of eql_based_rule
        if self.eql_based_rule:
            _dict['eql_based_rule'] = self.eql_based_rule.to_dict()
        # override the default output from pydantic by calling `to_dict()` of custom_based_rule
        if self.custom_based_rule:
            _dict['custom_based_rule'] = self.custom_based_rule.to_dict()
        # override the default output from pydantic by calling `to_dict()` of loading_based_rule
        if self.loading_based_rule:
            _dict['loading_based_rule'] = self.loading_based_rule.to_dict()
        # override the default output from pydantic by calling `to_dict()` of segment_based_rule
        if self.segment_based_rule:
            _dict['segment_based_rule'] = self.segment_based_rule.to_dict()
        # override the default output from pydantic by calling `to_dict()` of basic_measure_based_rule
        if self.basic_measure_based_rule:
            _dict['basic_measure_based_rule'] = self.basic_measure_based_rule.to_dict()
        # override the default output from pydantic by calling `to_dict()` of rfm_based_rule
        if self.rfm_based_rule:
            _dict['rfm_based_rule'] = self.rfm_based_rule.to_dict()
        # override the default output from pydantic by calling `to_dict()` of segment_filter
        if self.segment_filter:
            _dict['segment_filter'] = self.segment_filter.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in entity_set_rules (list)
        _items = []
        if self.entity_set_rules:
            for _item_entity_set_rules in self.entity_set_rules:
                if _item_entity_set_rules:
                    _items.append(_item_entity_set_rules.to_dict())
            _dict['entity_set_rules'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1TagRuleExpression from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "tag_type": obj.get("tag_type"),
            "sql_based_rule": SensorsdataHorizonV1SqlBasedRuleExpression.from_dict(obj["sql_based_rule"]) if obj.get("sql_based_rule") is not None else None,
            "eql_based_rule": SensorsdataHorizonV1EqlBasedRuleExpression.from_dict(obj["eql_based_rule"]) if obj.get("eql_based_rule") is not None else None,
            "custom_based_rule": SensorsdataHorizonV1CustomBasedRuleExpression.from_dict(obj["custom_based_rule"]) if obj.get("custom_based_rule") is not None else None,
            "loading_based_rule": SensorsdataHorizonV1LoadingBasedRuleExpression.from_dict(obj["loading_based_rule"]) if obj.get("loading_based_rule") is not None else None,
            "segment_based_rule": SensorsdataHorizonV1SegmentBasedRuleExpression.from_dict(obj["segment_based_rule"]) if obj.get("segment_based_rule") is not None else None,
            "basic_measure_based_rule": SensorsdataHorizonV1BasicMeasureBasedRuleExpression.from_dict(obj["basic_measure_based_rule"]) if obj.get("basic_measure_based_rule") is not None else None,
            "rfm_based_rule": SensorsdataHorizonV1RfmBasedRuleExpression.from_dict(obj["rfm_based_rule"]) if obj.get("rfm_based_rule") is not None else None,
            "virtual": obj.get("virtual"),
            "time_zone": obj.get("time_zone"),
            "segment_filter": SensorsdataHorizonV1SegmentRuleExpression.from_dict(obj["segment_filter"]) if obj.get("segment_filter") is not None else None,
            "filter_segment_at_last": obj.get("filter_segment_at_last"),
            "entity_set_rules": [SensorsdataHorizonV1SegmentRule.from_dict(_item) for _item in obj["entity_set_rules"]] if obj.get("entity_set_rules") is not None else None,
            "version": obj.get("version")
        })
        return _obj


