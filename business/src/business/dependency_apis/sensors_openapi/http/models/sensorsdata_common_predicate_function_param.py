# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_literal import SensorsdataCommonLiteral
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_time_point import SensorsdataCommonTimePoint
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataCommonPredicateFunctionParam(BaseModel):
    """
    ```  函数的参数，有几种可能  1. 一个 field, 如 events.$Anything.$city  2. 一个 Literal 的常量  3. 一个变量, 如 $ENTRY_TIME, 需要在执行上下文里进行替换  4. 一个 TimePoint, 代表一个相对或绝对的时间点, 用于时间类型的条件  5. 一个 expression user.p1/user.p2+user.p3  ```
    """ # noqa: E501
    param_type: Optional[StrictStr] = Field(default=None, description="函数的参数类型")
    var_field: Optional[StrictStr] = Field(default=None, description="字段名", alias="field")
    value: Optional[SensorsdataCommonLiteral] = None
    variable: Optional[StrictStr] = Field(default=None, description="变量，需要在执行上下文里进行替换")
    time_point: Optional[SensorsdataCommonTimePoint] = None
    expression: Optional[StrictStr] = Field(default=None, description="自定义表达式")
    __properties: ClassVar[List[str]] = ["param_type", "field", "value", "variable", "time_point", "expression"]

    @field_validator('param_type')
    def param_type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['FUNCTION_PARAM_TYPE_UNSPECIFIED', 'FIELD', 'VALUE', 'VARIABLE', 'TIME_POINT', 'EXPRESSION']):
            raise ValueError("must be one of enum values ('FUNCTION_PARAM_TYPE_UNSPECIFIED', 'FIELD', 'VALUE', 'VARIABLE', 'TIME_POINT', 'EXPRESSION')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataCommonPredicateFunctionParam from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of value
        if self.value:
            _dict['value'] = self.value.to_dict()
        # override the default output from pydantic by calling `to_dict()` of time_point
        if self.time_point:
            _dict['time_point'] = self.time_point.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataCommonPredicateFunctionParam from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "param_type": obj.get("param_type"),
            "field": obj.get("field"),
            "value": SensorsdataCommonLiteral.from_dict(obj["value"]) if obj.get("value") is not None else None,
            "variable": obj.get("variable"),
            "time_point": SensorsdataCommonTimePoint.from_dict(obj["time_point"]) if obj.get("time_point") is not None else None,
            "expression": obj.get("expression")
        })
        return _obj


