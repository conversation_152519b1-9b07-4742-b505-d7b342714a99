# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_schema_data_mapping_schema_source_table import SensorsdataHorizonV1SchemaDataMappingSchemaSourceTable
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_schema_extended_table import SensorsdataHorizonV1SchemaExtendedTable
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1SchemaDataMapping(BaseModel):
    """
    
    """ # noqa: E501
    view_table: Optional[SensorsdataHorizonV1SchemaDataMappingSchemaSourceTable] = None
    main_table: Optional[SensorsdataHorizonV1SchemaDataMappingSchemaSourceTable] = None
    extended_tables: Optional[List[SensorsdataHorizonV1SchemaExtendedTable]] = None
    __properties: ClassVar[List[str]] = ["view_table", "main_table", "extended_tables"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1SchemaDataMapping from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of view_table
        if self.view_table:
            _dict['view_table'] = self.view_table.to_dict()
        # override the default output from pydantic by calling `to_dict()` of main_table
        if self.main_table:
            _dict['main_table'] = self.main_table.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in extended_tables (list)
        _items = []
        if self.extended_tables:
            for _item_extended_tables in self.extended_tables:
                if _item_extended_tables:
                    _items.append(_item_extended_tables.to_dict())
            _dict['extended_tables'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1SchemaDataMapping from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "view_table": SensorsdataHorizonV1SchemaDataMappingSchemaSourceTable.from_dict(obj["view_table"]) if obj.get("view_table") is not None else None,
            "main_table": SensorsdataHorizonV1SchemaDataMappingSchemaSourceTable.from_dict(obj["main_table"]) if obj.get("main_table") is not None else None,
            "extended_tables": [SensorsdataHorizonV1SchemaExtendedTable.from_dict(_item) for _item in obj["extended_tables"]] if obj.get("extended_tables") is not None else None
        })
        return _obj


