# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field_view_data_source_complex_expression import SensorsdataHorizonV1FieldViewDataSourceComplexExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field_view_data_source_inline_expression import SensorsdataHorizonV1FieldViewDataSourceInlineExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field_view_data_source_view_column_source_data import SensorsdataHorizonV1FieldViewDataSourceViewColumnSourceData
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1FieldViewDataSource(BaseModel):
    """
    
    """ # noqa: E501
    field_name: StrictStr
    view_column_source_data: Optional[SensorsdataHorizonV1FieldViewDataSourceViewColumnSourceData] = None
    complex_expression: Optional[SensorsdataHorizonV1FieldViewDataSourceComplexExpression] = None
    inline_expression: Optional[SensorsdataHorizonV1FieldViewDataSourceInlineExpression] = None
    version: Optional[StrictInt] = None
    __properties: ClassVar[List[str]] = ["field_name", "view_column_source_data", "complex_expression", "inline_expression", "version"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1FieldViewDataSource from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of view_column_source_data
        if self.view_column_source_data:
            _dict['view_column_source_data'] = self.view_column_source_data.to_dict()
        # override the default output from pydantic by calling `to_dict()` of complex_expression
        if self.complex_expression:
            _dict['complex_expression'] = self.complex_expression.to_dict()
        # override the default output from pydantic by calling `to_dict()` of inline_expression
        if self.inline_expression:
            _dict['inline_expression'] = self.inline_expression.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1FieldViewDataSource from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "field_name": obj.get("field_name"),
            "view_column_source_data": SensorsdataHorizonV1FieldViewDataSourceViewColumnSourceData.from_dict(obj["view_column_source_data"]) if obj.get("view_column_source_data") is not None else None,
            "complex_expression": SensorsdataHorizonV1FieldViewDataSourceComplexExpression.from_dict(obj["complex_expression"]) if obj.get("complex_expression") is not None else None,
            "inline_expression": SensorsdataHorizonV1FieldViewDataSourceInlineExpression.from_dict(obj["inline_expression"]) if obj.get("inline_expression") is not None else None,
            "version": obj.get("version")
        })
        return _obj


