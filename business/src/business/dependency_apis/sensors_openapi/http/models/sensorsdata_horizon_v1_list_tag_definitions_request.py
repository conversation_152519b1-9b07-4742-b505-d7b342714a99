# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictBool, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1ListTagDefinitionsRequest(BaseModel):
    """
    
    """ # noqa: E501
    page_size: Optional[StrictInt] = None
    page: Optional[StrictInt] = None
    project_id: StrictInt
    entity_name: StrictStr
    tag_names: Optional[List[StrictStr]] = None
    show_deleted: Optional[StrictBool] = None
    show_invisible: Optional[StrictBool] = None
    field_mask: Optional[StrictStr] = None
    only_brief: Optional[StrictBool] = None
    __properties: ClassVar[List[str]] = ["page_size", "page", "project_id", "entity_name", "tag_names", "show_deleted", "show_invisible", "field_mask", "only_brief"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1ListTagDefinitionsRequest from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1ListTagDefinitionsRequest from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "page_size": obj.get("page_size"),
            "page": obj.get("page"),
            "project_id": obj.get("project_id"),
            "entity_name": obj.get("entity_name"),
            "tag_names": obj.get("tag_names"),
            "show_deleted": obj.get("show_deleted"),
            "show_invisible": obj.get("show_invisible"),
            "field_mask": obj.get("field_mask"),
            "only_brief": obj.get("only_brief")
        })
        return _obj


