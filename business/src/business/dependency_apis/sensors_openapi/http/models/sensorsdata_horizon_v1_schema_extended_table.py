# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_access_info import SensorsdataHorizonV1AccessInfo
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1SchemaExtendedTable(BaseModel):
    """
    
    """ # noqa: E501
    extended_table_id: StrictStr
    schema_name: StrictStr
    db_name: StrictStr
    table_name: StrictStr
    table_display_type: Optional[StrictStr] = None
    display_name: Optional[StrictStr] = None
    join_on_expression: StrictStr
    join_key_columns: Optional[List[StrictStr]] = None
    join_mode: Optional[StrictStr] = None
    access_info: Optional[SensorsdataHorizonV1AccessInfo] = None
    __properties: ClassVar[List[str]] = ["extended_table_id", "schema_name", "db_name", "table_name", "table_display_type", "display_name", "join_on_expression", "join_key_columns", "join_mode", "access_info"]

    @field_validator('join_mode')
    def join_mode_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['SCHEMA_TABLE_JOIN_MODE_UNSPECIFIED', 'INNER_JOIN', 'LEFT_JOIN', 'RIGHT_JOIN', 'LEFT_ANTI_JOIN', 'LEFT_SEMI_JOIN', 'FULL_OUTER_JOIN']):
            raise ValueError("must be one of enum values ('SCHEMA_TABLE_JOIN_MODE_UNSPECIFIED', 'INNER_JOIN', 'LEFT_JOIN', 'RIGHT_JOIN', 'LEFT_ANTI_JOIN', 'LEFT_SEMI_JOIN', 'FULL_OUTER_JOIN')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1SchemaExtendedTable from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of access_info
        if self.access_info:
            _dict['access_info'] = self.access_info.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1SchemaExtendedTable from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "extended_table_id": obj.get("extended_table_id"),
            "schema_name": obj.get("schema_name"),
            "db_name": obj.get("db_name"),
            "table_name": obj.get("table_name"),
            "table_display_type": obj.get("table_display_type"),
            "display_name": obj.get("display_name"),
            "join_on_expression": obj.get("join_on_expression"),
            "join_key_columns": obj.get("join_key_columns"),
            "join_mode": obj.get("join_mode"),
            "access_info": SensorsdataHorizonV1AccessInfo.from_dict(obj["access_info"]) if obj.get("access_info") is not None else None
        })
        return _obj


