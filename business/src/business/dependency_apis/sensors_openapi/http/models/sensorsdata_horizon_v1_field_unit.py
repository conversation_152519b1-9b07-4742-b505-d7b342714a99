# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictBool, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_access_info import SensorsdataHorizonV1AccessInfo
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_data_type import SensorsdataHorizonV1DataType
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1FieldUnit(BaseModel):
    """
    
    """ # noqa: E501
    name: Optional[StrictStr] = None
    display_name: StrictStr
    builtin: Optional[StrictBool] = None
    data_type: Optional[SensorsdataHorizonV1DataType] = None
    base: Optional[StrictInt] = None
    unit_group_name: Optional[StrictStr] = None
    unit_group_display_name: Optional[StrictStr] = None
    access_info: Optional[SensorsdataHorizonV1AccessInfo] = None
    __properties: ClassVar[List[str]] = ["name", "display_name", "builtin", "data_type", "base", "unit_group_name", "unit_group_display_name", "access_info"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1FieldUnit from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of data_type
        if self.data_type:
            _dict['data_type'] = self.data_type.to_dict()
        # override the default output from pydantic by calling `to_dict()` of access_info
        if self.access_info:
            _dict['access_info'] = self.access_info.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1FieldUnit from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "name": obj.get("name"),
            "display_name": obj.get("display_name"),
            "builtin": obj.get("builtin"),
            "data_type": SensorsdataHorizonV1DataType.from_dict(obj["data_type"]) if obj.get("data_type") is not None else None,
            "base": obj.get("base"),
            "unit_group_name": obj.get("unit_group_name"),
            "unit_group_display_name": obj.get("unit_group_display_name"),
            "access_info": SensorsdataHorizonV1AccessInfo.from_dict(obj["access_info"]) if obj.get("access_info") is not None else None
        })
        return _obj


