# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_filter_condition import SensorsdataCommonFilterCondition
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataCommonCompoundFilterCondition(BaseModel):
    """
    复合过滤条件，用于描述 condition 或 compound_filter 之间的关系
    """ # noqa: E501
    operator: Optional[StrictStr] = Field(default=None, description="逻辑运算法")
    conditions: Optional[List[SensorsdataCommonFilterCondition]] = Field(default=None, description="过滤条件列表，只有最后一级才有")
    compound_conditions: Optional[List[SensorsdataCommonCompoundFilterCondition]] = Field(default=None, description="嵌套的下一级复合过滤条件")
    index: Optional[StrictInt] = Field(default=None, description="当前筛选条件所处的位置下标，主要为了保证前端展示顺序的稳定")
    __properties: ClassVar[List[str]] = ["operator", "conditions", "compound_conditions", "index"]

    @field_validator('operator')
    def operator_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['LOGICAL_OPERATOR_UNSPECIFIED', 'AND', 'OR']):
            raise ValueError("must be one of enum values ('LOGICAL_OPERATOR_UNSPECIFIED', 'AND', 'OR')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataCommonCompoundFilterCondition from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in conditions (list)
        _items = []
        if self.conditions:
            for _item_conditions in self.conditions:
                if _item_conditions:
                    _items.append(_item_conditions.to_dict())
            _dict['conditions'] = _items
        # override the default output from pydantic by calling `to_dict()` of each item in compound_conditions (list)
        _items = []
        if self.compound_conditions:
            for _item_compound_conditions in self.compound_conditions:
                if _item_compound_conditions:
                    _items.append(_item_compound_conditions.to_dict())
            _dict['compound_conditions'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataCommonCompoundFilterCondition from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "operator": obj.get("operator"),
            "conditions": [SensorsdataCommonFilterCondition.from_dict(_item) for _item in obj["conditions"]] if obj.get("conditions") is not None else None,
            "compound_conditions": [SensorsdataCommonCompoundFilterCondition.from_dict(_item) for _item in obj["compound_conditions"]] if obj.get("compound_conditions") is not None else None,
            "index": obj.get("index")
        })
        return _obj

# TODO: Rewrite to not use raise_errors
SensorsdataCommonCompoundFilterCondition.model_rebuild(raise_errors=False)

