# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_rfm_based_rule_expression_rfm_factor import SensorsdataHorizonV1RfmBasedRuleExpressionRfmFactor
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_rule_expression import SensorsdataHorizonV1SegmentRuleExpression
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1RfmBasedRuleExpression(BaseModel):
    """
    
    """ # noqa: E501
    segment: Optional[SensorsdataHorizonV1SegmentRuleExpression] = None
    r_factor: Optional[SensorsdataHorizonV1RfmBasedRuleExpressionRfmFactor] = None
    f_factor: Optional[SensorsdataHorizonV1RfmBasedRuleExpressionRfmFactor] = None
    m_factor: Optional[SensorsdataHorizonV1RfmBasedRuleExpressionRfmFactor] = None
    layer_params: Optional[Dict[str, StrictStr]] = None
    __properties: ClassVar[List[str]] = ["segment", "r_factor", "f_factor", "m_factor", "layer_params"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1RfmBasedRuleExpression from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of segment
        if self.segment:
            _dict['segment'] = self.segment.to_dict()
        # override the default output from pydantic by calling `to_dict()` of r_factor
        if self.r_factor:
            _dict['r_factor'] = self.r_factor.to_dict()
        # override the default output from pydantic by calling `to_dict()` of f_factor
        if self.f_factor:
            _dict['f_factor'] = self.f_factor.to_dict()
        # override the default output from pydantic by calling `to_dict()` of m_factor
        if self.m_factor:
            _dict['m_factor'] = self.m_factor.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1RfmBasedRuleExpression from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "segment": SensorsdataHorizonV1SegmentRuleExpression.from_dict(obj["segment"]) if obj.get("segment") is not None else None,
            "r_factor": SensorsdataHorizonV1RfmBasedRuleExpressionRfmFactor.from_dict(obj["r_factor"]) if obj.get("r_factor") is not None else None,
            "f_factor": SensorsdataHorizonV1RfmBasedRuleExpressionRfmFactor.from_dict(obj["f_factor"]) if obj.get("f_factor") is not None else None,
            "m_factor": SensorsdataHorizonV1RfmBasedRuleExpressionRfmFactor.from_dict(obj["m_factor"]) if obj.get("m_factor") is not None else None,
            "layer_params": obj.get("layer_params")
        })
        return _obj


