# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1ListLogicalSchemasRequest(BaseModel):
    """
    
    """ # noqa: E501
    project_id: StrictInt
    physical_schema_name: StrictStr
    schema_type: Optional[StrictStr] = None
    contain_field_names: Optional[List[StrictStr]] = None
    display_names: Optional[List[StrictStr]] = None
    expand: Optional[StrictStr] = None
    page: Optional[StrictInt] = None
    page_size: Optional[StrictInt] = None
    __properties: ClassVar[List[str]] = ["project_id", "physical_schema_name", "schema_type", "contain_field_names", "display_names", "expand", "page", "page_size"]

    @field_validator('schema_type')
    def schema_type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['SCHEMA_TYPE_UNSPECIFIED', 'PHYSICAL', 'LOGICAL', 'VIRTUAL']):
            raise ValueError("must be one of enum values ('SCHEMA_TYPE_UNSPECIFIED', 'PHYSICAL', 'LOGICAL', 'VIRTUAL')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1ListLogicalSchemasRequest from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1ListLogicalSchemasRequest from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "project_id": obj.get("project_id"),
            "physical_schema_name": obj.get("physical_schema_name"),
            "schema_type": obj.get("schema_type"),
            "contain_field_names": obj.get("contain_field_names"),
            "display_names": obj.get("display_names"),
            "expand": obj.get("expand"),
            "page": obj.get("page"),
            "page_size": obj.get("page_size")
        })
        return _obj


