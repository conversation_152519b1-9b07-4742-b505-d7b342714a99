# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, StrictBool, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataAnalyticsV1EventDefine(BaseModel):
    """
    
    """ # noqa: E501
    id: Optional[StrictInt] = None
    name: Optional[StrictStr] = None
    cname: Optional[StrictStr] = None
    is_virtual: Optional[StrictBool] = None
    tags: Optional[List[StrictInt]] = None
    comment: Optional[StrictStr] = None
    total_count: Optional[StrictInt] = None
    platforms: Optional[List[StrictStr]] = None
    trigger_opportunity: Optional[StrictStr] = None
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    __properties: ClassVar[List[str]] = ["id", "name", "cname", "is_virtual", "tags", "comment", "total_count", "platforms", "trigger_opportunity", "create_time", "update_time"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataAnalyticsV1EventDefine from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataAnalyticsV1EventDefine from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "id": obj.get("id"),
            "name": obj.get("name"),
            "cname": obj.get("cname"),
            "is_virtual": obj.get("is_virtual"),
            "tags": obj.get("tags"),
            "comment": obj.get("comment"),
            "total_count": obj.get("total_count"),
            "platforms": obj.get("platforms"),
            "trigger_opportunity": obj.get("trigger_opportunity"),
            "create_time": obj.get("create_time"),
            "update_time": obj.get("update_time")
        })
        return _obj


