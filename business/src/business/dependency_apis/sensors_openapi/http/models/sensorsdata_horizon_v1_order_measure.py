# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_compound_filter_condition import SensorsdataCommonCompoundFilterCondition
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_aggregator_field import SensorsdataHorizonV1AggregatorField
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1OrderMeasure(BaseModel):
    """
    
    """ # noqa: E501
    type: Optional[StrictStr] = None
    plain_field: Optional[StrictStr] = None
    aggregator_field: Optional[SensorsdataHorizonV1AggregatorField] = None
    condition: Optional[SensorsdataCommonCompoundFilterCondition] = None
    eql_measure: Optional[StrictStr] = None
    __properties: ClassVar[List[str]] = ["type", "plain_field", "aggregator_field", "condition", "eql_measure"]

    @field_validator('type')
    def type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['FIELD_TYPE_UNSPECIFIED', 'PLAIN_FIELD', 'AGGREGATOR_FIELD', 'EQL_MEASURE_FIELD']):
            raise ValueError("must be one of enum values ('FIELD_TYPE_UNSPECIFIED', 'PLAIN_FIELD', 'AGGREGATOR_FIELD', 'EQL_MEASURE_FIELD')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1OrderMeasure from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of aggregator_field
        if self.aggregator_field:
            _dict['aggregator_field'] = self.aggregator_field.to_dict()
        # override the default output from pydantic by calling `to_dict()` of condition
        if self.condition:
            _dict['condition'] = self.condition.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1OrderMeasure from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "type": obj.get("type"),
            "plain_field": obj.get("plain_field"),
            "aggregator_field": SensorsdataHorizonV1AggregatorField.from_dict(obj["aggregator_field"]) if obj.get("aggregator_field") is not None else None,
            "condition": SensorsdataCommonCompoundFilterCondition.from_dict(obj["condition"]) if obj.get("condition") is not None else None,
            "eql_measure": obj.get("eql_measure")
        })
        return _obj


