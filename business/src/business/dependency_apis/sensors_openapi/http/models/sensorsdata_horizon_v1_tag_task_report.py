# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_task_report_tag_loading_report import SensorsdataHorizonV1TagTaskReportTagLoadingReport
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1TagTaskReport(BaseModel):
    """
    
    """ # noqa: E501
    count: Optional[StrictInt] = None
    tag_loading_report: Optional[SensorsdataHorizonV1TagTaskReportTagLoadingReport] = None
    extend_report_info: Optional[Dict[str, StrictStr]] = None
    __properties: ClassVar[List[str]] = ["count", "tag_loading_report", "extend_report_info"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1TagTaskReport from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of tag_loading_report
        if self.tag_loading_report:
            _dict['tag_loading_report'] = self.tag_loading_report.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1TagTaskReport from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "count": obj.get("count"),
            "tag_loading_report": SensorsdataHorizonV1TagTaskReportTagLoadingReport.from_dict(obj["tag_loading_report"]) if obj.get("tag_loading_report") is not None else None,
            "extend_report_info": obj.get("extend_report_info")
        })
        return _obj


