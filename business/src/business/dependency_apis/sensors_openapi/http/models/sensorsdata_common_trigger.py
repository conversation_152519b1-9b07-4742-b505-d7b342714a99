# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_cron_trigger import SensorsdataCommonCronTrigger
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_periodic_trigger import SensorsdataCommonPeriodicTrigger
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_static_trigger import SensorsdataCommonStaticTrigger
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataCommonTrigger(BaseModel):
    """
    
    """ # noqa: E501
    trigger_type: Optional[StrictStr] = None
    periodic_trigger: Optional[SensorsdataCommonPeriodicTrigger] = None
    cron_trigger: Optional[SensorsdataCommonCronTrigger] = None
    static_trigger: Optional[SensorsdataCommonStaticTrigger] = None
    __properties: ClassVar[List[str]] = ["trigger_type", "periodic_trigger", "cron_trigger", "static_trigger"]

    @field_validator('trigger_type')
    def trigger_type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['TRIGGER_TYPE_UNSPECIFIED', 'MANUAL', 'PERIODIC', 'CRON', 'STATIC', 'DYNAMIC']):
            raise ValueError("must be one of enum values ('TRIGGER_TYPE_UNSPECIFIED', 'MANUAL', 'PERIODIC', 'CRON', 'STATIC', 'DYNAMIC')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataCommonTrigger from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of periodic_trigger
        if self.periodic_trigger:
            _dict['periodic_trigger'] = self.periodic_trigger.to_dict()
        # override the default output from pydantic by calling `to_dict()` of cron_trigger
        if self.cron_trigger:
            _dict['cron_trigger'] = self.cron_trigger.to_dict()
        # override the default output from pydantic by calling `to_dict()` of static_trigger
        if self.static_trigger:
            _dict['static_trigger'] = self.static_trigger.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataCommonTrigger from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "trigger_type": obj.get("trigger_type"),
            "periodic_trigger": SensorsdataCommonPeriodicTrigger.from_dict(obj["periodic_trigger"]) if obj.get("periodic_trigger") is not None else None,
            "cron_trigger": SensorsdataCommonCronTrigger.from_dict(obj["cron_trigger"]) if obj.get("cron_trigger") is not None else None,
            "static_trigger": SensorsdataCommonStaticTrigger.from_dict(obj["static_trigger"]) if obj.get("static_trigger") is not None else None
        })
        return _obj


