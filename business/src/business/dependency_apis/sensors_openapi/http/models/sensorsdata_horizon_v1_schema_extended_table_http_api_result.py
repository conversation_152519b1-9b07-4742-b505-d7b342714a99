# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_error_info import SensorsdataCommonErrorInfo
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_schema_extended_table import SensorsdataHorizonV1SchemaExtendedTable
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1SchemaExtendedTableHttpApiResult(BaseModel):
    """
    sensorsdata.horizon.v1.SchemaExtendedTable
    """ # noqa: E501
    code: Optional[StrictStr] = None
    message: Optional[StrictStr] = None
    request_id: Optional[StrictStr] = None
    data: Optional[SensorsdataHorizonV1SchemaExtendedTable] = None
    error_info: Optional[SensorsdataCommonErrorInfo] = None
    __properties: ClassVar[List[str]] = ["code", "message", "request_id", "data", "error_info"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1SchemaExtendedTableHttpApiResult from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of data
        if self.data:
            _dict['data'] = self.data.to_dict()
        # override the default output from pydantic by calling `to_dict()` of error_info
        if self.error_info:
            _dict['error_info'] = self.error_info.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1SchemaExtendedTableHttpApiResult from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "code": obj.get("code"),
            "message": obj.get("message"),
            "request_id": obj.get("request_id"),
            "data": SensorsdataHorizonV1SchemaExtendedTable.from_dict(obj["data"]) if obj.get("data") is not None else None,
            "error_info": SensorsdataCommonErrorInfo.from_dict(obj["error_info"]) if obj.get("error_info") is not None else None
        })
        return _obj


