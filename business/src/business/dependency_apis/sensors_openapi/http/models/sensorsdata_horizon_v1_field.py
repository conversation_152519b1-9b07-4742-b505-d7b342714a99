# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictBool, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_access_info import SensorsdataHorizonV1AccessInfo
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_data_type import SensorsdataHorizonV1DataType
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field_data_mapping import SensorsdataHorizonV1FieldDataMapping
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field_object_field import SensorsdataHorizonV1FieldObjectField
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field_unit import SensorsdataHorizonV1FieldUnit
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field_view_data_source import SensorsdataHorizonV1FieldViewDataSource
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1Field(BaseModel):
    """
    
    """ # noqa: E501
    name: StrictStr
    display_name: Optional[StrictStr] = None
    data_type: SensorsdataHorizonV1DataType
    field_display_type: Optional[StrictStr] = None
    created_by: StrictStr
    builtin: Optional[StrictBool] = None
    has_data: Optional[StrictBool] = None
    has_dict: Optional[StrictBool] = None
    visible: Optional[StrictBool] = None
    enable: Optional[StrictBool] = None
    subscribable: Optional[StrictBool] = None
    primary_identity: Optional[StrictBool] = None
    identity: Optional[StrictBool] = None
    required: Optional[StrictBool] = None
    data_versioned: Optional[StrictBool] = None
    data_version_form: Optional[StrictStr] = None
    data_mapping: Optional[SensorsdataHorizonV1FieldDataMapping] = None
    view_data_source: Optional[SensorsdataHorizonV1FieldViewDataSource] = None
    custom_params: Optional[Dict[str, StrictStr]] = None
    unit: Optional[SensorsdataHorizonV1FieldUnit] = None
    object_fields: Optional[List[SensorsdataHorizonV1FieldObjectField]] = None
    version: Optional[StrictInt] = None
    access_info: Optional[SensorsdataHorizonV1AccessInfo] = None
    legacy_id: Optional[StrictInt] = None
    __properties: ClassVar[List[str]] = ["name", "display_name", "data_type", "field_display_type", "created_by", "builtin", "has_data", "has_dict", "visible", "enable", "subscribable", "primary_identity", "identity", "required", "data_versioned", "data_version_form", "data_mapping", "view_data_source", "custom_params", "unit", "object_fields", "version", "access_info", "legacy_id"]

    @field_validator('data_version_form')
    def data_version_form_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['FIELD_VERSION_LEVEL_UNSPECIFIED', 'HOUR', 'DAY', 'WEEK', 'MONTH', 'YEAR', 'CUSTOM']):
            raise ValueError("must be one of enum values ('FIELD_VERSION_LEVEL_UNSPECIFIED', 'HOUR', 'DAY', 'WEEK', 'MONTH', 'YEAR', 'CUSTOM')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1Field from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of data_type
        if self.data_type:
            _dict['data_type'] = self.data_type.to_dict()
        # override the default output from pydantic by calling `to_dict()` of data_mapping
        if self.data_mapping:
            _dict['data_mapping'] = self.data_mapping.to_dict()
        # override the default output from pydantic by calling `to_dict()` of view_data_source
        if self.view_data_source:
            _dict['view_data_source'] = self.view_data_source.to_dict()
        # override the default output from pydantic by calling `to_dict()` of unit
        if self.unit:
            _dict['unit'] = self.unit.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in object_fields (list)
        _items = []
        if self.object_fields:
            for _item_object_fields in self.object_fields:
                if _item_object_fields:
                    _items.append(_item_object_fields.to_dict())
            _dict['object_fields'] = _items
        # override the default output from pydantic by calling `to_dict()` of access_info
        if self.access_info:
            _dict['access_info'] = self.access_info.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1Field from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "name": obj.get("name"),
            "display_name": obj.get("display_name"),
            "data_type": SensorsdataHorizonV1DataType.from_dict(obj["data_type"]) if obj.get("data_type") is not None else None,
            "field_display_type": obj.get("field_display_type"),
            "created_by": obj.get("created_by"),
            "builtin": obj.get("builtin"),
            "has_data": obj.get("has_data"),
            "has_dict": obj.get("has_dict"),
            "visible": obj.get("visible"),
            "enable": obj.get("enable"),
            "subscribable": obj.get("subscribable"),
            "primary_identity": obj.get("primary_identity"),
            "identity": obj.get("identity"),
            "required": obj.get("required"),
            "data_versioned": obj.get("data_versioned"),
            "data_version_form": obj.get("data_version_form"),
            "data_mapping": SensorsdataHorizonV1FieldDataMapping.from_dict(obj["data_mapping"]) if obj.get("data_mapping") is not None else None,
            "view_data_source": SensorsdataHorizonV1FieldViewDataSource.from_dict(obj["view_data_source"]) if obj.get("view_data_source") is not None else None,
            "custom_params": obj.get("custom_params"),
            "unit": SensorsdataHorizonV1FieldUnit.from_dict(obj["unit"]) if obj.get("unit") is not None else None,
            "object_fields": [SensorsdataHorizonV1FieldObjectField.from_dict(_item) for _item in obj["object_fields"]] if obj.get("object_fields") is not None else None,
            "version": obj.get("version"),
            "access_info": SensorsdataHorizonV1AccessInfo.from_dict(obj["access_info"]) if obj.get("access_info") is not None else None,
            "legacy_id": obj.get("legacy_id")
        })
        return _obj


