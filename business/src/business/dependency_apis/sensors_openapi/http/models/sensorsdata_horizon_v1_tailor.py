# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_predicate_function_param import SensorsdataCommonPredicateFunctionParam
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_rank_field import SensorsdataHorizonV1RankField
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1Tailor(BaseModel):
    """
    
    """ # noqa: E501
    rank_field: Optional[SensorsdataHorizonV1RankField] = None
    function: Optional[StrictStr] = None
    params: Optional[List[SensorsdataCommonPredicateFunctionParam]] = None
    __properties: ClassVar[List[str]] = ["rank_field", "function", "params"]

    @field_validator('function')
    def function_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['PREDICATE_FUNCTION_TYPE_UNSPECIFIED', 'IS_SET', 'IS_NOT_SET', 'EQUAL', 'NOT_EQUAL', 'IN', 'NOT_IN', 'LESS', 'LESS_EQUAL', 'GREATER', 'GREATER_EQUAL', 'BETWEEN', 'RIGHT_OPEN_BETWEEN', 'HASH_BETWEEN', 'LEFT_OPEN_BETWEEN', 'OPEN_BETWEEN', 'IS_TRUE', 'IS_FALSE', 'CONTAIN', 'NOT_CONTAIN', 'IS_EMPTY', 'IS_NOT_EMPTY', 'RLIKE', 'NOT_RLIKE', 'INCLUDE', 'NOT_INCLUDE', 'ABSOLUTE_BETWEEN', 'RELATIVE_WITHIN', 'RELATIVE_BETWEEN', 'RELATIVE_EVENT_TIME', 'RELATIVE_BEFORE', 'INTERSECTS', 'NOT_INTERSECTS', 'SUBSET_OF', 'NOT_SUBSET_OF', 'SUPERSET_OF', 'NOT_SUPERSET_OF']):
            raise ValueError("must be one of enum values ('PREDICATE_FUNCTION_TYPE_UNSPECIFIED', 'IS_SET', 'IS_NOT_SET', 'EQUAL', 'NOT_EQUAL', 'IN', 'NOT_IN', 'LESS', 'LESS_EQUAL', 'GREATER', 'GREATER_EQUAL', 'BETWEEN', 'RIGHT_OPEN_BETWEEN', 'HASH_BETWEEN', 'LEFT_OPEN_BETWEEN', 'OPEN_BETWEEN', 'IS_TRUE', 'IS_FALSE', 'CONTAIN', 'NOT_CONTAIN', 'IS_EMPTY', 'IS_NOT_EMPTY', 'RLIKE', 'NOT_RLIKE', 'INCLUDE', 'NOT_INCLUDE', 'ABSOLUTE_BETWEEN', 'RELATIVE_WITHIN', 'RELATIVE_BETWEEN', 'RELATIVE_EVENT_TIME', 'RELATIVE_BEFORE', 'INTERSECTS', 'NOT_INTERSECTS', 'SUBSET_OF', 'NOT_SUBSET_OF', 'SUPERSET_OF', 'NOT_SUPERSET_OF')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1Tailor from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of rank_field
        if self.rank_field:
            _dict['rank_field'] = self.rank_field.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in params (list)
        _items = []
        if self.params:
            for _item_params in self.params:
                if _item_params:
                    _items.append(_item_params.to_dict())
            _dict['params'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1Tailor from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "rank_field": SensorsdataHorizonV1RankField.from_dict(obj["rank_field"]) if obj.get("rank_field") is not None else None,
            "function": obj.get("function"),
            "params": [SensorsdataCommonPredicateFunctionParam.from_dict(_item) for _item in obj["params"]] if obj.get("params") is not None else None
        })
        return _obj


