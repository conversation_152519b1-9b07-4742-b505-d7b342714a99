# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictBool, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_access_info import SensorsdataHorizonV1AccessInfo
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field import SensorsdataHorizonV1Field
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_schema_data_mapping import SensorsdataHorizonV1SchemaDataMapping
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_schema_schema_mapping import SensorsdataHorizonV1SchemaSchemaMapping
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_schema_statistics import SensorsdataHorizonV1SchemaStatistics
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1Schema(BaseModel):
    """
    
    """ # noqa: E501
    project_id: Optional[StrictInt] = None
    name: Optional[StrictStr] = None
    original_name: StrictStr
    display_name: Optional[StrictStr] = None
    type: StrictStr
    schema_class: StrictStr
    schema_display_type: Optional[StrictStr] = None
    builtin: Optional[StrictBool] = None
    has_data: Optional[StrictBool] = None
    visible: Optional[StrictBool] = None
    enable: Optional[StrictBool] = None
    mapping: Optional[SensorsdataHorizonV1SchemaSchemaMapping] = None
    data_mapping: Optional[SensorsdataHorizonV1SchemaDataMapping] = None
    schema_data_mapping_type: Optional[StrictStr] = None
    custom_params: Optional[Dict[str, StrictStr]] = None
    fields: Optional[List[SensorsdataHorizonV1Field]] = None
    version: Optional[StrictInt] = None
    access_info: Optional[SensorsdataHorizonV1AccessInfo] = None
    statistics: Optional[SensorsdataHorizonV1SchemaStatistics] = None
    legacy_id: Optional[StrictInt] = None
    __properties: ClassVar[List[str]] = ["project_id", "name", "original_name", "display_name", "type", "schema_class", "schema_display_type", "builtin", "has_data", "visible", "enable", "mapping", "data_mapping", "schema_data_mapping_type", "custom_params", "fields", "version", "access_info", "statistics", "legacy_id"]

    @field_validator('type')
    def type_validate_enum(cls, value):
        """Validates the enum"""
        if value not in set(['SCHEMA_TYPE_UNSPECIFIED', 'PHYSICAL', 'LOGICAL', 'VIRTUAL']):
            raise ValueError("must be one of enum values ('SCHEMA_TYPE_UNSPECIFIED', 'PHYSICAL', 'LOGICAL', 'VIRTUAL')")
        return value

    @field_validator('schema_class')
    def schema_class_validate_enum(cls, value):
        """Validates the enum"""
        if value not in set(['SCHEMA_CLASS_UNSPECIFIED', 'EVENT', 'USER', 'ITEM', 'DETAIL']):
            raise ValueError("must be one of enum values ('SCHEMA_CLASS_UNSPECIFIED', 'EVENT', 'USER', 'ITEM', 'DETAIL')")
        return value

    @field_validator('schema_data_mapping_type')
    def schema_data_mapping_type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['SCHEMA_DATA_MAPPING_TYPE_UNSPECIFIED', 'BUILTIN_TABLE', 'REFERENCE_TABLE']):
            raise ValueError("must be one of enum values ('SCHEMA_DATA_MAPPING_TYPE_UNSPECIFIED', 'BUILTIN_TABLE', 'REFERENCE_TABLE')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1Schema from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of mapping
        if self.mapping:
            _dict['mapping'] = self.mapping.to_dict()
        # override the default output from pydantic by calling `to_dict()` of data_mapping
        if self.data_mapping:
            _dict['data_mapping'] = self.data_mapping.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in fields (list)
        _items = []
        if self.fields:
            for _item_fields in self.fields:
                if _item_fields:
                    _items.append(_item_fields.to_dict())
            _dict['fields'] = _items
        # override the default output from pydantic by calling `to_dict()` of access_info
        if self.access_info:
            _dict['access_info'] = self.access_info.to_dict()
        # override the default output from pydantic by calling `to_dict()` of statistics
        if self.statistics:
            _dict['statistics'] = self.statistics.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1Schema from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "project_id": obj.get("project_id"),
            "name": obj.get("name"),
            "original_name": obj.get("original_name"),
            "display_name": obj.get("display_name"),
            "type": obj.get("type"),
            "schema_class": obj.get("schema_class"),
            "schema_display_type": obj.get("schema_display_type"),
            "builtin": obj.get("builtin"),
            "has_data": obj.get("has_data"),
            "visible": obj.get("visible"),
            "enable": obj.get("enable"),
            "mapping": SensorsdataHorizonV1SchemaSchemaMapping.from_dict(obj["mapping"]) if obj.get("mapping") is not None else None,
            "data_mapping": SensorsdataHorizonV1SchemaDataMapping.from_dict(obj["data_mapping"]) if obj.get("data_mapping") is not None else None,
            "schema_data_mapping_type": obj.get("schema_data_mapping_type"),
            "custom_params": obj.get("custom_params"),
            "fields": [SensorsdataHorizonV1Field.from_dict(_item) for _item in obj["fields"]] if obj.get("fields") is not None else None,
            "version": obj.get("version"),
            "access_info": SensorsdataHorizonV1AccessInfo.from_dict(obj["access_info"]) if obj.get("access_info") is not None else None,
            "statistics": SensorsdataHorizonV1SchemaStatistics.from_dict(obj["statistics"]) if obj.get("statistics") is not None else None,
            "legacy_id": obj.get("legacy_id")
        })
        return _obj


