# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, StrictBool, StrictFloat, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataCommonLiteral(BaseModel):
    """
    
    """ # noqa: E501
    data_type: Optional[StrictStr] = None
    bool_value: Optional[StrictBool] = None
    int_value: Optional[StrictInt] = None
    number_value: Optional[Union[StrictFloat, StrictInt]] = None
    string_value: Optional[StrictStr] = None
    list_value: Optional[List[StrictStr]] = None
    datetime_value: Optional[datetime] = None
    date_value: Optional[StrictStr] = None
    bigint_value: Optional[StrictInt] = None
    decimal_value: Optional[StrictStr] = None
    __properties: ClassVar[List[str]] = ["data_type", "bool_value", "int_value", "number_value", "string_value", "list_value", "datetime_value", "date_value", "bigint_value", "decimal_value"]

    @field_validator('data_type')
    def data_type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['DATA_TYPE_UNSPECIFIED', 'BOOL', 'INT', 'NUMBER', 'STRING', 'LIST', 'DATETIME', 'DATE', 'BIGINT', 'DECIMAL']):
            raise ValueError("must be one of enum values ('DATA_TYPE_UNSPECIFIED', 'BOOL', 'INT', 'NUMBER', 'STRING', 'LIST', 'DATETIME', 'DATE', 'BIGINT', 'DECIMAL')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataCommonLiteral from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataCommonLiteral from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "data_type": obj.get("data_type"),
            "bool_value": obj.get("bool_value"),
            "int_value": obj.get("int_value"),
            "number_value": obj.get("number_value"),
            "string_value": obj.get("string_value"),
            "list_value": obj.get("list_value"),
            "datetime_value": obj.get("datetime_value"),
            "date_value": obj.get("date_value"),
            "bigint_value": obj.get("bigint_value"),
            "decimal_value": obj.get("decimal_value")
        })
        return _obj


