# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_user_tag_dir_define import SensorsdataAnalyticsV1UserTagDirDefine
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataAnalyticsV1UserTagDirResponse(BaseModel):
    """
    
    """ # noqa: E501
    user_tags: Optional[List[SensorsdataAnalyticsV1UserTagDirDefine]] = Field(default=None, description="用户标签列表")
    __properties: ClassVar[List[str]] = ["user_tags"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataAnalyticsV1UserTagDirResponse from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in user_tags (list)
        _items = []
        if self.user_tags:
            for _item_user_tags in self.user_tags:
                if _item_user_tags:
                    _items.append(_item_user_tags.to_dict())
            _dict['user_tags'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataAnalyticsV1UserTagDirResponse from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "user_tags": [SensorsdataAnalyticsV1UserTagDirDefine.from_dict(_item) for _item in obj["user_tags"]] if obj.get("user_tags") is not None else None
        })
        return _obj


