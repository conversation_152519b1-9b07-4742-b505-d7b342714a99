# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_error_cause import SensorsdataCommonErrorCause
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_error_context import SensorsdataCommonErrorContext
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataCommonErrorInfo(BaseModel):
    """
    SensorsdataCommonErrorInfo
    """ # noqa: E501
    code: Optional[StrictStr] = Field(default=None, description="具体的错误码")
    description: Optional[StrictStr] = Field(default=None, description="错误描述")
    system_response: Optional[StrictStr] = Field(default=None, description="致错的可能原因列表")
    error_causes: Optional[List[SensorsdataCommonErrorCause]] = None
    context: Optional[SensorsdataCommonErrorContext] = None
    __properties: ClassVar[List[str]] = ["code", "description", "system_response", "error_causes", "context"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataCommonErrorInfo from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in error_causes (list)
        _items = []
        if self.error_causes:
            for _item_error_causes in self.error_causes:
                if _item_error_causes:
                    _items.append(_item_error_causes.to_dict())
            _dict['error_causes'] = _items
        # override the default output from pydantic by calling `to_dict()` of context
        if self.context:
            _dict['context'] = self.context.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataCommonErrorInfo from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "code": obj.get("code"),
            "description": obj.get("description"),
            "system_response": obj.get("system_response"),
            "error_causes": [SensorsdataCommonErrorCause.from_dict(_item) for _item in obj["error_causes"]] if obj.get("error_causes") is not None else None,
            "context": SensorsdataCommonErrorContext.from_dict(obj["context"]) if obj.get("context") is not None else None
        })
        return _obj


