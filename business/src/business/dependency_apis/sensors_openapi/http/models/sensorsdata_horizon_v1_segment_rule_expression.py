# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictBool, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_custom_segment_rule_expression import SensorsdataHorizonV1CustomSegmentRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_eql_segment_rule_expression import SensorsdataHorizonV1EqlSegmentRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_event_sequence_segment_rule_expression import SensorsdataHorizonV1EventSequenceSegmentRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_loading_segment_rule_expression import SensorsdataHorizonV1LoadingSegmentRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_sql_segment_rule_expression import SensorsdataHorizonV1SqlSegmentRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tailor import SensorsdataHorizonV1Tailor
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1SegmentRuleExpression(BaseModel):
    """
    
    """ # noqa: E501
    type: Optional[StrictStr] = None
    sql_segment_rule: Optional[SensorsdataHorizonV1SqlSegmentRuleExpression] = None
    eql_segment_rule: Optional[SensorsdataHorizonV1EqlSegmentRuleExpression] = None
    loading_segment_rule: Optional[SensorsdataHorizonV1LoadingSegmentRuleExpression] = None
    custom_rule: Optional[SensorsdataHorizonV1CustomSegmentRuleExpression] = None
    event_sequence_rule: Optional[SensorsdataHorizonV1EventSequenceSegmentRuleExpression] = None
    group_expression_rule: Optional[SensorsdataHorizonV1GroupExpressionSegmentRuleExpression] = None
    virtual: Optional[StrictBool] = None
    tailor: Optional[SensorsdataHorizonV1Tailor] = None
    entity_set_rules: Optional[List[SensorsdataHorizonV1SegmentRule]] = None
    time_zone: Optional[StrictStr] = None
    version: Optional[StrictInt] = None
    __properties: ClassVar[List[str]] = ["type", "sql_segment_rule", "eql_segment_rule", "loading_segment_rule", "custom_rule", "event_sequence_rule", "group_expression_rule", "virtual", "tailor", "entity_set_rules", "time_zone", "version"]

    @field_validator('type')
    def type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['SIMPLE_USER_GROUP_TYPE_UNSPECIFIED', 'SQL_BASED', 'EQL_BASED', 'LOADING_BASED', 'CUSTOM_BASED', 'EVENT_SEQUENCE_BASED', 'GROUP_EXPRESSION_BASED', 'ALL']):
            raise ValueError("must be one of enum values ('SIMPLE_USER_GROUP_TYPE_UNSPECIFIED', 'SQL_BASED', 'EQL_BASED', 'LOADING_BASED', 'CUSTOM_BASED', 'EVENT_SEQUENCE_BASED', 'GROUP_EXPRESSION_BASED', 'ALL')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1SegmentRuleExpression from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of sql_segment_rule
        if self.sql_segment_rule:
            _dict['sql_segment_rule'] = self.sql_segment_rule.to_dict()
        # override the default output from pydantic by calling `to_dict()` of eql_segment_rule
        if self.eql_segment_rule:
            _dict['eql_segment_rule'] = self.eql_segment_rule.to_dict()
        # override the default output from pydantic by calling `to_dict()` of loading_segment_rule
        if self.loading_segment_rule:
            _dict['loading_segment_rule'] = self.loading_segment_rule.to_dict()
        # override the default output from pydantic by calling `to_dict()` of custom_rule
        if self.custom_rule:
            _dict['custom_rule'] = self.custom_rule.to_dict()
        # override the default output from pydantic by calling `to_dict()` of event_sequence_rule
        if self.event_sequence_rule:
            _dict['event_sequence_rule'] = self.event_sequence_rule.to_dict()
        # override the default output from pydantic by calling `to_dict()` of group_expression_rule
        if self.group_expression_rule:
            _dict['group_expression_rule'] = self.group_expression_rule.to_dict()
        # override the default output from pydantic by calling `to_dict()` of tailor
        if self.tailor:
            _dict['tailor'] = self.tailor.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in entity_set_rules (list)
        _items = []
        if self.entity_set_rules:
            for _item_entity_set_rules in self.entity_set_rules:
                if _item_entity_set_rules:
                    _items.append(_item_entity_set_rules.to_dict())
            _dict['entity_set_rules'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1SegmentRuleExpression from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "type": obj.get("type"),
            "sql_segment_rule": SensorsdataHorizonV1SqlSegmentRuleExpression.from_dict(obj["sql_segment_rule"]) if obj.get("sql_segment_rule") is not None else None,
            "eql_segment_rule": SensorsdataHorizonV1EqlSegmentRuleExpression.from_dict(obj["eql_segment_rule"]) if obj.get("eql_segment_rule") is not None else None,
            "loading_segment_rule": SensorsdataHorizonV1LoadingSegmentRuleExpression.from_dict(obj["loading_segment_rule"]) if obj.get("loading_segment_rule") is not None else None,
            "custom_rule": SensorsdataHorizonV1CustomSegmentRuleExpression.from_dict(obj["custom_rule"]) if obj.get("custom_rule") is not None else None,
            "event_sequence_rule": SensorsdataHorizonV1EventSequenceSegmentRuleExpression.from_dict(obj["event_sequence_rule"]) if obj.get("event_sequence_rule") is not None else None,
            "group_expression_rule": SensorsdataHorizonV1GroupExpressionSegmentRuleExpression.from_dict(obj["group_expression_rule"]) if obj.get("group_expression_rule") is not None else None,
            "virtual": obj.get("virtual"),
            "tailor": SensorsdataHorizonV1Tailor.from_dict(obj["tailor"]) if obj.get("tailor") is not None else None,
            "entity_set_rules": [SensorsdataHorizonV1SegmentRule.from_dict(_item) for _item in obj["entity_set_rules"]] if obj.get("entity_set_rules") is not None else None,
            "time_zone": obj.get("time_zone"),
            "version": obj.get("version")
        })
        return _obj

from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_group_expression_segment_rule_expression import SensorsdataHorizonV1GroupExpressionSegmentRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_rule import SensorsdataHorizonV1SegmentRule
# TODO: Rewrite to not use raise_errors
SensorsdataHorizonV1SegmentRuleExpression.model_rebuild(raise_errors=False)

