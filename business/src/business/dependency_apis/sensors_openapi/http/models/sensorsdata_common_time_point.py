# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_time_point_relative_time_point import SensorsdataCommonTimePointRelativeTimePoint
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataCommonTimePoint(BaseModel):
    """
    
    """ # noqa: E501
    type: Optional[StrictStr] = None
    trunc_unit: Optional[StrictStr] = None
    static_time: Optional[datetime] = None
    relative_time: Optional[SensorsdataCommonTimePointRelativeTimePoint] = None
    expression: Optional[StrictStr] = None
    __properties: ClassVar[List[str]] = ["type", "trunc_unit", "static_time", "relative_time", "expression"]

    @field_validator('type')
    def type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['TIME_POINT_TYPE_UNSPECIFIED', 'STATIC', 'RELATIVE', 'EXPRESSION']):
            raise ValueError("must be one of enum values ('TIME_POINT_TYPE_UNSPECIFIED', 'STATIC', 'RELATIVE', 'EXPRESSION')")
        return value

    @field_validator('trunc_unit')
    def trunc_unit_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['TRUNC_UNIT_UNSPECIFIED', 'TRUNC_HOUR', 'TRUNC_DAY', 'TRUNC_WEEK', 'TRUNC_MONTH', 'TRUNC_YEAR', 'TRUNC_BIGBANG']):
            raise ValueError("must be one of enum values ('TRUNC_UNIT_UNSPECIFIED', 'TRUNC_HOUR', 'TRUNC_DAY', 'TRUNC_WEEK', 'TRUNC_MONTH', 'TRUNC_YEAR', 'TRUNC_BIGBANG')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataCommonTimePoint from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of relative_time
        if self.relative_time:
            _dict['relative_time'] = self.relative_time.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataCommonTimePoint from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "type": obj.get("type"),
            "trunc_unit": obj.get("trunc_unit"),
            "static_time": obj.get("static_time"),
            "relative_time": SensorsdataCommonTimePointRelativeTimePoint.from_dict(obj["relative_time"]) if obj.get("relative_time") is not None else None,
            "expression": obj.get("expression")
        })
        return _obj


