# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_literal import SensorsdataCommonLiteral
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_rule_expression import SensorsdataHorizonV1TagRuleExpression
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1EvaluateTagRequestEvaluateTagParam(BaseModel):
    """
    
    """ # noqa: E501
    tag_definition_name: StrictStr
    tag_task_params: Optional[Dict[str, SensorsdataCommonLiteral]] = None
    tag_rule: Optional[SensorsdataHorizonV1TagRuleExpression] = None
    base_time: datetime
    __properties: ClassVar[List[str]] = ["tag_definition_name", "tag_task_params", "tag_rule", "base_time"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1EvaluateTagRequestEvaluateTagParam from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each value in tag_task_params (dict)
        _field_dict = {}
        if self.tag_task_params:
            for _key_tag_task_params in self.tag_task_params:
                if self.tag_task_params[_key_tag_task_params]:
                    _field_dict[_key_tag_task_params] = self.tag_task_params[_key_tag_task_params].to_dict()
            _dict['tag_task_params'] = _field_dict
        # override the default output from pydantic by calling `to_dict()` of tag_rule
        if self.tag_rule:
            _dict['tag_rule'] = self.tag_rule.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1EvaluateTagRequestEvaluateTagParam from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "tag_definition_name": obj.get("tag_definition_name"),
            "tag_task_params": dict(
                (_k, SensorsdataCommonLiteral.from_dict(_v))
                for _k, _v in obj["tag_task_params"].items()
            )
            if obj.get("tag_task_params") is not None
            else None,
            "tag_rule": SensorsdataHorizonV1TagRuleExpression.from_dict(obj["tag_rule"]) if obj.get("tag_rule") is not None else None,
            "base_time": obj.get("base_time")
        })
        return _obj


