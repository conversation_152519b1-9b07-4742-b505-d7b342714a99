# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, StrictBool, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_literal import SensorsdataCommonLiteral
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_trigger import SensorsdataCommonTrigger
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_alarm_config import SensorsdataHorizonV1SegmentAlarmConfig
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_definition_source import SensorsdataHorizonV1SegmentDefinitionSource
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_notify_params import SensorsdataHorizonV1SegmentNotifyParams
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_rule_ref import SensorsdataHorizonV1SegmentRuleRef
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_storage_settings import SensorsdataHorizonV1SegmentStorageSettings
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1SegmentDefinition(BaseModel):
    """
    
    """ # noqa: E501
    entity_name: StrictStr
    name: StrictStr
    display_name: Optional[StrictStr] = None
    comment: Optional[StrictStr] = None
    segment_rule_ref: Optional[SensorsdataHorizonV1SegmentRuleRef] = None
    trigger: Optional[SensorsdataCommonTrigger] = None
    custom_params: Optional[Dict[str, SensorsdataCommonLiteral]] = None
    visible: Optional[StrictBool] = None
    managed: Optional[StrictBool] = None
    status: Optional[StrictStr] = None
    created_by: Optional[StrictStr] = None
    create_type: Optional[StrictStr] = None
    storage_settings: Optional[SensorsdataHorizonV1SegmentStorageSettings] = None
    alarm_config: Optional[SensorsdataHorizonV1SegmentAlarmConfig] = None
    creator_id: Optional[StrictStr] = None
    modifier_id: Optional[StrictStr] = None
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    notify_params: Optional[SensorsdataHorizonV1SegmentNotifyParams] = None
    delete_time: Optional[datetime] = None
    delete_account_id: Optional[StrictStr] = None
    source: Optional[SensorsdataHorizonV1SegmentDefinitionSource] = None
    legacy_id: Optional[StrictInt] = None
    __properties: ClassVar[List[str]] = ["entity_name", "name", "display_name", "comment", "segment_rule_ref", "trigger", "custom_params", "visible", "managed", "status", "created_by", "create_type", "storage_settings", "alarm_config", "creator_id", "modifier_id", "create_time", "update_time", "notify_params", "delete_time", "delete_account_id", "source", "legacy_id"]

    @field_validator('status')
    def status_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['STATUS_UNSPECIFIED', 'ACTIVE', 'SUSPENDED', 'DELETE_REQUESTED']):
            raise ValueError("must be one of enum values ('STATUS_UNSPECIFIED', 'ACTIVE', 'SUSPENDED', 'DELETE_REQUESTED')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1SegmentDefinition from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of segment_rule_ref
        if self.segment_rule_ref:
            _dict['segment_rule_ref'] = self.segment_rule_ref.to_dict()
        # override the default output from pydantic by calling `to_dict()` of trigger
        if self.trigger:
            _dict['trigger'] = self.trigger.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each value in custom_params (dict)
        _field_dict = {}
        if self.custom_params:
            for _key_custom_params in self.custom_params:
                if self.custom_params[_key_custom_params]:
                    _field_dict[_key_custom_params] = self.custom_params[_key_custom_params].to_dict()
            _dict['custom_params'] = _field_dict
        # override the default output from pydantic by calling `to_dict()` of storage_settings
        if self.storage_settings:
            _dict['storage_settings'] = self.storage_settings.to_dict()
        # override the default output from pydantic by calling `to_dict()` of alarm_config
        if self.alarm_config:
            _dict['alarm_config'] = self.alarm_config.to_dict()
        # override the default output from pydantic by calling `to_dict()` of notify_params
        if self.notify_params:
            _dict['notify_params'] = self.notify_params.to_dict()
        # override the default output from pydantic by calling `to_dict()` of source
        if self.source:
            _dict['source'] = self.source.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1SegmentDefinition from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "entity_name": obj.get("entity_name"),
            "name": obj.get("name"),
            "display_name": obj.get("display_name"),
            "comment": obj.get("comment"),
            "segment_rule_ref": SensorsdataHorizonV1SegmentRuleRef.from_dict(obj["segment_rule_ref"]) if obj.get("segment_rule_ref") is not None else None,
            "trigger": SensorsdataCommonTrigger.from_dict(obj["trigger"]) if obj.get("trigger") is not None else None,
            "custom_params": dict(
                (_k, SensorsdataCommonLiteral.from_dict(_v))
                for _k, _v in obj["custom_params"].items()
            )
            if obj.get("custom_params") is not None
            else None,
            "visible": obj.get("visible"),
            "managed": obj.get("managed"),
            "status": obj.get("status"),
            "created_by": obj.get("created_by"),
            "create_type": obj.get("create_type"),
            "storage_settings": SensorsdataHorizonV1SegmentStorageSettings.from_dict(obj["storage_settings"]) if obj.get("storage_settings") is not None else None,
            "alarm_config": SensorsdataHorizonV1SegmentAlarmConfig.from_dict(obj["alarm_config"]) if obj.get("alarm_config") is not None else None,
            "creator_id": obj.get("creator_id"),
            "modifier_id": obj.get("modifier_id"),
            "create_time": obj.get("create_time"),
            "update_time": obj.get("update_time"),
            "notify_params": SensorsdataHorizonV1SegmentNotifyParams.from_dict(obj["notify_params"]) if obj.get("notify_params") is not None else None,
            "delete_time": obj.get("delete_time"),
            "delete_account_id": obj.get("delete_account_id"),
            "source": SensorsdataHorizonV1SegmentDefinitionSource.from_dict(obj["source"]) if obj.get("source") is not None else None,
            "legacy_id": obj.get("legacy_id")
        })
        return _obj


