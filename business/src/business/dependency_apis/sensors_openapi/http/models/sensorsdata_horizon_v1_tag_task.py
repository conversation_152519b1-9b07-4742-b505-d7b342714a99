# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_literal import SensorsdataCommonLiteral
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_rule_expression import SensorsdataHorizonV1TagRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_task_report import SensorsdataHorizonV1TagTaskReport
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_task_tag_task_failure_detail import SensorsdataHorizonV1TagTaskTagTaskFailureDetail
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1TagTask(BaseModel):
    """
    
    """ # noqa: E501
    id: Optional[StrictInt] = None
    status: Optional[StrictStr] = None
    entity_name: StrictStr
    tag_definition_name: Optional[StrictStr] = None
    rule: Optional[SensorsdataHorizonV1TagRuleExpression] = None
    rule_version: Optional[StrictInt] = None
    base_time: Optional[datetime] = None
    tag_task_params: Optional[Dict[str, SensorsdataCommonLiteral]] = None
    failure_detail: Optional[SensorsdataHorizonV1TagTaskTagTaskFailureDetail] = None
    tag_task_report: Optional[SensorsdataHorizonV1TagTaskReport] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    failed_times: Optional[StrictInt] = None
    creator_id: Optional[StrictStr] = None
    modifier_id: Optional[StrictStr] = None
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    __properties: ClassVar[List[str]] = ["id", "status", "entity_name", "tag_definition_name", "rule", "rule_version", "base_time", "tag_task_params", "failure_detail", "tag_task_report", "start_time", "end_time", "failed_times", "creator_id", "modifier_id", "create_time", "update_time"]

    @field_validator('status')
    def status_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['TAG_TASK_STATUS_UNSPECIFIED', 'TAG_PREPARING', 'TAG_WAITING_DATA', 'TAG_COMPUTING', 'TAG_SUCCESS', 'TAG_FAILED', 'TAG_TASK_CANCELLED']):
            raise ValueError("must be one of enum values ('TAG_TASK_STATUS_UNSPECIFIED', 'TAG_PREPARING', 'TAG_WAITING_DATA', 'TAG_COMPUTING', 'TAG_SUCCESS', 'TAG_FAILED', 'TAG_TASK_CANCELLED')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1TagTask from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of rule
        if self.rule:
            _dict['rule'] = self.rule.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each value in tag_task_params (dict)
        _field_dict = {}
        if self.tag_task_params:
            for _key_tag_task_params in self.tag_task_params:
                if self.tag_task_params[_key_tag_task_params]:
                    _field_dict[_key_tag_task_params] = self.tag_task_params[_key_tag_task_params].to_dict()
            _dict['tag_task_params'] = _field_dict
        # override the default output from pydantic by calling `to_dict()` of failure_detail
        if self.failure_detail:
            _dict['failure_detail'] = self.failure_detail.to_dict()
        # override the default output from pydantic by calling `to_dict()` of tag_task_report
        if self.tag_task_report:
            _dict['tag_task_report'] = self.tag_task_report.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1TagTask from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "id": obj.get("id"),
            "status": obj.get("status"),
            "entity_name": obj.get("entity_name"),
            "tag_definition_name": obj.get("tag_definition_name"),
            "rule": SensorsdataHorizonV1TagRuleExpression.from_dict(obj["rule"]) if obj.get("rule") is not None else None,
            "rule_version": obj.get("rule_version"),
            "base_time": obj.get("base_time"),
            "tag_task_params": dict(
                (_k, SensorsdataCommonLiteral.from_dict(_v))
                for _k, _v in obj["tag_task_params"].items()
            )
            if obj.get("tag_task_params") is not None
            else None,
            "failure_detail": SensorsdataHorizonV1TagTaskTagTaskFailureDetail.from_dict(obj["failure_detail"]) if obj.get("failure_detail") is not None else None,
            "tag_task_report": SensorsdataHorizonV1TagTaskReport.from_dict(obj["tag_task_report"]) if obj.get("tag_task_report") is not None else None,
            "start_time": obj.get("start_time"),
            "end_time": obj.get("end_time"),
            "failed_times": obj.get("failed_times"),
            "creator_id": obj.get("creator_id"),
            "modifier_id": obj.get("modifier_id"),
            "create_time": obj.get("create_time"),
            "update_time": obj.get("update_time")
        })
        return _obj


