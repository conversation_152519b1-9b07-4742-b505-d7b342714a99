# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1ListTagPartitionsRequest(BaseModel):
    """
    
    """ # noqa: E501
    project_id: Optional[StrictInt] = None
    entity_name: Optional[StrictStr] = None
    tag_name: Optional[StrictStr] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    base_time_list: Optional[List[datetime]] = None
    field_mask: Optional[StrictStr] = None
    limit: Optional[StrictInt] = None
    __properties: ClassVar[List[str]] = ["project_id", "entity_name", "tag_name", "start_time", "end_time", "base_time_list", "field_mask", "limit"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1ListTagPartitionsRequest from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1ListTagPartitionsRequest from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "project_id": obj.get("project_id"),
            "entity_name": obj.get("entity_name"),
            "tag_name": obj.get("tag_name"),
            "start_time": obj.get("start_time"),
            "end_time": obj.get("end_time"),
            "base_time_list": obj.get("base_time_list"),
            "field_mask": obj.get("field_mask"),
            "limit": obj.get("limit")
        })
        return _obj


