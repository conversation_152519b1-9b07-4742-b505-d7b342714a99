# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_rfm_based_rule_expression_rfm_factor_rfm_factor_group_rule import SensorsdataHorizonV1RfmBasedRuleExpressionRfmFactorRfmFactorGroupRule
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1RfmBasedRuleExpressionRfmFactor(BaseModel):
    """
    
    """ # noqa: E501
    factor_eql: Optional[StrictStr] = None
    factor_group_rules: Optional[List[SensorsdataHorizonV1RfmBasedRuleExpressionRfmFactorRfmFactorGroupRule]] = None
    __properties: ClassVar[List[str]] = ["factor_eql", "factor_group_rules"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1RfmBasedRuleExpressionRfmFactor from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in factor_group_rules (list)
        _items = []
        if self.factor_group_rules:
            for _item_factor_group_rules in self.factor_group_rules:
                if _item_factor_group_rules:
                    _items.append(_item_factor_group_rules.to_dict())
            _dict['factor_group_rules'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1RfmBasedRuleExpressionRfmFactor from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "factor_eql": obj.get("factor_eql"),
            "factor_group_rules": [SensorsdataHorizonV1RfmBasedRuleExpressionRfmFactorRfmFactorGroupRule.from_dict(_item) for _item in obj["factor_group_rules"]] if obj.get("factor_group_rules") is not None else None
        })
        return _obj


