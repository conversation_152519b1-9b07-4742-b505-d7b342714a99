# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, StrictBool, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_literal import SensorsdataCommonLiteral
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_trigger import SensorsdataCommonTrigger
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_alarm_config import SensorsdataHorizonV1TagAlarmConfig
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_definition_source import SensorsdataHorizonV1TagDefinitionSource
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_notify_params import SensorsdataHorizonV1TagNotifyParams
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_post_action import SensorsdataHorizonV1TagPostAction
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_rule_expression import SensorsdataHorizonV1TagRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_storage_settings import SensorsdataHorizonV1TagStorageSettings
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1TagDefinition(BaseModel):
    """
    
    """ # noqa: E501
    entity_name: StrictStr
    name: StrictStr
    display_name: Optional[StrictStr] = None
    comment: Optional[StrictStr] = None
    data_type: Optional[StrictStr] = None
    folder: Optional[StrictStr] = None
    expression: Optional[SensorsdataHorizonV1TagRuleExpression] = None
    current_rule_available: Optional[StrictBool] = None
    rule_version: Optional[StrictInt] = None
    visible: Optional[StrictBool] = None
    managed: Optional[StrictBool] = None
    trigger: Optional[SensorsdataCommonTrigger] = None
    custom_params: Optional[Dict[str, SensorsdataCommonLiteral]] = None
    status: Optional[StrictStr] = None
    created_by: Optional[StrictStr] = None
    create_type: StrictStr
    storage_settings: Optional[SensorsdataHorizonV1TagStorageSettings] = None
    tag_post_actions: Optional[List[SensorsdataHorizonV1TagPostAction]] = None
    alarm_config: Optional[SensorsdataHorizonV1TagAlarmConfig] = None
    creator_id: Optional[StrictStr] = None
    modifier_id: Optional[StrictStr] = None
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    delete_time: Optional[datetime] = None
    delete_account_id: Optional[StrictStr] = None
    notify_params: Optional[SensorsdataHorizonV1TagNotifyParams] = None
    source: Optional[SensorsdataHorizonV1TagDefinitionSource] = None
    __properties: ClassVar[List[str]] = ["entity_name", "name", "display_name", "comment", "data_type", "folder", "expression", "current_rule_available", "rule_version", "visible", "managed", "trigger", "custom_params", "status", "created_by", "create_type", "storage_settings", "tag_post_actions", "alarm_config", "creator_id", "modifier_id", "create_time", "update_time", "delete_time", "delete_account_id", "notify_params", "source"]

    @field_validator('data_type')
    def data_type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['DATA_TYPE_UNSPECIFIED', 'BOOL', 'INT', 'NUMBER', 'STRING', 'LIST', 'DATETIME', 'DATE', 'BIGINT', 'DECIMAL']):
            raise ValueError("must be one of enum values ('DATA_TYPE_UNSPECIFIED', 'BOOL', 'INT', 'NUMBER', 'STRING', 'LIST', 'DATETIME', 'DATE', 'BIGINT', 'DECIMAL')")
        return value

    @field_validator('status')
    def status_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['TAG_STATUS_UNSPECIFIED', 'TAG_NEW', 'TAG_ACTIVE', 'TAG_SUSPENDED', 'TAG_DELETE_REQUESTED']):
            raise ValueError("must be one of enum values ('TAG_STATUS_UNSPECIFIED', 'TAG_NEW', 'TAG_ACTIVE', 'TAG_SUSPENDED', 'TAG_DELETE_REQUESTED')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1TagDefinition from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of expression
        if self.expression:
            _dict['expression'] = self.expression.to_dict()
        # override the default output from pydantic by calling `to_dict()` of trigger
        if self.trigger:
            _dict['trigger'] = self.trigger.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each value in custom_params (dict)
        _field_dict = {}
        if self.custom_params:
            for _key_custom_params in self.custom_params:
                if self.custom_params[_key_custom_params]:
                    _field_dict[_key_custom_params] = self.custom_params[_key_custom_params].to_dict()
            _dict['custom_params'] = _field_dict
        # override the default output from pydantic by calling `to_dict()` of storage_settings
        if self.storage_settings:
            _dict['storage_settings'] = self.storage_settings.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in tag_post_actions (list)
        _items = []
        if self.tag_post_actions:
            for _item_tag_post_actions in self.tag_post_actions:
                if _item_tag_post_actions:
                    _items.append(_item_tag_post_actions.to_dict())
            _dict['tag_post_actions'] = _items
        # override the default output from pydantic by calling `to_dict()` of alarm_config
        if self.alarm_config:
            _dict['alarm_config'] = self.alarm_config.to_dict()
        # override the default output from pydantic by calling `to_dict()` of notify_params
        if self.notify_params:
            _dict['notify_params'] = self.notify_params.to_dict()
        # override the default output from pydantic by calling `to_dict()` of source
        if self.source:
            _dict['source'] = self.source.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1TagDefinition from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "entity_name": obj.get("entity_name"),
            "name": obj.get("name"),
            "display_name": obj.get("display_name"),
            "comment": obj.get("comment"),
            "data_type": obj.get("data_type"),
            "folder": obj.get("folder"),
            "expression": SensorsdataHorizonV1TagRuleExpression.from_dict(obj["expression"]) if obj.get("expression") is not None else None,
            "current_rule_available": obj.get("current_rule_available"),
            "rule_version": obj.get("rule_version"),
            "visible": obj.get("visible"),
            "managed": obj.get("managed"),
            "trigger": SensorsdataCommonTrigger.from_dict(obj["trigger"]) if obj.get("trigger") is not None else None,
            "custom_params": dict(
                (_k, SensorsdataCommonLiteral.from_dict(_v))
                for _k, _v in obj["custom_params"].items()
            )
            if obj.get("custom_params") is not None
            else None,
            "status": obj.get("status"),
            "created_by": obj.get("created_by"),
            "create_type": obj.get("create_type"),
            "storage_settings": SensorsdataHorizonV1TagStorageSettings.from_dict(obj["storage_settings"]) if obj.get("storage_settings") is not None else None,
            "tag_post_actions": [SensorsdataHorizonV1TagPostAction.from_dict(_item) for _item in obj["tag_post_actions"]] if obj.get("tag_post_actions") is not None else None,
            "alarm_config": SensorsdataHorizonV1TagAlarmConfig.from_dict(obj["alarm_config"]) if obj.get("alarm_config") is not None else None,
            "creator_id": obj.get("creator_id"),
            "modifier_id": obj.get("modifier_id"),
            "create_time": obj.get("create_time"),
            "update_time": obj.get("update_time"),
            "delete_time": obj.get("delete_time"),
            "delete_account_id": obj.get("delete_account_id"),
            "notify_params": SensorsdataHorizonV1TagNotifyParams.from_dict(obj["notify_params"]) if obj.get("notify_params") is not None else None,
            "source": SensorsdataHorizonV1TagDefinitionSource.from_dict(obj["source"]) if obj.get("source") is not None else None
        })
        return _obj


