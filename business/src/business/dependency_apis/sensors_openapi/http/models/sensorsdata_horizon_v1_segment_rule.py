# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, StrictBool, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_literal import SensorsdataCommonLiteral
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1SegmentRule(BaseModel):
    """
    
    """ # noqa: E501
    entity_name: StrictStr
    name: Optional[StrictStr] = None
    display_name: Optional[StrictStr] = None
    comment: Optional[StrictStr] = None
    id: Optional[StrictInt] = None
    status: Optional[StrictStr] = None
    expression: SensorsdataHorizonV1SegmentRuleExpression
    version: Optional[StrictStr] = None
    rule_available: Optional[StrictBool] = None
    created_by: Optional[StrictStr] = None
    custom_params: Optional[Dict[str, SensorsdataCommonLiteral]] = None
    creator_id: Optional[StrictStr] = None
    modifier_id: Optional[StrictStr] = None
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    type: Optional[StrictStr] = None
    read_only: Optional[StrictBool] = None
    delete_flag: Optional[StrictInt] = None
    __properties: ClassVar[List[str]] = ["entity_name", "name", "display_name", "comment", "id", "status", "expression", "version", "rule_available", "created_by", "custom_params", "creator_id", "modifier_id", "create_time", "update_time", "type", "read_only", "delete_flag"]

    @field_validator('status')
    def status_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['STATUS_UNSPECIFIED', 'ACTIVE', 'INACTIVE', 'DELETE_REQUESTED']):
            raise ValueError("must be one of enum values ('STATUS_UNSPECIFIED', 'ACTIVE', 'INACTIVE', 'DELETE_REQUESTED')")
        return value

    @field_validator('type')
    def type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['TYPE_UNSPECIFIED', 'SEGMENT_DEFINITION_RULE', 'ENTITY_SET_RULE']):
            raise ValueError("must be one of enum values ('TYPE_UNSPECIFIED', 'SEGMENT_DEFINITION_RULE', 'ENTITY_SET_RULE')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1SegmentRule from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of expression
        if self.expression:
            _dict['expression'] = self.expression.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each value in custom_params (dict)
        _field_dict = {}
        if self.custom_params:
            for _key_custom_params in self.custom_params:
                if self.custom_params[_key_custom_params]:
                    _field_dict[_key_custom_params] = self.custom_params[_key_custom_params].to_dict()
            _dict['custom_params'] = _field_dict
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1SegmentRule from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "entity_name": obj.get("entity_name"),
            "name": obj.get("name"),
            "display_name": obj.get("display_name"),
            "comment": obj.get("comment"),
            "id": obj.get("id"),
            "status": obj.get("status"),
            "expression": SensorsdataHorizonV1SegmentRuleExpression.from_dict(obj["expression"]) if obj.get("expression") is not None else None,
            "version": obj.get("version"),
            "rule_available": obj.get("rule_available"),
            "created_by": obj.get("created_by"),
            "custom_params": dict(
                (_k, SensorsdataCommonLiteral.from_dict(_v))
                for _k, _v in obj["custom_params"].items()
            )
            if obj.get("custom_params") is not None
            else None,
            "creator_id": obj.get("creator_id"),
            "modifier_id": obj.get("modifier_id"),
            "create_time": obj.get("create_time"),
            "update_time": obj.get("update_time"),
            "type": obj.get("type"),
            "read_only": obj.get("read_only"),
            "delete_flag": obj.get("delete_flag")
        })
        return _obj

from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_rule_expression import SensorsdataHorizonV1SegmentRuleExpression
# TODO: Rewrite to not use raise_errors
SensorsdataHorizonV1SegmentRule.model_rebuild(raise_errors=False)

