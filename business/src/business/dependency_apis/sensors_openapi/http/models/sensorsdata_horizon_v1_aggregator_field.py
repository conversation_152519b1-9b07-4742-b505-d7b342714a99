# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1AggregatorField(BaseModel):
    """
    
    """ # noqa: E501
    aggregator: Optional[StrictStr] = None
    var_field: Optional[StrictStr] = Field(default=None, alias="field")
    __properties: ClassVar[List[str]] = ["aggregator", "field"]

    @field_validator('aggregator')
    def aggregator_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['AGGREGATOR_TYPE_UNSPECIFIED', 'COUNT', 'AVG', 'MAX', 'MIN', 'SUM', 'UNIQUE_COUNT', 'UNIQUE_AVG', 'BOUNCE_RATE', 'EXIT_RATE', 'UNIQUE_COUNT_APPROX', 'COUNT_PERCENT', 'RANK_ASC', 'RANK_DESC', 'PERCENT_RANK_ASC', 'PERCENT_RANK_DESC', 'SESSION_COUNT', 'FIRST_TIME', 'LAST_TIME', 'FIRST_TIME_INTERVAL', 'LAST_TIME_INTERVAL', 'GROUP_CONCAT', 'UNIQUE_COUNT_BITMAP', 'UNIQUE_COUNT_APPROX_ORDINARY', 'COUNT_DISTINCT', 'LTV', 'LTV_AVG', 'COUNT_RANK', 'AVG_RANK', 'MAX_RANK', 'MIN_RANK', 'SUM_RANK', 'COUNT_RANK_ASC', 'COUNT_RANK_DESC', 'COUNT_PERCENT_RANK_ASC', 'COUNT_PERCENT_RANK_DESC', 'AVG_RANK_ASC', 'AVG_RANK_DESC', 'AVG_PERCENT_RANK_ASC', 'AVG_PERCENT_RANK_DESC', 'MAX_RANK_ASC', 'MAX_RANK_DESC', 'MAX_PERCENT_RANK_ASC', 'MAX_PERCENT_RANK_DESC', 'MIN_RANK_ASC', 'MIN_RANK_DESC', 'MIN_PERCENT_RANK_ASC', 'MIN_PERCENT_RANK_DESC', 'SUM_RANK_ASC', 'SUM_RANK_DESC', 'SUM_PERCENT_RANK_ASC', 'SUM_PERCENT_RANK_DESC']):
            raise ValueError("must be one of enum values ('AGGREGATOR_TYPE_UNSPECIFIED', 'COUNT', 'AVG', 'MAX', 'MIN', 'SUM', 'UNIQUE_COUNT', 'UNIQUE_AVG', 'BOUNCE_RATE', 'EXIT_RATE', 'UNIQUE_COUNT_APPROX', 'COUNT_PERCENT', 'RANK_ASC', 'RANK_DESC', 'PERCENT_RANK_ASC', 'PERCENT_RANK_DESC', 'SESSION_COUNT', 'FIRST_TIME', 'LAST_TIME', 'FIRST_TIME_INTERVAL', 'LAST_TIME_INTERVAL', 'GROUP_CONCAT', 'UNIQUE_COUNT_BITMAP', 'UNIQUE_COUNT_APPROX_ORDINARY', 'COUNT_DISTINCT', 'LTV', 'LTV_AVG', 'COUNT_RANK', 'AVG_RANK', 'MAX_RANK', 'MIN_RANK', 'SUM_RANK', 'COUNT_RANK_ASC', 'COUNT_RANK_DESC', 'COUNT_PERCENT_RANK_ASC', 'COUNT_PERCENT_RANK_DESC', 'AVG_RANK_ASC', 'AVG_RANK_DESC', 'AVG_PERCENT_RANK_ASC', 'AVG_PERCENT_RANK_DESC', 'MAX_RANK_ASC', 'MAX_RANK_DESC', 'MAX_PERCENT_RANK_ASC', 'MAX_PERCENT_RANK_DESC', 'MIN_RANK_ASC', 'MIN_RANK_DESC', 'MIN_PERCENT_RANK_ASC', 'MIN_PERCENT_RANK_DESC', 'SUM_RANK_ASC', 'SUM_RANK_DESC', 'SUM_PERCENT_RANK_ASC', 'SUM_PERCENT_RANK_DESC')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1AggregatorField from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1AggregatorField from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "aggregator": obj.get("aggregator"),
            "field": obj.get("field")
        })
        return _obj


