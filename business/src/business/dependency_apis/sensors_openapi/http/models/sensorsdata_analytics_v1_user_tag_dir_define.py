# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataAnalyticsV1UserTagDirDefine(BaseModel):
    """
    
    """ # noqa: E501
    name: Optional[StrictStr] = None
    cname: Optional[StrictStr] = None
    data_type: Optional[StrictStr] = None
    type: Optional[StrictStr] = None
    sub_nodes: Optional[List[SensorsdataAnalyticsV1UserTagDirDefine]] = None
    __properties: ClassVar[List[str]] = ["name", "cname", "data_type", "type", "sub_nodes"]

    @field_validator('type')
    def type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['USER_TAG_TYPE_UNSPECIFIED', 'DIR', 'TAG']):
            raise ValueError("must be one of enum values ('USER_TAG_TYPE_UNSPECIFIED', 'DIR', 'TAG')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataAnalyticsV1UserTagDirDefine from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in sub_nodes (list)
        _items = []
        if self.sub_nodes:
            for _item_sub_nodes in self.sub_nodes:
                if _item_sub_nodes:
                    _items.append(_item_sub_nodes.to_dict())
            _dict['sub_nodes'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataAnalyticsV1UserTagDirDefine from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "name": obj.get("name"),
            "cname": obj.get("cname"),
            "data_type": obj.get("data_type"),
            "type": obj.get("type"),
            "sub_nodes": [SensorsdataAnalyticsV1UserTagDirDefine.from_dict(_item) for _item in obj["sub_nodes"]] if obj.get("sub_nodes") is not None else None
        })
        return _obj

# TODO: Rewrite to not use raise_errors
SensorsdataAnalyticsV1UserTagDirDefine.model_rebuild(raise_errors=False)

