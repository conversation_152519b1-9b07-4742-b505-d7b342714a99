# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict
from typing import Any, ClassVar, Dict, List, Optional
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_time_range import SensorsdataCommonTimeRange
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_event_sequence_segment_rule_expression_simple_event_sequence_segment_rule_expression_event_step import SensorsdataHorizonV1EventSequenceSegmentRuleExpressionSimpleEventSequenceSegmentRuleExpressionEventStep
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_event_sequence_segment_rule_expression_simple_event_sequence_segment_rule_expression_time_window import SensorsdataHorizonV1EventSequenceSegmentRuleExpressionSimpleEventSequenceSegmentRuleExpressionTimeWindow
from typing import Optional, Set
from typing_extensions import Self

class SensorsdataHorizonV1EventSequenceSegmentRuleExpressionSimpleEventSequenceSegmentRuleExpression(BaseModel):
    """
    
    """ # noqa: E501
    event_steps: Optional[List[SensorsdataHorizonV1EventSequenceSegmentRuleExpressionSimpleEventSequenceSegmentRuleExpressionEventStep]] = None
    time_range: Optional[SensorsdataCommonTimeRange] = None
    time_window: Optional[SensorsdataHorizonV1EventSequenceSegmentRuleExpressionSimpleEventSequenceSegmentRuleExpressionTimeWindow] = None
    __properties: ClassVar[List[str]] = ["event_steps", "time_range", "time_window"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1EventSequenceSegmentRuleExpressionSimpleEventSequenceSegmentRuleExpression from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in event_steps (list)
        _items = []
        if self.event_steps:
            for _item_event_steps in self.event_steps:
                if _item_event_steps:
                    _items.append(_item_event_steps.to_dict())
            _dict['event_steps'] = _items
        # override the default output from pydantic by calling `to_dict()` of time_range
        if self.time_range:
            _dict['time_range'] = self.time_range.to_dict()
        # override the default output from pydantic by calling `to_dict()` of time_window
        if self.time_window:
            _dict['time_window'] = self.time_window.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SensorsdataHorizonV1EventSequenceSegmentRuleExpressionSimpleEventSequenceSegmentRuleExpression from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "event_steps": [SensorsdataHorizonV1EventSequenceSegmentRuleExpressionSimpleEventSequenceSegmentRuleExpressionEventStep.from_dict(_item) for _item in obj["event_steps"]] if obj.get("event_steps") is not None else None,
            "time_range": SensorsdataCommonTimeRange.from_dict(obj["time_range"]) if obj.get("time_range") is not None else None,
            "time_window": SensorsdataHorizonV1EventSequenceSegmentRuleExpressionSimpleEventSequenceSegmentRuleExpressionTimeWindow.from_dict(obj["time_window"]) if obj.get("time_window") is not None else None
        })
        return _obj


