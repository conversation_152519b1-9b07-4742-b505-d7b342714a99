# coding: utf-8

# flake8: noqa

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501

__version__ = "1.0.0"

# import apis into sdk package
from business.dependency_apis.sensors_openapi.http.apis.sa_event_meta_api import SaEventMetaApi
from business.dependency_apis.sensors_openapi.http.apis.sa_property_meta_api import SaPropertyMetaApi
from business.dependency_apis.sensors_openapi.http.apis.sdh_schema_api import SdhSchemaApi
from business.dependency_apis.sensors_openapi.http.apis.sdh_segment_api import SdhSegmentApi
from business.dependency_apis.sensors_openapi.http.apis.sdh_tag_api import SdhTagApi

# import ApiClient
from business.dependency_apis.sensors_openapi.http.api_response import ApiResponse
from business.dependency_apis.sensors_openapi.http.api_client import ApiClient
from business.dependency_apis.sensors_openapi.http.configuration import Configuration
from business.dependency_apis.sensors_openapi.http.exceptions import OpenApiException
from business.dependency_apis.sensors_openapi.http.exceptions import ApiTypeError
from business.dependency_apis.sensors_openapi.http.exceptions import ApiValueError
from business.dependency_apis.sensors_openapi.http.exceptions import ApiKeyError
from business.dependency_apis.sensors_openapi.http.exceptions import ApiAttributeError
from business.dependency_apis.sensors_openapi.http.exceptions import ApiException

# import models into sdk package
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_event_define import \
    SensorsdataAnalyticsV1EventDefine
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_event_property_all_response import \
    SensorsdataAnalyticsV1EventPropertyAllResponse
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_event_property_all_response_http_api_result import \
    SensorsdataAnalyticsV1EventPropertyAllResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_event_property_request import \
    SensorsdataAnalyticsV1EventPropertyRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_event_property_response import \
    SensorsdataAnalyticsV1EventPropertyResponse
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_event_property_response_http_api_result import \
    SensorsdataAnalyticsV1EventPropertyResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_event_with_property import \
    SensorsdataAnalyticsV1EventWithProperty
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_get_property_value_request import \
    SensorsdataAnalyticsV1GetPropertyValueRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_get_property_value_response import \
    SensorsdataAnalyticsV1GetPropertyValueResponse
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_get_property_value_response_http_api_result import \
    SensorsdataAnalyticsV1GetPropertyValueResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_list_event_all_response import \
    SensorsdataAnalyticsV1ListEventAllResponse
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_list_event_all_response_http_api_result import \
    SensorsdataAnalyticsV1ListEventAllResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_list_event_tags_response import \
    SensorsdataAnalyticsV1ListEventTagsResponse
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_list_event_tags_response_http_api_result import \
    SensorsdataAnalyticsV1ListEventTagsResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_property_define import \
    SensorsdataAnalyticsV1PropertyDefine
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_tag_info import \
    SensorsdataAnalyticsV1TagInfo
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_user_group_all_response import \
    SensorsdataAnalyticsV1UserGroupAllResponse
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_user_group_all_response_http_api_result import \
    SensorsdataAnalyticsV1UserGroupAllResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_user_group_define import \
    SensorsdataAnalyticsV1UserGroupDefine
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_user_property_all_response import \
    SensorsdataAnalyticsV1UserPropertyAllResponse
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_user_property_all_response_http_api_result import \
    SensorsdataAnalyticsV1UserPropertyAllResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_user_tag_dir_define import \
    SensorsdataAnalyticsV1UserTagDirDefine
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_user_tag_dir_response import \
    SensorsdataAnalyticsV1UserTagDirResponse
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_user_tag_dir_response_http_api_result import \
    SensorsdataAnalyticsV1UserTagDirResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_compound_filter_condition import \
    SensorsdataCommonCompoundFilterCondition
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_cron_trigger import \
    SensorsdataCommonCronTrigger
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_error_cause import \
    SensorsdataCommonErrorCause
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_error_context import \
    SensorsdataCommonErrorContext
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_error_info import \
    SensorsdataCommonErrorInfo
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_filter_condition import \
    SensorsdataCommonFilterCondition
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_http_api_result import \
    SensorsdataCommonHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_literal import SensorsdataCommonLiteral
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_periodic_trigger import \
    SensorsdataCommonPeriodicTrigger
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_predicate_function_param import \
    SensorsdataCommonPredicateFunctionParam
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_static_trigger import \
    SensorsdataCommonStaticTrigger
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_time_interval import \
    SensorsdataCommonTimeInterval
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_time_point import \
    SensorsdataCommonTimePoint
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_time_point_relative_time_point import \
    SensorsdataCommonTimePointRelativeTimePoint
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_time_range import \
    SensorsdataCommonTimeRange
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_trigger import SensorsdataCommonTrigger
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_access_info import \
    SensorsdataHorizonV1AccessInfo
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_aggregator_field import \
    SensorsdataHorizonV1AggregatorField
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_basic_measure_based_rule_expression import \
    SensorsdataHorizonV1BasicMeasureBasedRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_batch_create_logical_schemas_request import \
    SensorsdataHorizonV1BatchCreateLogicalSchemasRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_batch_create_schema_extended_tables_request import \
    SensorsdataHorizonV1BatchCreateSchemaExtendedTablesRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_batch_create_schema_fields_request import \
    SensorsdataHorizonV1BatchCreateSchemaFieldsRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_batch_create_schema_fields_request_field_create_descriptor import \
    SensorsdataHorizonV1BatchCreateSchemaFieldsRequestFieldCreateDescriptor
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_batch_create_schema_fields_response_http_api_result import \
    SensorsdataHorizonV1BatchCreateSchemaFieldsResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_cancel_segment_task_request import \
    SensorsdataHorizonV1CancelSegmentTaskRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_cancel_tag_task_request import \
    SensorsdataHorizonV1CancelTagTaskRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_create_segment_definition_with_rule_request import \
    SensorsdataHorizonV1CreateSegmentDefinitionWithRuleRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_create_tag_definition_request import \
    SensorsdataHorizonV1CreateTagDefinitionRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_custom_based_rule_expression import \
    SensorsdataHorizonV1CustomBasedRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_custom_segment_rule_expression import \
    SensorsdataHorizonV1CustomSegmentRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_data_type import \
    SensorsdataHorizonV1DataType
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_eql_based_rule_expression import \
    SensorsdataHorizonV1EqlBasedRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_eql_segment_rule_expression import \
    SensorsdataHorizonV1EqlSegmentRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_evaluate_segment_request import \
    SensorsdataHorizonV1EvaluateSegmentRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_evaluate_segment_request_evaluate_segment_param import \
    SensorsdataHorizonV1EvaluateSegmentRequestEvaluateSegmentParam
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_evaluate_segment_request_evaluate_segment_param_segment_task_params_entry import \
    SensorsdataHorizonV1EvaluateSegmentRequestEvaluateSegmentParamSegmentTaskParamsEntry
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_evaluate_tag_request import \
    SensorsdataHorizonV1EvaluateTagRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_evaluate_tag_request_evaluate_tag_param import \
    SensorsdataHorizonV1EvaluateTagRequestEvaluateTagParam
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_evaluate_tag_request_evaluate_tag_param_tag_task_params_entry import \
    SensorsdataHorizonV1EvaluateTagRequestEvaluateTagParamTagTaskParamsEntry
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_event_sequence_segment_rule_expression import \
    SensorsdataHorizonV1EventSequenceSegmentRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_event_sequence_segment_rule_expression_simple_event_sequence_segment_rule_expression import \
    SensorsdataHorizonV1EventSequenceSegmentRuleExpressionSimpleEventSequenceSegmentRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_event_sequence_segment_rule_expression_simple_event_sequence_segment_rule_expression_event_step import \
    SensorsdataHorizonV1EventSequenceSegmentRuleExpressionSimpleEventSequenceSegmentRuleExpressionEventStep
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_event_sequence_segment_rule_expression_simple_event_sequence_segment_rule_expression_time_window import \
    SensorsdataHorizonV1EventSequenceSegmentRuleExpressionSimpleEventSequenceSegmentRuleExpressionTimeWindow
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field import SensorsdataHorizonV1Field
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field_custom_params_entry import \
    SensorsdataHorizonV1FieldCustomParamsEntry
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field_data_mapping import \
    SensorsdataHorizonV1FieldDataMapping
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field_data_mapping_extended_column import \
    SensorsdataHorizonV1FieldDataMappingExtendedColumn
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field_http_api_result import \
    SensorsdataHorizonV1FieldHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field_object_field import \
    SensorsdataHorizonV1FieldObjectField
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field_unit import \
    SensorsdataHorizonV1FieldUnit
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field_view_data_source import \
    SensorsdataHorizonV1FieldViewDataSource
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field_view_data_source_complex_expression import \
    SensorsdataHorizonV1FieldViewDataSourceComplexExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field_view_data_source_inline_expression import \
    SensorsdataHorizonV1FieldViewDataSourceInlineExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field_view_data_source_view_column_source_data import \
    SensorsdataHorizonV1FieldViewDataSourceViewColumnSourceData
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_lasted_tag_partition_request import \
    SensorsdataHorizonV1GetLastedTagPartitionRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_lasted_tag_partition_response import \
    SensorsdataHorizonV1GetLastedTagPartitionResponse
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_lasted_tag_partition_response_http_api_result import \
    SensorsdataHorizonV1GetLastedTagPartitionResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_lasted_tag_partition_response_lasted_tag_partition_map_entry import \
    SensorsdataHorizonV1GetLastedTagPartitionResponseLastedTagPartitionMapEntry
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_schema_by_original_name_request import \
    SensorsdataHorizonV1GetSchemaByOriginalNameRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_schema_field_request import \
    SensorsdataHorizonV1GetSchemaFieldRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_schema_request import \
    SensorsdataHorizonV1GetSchemaRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_segment_definition_request import \
    SensorsdataHorizonV1GetSegmentDefinitionRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_segment_task_request import \
    SensorsdataHorizonV1GetSegmentTaskRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_segment_task_response import \
    SensorsdataHorizonV1GetSegmentTaskResponse
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_segment_task_response_http_api_result import \
    SensorsdataHorizonV1GetSegmentTaskResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_tag_definition_request import \
    SensorsdataHorizonV1GetTagDefinitionRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_tag_task_request import \
    SensorsdataHorizonV1GetTagTaskRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_tag_task_response import \
    SensorsdataHorizonV1GetTagTaskResponse
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_tag_task_response_http_api_result import \
    SensorsdataHorizonV1GetTagTaskResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_group_expression_segment_rule_expression import \
    SensorsdataHorizonV1GroupExpressionSegmentRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_logical_schemas_request import \
    SensorsdataHorizonV1ListLogicalSchemasRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_logical_schemas_response import \
    SensorsdataHorizonV1ListLogicalSchemasResponse
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_logical_schemas_response_http_api_result import \
    SensorsdataHorizonV1ListLogicalSchemasResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_schema_extended_tables_request import \
    SensorsdataHorizonV1ListSchemaExtendedTablesRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_schema_extended_tables_response import \
    SensorsdataHorizonV1ListSchemaExtendedTablesResponse
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_schema_extended_tables_response_http_api_result import \
    SensorsdataHorizonV1ListSchemaExtendedTablesResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_schema_fields_request import \
    SensorsdataHorizonV1ListSchemaFieldsRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_schema_fields_response import \
    SensorsdataHorizonV1ListSchemaFieldsResponse
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_schema_fields_response_http_api_result import \
    SensorsdataHorizonV1ListSchemaFieldsResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_segment_definitions_request import \
    SensorsdataHorizonV1ListSegmentDefinitionsRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_segment_definitions_response import \
    SensorsdataHorizonV1ListSegmentDefinitionsResponse
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_segment_definitions_response_http_api_result import \
    SensorsdataHorizonV1ListSegmentDefinitionsResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_segment_items_request import \
    SensorsdataHorizonV1ListSegmentItemsRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_segment_items_response import \
    SensorsdataHorizonV1ListSegmentItemsResponse
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_segment_items_response_http_api_result import \
    SensorsdataHorizonV1ListSegmentItemsResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_tag_definitions_request import \
    SensorsdataHorizonV1ListTagDefinitionsRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_tag_definitions_response import \
    SensorsdataHorizonV1ListTagDefinitionsResponse
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_tag_definitions_response_http_api_result import \
    SensorsdataHorizonV1ListTagDefinitionsResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_tag_partitions_request import \
    SensorsdataHorizonV1ListTagPartitionsRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_tag_partitions_response import \
    SensorsdataHorizonV1ListTagPartitionsResponse
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_tag_partitions_response_http_api_result import \
    SensorsdataHorizonV1ListTagPartitionsResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_loading_based_rule_expression import \
    SensorsdataHorizonV1LoadingBasedRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_loading_segment_rule_expression import \
    SensorsdataHorizonV1LoadingSegmentRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_order import SensorsdataHorizonV1Order
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_order_measure import \
    SensorsdataHorizonV1OrderMeasure
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_rank_field import \
    SensorsdataHorizonV1RankField
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_rfm_based_rule_expression import \
    SensorsdataHorizonV1RfmBasedRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_rfm_based_rule_expression_layer_params_entry import \
    SensorsdataHorizonV1RfmBasedRuleExpressionLayerParamsEntry
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_rfm_based_rule_expression_rfm_factor import \
    SensorsdataHorizonV1RfmBasedRuleExpressionRfmFactor
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_rfm_based_rule_expression_rfm_factor_rfm_factor_group_rule import \
    SensorsdataHorizonV1RfmBasedRuleExpressionRfmFactorRfmFactorGroupRule
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_schema import \
    SensorsdataHorizonV1Schema
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_schema_custom_params_entry import \
    SensorsdataHorizonV1SchemaCustomParamsEntry
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_schema_data_mapping import \
    SensorsdataHorizonV1SchemaDataMapping
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_schema_data_mapping_schema_source_table import \
    SensorsdataHorizonV1SchemaDataMappingSchemaSourceTable
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_schema_extended_table import \
    SensorsdataHorizonV1SchemaExtendedTable
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_schema_extended_table_http_api_result import \
    SensorsdataHorizonV1SchemaExtendedTableHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_schema_field_dimension_dict import \
    SensorsdataHorizonV1SchemaFieldDimensionDict
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_schema_field_dimension_dict_dict_entry import \
    SensorsdataHorizonV1SchemaFieldDimensionDictDictEntry
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_schema_http_api_result import \
    SensorsdataHorizonV1SchemaHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_schema_schema_mapping import \
    SensorsdataHorizonV1SchemaSchemaMapping
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_schema_statistics import \
    SensorsdataHorizonV1SchemaStatistics
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_schema_statistics_schema_track_platform import \
    SensorsdataHorizonV1SchemaStatisticsSchemaTrackPlatform
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_alarm_config import \
    SensorsdataHorizonV1SegmentAlarmConfig
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_alarm_config_alarm_rule import \
    SensorsdataHorizonV1SegmentAlarmConfigAlarmRule
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_alarm_config_alarm_rule_alarm_params_entry import \
    SensorsdataHorizonV1SegmentAlarmConfigAlarmRuleAlarmParamsEntry
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_based_rule_expression import \
    SensorsdataHorizonV1SegmentBasedRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_based_rule_expression_segment_tag_entry import \
    SensorsdataHorizonV1SegmentBasedRuleExpressionSegmentTagEntry
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_definition import \
    SensorsdataHorizonV1SegmentDefinition
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_definition_custom_params_entry import \
    SensorsdataHorizonV1SegmentDefinitionCustomParamsEntry
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_definition_http_api_result import \
    SensorsdataHorizonV1SegmentDefinitionHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_definition_source import \
    SensorsdataHorizonV1SegmentDefinitionSource
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_item import \
    SensorsdataHorizonV1SegmentItem
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_notify_params import \
    SensorsdataHorizonV1SegmentNotifyParams
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_notify_params_content import \
    SensorsdataHorizonV1SegmentNotifyParamsContent
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_notify_params_notify_param import \
    SensorsdataHorizonV1SegmentNotifyParamsNotifyParam
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_rule import \
    SensorsdataHorizonV1SegmentRule
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_rule_custom_params_entry import \
    SensorsdataHorizonV1SegmentRuleCustomParamsEntry
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_rule_expression import \
    SensorsdataHorizonV1SegmentRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_rule_ref import \
    SensorsdataHorizonV1SegmentRuleRef
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_storage_settings import \
    SensorsdataHorizonV1SegmentStorageSettings
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_task import \
    SensorsdataHorizonV1SegmentTask
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_task_custom_params_entry import \
    SensorsdataHorizonV1SegmentTaskCustomParamsEntry
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_task_report import \
    SensorsdataHorizonV1SegmentTaskReport
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_task_report_segment_loading_report import \
    SensorsdataHorizonV1SegmentTaskReportSegmentLoadingReport
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_task_segment_task_failure_detail import \
    SensorsdataHorizonV1SegmentTaskSegmentTaskFailureDetail
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_task_segment_task_params_entry import \
    SensorsdataHorizonV1SegmentTaskSegmentTaskParamsEntry
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_sql_based_rule_expression import \
    SensorsdataHorizonV1SqlBasedRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_sql_based_rule_expression_query_option import \
    SensorsdataHorizonV1SqlBasedRuleExpressionQueryOption
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_sql_segment_rule_expression import \
    SensorsdataHorizonV1SqlSegmentRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_sql_segment_rule_expression_query_option import \
    SensorsdataHorizonV1SqlSegmentRuleExpressionQueryOption
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_alarm_config import \
    SensorsdataHorizonV1TagAlarmConfig
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_alarm_config_alarm_rule import \
    SensorsdataHorizonV1TagAlarmConfigAlarmRule
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_alarm_config_alarm_rule_alarm_params_entry import \
    SensorsdataHorizonV1TagAlarmConfigAlarmRuleAlarmParamsEntry
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_definition import \
    SensorsdataHorizonV1TagDefinition
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_definition_custom_params_entry import \
    SensorsdataHorizonV1TagDefinitionCustomParamsEntry
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_definition_http_api_result import \
    SensorsdataHorizonV1TagDefinitionHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_definition_source import \
    SensorsdataHorizonV1TagDefinitionSource
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_notify_params import \
    SensorsdataHorizonV1TagNotifyParams
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_notify_params_content import \
    SensorsdataHorizonV1TagNotifyParamsContent
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_notify_params_content_ext_params_entry import \
    SensorsdataHorizonV1TagNotifyParamsContentExtParamsEntry
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_notify_params_notify_param import \
    SensorsdataHorizonV1TagNotifyParamsNotifyParam
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_partition import \
    SensorsdataHorizonV1TagPartition
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_partition_custom_params_entry import \
    SensorsdataHorizonV1TagPartitionCustomParamsEntry
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_post_action import \
    SensorsdataHorizonV1TagPostAction
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_post_action_custom_param_entry import \
    SensorsdataHorizonV1TagPostActionCustomParamEntry
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_rule_expression import \
    SensorsdataHorizonV1TagRuleExpression
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_storage_settings import \
    SensorsdataHorizonV1TagStorageSettings
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_task import \
    SensorsdataHorizonV1TagTask
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_task_report import \
    SensorsdataHorizonV1TagTaskReport
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_task_report_extend_report_info_entry import \
    SensorsdataHorizonV1TagTaskReportExtendReportInfoEntry
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_task_report_tag_loading_report import \
    SensorsdataHorizonV1TagTaskReportTagLoadingReport
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_task_tag_task_failure_detail import \
    SensorsdataHorizonV1TagTaskTagTaskFailureDetail
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_task_tag_task_params_entry import \
    SensorsdataHorizonV1TagTaskTagTaskParamsEntry
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tailor import \
    SensorsdataHorizonV1Tailor
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_update_schema_extended_table_request import \
    SensorsdataHorizonV1UpdateSchemaExtendedTableRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_update_schema_field_request import \
    SensorsdataHorizonV1UpdateSchemaFieldRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_update_schema_request import \
    SensorsdataHorizonV1UpdateSchemaRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_update_segment_definition_with_rule_request import \
    SensorsdataHorizonV1UpdateSegmentDefinitionWithRuleRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_update_segment_scheduler_status_request import \
    SensorsdataHorizonV1UpdateSegmentSchedulerStatusRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_update_tag_definition_request import \
    SensorsdataHorizonV1UpdateTagDefinitionRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_update_tag_scheduler_status_request import \
    SensorsdataHorizonV1UpdateTagSchedulerStatusRequest
