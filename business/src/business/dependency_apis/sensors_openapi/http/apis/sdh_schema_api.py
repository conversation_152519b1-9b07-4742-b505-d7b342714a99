# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501

import warnings
from pydantic import validate_call, Field, StrictFloat, StrictStr, StrictInt
from typing import Any, Dict, List, Optional, Tuple, Union
from typing_extensions import Annotated

from pydantic import Field, StrictStr
from typing import Optional
from typing_extensions import Annotated
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_http_api_result import SensorsdataCommonHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_batch_create_logical_schemas_request import SensorsdataHorizonV1BatchCreateLogicalSchemasRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_batch_create_schema_extended_tables_request import SensorsdataHorizonV1BatchCreateSchemaExtendedTablesRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_batch_create_schema_fields_request import SensorsdataHorizonV1BatchCreateSchemaFieldsRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_batch_create_schema_fields_response_http_api_result import SensorsdataHorizonV1BatchCreateSchemaFieldsResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field_http_api_result import SensorsdataHorizonV1FieldHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_schema_by_original_name_request import SensorsdataHorizonV1GetSchemaByOriginalNameRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_schema_field_request import SensorsdataHorizonV1GetSchemaFieldRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_schema_request import SensorsdataHorizonV1GetSchemaRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_logical_schemas_request import SensorsdataHorizonV1ListLogicalSchemasRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_logical_schemas_response_http_api_result import SensorsdataHorizonV1ListLogicalSchemasResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_schema_extended_tables_request import SensorsdataHorizonV1ListSchemaExtendedTablesRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_schema_extended_tables_response_http_api_result import SensorsdataHorizonV1ListSchemaExtendedTablesResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_schema_fields_request import SensorsdataHorizonV1ListSchemaFieldsRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_schema_fields_response_http_api_result import SensorsdataHorizonV1ListSchemaFieldsResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_schema_extended_table_http_api_result import SensorsdataHorizonV1SchemaExtendedTableHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_schema_http_api_result import SensorsdataHorizonV1SchemaHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_update_schema_extended_table_request import SensorsdataHorizonV1UpdateSchemaExtendedTableRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_update_schema_field_request import SensorsdataHorizonV1UpdateSchemaFieldRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_update_schema_request import SensorsdataHorizonV1UpdateSchemaRequest

from business.dependency_apis.sensors_openapi.http.api_client import ApiClient, RequestSerialized
from business.dependency_apis.sensors_openapi.http.api_response import ApiResponse
from business.dependency_apis.sensors_openapi.http.rest import RESTResponseType


class SdhSchemaApi:
    """NOTE: This class is auto generated by OpenAPI Generator
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    def __init__(self, api_client=None) -> None:
        if api_client is None:
            api_client = ApiClient.get_default()
        self.api_client = api_client


    @validate_call
    async def batch_create_schema_extended_tables(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        batch_create_schema_extended_tables_request: Optional[SensorsdataHorizonV1BatchCreateSchemaExtendedTablesRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataCommonHttpApiResult:
        """创建维度表关联关系

        创建维度表关联关系

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param batch_create_schema_extended_tables_request:
        :type batch_create_schema_extended_tables_request: SensorsdataHorizonV1BatchCreateSchemaExtendedTablesRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._batch_create_schema_extended_tables_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            batch_create_schema_extended_tables_request=batch_create_schema_extended_tables_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def batch_create_schema_extended_tables_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        batch_create_schema_extended_tables_request: Optional[SensorsdataHorizonV1BatchCreateSchemaExtendedTablesRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataCommonHttpApiResult]:
        """创建维度表关联关系

        创建维度表关联关系

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param batch_create_schema_extended_tables_request:
        :type batch_create_schema_extended_tables_request: SensorsdataHorizonV1BatchCreateSchemaExtendedTablesRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._batch_create_schema_extended_tables_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            batch_create_schema_extended_tables_request=batch_create_schema_extended_tables_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def batch_create_schema_extended_tables_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        batch_create_schema_extended_tables_request: Optional[SensorsdataHorizonV1BatchCreateSchemaExtendedTablesRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """创建维度表关联关系

        创建维度表关联关系

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param batch_create_schema_extended_tables_request:
        :type batch_create_schema_extended_tables_request: SensorsdataHorizonV1BatchCreateSchemaExtendedTablesRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._batch_create_schema_extended_tables_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            batch_create_schema_extended_tables_request=batch_create_schema_extended_tables_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _batch_create_schema_extended_tables_serialize(
        self,
        api_key,
        sensorsdata_project,
        batch_create_schema_extended_tables_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if batch_create_schema_extended_tables_request is not None:
            _body_params = batch_create_schema_extended_tables_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/schema/extended-table/batch-create',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def batch_create_schema_fields(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        batch_create_schema_fields_request: Optional[SensorsdataHorizonV1BatchCreateSchemaFieldsRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1BatchCreateSchemaFieldsResponseHttpApiResult:
        """批量创建属性

        批量创建属性  命名规则 `^[a-zA-Z_$][a-zA-Z\\d_$]{0,99}$`  不允许创建以下保留字段及包含保留前缀的字段  - 保留字段    - 公共保留字段      - distinct_id, original_id, time, properties, id, first_id, second_id, users, events, event, user_id, date, datetime    - SchemaClass = EVENT      - 公共保留字段以及 event_id, event_bucket, day, week_id, month_id, _offset, sampling_group    - SchemaClass = USER      - 公共保留字段以及 _offset, first_id_type, second_id_type, generated_from, merged_to    - SchemaClass = ITEM      - 公共保留字段以及 item_type, item_id  - 保留前缀    - user_tag, user_group, $identity_, identity_, segment_ (可以创建 $ 开头的属性)

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param batch_create_schema_fields_request:
        :type batch_create_schema_fields_request: SensorsdataHorizonV1BatchCreateSchemaFieldsRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._batch_create_schema_fields_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            batch_create_schema_fields_request=batch_create_schema_fields_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1BatchCreateSchemaFieldsResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def batch_create_schema_fields_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        batch_create_schema_fields_request: Optional[SensorsdataHorizonV1BatchCreateSchemaFieldsRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1BatchCreateSchemaFieldsResponseHttpApiResult]:
        """批量创建属性

        批量创建属性  命名规则 `^[a-zA-Z_$][a-zA-Z\\d_$]{0,99}$`  不允许创建以下保留字段及包含保留前缀的字段  - 保留字段    - 公共保留字段      - distinct_id, original_id, time, properties, id, first_id, second_id, users, events, event, user_id, date, datetime    - SchemaClass = EVENT      - 公共保留字段以及 event_id, event_bucket, day, week_id, month_id, _offset, sampling_group    - SchemaClass = USER      - 公共保留字段以及 _offset, first_id_type, second_id_type, generated_from, merged_to    - SchemaClass = ITEM      - 公共保留字段以及 item_type, item_id  - 保留前缀    - user_tag, user_group, $identity_, identity_, segment_ (可以创建 $ 开头的属性)

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param batch_create_schema_fields_request:
        :type batch_create_schema_fields_request: SensorsdataHorizonV1BatchCreateSchemaFieldsRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._batch_create_schema_fields_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            batch_create_schema_fields_request=batch_create_schema_fields_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1BatchCreateSchemaFieldsResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def batch_create_schema_fields_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        batch_create_schema_fields_request: Optional[SensorsdataHorizonV1BatchCreateSchemaFieldsRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """批量创建属性

        批量创建属性  命名规则 `^[a-zA-Z_$][a-zA-Z\\d_$]{0,99}$`  不允许创建以下保留字段及包含保留前缀的字段  - 保留字段    - 公共保留字段      - distinct_id, original_id, time, properties, id, first_id, second_id, users, events, event, user_id, date, datetime    - SchemaClass = EVENT      - 公共保留字段以及 event_id, event_bucket, day, week_id, month_id, _offset, sampling_group    - SchemaClass = USER      - 公共保留字段以及 _offset, first_id_type, second_id_type, generated_from, merged_to    - SchemaClass = ITEM      - 公共保留字段以及 item_type, item_id  - 保留前缀    - user_tag, user_group, $identity_, identity_, segment_ (可以创建 $ 开头的属性)

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param batch_create_schema_fields_request:
        :type batch_create_schema_fields_request: SensorsdataHorizonV1BatchCreateSchemaFieldsRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._batch_create_schema_fields_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            batch_create_schema_fields_request=batch_create_schema_fields_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1BatchCreateSchemaFieldsResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _batch_create_schema_fields_serialize(
        self,
        api_key,
        sensorsdata_project,
        batch_create_schema_fields_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if batch_create_schema_fields_request is not None:
            _body_params = batch_create_schema_fields_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/schema/field/batch-create',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def create_event(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        batch_create_logical_schemas_request: Optional[SensorsdataHorizonV1BatchCreateLogicalSchemasRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataCommonHttpApiResult:
        """创建事件定义

        创建事件定义

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param batch_create_logical_schemas_request:
        :type batch_create_logical_schemas_request: SensorsdataHorizonV1BatchCreateLogicalSchemasRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._create_event_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            batch_create_logical_schemas_request=batch_create_logical_schemas_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def create_event_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        batch_create_logical_schemas_request: Optional[SensorsdataHorizonV1BatchCreateLogicalSchemasRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataCommonHttpApiResult]:
        """创建事件定义

        创建事件定义

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param batch_create_logical_schemas_request:
        :type batch_create_logical_schemas_request: SensorsdataHorizonV1BatchCreateLogicalSchemasRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._create_event_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            batch_create_logical_schemas_request=batch_create_logical_schemas_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def create_event_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        batch_create_logical_schemas_request: Optional[SensorsdataHorizonV1BatchCreateLogicalSchemasRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """创建事件定义

        创建事件定义

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param batch_create_logical_schemas_request:
        :type batch_create_logical_schemas_request: SensorsdataHorizonV1BatchCreateLogicalSchemasRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._create_event_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            batch_create_logical_schemas_request=batch_create_logical_schemas_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _create_event_serialize(
        self,
        api_key,
        sensorsdata_project,
        batch_create_logical_schemas_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if batch_create_logical_schemas_request is not None:
            _body_params = batch_create_logical_schemas_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/schema/event/create',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def get_event(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_schema_by_original_name_request: Optional[SensorsdataHorizonV1GetSchemaByOriginalNameRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1SchemaHttpApiResult:
        """获取事件定义

        获取事件定义

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_schema_by_original_name_request:
        :type get_schema_by_original_name_request: SensorsdataHorizonV1GetSchemaByOriginalNameRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_event_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_schema_by_original_name_request=get_schema_by_original_name_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1SchemaHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def get_event_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_schema_by_original_name_request: Optional[SensorsdataHorizonV1GetSchemaByOriginalNameRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1SchemaHttpApiResult]:
        """获取事件定义

        获取事件定义

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_schema_by_original_name_request:
        :type get_schema_by_original_name_request: SensorsdataHorizonV1GetSchemaByOriginalNameRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_event_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_schema_by_original_name_request=get_schema_by_original_name_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1SchemaHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def get_event_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_schema_by_original_name_request: Optional[SensorsdataHorizonV1GetSchemaByOriginalNameRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """获取事件定义

        获取事件定义

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_schema_by_original_name_request:
        :type get_schema_by_original_name_request: SensorsdataHorizonV1GetSchemaByOriginalNameRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_event_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_schema_by_original_name_request=get_schema_by_original_name_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1SchemaHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _get_event_serialize(
        self,
        api_key,
        sensorsdata_project,
        get_schema_by_original_name_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if get_schema_by_original_name_request is not None:
            _body_params = get_schema_by_original_name_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/schema/event/get',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def get_schema(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_schema_request: Optional[SensorsdataHorizonV1GetSchemaRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1SchemaHttpApiResult:
        """获取用户表/事件表元信息

        获取用户表/事件表元信息

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_schema_request:
        :type get_schema_request: SensorsdataHorizonV1GetSchemaRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_schema_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_schema_request=get_schema_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1SchemaHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def get_schema_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_schema_request: Optional[SensorsdataHorizonV1GetSchemaRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1SchemaHttpApiResult]:
        """获取用户表/事件表元信息

        获取用户表/事件表元信息

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_schema_request:
        :type get_schema_request: SensorsdataHorizonV1GetSchemaRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_schema_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_schema_request=get_schema_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1SchemaHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def get_schema_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_schema_request: Optional[SensorsdataHorizonV1GetSchemaRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """获取用户表/事件表元信息

        获取用户表/事件表元信息

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_schema_request:
        :type get_schema_request: SensorsdataHorizonV1GetSchemaRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_schema_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_schema_request=get_schema_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1SchemaHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _get_schema_serialize(
        self,
        api_key,
        sensorsdata_project,
        get_schema_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if get_schema_request is not None:
            _body_params = get_schema_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/schema/get',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def get_schema_field(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_schema_field_request: Optional[SensorsdataHorizonV1GetSchemaFieldRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1FieldHttpApiResult:
        """获取属性信息

        获取属性信息

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_schema_field_request:
        :type get_schema_field_request: SensorsdataHorizonV1GetSchemaFieldRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_schema_field_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_schema_field_request=get_schema_field_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1FieldHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def get_schema_field_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_schema_field_request: Optional[SensorsdataHorizonV1GetSchemaFieldRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1FieldHttpApiResult]:
        """获取属性信息

        获取属性信息

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_schema_field_request:
        :type get_schema_field_request: SensorsdataHorizonV1GetSchemaFieldRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_schema_field_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_schema_field_request=get_schema_field_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1FieldHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def get_schema_field_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_schema_field_request: Optional[SensorsdataHorizonV1GetSchemaFieldRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """获取属性信息

        获取属性信息

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_schema_field_request:
        :type get_schema_field_request: SensorsdataHorizonV1GetSchemaFieldRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_schema_field_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_schema_field_request=get_schema_field_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1FieldHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _get_schema_field_serialize(
        self,
        api_key,
        sensorsdata_project,
        get_schema_field_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if get_schema_field_request is not None:
            _body_params = get_schema_field_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/schema/field/get',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def list_event_fields(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_schema_fields_request: Optional[SensorsdataHorizonV1ListSchemaFieldsRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1ListSchemaFieldsResponseHttpApiResult:
        """获取事件属性

        获取事件属性

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_schema_fields_request:
        :type list_schema_fields_request: SensorsdataHorizonV1ListSchemaFieldsRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_event_fields_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_schema_fields_request=list_schema_fields_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListSchemaFieldsResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def list_event_fields_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_schema_fields_request: Optional[SensorsdataHorizonV1ListSchemaFieldsRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1ListSchemaFieldsResponseHttpApiResult]:
        """获取事件属性

        获取事件属性

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_schema_fields_request:
        :type list_schema_fields_request: SensorsdataHorizonV1ListSchemaFieldsRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_event_fields_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_schema_fields_request=list_schema_fields_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListSchemaFieldsResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def list_event_fields_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_schema_fields_request: Optional[SensorsdataHorizonV1ListSchemaFieldsRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """获取事件属性

        获取事件属性

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_schema_fields_request:
        :type list_schema_fields_request: SensorsdataHorizonV1ListSchemaFieldsRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_event_fields_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_schema_fields_request=list_schema_fields_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListSchemaFieldsResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _list_event_fields_serialize(
        self,
        api_key,
        sensorsdata_project,
        list_schema_fields_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if list_schema_fields_request is not None:
            _body_params = list_schema_fields_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/schema/event/field/list',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def list_event_schemas(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_logical_schemas_request: Optional[SensorsdataHorizonV1ListLogicalSchemasRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1ListLogicalSchemasResponseHttpApiResult:
        """获取事件定义列表

        获取事件定义列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_logical_schemas_request:
        :type list_logical_schemas_request: SensorsdataHorizonV1ListLogicalSchemasRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_event_schemas_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_logical_schemas_request=list_logical_schemas_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListLogicalSchemasResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def list_event_schemas_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_logical_schemas_request: Optional[SensorsdataHorizonV1ListLogicalSchemasRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1ListLogicalSchemasResponseHttpApiResult]:
        """获取事件定义列表

        获取事件定义列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_logical_schemas_request:
        :type list_logical_schemas_request: SensorsdataHorizonV1ListLogicalSchemasRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_event_schemas_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_logical_schemas_request=list_logical_schemas_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListLogicalSchemasResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def list_event_schemas_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_logical_schemas_request: Optional[SensorsdataHorizonV1ListLogicalSchemasRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """获取事件定义列表

        获取事件定义列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_logical_schemas_request:
        :type list_logical_schemas_request: SensorsdataHorizonV1ListLogicalSchemasRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_event_schemas_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_logical_schemas_request=list_logical_schemas_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListLogicalSchemasResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _list_event_schemas_serialize(
        self,
        api_key,
        sensorsdata_project,
        list_logical_schemas_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if list_logical_schemas_request is not None:
            _body_params = list_logical_schemas_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/schema/event/list',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def list_schema_extended_tables(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_schema_extended_tables_request: Optional[SensorsdataHorizonV1ListSchemaExtendedTablesRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1ListSchemaExtendedTablesResponseHttpApiResult:
        """查询 Schema 关联的 Table

        查询 Schema 关联的 Table

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_schema_extended_tables_request:
        :type list_schema_extended_tables_request: SensorsdataHorizonV1ListSchemaExtendedTablesRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_schema_extended_tables_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_schema_extended_tables_request=list_schema_extended_tables_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListSchemaExtendedTablesResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def list_schema_extended_tables_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_schema_extended_tables_request: Optional[SensorsdataHorizonV1ListSchemaExtendedTablesRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1ListSchemaExtendedTablesResponseHttpApiResult]:
        """查询 Schema 关联的 Table

        查询 Schema 关联的 Table

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_schema_extended_tables_request:
        :type list_schema_extended_tables_request: SensorsdataHorizonV1ListSchemaExtendedTablesRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_schema_extended_tables_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_schema_extended_tables_request=list_schema_extended_tables_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListSchemaExtendedTablesResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def list_schema_extended_tables_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_schema_extended_tables_request: Optional[SensorsdataHorizonV1ListSchemaExtendedTablesRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """查询 Schema 关联的 Table

        查询 Schema 关联的 Table

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_schema_extended_tables_request:
        :type list_schema_extended_tables_request: SensorsdataHorizonV1ListSchemaExtendedTablesRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_schema_extended_tables_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_schema_extended_tables_request=list_schema_extended_tables_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListSchemaExtendedTablesResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _list_schema_extended_tables_serialize(
        self,
        api_key,
        sensorsdata_project,
        list_schema_extended_tables_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if list_schema_extended_tables_request is not None:
            _body_params = list_schema_extended_tables_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/schema/extended-table/list',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def list_schema_fields(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_schema_fields_request: Optional[SensorsdataHorizonV1ListSchemaFieldsRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1ListSchemaFieldsResponseHttpApiResult:
        """获取属性列表

        获取属性列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_schema_fields_request:
        :type list_schema_fields_request: SensorsdataHorizonV1ListSchemaFieldsRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_schema_fields_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_schema_fields_request=list_schema_fields_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListSchemaFieldsResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def list_schema_fields_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_schema_fields_request: Optional[SensorsdataHorizonV1ListSchemaFieldsRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1ListSchemaFieldsResponseHttpApiResult]:
        """获取属性列表

        获取属性列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_schema_fields_request:
        :type list_schema_fields_request: SensorsdataHorizonV1ListSchemaFieldsRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_schema_fields_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_schema_fields_request=list_schema_fields_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListSchemaFieldsResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def list_schema_fields_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_schema_fields_request: Optional[SensorsdataHorizonV1ListSchemaFieldsRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """获取属性列表

        获取属性列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_schema_fields_request:
        :type list_schema_fields_request: SensorsdataHorizonV1ListSchemaFieldsRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_schema_fields_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_schema_fields_request=list_schema_fields_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListSchemaFieldsResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _list_schema_fields_serialize(
        self,
        api_key,
        sensorsdata_project,
        list_schema_fields_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if list_schema_fields_request is not None:
            _body_params = list_schema_fields_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/schema/field/list',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def update_event(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        update_schema_request: Optional[SensorsdataHorizonV1UpdateSchemaRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1SchemaHttpApiResult:
        """更新事件定义

        更新事件定义

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param update_schema_request:
        :type update_schema_request: SensorsdataHorizonV1UpdateSchemaRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._update_event_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            update_schema_request=update_schema_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1SchemaHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def update_event_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        update_schema_request: Optional[SensorsdataHorizonV1UpdateSchemaRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1SchemaHttpApiResult]:
        """更新事件定义

        更新事件定义

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param update_schema_request:
        :type update_schema_request: SensorsdataHorizonV1UpdateSchemaRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._update_event_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            update_schema_request=update_schema_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1SchemaHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def update_event_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        update_schema_request: Optional[SensorsdataHorizonV1UpdateSchemaRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """更新事件定义

        更新事件定义

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param update_schema_request:
        :type update_schema_request: SensorsdataHorizonV1UpdateSchemaRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._update_event_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            update_schema_request=update_schema_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1SchemaHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _update_event_serialize(
        self,
        api_key,
        sensorsdata_project,
        update_schema_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if update_schema_request is not None:
            _body_params = update_schema_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/schema/event/update',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def update_schema_extended_table(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        update_schema_extended_table_request: Optional[SensorsdataHorizonV1UpdateSchemaExtendedTableRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1SchemaExtendedTableHttpApiResult:
        """更新 SchemaExtendedTable

        更新 SchemaExtendedTable

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param update_schema_extended_table_request:
        :type update_schema_extended_table_request: SensorsdataHorizonV1UpdateSchemaExtendedTableRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._update_schema_extended_table_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            update_schema_extended_table_request=update_schema_extended_table_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1SchemaExtendedTableHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def update_schema_extended_table_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        update_schema_extended_table_request: Optional[SensorsdataHorizonV1UpdateSchemaExtendedTableRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1SchemaExtendedTableHttpApiResult]:
        """更新 SchemaExtendedTable

        更新 SchemaExtendedTable

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param update_schema_extended_table_request:
        :type update_schema_extended_table_request: SensorsdataHorizonV1UpdateSchemaExtendedTableRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._update_schema_extended_table_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            update_schema_extended_table_request=update_schema_extended_table_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1SchemaExtendedTableHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def update_schema_extended_table_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        update_schema_extended_table_request: Optional[SensorsdataHorizonV1UpdateSchemaExtendedTableRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """更新 SchemaExtendedTable

        更新 SchemaExtendedTable

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param update_schema_extended_table_request:
        :type update_schema_extended_table_request: SensorsdataHorizonV1UpdateSchemaExtendedTableRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._update_schema_extended_table_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            update_schema_extended_table_request=update_schema_extended_table_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1SchemaExtendedTableHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _update_schema_extended_table_serialize(
        self,
        api_key,
        sensorsdata_project,
        update_schema_extended_table_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if update_schema_extended_table_request is not None:
            _body_params = update_schema_extended_table_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/schema/extended-table/update',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def update_schema_field(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        update_schema_field_request: Optional[SensorsdataHorizonV1UpdateSchemaFieldRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1FieldHttpApiResult:
        """更新属性

        更新属性  更新属性  支持修改 display_name,unit,custom_param,visible,enable,has_data,required,data_type,view_data_source,dict  支持更新维度字典

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param update_schema_field_request:
        :type update_schema_field_request: SensorsdataHorizonV1UpdateSchemaFieldRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._update_schema_field_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            update_schema_field_request=update_schema_field_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1FieldHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def update_schema_field_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        update_schema_field_request: Optional[SensorsdataHorizonV1UpdateSchemaFieldRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1FieldHttpApiResult]:
        """更新属性

        更新属性  更新属性  支持修改 display_name,unit,custom_param,visible,enable,has_data,required,data_type,view_data_source,dict  支持更新维度字典

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param update_schema_field_request:
        :type update_schema_field_request: SensorsdataHorizonV1UpdateSchemaFieldRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._update_schema_field_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            update_schema_field_request=update_schema_field_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1FieldHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def update_schema_field_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        update_schema_field_request: Optional[SensorsdataHorizonV1UpdateSchemaFieldRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """更新属性

        更新属性  更新属性  支持修改 display_name,unit,custom_param,visible,enable,has_data,required,data_type,view_data_source,dict  支持更新维度字典

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param update_schema_field_request:
        :type update_schema_field_request: SensorsdataHorizonV1UpdateSchemaFieldRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._update_schema_field_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            update_schema_field_request=update_schema_field_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1FieldHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _update_schema_field_serialize(
        self,
        api_key,
        sensorsdata_project,
        update_schema_field_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if update_schema_field_request is not None:
            _body_params = update_schema_field_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/schema/field/update',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )


