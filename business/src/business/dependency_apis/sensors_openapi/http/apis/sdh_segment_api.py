# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501

import warnings
from pydantic import validate_call, Field, StrictFloat, StrictStr, StrictInt
from typing import Any, Dict, List, Optional, Tuple, Union
from typing_extensions import Annotated

from pydantic import Field, StrictStr
from typing import Optional
from typing_extensions import Annotated
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_http_api_result import SensorsdataCommonHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_cancel_segment_task_request import SensorsdataHorizonV1CancelSegmentTaskRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_create_segment_definition_with_rule_request import SensorsdataHorizonV1CreateSegmentDefinitionWithRuleRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_evaluate_segment_request import SensorsdataHorizonV1EvaluateSegmentRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_segment_definition_request import SensorsdataHorizonV1GetSegmentDefinitionRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_segment_task_request import SensorsdataHorizonV1GetSegmentTaskRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_segment_task_response_http_api_result import SensorsdataHorizonV1GetSegmentTaskResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_segment_definitions_request import SensorsdataHorizonV1ListSegmentDefinitionsRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_segment_definitions_response_http_api_result import SensorsdataHorizonV1ListSegmentDefinitionsResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_segment_items_request import SensorsdataHorizonV1ListSegmentItemsRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_segment_items_response_http_api_result import SensorsdataHorizonV1ListSegmentItemsResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_segment_definition_http_api_result import SensorsdataHorizonV1SegmentDefinitionHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_update_segment_definition_with_rule_request import SensorsdataHorizonV1UpdateSegmentDefinitionWithRuleRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_update_segment_scheduler_status_request import SensorsdataHorizonV1UpdateSegmentSchedulerStatusRequest

from business.dependency_apis.sensors_openapi.http.api_client import ApiClient, RequestSerialized
from business.dependency_apis.sensors_openapi.http.api_response import ApiResponse
from business.dependency_apis.sensors_openapi.http.rest import RESTResponseType


class SdhSegmentApi:
    """NOTE: This class is auto generated by OpenAPI Generator
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    def __init__(self, api_client=None) -> None:
        if api_client is None:
            api_client = ApiClient.get_default()
        self.api_client = api_client


    @validate_call
    async def cancel_segment_task(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        cancel_segment_task_request: Optional[SensorsdataHorizonV1CancelSegmentTaskRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataCommonHttpApiResult:
        """停止分群任务

        停止分群任务

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param cancel_segment_task_request:
        :type cancel_segment_task_request: SensorsdataHorizonV1CancelSegmentTaskRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._cancel_segment_task_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            cancel_segment_task_request=cancel_segment_task_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def cancel_segment_task_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        cancel_segment_task_request: Optional[SensorsdataHorizonV1CancelSegmentTaskRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataCommonHttpApiResult]:
        """停止分群任务

        停止分群任务

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param cancel_segment_task_request:
        :type cancel_segment_task_request: SensorsdataHorizonV1CancelSegmentTaskRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._cancel_segment_task_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            cancel_segment_task_request=cancel_segment_task_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def cancel_segment_task_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        cancel_segment_task_request: Optional[SensorsdataHorizonV1CancelSegmentTaskRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """停止分群任务

        停止分群任务

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param cancel_segment_task_request:
        :type cancel_segment_task_request: SensorsdataHorizonV1CancelSegmentTaskRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._cancel_segment_task_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            cancel_segment_task_request=cancel_segment_task_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _cancel_segment_task_serialize(
        self,
        api_key,
        sensorsdata_project,
        cancel_segment_task_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if cancel_segment_task_request is not None:
            _body_params = cancel_segment_task_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/segment/task/cancel',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def create_segment_definition_with_rule(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        create_segment_definition_with_rule_request: Optional[SensorsdataHorizonV1CreateSegmentDefinitionWithRuleRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1SegmentDefinitionHttpApiResult:
        """创建分群

        创建分群

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param create_segment_definition_with_rule_request:
        :type create_segment_definition_with_rule_request: SensorsdataHorizonV1CreateSegmentDefinitionWithRuleRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._create_segment_definition_with_rule_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            create_segment_definition_with_rule_request=create_segment_definition_with_rule_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1SegmentDefinitionHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def create_segment_definition_with_rule_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        create_segment_definition_with_rule_request: Optional[SensorsdataHorizonV1CreateSegmentDefinitionWithRuleRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1SegmentDefinitionHttpApiResult]:
        """创建分群

        创建分群

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param create_segment_definition_with_rule_request:
        :type create_segment_definition_with_rule_request: SensorsdataHorizonV1CreateSegmentDefinitionWithRuleRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._create_segment_definition_with_rule_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            create_segment_definition_with_rule_request=create_segment_definition_with_rule_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1SegmentDefinitionHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def create_segment_definition_with_rule_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        create_segment_definition_with_rule_request: Optional[SensorsdataHorizonV1CreateSegmentDefinitionWithRuleRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """创建分群

        创建分群

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param create_segment_definition_with_rule_request:
        :type create_segment_definition_with_rule_request: SensorsdataHorizonV1CreateSegmentDefinitionWithRuleRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._create_segment_definition_with_rule_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            create_segment_definition_with_rule_request=create_segment_definition_with_rule_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1SegmentDefinitionHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _create_segment_definition_with_rule_serialize(
        self,
        api_key,
        sensorsdata_project,
        create_segment_definition_with_rule_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if create_segment_definition_with_rule_request is not None:
            _body_params = create_segment_definition_with_rule_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/segment/definition/create-with-rule',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def evaluate_segment(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        evaluate_segment_request: Optional[SensorsdataHorizonV1EvaluateSegmentRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataCommonHttpApiResult:
        """触发分群计算

        触发分群计算

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param evaluate_segment_request:
        :type evaluate_segment_request: SensorsdataHorizonV1EvaluateSegmentRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._evaluate_segment_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            evaluate_segment_request=evaluate_segment_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def evaluate_segment_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        evaluate_segment_request: Optional[SensorsdataHorizonV1EvaluateSegmentRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataCommonHttpApiResult]:
        """触发分群计算

        触发分群计算

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param evaluate_segment_request:
        :type evaluate_segment_request: SensorsdataHorizonV1EvaluateSegmentRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._evaluate_segment_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            evaluate_segment_request=evaluate_segment_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def evaluate_segment_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        evaluate_segment_request: Optional[SensorsdataHorizonV1EvaluateSegmentRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """触发分群计算

        触发分群计算

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param evaluate_segment_request:
        :type evaluate_segment_request: SensorsdataHorizonV1EvaluateSegmentRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._evaluate_segment_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            evaluate_segment_request=evaluate_segment_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _evaluate_segment_serialize(
        self,
        api_key,
        sensorsdata_project,
        evaluate_segment_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if evaluate_segment_request is not None:
            _body_params = evaluate_segment_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/segment/definition/evaluate',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def get_segment_definition(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_segment_definition_request: Optional[SensorsdataHorizonV1GetSegmentDefinitionRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1SegmentDefinitionHttpApiResult:
        """获取分群信息

        获取分群信息

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_segment_definition_request:
        :type get_segment_definition_request: SensorsdataHorizonV1GetSegmentDefinitionRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_segment_definition_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_segment_definition_request=get_segment_definition_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1SegmentDefinitionHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def get_segment_definition_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_segment_definition_request: Optional[SensorsdataHorizonV1GetSegmentDefinitionRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1SegmentDefinitionHttpApiResult]:
        """获取分群信息

        获取分群信息

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_segment_definition_request:
        :type get_segment_definition_request: SensorsdataHorizonV1GetSegmentDefinitionRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_segment_definition_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_segment_definition_request=get_segment_definition_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1SegmentDefinitionHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def get_segment_definition_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_segment_definition_request: Optional[SensorsdataHorizonV1GetSegmentDefinitionRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """获取分群信息

        获取分群信息

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_segment_definition_request:
        :type get_segment_definition_request: SensorsdataHorizonV1GetSegmentDefinitionRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_segment_definition_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_segment_definition_request=get_segment_definition_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1SegmentDefinitionHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _get_segment_definition_serialize(
        self,
        api_key,
        sensorsdata_project,
        get_segment_definition_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if get_segment_definition_request is not None:
            _body_params = get_segment_definition_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/segment/definition/get',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def get_segment_task(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_segment_task_request: Optional[SensorsdataHorizonV1GetSegmentTaskRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1GetSegmentTaskResponseHttpApiResult:
        """获取分群任务状态

        获取分群任务状态

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_segment_task_request:
        :type get_segment_task_request: SensorsdataHorizonV1GetSegmentTaskRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_segment_task_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_segment_task_request=get_segment_task_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1GetSegmentTaskResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def get_segment_task_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_segment_task_request: Optional[SensorsdataHorizonV1GetSegmentTaskRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1GetSegmentTaskResponseHttpApiResult]:
        """获取分群任务状态

        获取分群任务状态

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_segment_task_request:
        :type get_segment_task_request: SensorsdataHorizonV1GetSegmentTaskRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_segment_task_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_segment_task_request=get_segment_task_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1GetSegmentTaskResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def get_segment_task_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_segment_task_request: Optional[SensorsdataHorizonV1GetSegmentTaskRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """获取分群任务状态

        获取分群任务状态

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_segment_task_request:
        :type get_segment_task_request: SensorsdataHorizonV1GetSegmentTaskRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_segment_task_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_segment_task_request=get_segment_task_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1GetSegmentTaskResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _get_segment_task_serialize(
        self,
        api_key,
        sensorsdata_project,
        get_segment_task_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if get_segment_task_request is not None:
            _body_params = get_segment_task_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/segment/task/get',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def list_segment_definitions(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_segment_definitions_request: Optional[SensorsdataHorizonV1ListSegmentDefinitionsRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1ListSegmentDefinitionsResponseHttpApiResult:
        """获取分群列表

        获取分群列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_segment_definitions_request:
        :type list_segment_definitions_request: SensorsdataHorizonV1ListSegmentDefinitionsRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_segment_definitions_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_segment_definitions_request=list_segment_definitions_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListSegmentDefinitionsResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def list_segment_definitions_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_segment_definitions_request: Optional[SensorsdataHorizonV1ListSegmentDefinitionsRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1ListSegmentDefinitionsResponseHttpApiResult]:
        """获取分群列表

        获取分群列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_segment_definitions_request:
        :type list_segment_definitions_request: SensorsdataHorizonV1ListSegmentDefinitionsRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_segment_definitions_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_segment_definitions_request=list_segment_definitions_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListSegmentDefinitionsResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def list_segment_definitions_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_segment_definitions_request: Optional[SensorsdataHorizonV1ListSegmentDefinitionsRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """获取分群列表

        获取分群列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_segment_definitions_request:
        :type list_segment_definitions_request: SensorsdataHorizonV1ListSegmentDefinitionsRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_segment_definitions_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_segment_definitions_request=list_segment_definitions_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListSegmentDefinitionsResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _list_segment_definitions_serialize(
        self,
        api_key,
        sensorsdata_project,
        list_segment_definitions_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if list_segment_definitions_request is not None:
            _body_params = list_segment_definitions_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/segment/definition/list',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def list_segment_items(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_segment_items_request: Optional[SensorsdataHorizonV1ListSegmentItemsRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1ListSegmentItemsResponseHttpApiResult:
        """获取分群定义关联的分群包元素

        获取分群定义关联的分群包元素

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_segment_items_request:
        :type list_segment_items_request: SensorsdataHorizonV1ListSegmentItemsRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_segment_items_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_segment_items_request=list_segment_items_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListSegmentItemsResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def list_segment_items_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_segment_items_request: Optional[SensorsdataHorizonV1ListSegmentItemsRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1ListSegmentItemsResponseHttpApiResult]:
        """获取分群定义关联的分群包元素

        获取分群定义关联的分群包元素

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_segment_items_request:
        :type list_segment_items_request: SensorsdataHorizonV1ListSegmentItemsRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_segment_items_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_segment_items_request=list_segment_items_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListSegmentItemsResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def list_segment_items_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_segment_items_request: Optional[SensorsdataHorizonV1ListSegmentItemsRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """获取分群定义关联的分群包元素

        获取分群定义关联的分群包元素

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_segment_items_request:
        :type list_segment_items_request: SensorsdataHorizonV1ListSegmentItemsRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_segment_items_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_segment_items_request=list_segment_items_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListSegmentItemsResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _list_segment_items_serialize(
        self,
        api_key,
        sensorsdata_project,
        list_segment_items_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if list_segment_items_request is not None:
            _body_params = list_segment_items_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/segment/item/list',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def update_segment_definition_with_rule(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        update_segment_definition_with_rule_request: Optional[SensorsdataHorizonV1UpdateSegmentDefinitionWithRuleRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1SegmentDefinitionHttpApiResult:
        """编辑分群

        编辑分群

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param update_segment_definition_with_rule_request:
        :type update_segment_definition_with_rule_request: SensorsdataHorizonV1UpdateSegmentDefinitionWithRuleRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._update_segment_definition_with_rule_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            update_segment_definition_with_rule_request=update_segment_definition_with_rule_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1SegmentDefinitionHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def update_segment_definition_with_rule_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        update_segment_definition_with_rule_request: Optional[SensorsdataHorizonV1UpdateSegmentDefinitionWithRuleRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1SegmentDefinitionHttpApiResult]:
        """编辑分群

        编辑分群

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param update_segment_definition_with_rule_request:
        :type update_segment_definition_with_rule_request: SensorsdataHorizonV1UpdateSegmentDefinitionWithRuleRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._update_segment_definition_with_rule_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            update_segment_definition_with_rule_request=update_segment_definition_with_rule_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1SegmentDefinitionHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def update_segment_definition_with_rule_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        update_segment_definition_with_rule_request: Optional[SensorsdataHorizonV1UpdateSegmentDefinitionWithRuleRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """编辑分群

        编辑分群

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param update_segment_definition_with_rule_request:
        :type update_segment_definition_with_rule_request: SensorsdataHorizonV1UpdateSegmentDefinitionWithRuleRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._update_segment_definition_with_rule_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            update_segment_definition_with_rule_request=update_segment_definition_with_rule_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1SegmentDefinitionHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _update_segment_definition_with_rule_serialize(
        self,
        api_key,
        sensorsdata_project,
        update_segment_definition_with_rule_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if update_segment_definition_with_rule_request is not None:
            _body_params = update_segment_definition_with_rule_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/segment/definition/update-with-rule',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def update_segment_scheduler_status(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        update_segment_scheduler_status_request: Optional[SensorsdataHorizonV1UpdateSegmentSchedulerStatusRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataCommonHttpApiResult:
        """更新调度状态

        更新调度状态

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param update_segment_scheduler_status_request:
        :type update_segment_scheduler_status_request: SensorsdataHorizonV1UpdateSegmentSchedulerStatusRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._update_segment_scheduler_status_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            update_segment_scheduler_status_request=update_segment_scheduler_status_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def update_segment_scheduler_status_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        update_segment_scheduler_status_request: Optional[SensorsdataHorizonV1UpdateSegmentSchedulerStatusRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataCommonHttpApiResult]:
        """更新调度状态

        更新调度状态

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param update_segment_scheduler_status_request:
        :type update_segment_scheduler_status_request: SensorsdataHorizonV1UpdateSegmentSchedulerStatusRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._update_segment_scheduler_status_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            update_segment_scheduler_status_request=update_segment_scheduler_status_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def update_segment_scheduler_status_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        update_segment_scheduler_status_request: Optional[SensorsdataHorizonV1UpdateSegmentSchedulerStatusRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """更新调度状态

        更新调度状态

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param update_segment_scheduler_status_request:
        :type update_segment_scheduler_status_request: SensorsdataHorizonV1UpdateSegmentSchedulerStatusRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._update_segment_scheduler_status_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            update_segment_scheduler_status_request=update_segment_scheduler_status_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _update_segment_scheduler_status_serialize(
        self,
        api_key,
        sensorsdata_project,
        update_segment_scheduler_status_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if update_segment_scheduler_status_request is not None:
            _body_params = update_segment_scheduler_status_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/segment/definition/status/update',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )


