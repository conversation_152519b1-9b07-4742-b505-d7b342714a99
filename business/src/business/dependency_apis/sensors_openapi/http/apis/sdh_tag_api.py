# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501

import warnings
from pydantic import validate_call, Field, StrictFloat, StrictStr, StrictInt
from typing import Any, Dict, List, Optional, Tuple, Union
from typing_extensions import Annotated

from pydantic import Field, StrictStr
from typing import Optional
from typing_extensions import Annotated
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_common_http_api_result import SensorsdataCommonHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_cancel_tag_task_request import SensorsdataHorizonV1CancelTagTaskRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_create_tag_definition_request import SensorsdataHorizonV1CreateTagDefinitionRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_evaluate_tag_request import SensorsdataHorizonV1EvaluateTagRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_lasted_tag_partition_request import SensorsdataHorizonV1GetLastedTagPartitionRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_lasted_tag_partition_response_http_api_result import SensorsdataHorizonV1GetLastedTagPartitionResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_tag_definition_request import SensorsdataHorizonV1GetTagDefinitionRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_tag_task_request import SensorsdataHorizonV1GetTagTaskRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_get_tag_task_response_http_api_result import SensorsdataHorizonV1GetTagTaskResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_tag_definitions_request import SensorsdataHorizonV1ListTagDefinitionsRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_tag_definitions_response_http_api_result import SensorsdataHorizonV1ListTagDefinitionsResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_tag_partitions_request import SensorsdataHorizonV1ListTagPartitionsRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_tag_partitions_response_http_api_result import SensorsdataHorizonV1ListTagPartitionsResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_tag_definition_http_api_result import SensorsdataHorizonV1TagDefinitionHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_update_tag_definition_request import SensorsdataHorizonV1UpdateTagDefinitionRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_update_tag_scheduler_status_request import SensorsdataHorizonV1UpdateTagSchedulerStatusRequest

from business.dependency_apis.sensors_openapi.http.api_client import ApiClient, RequestSerialized
from business.dependency_apis.sensors_openapi.http.api_response import ApiResponse
from business.dependency_apis.sensors_openapi.http.rest import RESTResponseType


class SdhTagApi:
    """NOTE: This class is auto generated by OpenAPI Generator
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    def __init__(self, api_client=None) -> None:
        if api_client is None:
            api_client = ApiClient.get_default()
        self.api_client = api_client


    @validate_call
    async def cancel_tag_task(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        cancel_tag_task_request: Optional[SensorsdataHorizonV1CancelTagTaskRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataCommonHttpApiResult:
        """停止计算任务

        停止计算任务

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param cancel_tag_task_request:
        :type cancel_tag_task_request: SensorsdataHorizonV1CancelTagTaskRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._cancel_tag_task_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            cancel_tag_task_request=cancel_tag_task_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def cancel_tag_task_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        cancel_tag_task_request: Optional[SensorsdataHorizonV1CancelTagTaskRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataCommonHttpApiResult]:
        """停止计算任务

        停止计算任务

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param cancel_tag_task_request:
        :type cancel_tag_task_request: SensorsdataHorizonV1CancelTagTaskRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._cancel_tag_task_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            cancel_tag_task_request=cancel_tag_task_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def cancel_tag_task_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        cancel_tag_task_request: Optional[SensorsdataHorizonV1CancelTagTaskRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """停止计算任务

        停止计算任务

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param cancel_tag_task_request:
        :type cancel_tag_task_request: SensorsdataHorizonV1CancelTagTaskRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._cancel_tag_task_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            cancel_tag_task_request=cancel_tag_task_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _cancel_tag_task_serialize(
        self,
        api_key,
        sensorsdata_project,
        cancel_tag_task_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if cancel_tag_task_request is not None:
            _body_params = cancel_tag_task_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/tag/task/cancel',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def create_tag_definition(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        create_tag_definition_request: Optional[SensorsdataHorizonV1CreateTagDefinitionRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1TagDefinitionHttpApiResult:
        """创建标签定义

        创建标签定义

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param create_tag_definition_request:
        :type create_tag_definition_request: SensorsdataHorizonV1CreateTagDefinitionRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._create_tag_definition_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            create_tag_definition_request=create_tag_definition_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1TagDefinitionHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def create_tag_definition_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        create_tag_definition_request: Optional[SensorsdataHorizonV1CreateTagDefinitionRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1TagDefinitionHttpApiResult]:
        """创建标签定义

        创建标签定义

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param create_tag_definition_request:
        :type create_tag_definition_request: SensorsdataHorizonV1CreateTagDefinitionRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._create_tag_definition_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            create_tag_definition_request=create_tag_definition_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1TagDefinitionHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def create_tag_definition_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        create_tag_definition_request: Optional[SensorsdataHorizonV1CreateTagDefinitionRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """创建标签定义

        创建标签定义

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param create_tag_definition_request:
        :type create_tag_definition_request: SensorsdataHorizonV1CreateTagDefinitionRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._create_tag_definition_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            create_tag_definition_request=create_tag_definition_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1TagDefinitionHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _create_tag_definition_serialize(
        self,
        api_key,
        sensorsdata_project,
        create_tag_definition_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if create_tag_definition_request is not None:
            _body_params = create_tag_definition_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/tag/definition/create',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def evaluate_tag(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        evaluate_tag_request: Optional[SensorsdataHorizonV1EvaluateTagRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataCommonHttpApiResult:
        """执行标签计算

        执行标签计算

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param evaluate_tag_request:
        :type evaluate_tag_request: SensorsdataHorizonV1EvaluateTagRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._evaluate_tag_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            evaluate_tag_request=evaluate_tag_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def evaluate_tag_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        evaluate_tag_request: Optional[SensorsdataHorizonV1EvaluateTagRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataCommonHttpApiResult]:
        """执行标签计算

        执行标签计算

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param evaluate_tag_request:
        :type evaluate_tag_request: SensorsdataHorizonV1EvaluateTagRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._evaluate_tag_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            evaluate_tag_request=evaluate_tag_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def evaluate_tag_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        evaluate_tag_request: Optional[SensorsdataHorizonV1EvaluateTagRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """执行标签计算

        执行标签计算

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param evaluate_tag_request:
        :type evaluate_tag_request: SensorsdataHorizonV1EvaluateTagRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._evaluate_tag_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            evaluate_tag_request=evaluate_tag_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _evaluate_tag_serialize(
        self,
        api_key,
        sensorsdata_project,
        evaluate_tag_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if evaluate_tag_request is not None:
            _body_params = evaluate_tag_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/tag/definition/evaluate',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def get_lasted_tag_partition(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_lasted_tag_partition_request: Optional[SensorsdataHorizonV1GetLastedTagPartitionRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1GetLastedTagPartitionResponseHttpApiResult:
        """获取最新的 partition 信息

        获取最新的 partition 信息

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_lasted_tag_partition_request:
        :type get_lasted_tag_partition_request: SensorsdataHorizonV1GetLastedTagPartitionRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_lasted_tag_partition_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_lasted_tag_partition_request=get_lasted_tag_partition_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1GetLastedTagPartitionResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def get_lasted_tag_partition_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_lasted_tag_partition_request: Optional[SensorsdataHorizonV1GetLastedTagPartitionRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1GetLastedTagPartitionResponseHttpApiResult]:
        """获取最新的 partition 信息

        获取最新的 partition 信息

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_lasted_tag_partition_request:
        :type get_lasted_tag_partition_request: SensorsdataHorizonV1GetLastedTagPartitionRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_lasted_tag_partition_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_lasted_tag_partition_request=get_lasted_tag_partition_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1GetLastedTagPartitionResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def get_lasted_tag_partition_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_lasted_tag_partition_request: Optional[SensorsdataHorizonV1GetLastedTagPartitionRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """获取最新的 partition 信息

        获取最新的 partition 信息

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_lasted_tag_partition_request:
        :type get_lasted_tag_partition_request: SensorsdataHorizonV1GetLastedTagPartitionRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_lasted_tag_partition_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_lasted_tag_partition_request=get_lasted_tag_partition_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1GetLastedTagPartitionResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _get_lasted_tag_partition_serialize(
        self,
        api_key,
        sensorsdata_project,
        get_lasted_tag_partition_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if get_lasted_tag_partition_request is not None:
            _body_params = get_lasted_tag_partition_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/tag/latest-partition/get',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def get_tag_definition(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_tag_definition_request: Optional[SensorsdataHorizonV1GetTagDefinitionRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1TagDefinitionHttpApiResult:
        """查询单个标签信息

        查询单个标签信息

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_tag_definition_request:
        :type get_tag_definition_request: SensorsdataHorizonV1GetTagDefinitionRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_tag_definition_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_tag_definition_request=get_tag_definition_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1TagDefinitionHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def get_tag_definition_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_tag_definition_request: Optional[SensorsdataHorizonV1GetTagDefinitionRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1TagDefinitionHttpApiResult]:
        """查询单个标签信息

        查询单个标签信息

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_tag_definition_request:
        :type get_tag_definition_request: SensorsdataHorizonV1GetTagDefinitionRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_tag_definition_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_tag_definition_request=get_tag_definition_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1TagDefinitionHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def get_tag_definition_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_tag_definition_request: Optional[SensorsdataHorizonV1GetTagDefinitionRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """查询单个标签信息

        查询单个标签信息

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_tag_definition_request:
        :type get_tag_definition_request: SensorsdataHorizonV1GetTagDefinitionRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_tag_definition_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_tag_definition_request=get_tag_definition_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1TagDefinitionHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _get_tag_definition_serialize(
        self,
        api_key,
        sensorsdata_project,
        get_tag_definition_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if get_tag_definition_request is not None:
            _body_params = get_tag_definition_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/tag/definition/get',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def get_tag_task(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_tag_task_request: Optional[SensorsdataHorizonV1GetTagTaskRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1GetTagTaskResponseHttpApiResult:
        """获取标签任务信息

        获取标签任务信息

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_tag_task_request:
        :type get_tag_task_request: SensorsdataHorizonV1GetTagTaskRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_tag_task_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_tag_task_request=get_tag_task_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1GetTagTaskResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def get_tag_task_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_tag_task_request: Optional[SensorsdataHorizonV1GetTagTaskRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1GetTagTaskResponseHttpApiResult]:
        """获取标签任务信息

        获取标签任务信息

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_tag_task_request:
        :type get_tag_task_request: SensorsdataHorizonV1GetTagTaskRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_tag_task_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_tag_task_request=get_tag_task_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1GetTagTaskResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def get_tag_task_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_tag_task_request: Optional[SensorsdataHorizonV1GetTagTaskRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """获取标签任务信息

        获取标签任务信息

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_tag_task_request:
        :type get_tag_task_request: SensorsdataHorizonV1GetTagTaskRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_tag_task_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_tag_task_request=get_tag_task_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1GetTagTaskResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _get_tag_task_serialize(
        self,
        api_key,
        sensorsdata_project,
        get_tag_task_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if get_tag_task_request is not None:
            _body_params = get_tag_task_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/tag/task/get',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def list_tag_definitions(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_tag_definitions_request: Optional[SensorsdataHorizonV1ListTagDefinitionsRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1ListTagDefinitionsResponseHttpApiResult:
        """查询标签列表

        查询标签列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_tag_definitions_request:
        :type list_tag_definitions_request: SensorsdataHorizonV1ListTagDefinitionsRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_tag_definitions_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_tag_definitions_request=list_tag_definitions_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListTagDefinitionsResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def list_tag_definitions_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_tag_definitions_request: Optional[SensorsdataHorizonV1ListTagDefinitionsRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1ListTagDefinitionsResponseHttpApiResult]:
        """查询标签列表

        查询标签列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_tag_definitions_request:
        :type list_tag_definitions_request: SensorsdataHorizonV1ListTagDefinitionsRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_tag_definitions_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_tag_definitions_request=list_tag_definitions_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListTagDefinitionsResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def list_tag_definitions_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_tag_definitions_request: Optional[SensorsdataHorizonV1ListTagDefinitionsRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """查询标签列表

        查询标签列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_tag_definitions_request:
        :type list_tag_definitions_request: SensorsdataHorizonV1ListTagDefinitionsRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_tag_definitions_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_tag_definitions_request=list_tag_definitions_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListTagDefinitionsResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _list_tag_definitions_serialize(
        self,
        api_key,
        sensorsdata_project,
        list_tag_definitions_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if list_tag_definitions_request is not None:
            _body_params = list_tag_definitions_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/tag/definition/list',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def list_tag_partitions(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_tag_partitions_request: Optional[SensorsdataHorizonV1ListTagPartitionsRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1ListTagPartitionsResponseHttpApiResult:
        """获取标签的 partition 列表

        获取标签的 partition 列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_tag_partitions_request:
        :type list_tag_partitions_request: SensorsdataHorizonV1ListTagPartitionsRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_tag_partitions_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_tag_partitions_request=list_tag_partitions_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListTagPartitionsResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def list_tag_partitions_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_tag_partitions_request: Optional[SensorsdataHorizonV1ListTagPartitionsRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1ListTagPartitionsResponseHttpApiResult]:
        """获取标签的 partition 列表

        获取标签的 partition 列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_tag_partitions_request:
        :type list_tag_partitions_request: SensorsdataHorizonV1ListTagPartitionsRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_tag_partitions_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_tag_partitions_request=list_tag_partitions_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListTagPartitionsResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def list_tag_partitions_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        list_tag_partitions_request: Optional[SensorsdataHorizonV1ListTagPartitionsRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """获取标签的 partition 列表

        获取标签的 partition 列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param list_tag_partitions_request:
        :type list_tag_partitions_request: SensorsdataHorizonV1ListTagPartitionsRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_tag_partitions_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            list_tag_partitions_request=list_tag_partitions_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1ListTagPartitionsResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _list_tag_partitions_serialize(
        self,
        api_key,
        sensorsdata_project,
        list_tag_partitions_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if list_tag_partitions_request is not None:
            _body_params = list_tag_partitions_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/tag/partition/get',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def update_tag_definition(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        update_tag_definition_request: Optional[SensorsdataHorizonV1UpdateTagDefinitionRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataHorizonV1TagDefinitionHttpApiResult:
        """修改标签定义

        修改标签定义

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param update_tag_definition_request:
        :type update_tag_definition_request: SensorsdataHorizonV1UpdateTagDefinitionRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._update_tag_definition_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            update_tag_definition_request=update_tag_definition_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1TagDefinitionHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def update_tag_definition_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        update_tag_definition_request: Optional[SensorsdataHorizonV1UpdateTagDefinitionRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataHorizonV1TagDefinitionHttpApiResult]:
        """修改标签定义

        修改标签定义

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param update_tag_definition_request:
        :type update_tag_definition_request: SensorsdataHorizonV1UpdateTagDefinitionRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._update_tag_definition_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            update_tag_definition_request=update_tag_definition_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1TagDefinitionHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def update_tag_definition_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        update_tag_definition_request: Optional[SensorsdataHorizonV1UpdateTagDefinitionRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """修改标签定义

        修改标签定义

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param update_tag_definition_request:
        :type update_tag_definition_request: SensorsdataHorizonV1UpdateTagDefinitionRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._update_tag_definition_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            update_tag_definition_request=update_tag_definition_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataHorizonV1TagDefinitionHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _update_tag_definition_serialize(
        self,
        api_key,
        sensorsdata_project,
        update_tag_definition_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if update_tag_definition_request is not None:
            _body_params = update_tag_definition_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/tag/definition/update',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def update_tag_scheduler_status(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        update_tag_scheduler_status_request: Optional[SensorsdataHorizonV1UpdateTagSchedulerStatusRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataCommonHttpApiResult:
        """更改调度状态

        更改调度状态

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param update_tag_scheduler_status_request:
        :type update_tag_scheduler_status_request: SensorsdataHorizonV1UpdateTagSchedulerStatusRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._update_tag_scheduler_status_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            update_tag_scheduler_status_request=update_tag_scheduler_status_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def update_tag_scheduler_status_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        update_tag_scheduler_status_request: Optional[SensorsdataHorizonV1UpdateTagSchedulerStatusRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataCommonHttpApiResult]:
        """更改调度状态

        更改调度状态

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param update_tag_scheduler_status_request:
        :type update_tag_scheduler_status_request: SensorsdataHorizonV1UpdateTagSchedulerStatusRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._update_tag_scheduler_status_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            update_tag_scheduler_status_request=update_tag_scheduler_status_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def update_tag_scheduler_status_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        update_tag_scheduler_status_request: Optional[SensorsdataHorizonV1UpdateTagSchedulerStatusRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """更改调度状态

        更改调度状态

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param update_tag_scheduler_status_request:
        :type update_tag_scheduler_status_request: SensorsdataHorizonV1UpdateTagSchedulerStatusRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._update_tag_scheduler_status_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            update_tag_scheduler_status_request=update_tag_scheduler_status_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataCommonHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _update_tag_scheduler_status_serialize(
        self,
        api_key,
        sensorsdata_project,
        update_tag_scheduler_status_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if update_tag_scheduler_status_request is not None:
            _body_params = update_tag_scheduler_status_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/horizon/v1/tag/definition/status/update',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )


