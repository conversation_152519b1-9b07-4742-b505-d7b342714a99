# coding: utf-8

"""
    OpenAPI 文档

    Generate by openapi-generator-cli

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501

import warnings
from pydantic import validate_call, Field, StrictFloat, StrictStr, StrictInt
from typing import Any, Dict, List, Optional, Tuple, Union
from typing_extensions import Annotated

from pydantic import Field, StrictStr
from typing import Optional
from typing_extensions import Annotated
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_event_property_all_response_http_api_result import SensorsdataAnalyticsV1EventPropertyAllResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_event_property_request import SensorsdataAnalyticsV1EventPropertyRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_event_property_response_http_api_result import SensorsdataAnalyticsV1EventPropertyResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_get_property_value_request import SensorsdataAnalyticsV1GetPropertyValueRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_get_property_value_response_http_api_result import SensorsdataAnalyticsV1GetPropertyValueResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_user_group_all_response_http_api_result import SensorsdataAnalyticsV1UserGroupAllResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_user_property_all_response_http_api_result import SensorsdataAnalyticsV1UserPropertyAllResponseHttpApiResult
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_user_tag_dir_response_http_api_result import SensorsdataAnalyticsV1UserTagDirResponseHttpApiResult

from business.dependency_apis.sensors_openapi.http.api_client import ApiClient, RequestSerialized
from business.dependency_apis.sensors_openapi.http.api_response import ApiResponse
from business.dependency_apis.sensors_openapi.http.rest import RESTResponseType


class SaPropertyMetaApi:
    """NOTE: This class is auto generated by OpenAPI Generator
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    def __init__(self, api_client=None) -> None:
        if api_client is None:
            api_client = ApiClient.get_default()
        self.api_client = api_client


    @validate_call
    async def get_property_values(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_property_value_request: Optional[SensorsdataAnalyticsV1GetPropertyValueRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataAnalyticsV1GetPropertyValueResponseHttpApiResult:
        """获取属性候选值

        获取属性候选值

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_property_value_request:
        :type get_property_value_request: SensorsdataAnalyticsV1GetPropertyValueRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_property_values_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_property_value_request=get_property_value_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataAnalyticsV1GetPropertyValueResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def get_property_values_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_property_value_request: Optional[SensorsdataAnalyticsV1GetPropertyValueRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataAnalyticsV1GetPropertyValueResponseHttpApiResult]:
        """获取属性候选值

        获取属性候选值

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_property_value_request:
        :type get_property_value_request: SensorsdataAnalyticsV1GetPropertyValueRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_property_values_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_property_value_request=get_property_value_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataAnalyticsV1GetPropertyValueResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def get_property_values_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        get_property_value_request: Optional[SensorsdataAnalyticsV1GetPropertyValueRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """获取属性候选值

        获取属性候选值

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param get_property_value_request:
        :type get_property_value_request: SensorsdataAnalyticsV1GetPropertyValueRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._get_property_values_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            get_property_value_request=get_property_value_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataAnalyticsV1GetPropertyValueResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _get_property_values_serialize(
        self,
        api_key,
        sensorsdata_project,
        get_property_value_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if get_property_value_request is not None:
            _body_params = get_property_value_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/analytics/v1/property-meta/property/values',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def list_all_event_properties(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataAnalyticsV1EventPropertyAllResponseHttpApiResult:
        """获取所有事件属性

        获取所有事件属性列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_all_event_properties_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataAnalyticsV1EventPropertyAllResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def list_all_event_properties_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataAnalyticsV1EventPropertyAllResponseHttpApiResult]:
        """获取所有事件属性

        获取所有事件属性列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_all_event_properties_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataAnalyticsV1EventPropertyAllResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def list_all_event_properties_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """获取所有事件属性

        获取所有事件属性列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_all_event_properties_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataAnalyticsV1EventPropertyAllResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _list_all_event_properties_serialize(
        self,
        api_key,
        sensorsdata_project,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )


        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='GET',
            resource_path='/api/v3/analytics/v1/property-meta/event-properties/all',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def list_all_user_properties(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataAnalyticsV1UserPropertyAllResponseHttpApiResult:
        """获取所有用户属性列表

        获取所有用户属性列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_all_user_properties_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataAnalyticsV1UserPropertyAllResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def list_all_user_properties_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataAnalyticsV1UserPropertyAllResponseHttpApiResult]:
        """获取所有用户属性列表

        获取所有用户属性列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_all_user_properties_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataAnalyticsV1UserPropertyAllResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def list_all_user_properties_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """获取所有用户属性列表

        获取所有用户属性列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_all_user_properties_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataAnalyticsV1UserPropertyAllResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _list_all_user_properties_serialize(
        self,
        api_key,
        sensorsdata_project,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )


        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='GET',
            resource_path='/api/v3/analytics/v1/property-meta/user-properties/all',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def list_event_properties(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        event_property_request: Optional[SensorsdataAnalyticsV1EventPropertyRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataAnalyticsV1EventPropertyResponseHttpApiResult:
        """获取指定的事件和相关属性

        查询指定事件和属性列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param event_property_request:
        :type event_property_request: SensorsdataAnalyticsV1EventPropertyRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_event_properties_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            event_property_request=event_property_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataAnalyticsV1EventPropertyResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def list_event_properties_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        event_property_request: Optional[SensorsdataAnalyticsV1EventPropertyRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataAnalyticsV1EventPropertyResponseHttpApiResult]:
        """获取指定的事件和相关属性

        查询指定事件和属性列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param event_property_request:
        :type event_property_request: SensorsdataAnalyticsV1EventPropertyRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_event_properties_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            event_property_request=event_property_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataAnalyticsV1EventPropertyResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def list_event_properties_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        event_property_request: Optional[SensorsdataAnalyticsV1EventPropertyRequest] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """获取指定的事件和相关属性

        查询指定事件和属性列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param event_property_request:
        :type event_property_request: SensorsdataAnalyticsV1EventPropertyRequest
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_event_properties_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            event_property_request=event_property_request,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataAnalyticsV1EventPropertyResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _list_event_properties_serialize(
        self,
        api_key,
        sensorsdata_project,
        event_property_request,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter
        if event_property_request is not None:
            _body_params = event_property_request


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )

        # set the HTTP header `Content-Type`
        if _content_type:
            _header_params['Content-Type'] = _content_type
        else:
            _default_content_type = (
                self.api_client.select_header_content_type(
                    [
                        'application/json'
                    ]
                )
            )
            if _default_content_type is not None:
                _header_params['Content-Type'] = _default_content_type

        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/v3/analytics/v1/property-meta/event-properties',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def list_user_groups(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataAnalyticsV1UserGroupAllResponseHttpApiResult:
        """获取所有用户分群列表

        获取所有用户分群列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_user_groups_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataAnalyticsV1UserGroupAllResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def list_user_groups_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataAnalyticsV1UserGroupAllResponseHttpApiResult]:
        """获取所有用户分群列表

        获取所有用户分群列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_user_groups_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataAnalyticsV1UserGroupAllResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def list_user_groups_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """获取所有用户分群列表

        获取所有用户分群列表

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_user_groups_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataAnalyticsV1UserGroupAllResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _list_user_groups_serialize(
        self,
        api_key,
        sensorsdata_project,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )


        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='GET',
            resource_path='/api/v3/analytics/v1/property-meta/user-groups/all',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )




    @validate_call
    async def list_user_tags_with_dir(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> SensorsdataAnalyticsV1UserTagDirResponseHttpApiResult:
        """获取带有目录结构的用户标签列表

        获取带有目录结构的用户标签信息

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_user_tags_with_dir_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataAnalyticsV1UserTagDirResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    async def list_user_tags_with_dir_with_http_info(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[SensorsdataAnalyticsV1UserTagDirResponseHttpApiResult]:
        """获取带有目录结构的用户标签列表

        获取带有目录结构的用户标签信息

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_user_tags_with_dir_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataAnalyticsV1UserTagDirResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        await response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    async def list_user_tags_with_dir_without_preload_content(
        self,
        api_key: Annotated[StrictStr, Field(description="全局唯一的密钥，用于验证和授权访问 API 接口")],
        sensorsdata_project: Annotated[StrictStr, Field(description="项目名, 指定请求所属项目")],
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """获取带有目录结构的用户标签列表

        获取带有目录结构的用户标签信息

        :param api_key: 全局唯一的密钥，用于验证和授权访问 API 接口 (required)
        :type api_key: str
        :param sensorsdata_project: 项目名, 指定请求所属项目 (required)
        :type sensorsdata_project: str
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._list_user_tags_with_dir_serialize(
            api_key=api_key,
            sensorsdata_project=sensorsdata_project,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '2XX': "SensorsdataAnalyticsV1UserTagDirResponseHttpApiResult",
        }
        response_data = await self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _list_user_tags_with_dir_serialize(
        self,
        api_key,
        sensorsdata_project,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        # process the header parameters
        if api_key is not None:
            _header_params['api-key'] = api_key
        if sensorsdata_project is not None:
            _header_params['sensorsdata-project'] = sensorsdata_project
        # process the form parameters
        # process the body parameter


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json'
                ]
            )


        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='GET',
            resource_path='/api/v3/analytics/v1/property-meta/user-tags/dir',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )


