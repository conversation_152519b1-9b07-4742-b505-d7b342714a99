# 定义 api 依赖信息
# key 表示存储的目录(dependency_apis 相对路径)
# value 是配置信息, type 字段定义依赖类型, 其他配置字段根据类型定义
dependency_define = {
    "sensors_openapi/http": {
        "type": "openapi",
        "apis": {
            # SA 2.5.5 以上版本才支持该 API
            "SaEventMeta": "https://manual.sensorsdata.cn/openapi/api/v1/openapi_download?file_name=2.5.5-analytics-EventMeta-v1-swagger.json",
            "SaPropertyMeta": "https://manual.sensorsdata.cn/openapi/api/v1/openapi_download?file_name=2.5.5-analytics-PropertyMeta-v1-swagger.json",
            # SDH
            "SdhSchema": "https://manual.sensorsdata.cn/openapi/api/v1/openapi_download?file_name=1.3.1-horizon-Schema-v1-swagger.json",
            "SdhSegment": "https://manual.sensorsdata.cn/openapi/api/v1/openapi_download?file_name=1.3.1-horizon-Segment-v1-swagger.json",
            "SdhTag": "https://manual.sensorsdata.cn/openapi/api/v1/openapi_download?file_name=1.3.1-horizon-Tag-v1-swagger.json",
        },
        "path_with_server_url": True,
        "datetime_format": '%Y-%m-%d %H:%M:%S',
    }
}
