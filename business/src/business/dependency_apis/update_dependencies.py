import json
import os
import shutil
import subprocess
from typing import Dict

import requests
from dependencies import dependency_define

"""
更新项目依赖脚本
该脚本生成的依赖文件也会提交到仓库, 通过 git 维护变更
原则上该脚本仅在依赖的 api 变动时才需要执行
"""

# 当前脚本所在目录
CURRENT_SCRIPT_PATH = os.path.abspath(__file__)
DEPENDENCIES_DIR_PATH = os.path.dirname(os.path.abspath(__file__))
DEPENDENCIES_DIR_NAME = os.path.basename(DEPENDENCIES_DIR_PATH)


############################ 函数定义 ################################
def clear_dir(dir_path):
    """清空目录(删除重建)"""
    if os.path.exists(dir_path):
        shutil.rmtree(dir_path)
    os.makedirs(dir_path)


def move_dir_file(source_dir, destination_dir):
    """移动目录下的所有文件到目标目录"""
    for filename in os.listdir(source_dir):
        source_file_path = os.path.join(source_dir, filename)
        destination_path = os.path.join(destination_dir, filename)
        shutil.move(source_file_path, destination_path)
    shutil.rmtree(source_dir)


def replace_in_file(file_path, old_str, new_str):
    try:
        # 打开文件并读取内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        # 替换字符串
        new_content = content.replace(old_str, new_str)
        # 将替换后的内容写回文件
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(new_content)
    except Exception as e:
        print(f"ignore file replace error [file_path={file_path}, old_str={old_str}, new_str={new_str}, error={e}]")


def deep_merge_dicts(dict1, dict2):
    result = dict1.copy()
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = deep_merge_dicts(result[key], value)
        else:
            result[key] = value
    return result


def deduplicate_dicts(dict_list):
    seen = set()
    result = []
    for d in dict_list:
        # 将字典转换为元组，元组的元素是字典的键值对
        t = tuple(sorted(d.items()))
        if t not in seen:
            seen.add(t)
            result.append(d)
    return result


def gen_code_by_swagger_file(save_dir_name, config: Dict):
    """基于 swagger 文档生成 client 代码
    Args:
        save_dir_name: str; 保存代码的目录名称
        config: Dict; 配置信息
        {
            "apis": {}, # 定义 api 依赖信息
            "path_with_server_url": False, # 是否在 path 中加入 server url, 默认为 False, 若为 true, server url 必须在 swagger 文档中唯一定义
            "datetime_format": '%Y-%m-%dT%H:%M:%S%z' # 时间格式, 默认为 '%Y-%m-%dT%H:%M:%S%z'
        }
    """
    apis = config.get('apis')
    path_with_server_url = config.get('path_with_server_url', False)
    datetime_format = config.get('datetime_format', '%Y-%m-%dT%H:%M:%S%z')
    save_dir_path = os.path.join(DEPENDENCIES_DIR_PATH, save_dir_name)
    clear_dir(save_dir_path)
    code_package_name = DEPENDENCIES_DIR_NAME + "." + save_dir_name.replace("/", ".")
    final_code_dir_path = os.path.join(save_dir_path, code_package_name.replace(".", os.sep))
    api_define_mapping = {}
    for api_name, swagger_file_addr in apis.items():
        response = requests.get(swagger_file_addr)
        response.raise_for_status()
        api_define_mapping[api_name] = response.json()
    # 定义 tag 并在 path 中加入 tag
    final_swagger_define = {"openapi": "3.0.1",
                            "info": {
                                "title": "OpenAPI 文档",
                                "description": "Generate by openapi-generator-cli",
                                "version": "1.0.0"
                            },
                            'servers': deduplicate_dicts(
                                [y for x in api_define_mapping.values() for y in x.get('servers', [])]),
                            "tags": [
                                {"name": api_name, "description": api_define.get('info', {}).get('description', ''),
                                 "externalDocs": {"url": apis[api_name]}}
                                for api_name, api_define in api_define_mapping.items()]
                            }
    final_paths = {}
    final_components = {}
    for api_name, api_define in api_define_mapping.items():
        prefix_path = ''
        if path_with_server_url:
            servers = api_define.get('servers', [])
            if len(servers) == 1:
                server_url = servers[0].get('url')
                if server_url is not None and not server_url.startswith(('http://', 'https://')):
                    prefix_path = server_url
                    if prefix_path.endswith('/'):
                        prefix_path = prefix_path[:-1]
            if len(prefix_path) < 2:
                raise ValueError(
                    f"path_with_server_url is True, but server url is invalid({json.dumps(api_define.get('servers', []), ensure_ascii=False)}, {prefix_path}): " +
                    apis[api_name])
        for url_path, path_define in api_define.get('paths', {}).items():
            for api_operator in path_define.values():
                api_operator['tags'] = [api_name]
                response_def = api_operator.get('responses', {})
                # 兼容 OpenAPI 未严格按照规范定义 code 与 response 对应关系
                if response_def.get('default') is not None:
                    response_def['2XX'] = response_def['default']
                    del response_def['default']
            final_url_path = prefix_path + url_path
            if final_url_path in final_paths:
                raise ValueError("duplicate url path: " + final_url_path)
            final_paths[final_url_path] = path_define
        final_components = deep_merge_dicts(final_components, api_define.get('components', {}))
    final_swagger_define['paths'] = final_paths
    final_swagger_define['components'] = final_components
    swagger_file_path = os.path.join(save_dir_path, "swagger.json")
    with open(swagger_file_path, "w", encoding="utf-8") as f:
        f.write(json.dumps(final_swagger_define, ensure_ascii=False, indent=2))
    # 生成代码
    try:
        result = subprocess.run([
            "docker",
            "run",
            "--rm",
            "-u",
            f"{os.getuid()}:{os.getgid()}",
            "-v",
            f"{save_dir_path}:/local",
            "openapitools/openapi-generator-cli:latest-release",
            "generate",
            "--package-name",
            f"{code_package_name}",
            "--api-package",
            "apis",
            "--model-package",
            "models",
            "--skip-overwrite",
            "--http-user-agent",
            "SF-AI/python",
            f"--additional-properties=library=asyncio,setEnsureAsciiToFalse=true,generateSourceCodeOnly=true,datetimeFormat='{datetime_format}'",
            "--global-property=apiDocs,modelDocs,apiTests,modelTests=false",
            "-g",
            "python",
            "-o",
            "/local",
            "-i",
            f"{swagger_file_path.replace(save_dir_path, '/local')}"
        ], capture_output=True, text=True, check=True)
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        print(e.stderr)
        raise e
    move_dir_file(final_code_dir_path, save_dir_path)
    shutil.rmtree(os.path.join(save_dir_path, DEPENDENCIES_DIR_NAME))
    # TODO [BUG] __init__.py 文件中 api 导入路径不对, 暂时需手动改掉, 后续跟进新版本
    replace_in_file(os.path.join(save_dir_path, "__init__.py"), 'from apis',
                    f'from {code_package_name}.apis')
    replace_in_file(os.path.join(save_dir_path, "apis", "__init__.py"), 'from apis',
                    f'from {code_package_name}.apis')
    # TODO [BUG] 经过测试 setEnsureAsciiToFalse 配置并未生效, 官方源码中也没找到有处理这个配置项的代码
    #  可以暂时手动改掉 rest.py、api_client.py 中 json.dumps 逻辑, 后续跟进新版本
    replace_in_file(os.path.join(save_dir_path, "api_client.py"), 'json.dumps(v)',
                    'json.dumps(v, ensure_ascii=False)')
    replace_in_file(os.path.join(save_dir_path, "rest.py"), 'json.dumps(v)', 'json.dumps(v, ensure_ascii=False)')


############################ 更新依赖 ################################
# 更新依赖
if __name__ == '__main__':
    for dependency_name, dependency_config in dependency_define.items():
        if dependency_config['type'] == 'openapi':
            # 基于 openapi swagger 定义生成 client 代码
            gen_code_by_swagger_file(dependency_name, dependency_config)
            pass
        else:
            print(f"Unsupported dependency type: {dependency_config['type']}")
