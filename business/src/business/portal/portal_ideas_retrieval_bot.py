import json

from jinja2 import Template

from framework.agents.react import ReActAgent
from framework.agents.schema import Conversation, Role, ViewMessage
from common import tools as my_tools
from framework.prompt import PromptFactory
from .base_bot import BasicPortalBot
from .constants import DIFY_BASE_UTL, PLAIN_WORKFLOW_API_KEY
from .tools import KnowledgeRenderTool
from ..scenario.tools import DifyWorkflowTool

log = my_tools.get_logger()

FORMAT_ANSWER_PROMPT = """已知执行计划是一个包含多个步骤的，预计将按照这些步骤执行的计划。
他们的关系如下：
  - 一个执行计划中包含多个顶层执行步骤
  - 每个顶层执行步骤中包含多个子步骤
你的任务是根据知识内容，将知识内容整理成一个执行计划，并使用工具将执行计划渲染成可展示的知识格式（检索出来的所有内容只能被渲染成一个执行计划）。
渲染执行步骤的时候，需要按照知识格式内容，将该子步骤等一并渲染。每一个顶层的执行步骤的内容都需要尽可能详细一些。

**顶层执行步骤特殊性：**

顶层执行步骤需要展示给用户，如果顶层执行步骤粒度过粗，则用户看不到详细的执行内容，如果顶层执行步骤粒度过细，则用户会看到大量冗余内容，所以你需要仔细把握顶层步骤的粒度。

一个正确的、粒度合适的执行计划步骤分解示例：
  1. 先通过数据库查询 xx 数据
    1.1. 先查询当前数据库里面的 xx 数量
    1.2. ...
  2. 查询 xx 知识库，获取 xxx ...
  3. 验证数据准确性 ...
  4. 回复用户 ...
  
一个粒度过粗的执行计划示例（错误原因：把所有顶层步骤都放在了第一层）：
  1. 目标洞察分析执行计划 ...
    1.1. ...
    1.2. ...
  2. 回复用户 ...
  
注意事项：
  - 你不需要一个概括所有执行步骤的顶层执行步骤，顶层执行步骤应该是有多个步骤！
  - 知识的链接必须放在最顶层的 refers 中（与 resource_id 同级别）
  - 你的执行计划中，每个步骤的标题应该尽量贴切，每个步骤的内容应该尽量详细
  - 你的执行计划中，每个步骤的内容需要根据实际的知识内容来整理！你不能脱离实际知识而创造执行步骤！
  - 你不能省略知识中的分析步骤和细节，必须在你的执行计划中包含所有给你的知识内容！
  - 步骤中的 content 必须是完整的描述，不能是类似「进行以下分析：」这种不完整的描述！
  - 如果知识内容中存在判断环节，你应该在步骤标题中说明，比如步骤标题可以为：如果当前分析的目标类型为xxx
"""

USER_FORMAT_PROMPT = """我已检索到知识如下面<ideas></ideas>中所示：
<ideas>
{{ideas_response}}
</ideas>
请你按照要求整理知识，并使用工具将知识内容格式化成一个执行计划
"""


async def get_ideas_format_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="portal",
        business_cname="下一代产品 Demo",
        prompt_code="PORTAL_FORMAT_IDEAS_AS_PLAN_SYSTEM_PROMPT",
        prompt_name="Portal 检索思路格式化系统提示词",
        prompt_desc="Portal 检索思路格式化系统提示词",
        prompt_content=FORMAT_ANSWER_PROMPT,
    )
    return template


async def get_ideas_format_user_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="portal",
        business_cname="下一代产品 Demo",
        prompt_code="PORTAL_FORMAT_IDEAS_AS_PLAN_USER_PROMPT",
        prompt_name="Portal 检索思路格式化用户提示词",
        prompt_desc="Portal 检索思路格式化用户提示词",
        prompt_content=USER_FORMAT_PROMPT,
    )
    return template


class PortalIdeasRetrievalBot(BasicPortalBot):
    def __init__(
            self,
            result_limit: int = 3,
            scenario: str = "data_analyse",
            **kwargs,
    ):
        """
        Portal 思路检索机器人
        """
        kwargs = kwargs | {"name": "portal_ideas_retrieval_bot"}
        super().__init__(**kwargs)

        self.result_limit = result_limit
        self.tool = DifyWorkflowTool(
            dify_api_base=DIFY_BASE_UTL,
            dify_api_key=PLAIN_WORKFLOW_API_KEY,
            scene=scenario,
        )

    async def async_chat(self, history: list[Conversation]) -> Conversation:
        messages = [f"{h.role.name}: {h.content}" for h in history]
        ideas_response = await self.tool.arun(
            tool_input={
                "query": "\n".join(messages)
            }
        )

        log.info(f"检索到思路信息：{ideas_response}\n准备格式化：")

        resources = {}
        knowledge_render_tool = KnowledgeRenderTool(
            resource_callback=lambda id, resource: resources.update({id: resource})
        )
        answer_prompt = await get_ideas_format_prompt()
        agent = ReActAgent(
            tools=[knowledge_render_tool],
            llm=self.llm,
            verbose=True,
            answer_prompt=PromptFactory.render_prompt(answer_prompt),
            max_iterations=10
        )

        message = await get_ideas_format_user_prompt()
        response = await agent.achat(
            prompts=PromptFactory.render_prompt(message, ideas_response=ideas_response)
        )
        log.info(f"格式化 agent 返回：{response}")
        log.info(f"格式化 agent resources：{resources}")

        try:
            ideas_response = [_ for _ in resources.values()]
            if not ideas_response:
                log.error("无法格式化知识，请检查数据源！")
            else:
                new_ideas = []
                for idea in ideas_response:
                    if isinstance(idea, str):
                        new_ideas.append(json.loads(idea))
                    elif isinstance(idea, ViewMessage):
                        new_ideas.append(json.loads(idea.content))
                    else:
                        new_ideas.append(idea)
                ideas_response = new_ideas
                log.info(f"格式化后的结果：{json.dumps(ideas_response, ensure_ascii=False)}")

            return Conversation(
                role=Role.ASSISTANT,
                content=json.dumps(ideas_response, ensure_ascii=False)
            )
        except Exception as e:
            log.error(f"格式化知识失败：{e}")
            return Conversation(
                role=Role.ASSISTANT,
                content="[]"
            )
