#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import time
from typing import Any, List, Type
from pydantic import BaseModel, Field

from apis.definition.portal.portal_bot_pb2 import InsightParams
from business.portal.tools.cdp_sql_executor import CDPSqlExecutorTool
from langchain_core.tools import BaseTool
from common import tools
from framework.prompt import PromptFactory

log = tools.get_logger()

PORTAL_EXECUTION_PROGRESS_QUERY_TOOL_DESC=PromptFactory.create_or_get_prompt(
    business="portal",
    business_cname="下一代产品 Demo",
    prompt_code="PORTAL_EXECUTION_PROGRESS_QUERY_TOOL_DESC",
    prompt_name="Portal 执行进度查询工具描述",
    prompt_content="用于检索当前分析策略的执行度，会提供全选用户数、触达用户数、触达率等信息"
)

class _InnerModel(BaseModel):
    execution_threshold: float = Field(default=90.0, description="执行度阈值，默认90%")
    requirement_desc: str = Field(default="", description="本次分析的需求描述")


class PortalExecutionProgressQueryTool(BaseTool):
    name: str = "PortalExecutionProgressQueryTool"
    description: str = PromptFactory.render_prompt(PORTAL_EXECUTION_PROGRESS_QUERY_TOOL_DESC)
    args_schema: Type[BaseModel] = _InnerModel

    tenant_project_info: dict
    insight_params: InsightParams

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def _run(self, execution_threshold: float = 90.0, requirement_desc: str = "", **kwargs: Any) -> Any:
        # 检查 insight_params
        if not self.insight_params:
            insight_params = None
        else:
            insight_params = self.insight_params
            
            # 检查是否是默认的空对象
            if (getattr(insight_params, 'start_time', '') == '' and 
                getattr(insight_params, 'end_time', '') == '' and 
                getattr(insight_params, 'level', 0) == 0 and 
                getattr(insight_params, 'scene_id', 0) == 0 and 
                getattr(insight_params, 'campaign_case_id', 0) == 0 and 
                len(getattr(insight_params, 'strategies', [])) == 0):
                insight_params = None  # 重置为空，使用 requirement_desc 解析
        
        # 从insight_params提取strategy_ids
        strategy_ids = []
        if insight_params and hasattr(insight_params, 'strategies') and insight_params.strategies:
            for strategy in insight_params.strategies:
                if hasattr(strategy, 'ids') and strategy.ids:
                    for sid in strategy.ids:
                        if str(sid).isdigit():
                            strategy_ids.append(int(sid))

        # 如果没有从insight_params获取到，尝试从requirement_desc中解析
        if not strategy_ids and requirement_desc:
            # 尝试从 requirement_desc 中提取策略 ids
            pattern = r"\[([^\]]+)\]"
            match = re.search(pattern, requirement_desc)
            if match:
                ids_str = match.group(1)
                # 解析字符串中的数字
                ids_list = re.findall(r"'([^']+)'", ids_str)
                if not ids_list:
                    # 如果没有引号，尝试直接提取数字
                    ids_list = re.findall(r'(\d+)', ids_str)
                
                for sid in ids_list:
                    if sid.isdigit():
                        strategy_ids.append(int(sid))

        # 如果没有从insight_params获取到，使用默认值
        if not strategy_ids:
            strategy_ids = [1]
        
        # 验证阈值参数
        if execution_threshold <= 0:
            execution_threshold = 90.0

        # 获取聚合执行数据
        execution_data = self._get_aggregated_execution_data(strategy_ids)

        # 分析聚合执行率
        result = self._analyze_aggregated_execution_rate(strategy_ids, execution_data, execution_threshold)
        log.info(f"返回结果: {result}")
        return result

    def _get_aggregated_execution_data(self, strategy_ids: List[int]) -> dict:
        """获取策略聚合执行数据（使用真实SQL查询）"""
        
        # 获取时间范围
        start_time = "2024-01-01 10:00:00"
        end_time = "2024-01-08 10:00:00"
        
        if self.insight_params:
            if hasattr(self.insight_params, 'start_time') and self.insight_params.start_time:
                start_time = self.insight_params.start_time
            if hasattr(self.insight_params, 'end_time') and self.insight_params.end_time:
                end_time = self.insight_params.end_time
        
        # 构建策略ID字符串
        strategy_ids_str = ','.join([f"'{sid}'" for sid in strategy_ids])
        
        # 1. 查询聚合 delivery_count
        delivery_count = 0
        delivery_sql = f"""
        SELECT count(distinct user_id) as delivery_count
        FROM horizon_demo_3.events 
        WHERE event in('$PlanMsgSendDone','bannerShow','$PlanPopupDisplay','itemShow') 
          AND $sf_plan_id in ({strategy_ids_str})
        """
        
        try:
            cdp_executor = CDPSqlExecutorTool(
                tenant_project_info=self.tenant_project_info
            )
            
            response_text = cdp_executor.execute_query(delivery_sql)
            
            if response_text and not response_text.startswith("SQL 执行失败"):
                # 解析 CSV 结果
                import csv
                import io
                csv_reader = csv.DictReader(io.StringIO(response_text))
                rows = list(csv_reader)
                
                if rows and 'delivery_count' in rows[0]:
                    delivery_count = int(rows[0]['delivery_count'])
                else:
                    delivery_count = 0
            else:
                delivery_count = 0
                
        except Exception as e:
            delivery_count = 0
        
        # 2. 查询聚合 user_group_names
        user_groups = []
        user_groups_sql = f"""
        SELECT DISTINCT user_group_names
        FROM horizon_workspace_demo_3.ai_demo_resource_usage_03
        WHERE strategy_id IN ({','.join([str(sid) for sid in strategy_ids])})
        """
        
        try:
            response_text = cdp_executor.execute_query(user_groups_sql)
            
            if response_text and not response_text.startswith("SQL 执行失败"):
                # 解析 CSV 结果
                import csv
                import io
                csv_reader = csv.DictReader(io.StringIO(response_text))
                rows = list(csv_reader)
                
                if rows and 'user_group_names' in rows[0]:
                    for row in rows:
                        user_group_names_str = row.get('user_group_names', '')
                        if user_group_names_str:
                            try:
                                import json
                                user_groups_list = json.loads(user_group_names_str)
                                if isinstance(user_groups_list, list):
                                    user_groups.extend(user_groups_list)
                            except json.JSONDecodeError as e:
                                pass
                
        except Exception as e:
            pass
        
        # 3. 查询聚合 audience_count
        audience_count = 0
        if user_groups:
            # 构建 UNION SQL 查询多个用户组表
            union_queries = []
            for user_group in user_groups:
                # 添加前缀 "user_group_"
                table_name = f"horizon_demo_3.user_group_{user_group}"
                union_queries.append(f"SELECT user_id FROM {table_name}")
            
            # 使用 UNION 合并所有查询
            audience_sql = " UNION ".join(union_queries)
            audience_sql = f"""
            SELECT count(distinct user_id) as audience_count
            FROM ({audience_sql}) as combined_users
            """
            
            try:
                response_text = cdp_executor.execute_query(audience_sql)
                
                if response_text and not response_text.startswith("SQL 执行失败"):
                    # 解析 CSV 结果
                    import csv
                    import io
                    csv_reader = csv.DictReader(io.StringIO(response_text))
                    rows = list(csv_reader)
                    
                    if rows and 'audience_count' in rows[0]:
                        audience_count = int(rows[0]['audience_count'])
                    else:
                        audience_count = 0
                else:
                    audience_count = 0
                    
            except Exception as e:
                audience_count = 0
        else:
            pass
        
        # 4. 计算聚合 delivery_rate
        delivery_rate = 0.0
        if audience_count > 0:
            delivery_rate = round((delivery_count * 100.0) / audience_count, 1)
        
        # 返回聚合结果数据
        result_data = {
            "delivery_count": delivery_count,
            "audience_count": audience_count,
            "delivery_rate": delivery_rate,
            "status": "active",
            "start_time": start_time,
            "end_time": end_time,
            "strategy_count": len(strategy_ids)
        }
        
        return result_data

    def _analyze_aggregated_execution_rate(self, strategy_ids: List[int], execution_data: dict,
                                           threshold: float) -> str:
        """分析聚合执行率"""
        
        if not execution_data:
            return "❌ 未获取到任何执行数据"

        # 获取聚合指标
        delivery_count = execution_data.get('delivery_count', 0)
        audience_count = execution_data.get('audience_count', 0)
        delivery_rate = execution_data.get('delivery_rate', 0.0)
        strategy_count = execution_data.get('strategy_count', 0)
        start_time = execution_data.get('start_time', '')
        end_time = execution_data.get('end_time', '')
        
        # 判断执行状态
        status = "✅ 执行良好" if delivery_rate >= threshold else "⚠️ 需要优化"
        
        # 构建分析报告
        report = f"""
## 📊 策略执行进度分析

### 📈 执行统计
- **时间范围**: {start_time} 至 {end_time}
- **总触达用户数**: {delivery_count:,}
- **已圈选去重用户数**: {audience_count:,}
- **实际触达去重用户数**: {delivery_count:,}
- **实际去重触达率**: {delivery_rate}%
- **策略数量**: {strategy_count} 个
- **执行状态**: {status}
- **阈值要求**: {threshold}%
"""
        
        # 添加建议
        if delivery_rate < threshold:
            report += f"""
### 💡 优化建议
当前触达率 ({delivery_rate}%) 低于阈值 ({threshold}%)，建议：
1. 检查策略配置是否正确
2. 优化触达渠道和内容
3. 调整目标用户群体
4. 增加营销活动力度
"""
            log.info(f"💡 生成优化建议，当前触达率 {delivery_rate}% 低于阈值 {threshold}%")
        else:
            report += f"""
### ✅ 执行评估
当前触达率 ({delivery_rate}%) 已达到阈值要求，策略执行良好！
"""
            log.info(f"✅ 生成执行评估，当前触达率 {delivery_rate}% 已达到阈值要求")
        
        log.info(f"📝 分析报告生成完成，报告长度: {len(report)}")
        return report

    async def _arun(self, **kwargs: Any) -> str:
        """异步运行"""
        return self._run(**kwargs)
