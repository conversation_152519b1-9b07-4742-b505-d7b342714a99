from inspect import iscoroutinefunction
from typing import Type, Any, Callable, List, Dict, Literal, Optional
import json

from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from common import tools
from framework.agents.schema import ViewMessage

log = tools.get_logger()


class _Refer(BaseModel):
    id: Optional[int] = Field(description="知识文档标题", default=None)
    cname: Optional[str] = Field(description="知识文档名称", default=None)
    url: Optional[str] = Field(description="知识文档 URL", default=None)


class _IdeaUnit(BaseModel):
    title: Optional[str] = Field(description="知识文档标题", default=None)
    content: Optional[str] = Field(description="知识文档内容，需要尽可能详细", default=None)
    refers: Optional[List[_Refer]] = Field(description="知识文档引用列表", default=None)
    children_units: Optional[List["_IdeaUnit"]] = Field(description="知识/思路文档子文档列表", default=None)


class _InnerModel(BaseModel):
    resource_id: int = Field(description="指定的资源ID")
    units: List[_IdeaUnit] = Field(description="顶层数据格式化信息 / 顶层执行步骤")
    refers: Optional[List[_Refer]] = Field(description="当前层级知识文档引用列表", default=None)


class KnowledgeRenderTool(BaseTool):
    name: str = "KnowledgeRenderTool"
    description: str = "知识数据渲染工具，将知识结果渲染成结构化消息"
    args_schema: Type[BaseModel] = _InnerModel

    # 渲染结果回调
    resource_callback: Callable[[int, Any], Any] | None = None

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def _format_render_knowledge_unit(self, knowledge_unit: Any) -> dict | None:
        if knowledge_unit is None:
            return None
        if isinstance(knowledge_unit, _IdeaUnit):
            return knowledge_unit.model_dump()
        if isinstance(knowledge_unit, dict):
            return _IdeaUnit.model_validate(knowledge_unit).model_dump()
        if not isinstance(knowledge_unit, str):
            knowledge_unit = json.dumps(knowledge_unit, ensure_ascii=False)
        return _IdeaUnit.model_validate_json(knowledge_unit).model_dump()

    def _format_render_knowledge_refer(self, knowledge_refer: Any | None) -> dict | None:
        if knowledge_refer is None:
            return None
        if isinstance(knowledge_refer, _Refer):
            return knowledge_refer.model_dump()
        if isinstance(knowledge_refer, dict):
            return _Refer.model_validate(knowledge_refer).model_dump()
        if not isinstance(knowledge_refer, str):
            knowledge_refer = json.dumps(knowledge_refer, ensure_ascii=False)
        return _Refer.model_validate_json(knowledge_refer).model_dump()

    def _check_and_get_result(
            self,
            units: Any,
            refers: Any | None = None,
            **kwargs
    ) -> ViewMessage:
        if isinstance(units, list):
            units_result = [self._format_render_knowledge_unit(unit) for unit in units]
        else:
            units_result = [self._format_render_knowledge_unit(units)]

        if refers is None:
            refers_result = []
        if isinstance(refers, list):
            refers_result = [self._format_render_knowledge_refer(refer) for refer in refers]
        else:
            refers_result = [self._format_render_knowledge_refer(refers)]

        units_result = [r for r in units_result if r is not None]
        refers_result = [r for r in refers_result if r is not None]

        return ViewMessage(
            type="ANALYSE_IDEA",
            content=json.dumps({"units": units_result, "refers": refers_result}, ensure_ascii=False)
        )

    def _run(self, resource_id: int, **kwargs) -> str:
        try:
            result = self._check_and_get_result(**kwargs)
            self.display(resource_id, result)
            return f"已按照要求渲染知识内容，resource_id={resource_id}，知识内容：{result}, "
        except Exception as e:
            log.warning(f"render error.", e)
            return f"渲染失败，请检查数据格式是否正确: {str(e)}"

    async def _arun(self, resource_id: int, **kwargs: Any) -> str:
        try:
            result = self._check_and_get_result(**kwargs)
            await self.async_display(resource_id, result)
            return f"已按照要求渲染知识内容，resource_id={resource_id}，知识内容：{result}, "
        except Exception as e:
            log.warning(f"render error.", e)
            return f"渲染失败，请检查数据格式是否正确: {str(e)}"

    async def async_display(self, resource_id: int, content: ViewMessage):
        if content is None:
            return
        if self.resource_callback:
            try:
                if iscoroutinefunction(self.resource_callback):
                    await self.resource_callback(resource_id, content)
                else:
                    self.resource_callback(resource_id, content)
            except BaseException as e:
                log.warning('display callback error.', e)

    def display(self, resource_id: int, content: ViewMessage):
        if content is None:
            return
        if self.resource_callback:
            try:
                if iscoroutinefunction(self.resource_callback):
                    tools.asyncio_run(lambda: self.resource_callback(resource_id, content))
                else:
                    self.resource_callback(resource_id, content)
            except BaseException as e:
                log.warning('display callback error.', e)
