from typing import Type, Any, Dict, Optional
import time
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from framework.prompt import PromptFactory
from .cdp_sql_executor import CDPSqlExecutorTool
from apis.definition.portal.portal_bot_pb2 import InsightParams
from common import tools

log = tools.get_logger()

PORTAL_CAMPAIGN_TARGET_TOOL_DESC=PromptFactory.create_or_get_prompt(
    business="portal",
    business_cname="下一代产品 Demo",
    prompt_code="PORTAL_CAMPAIGN_TARGET_TOOL_DESC",
    prompt_name="Portal 营销活动目标工具描述",
    prompt_content="根据 ID 获取相关的营销活动目标信息，包括场景信息、业务目标、关键指标等"
)


class _InnerModel(BaseModel):
    requirement_desc: str = Field(default="", description="本次分析的需求描述")


class PortalCampaignTargetTool(BaseTool):
    name: str = "PortalCampaignTargetTool"
    description: str = PromptFactory.render_prompt(PORTAL_CAMPAIGN_TARGET_TOOL_DESC)
    args_schema: Type[BaseModel] = _InnerModel

    tenant_project_info: dict
    insight_params: InsightParams

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def _run(self, requirement_desc: str = "", **kwargs: Any) -> Any:
        """
        根据案例ID获取营销活动目标信息
        
        Args:
            insight_params: InsightParams对象，包含案例ID等信息
        """
        log.info(f"🔍 PortalCampaignTargetTool: 开始执行")
        cdp_executor = CDPSqlExecutorTool(tenant_project_info=self.tenant_project_info)
        insight_params = self.insight_params

        # 检查 insight_params 是否为空或无效
        if not self.insight_params:
            insight_params = None
        else:
            insight_params = self.insight_params

            # 检查是否是默认的空对象
            if (getattr(insight_params, 'start_time', '') == '' and
                    getattr(insight_params, 'end_time', '') == '' and
                    getattr(insight_params, 'level', 0) == 0 and
                    getattr(insight_params, 'scene_id', 0) == 0 and
                    getattr(insight_params, 'campaign_case_id', 0) == 0 and
                    len(getattr(insight_params, 'strategies', [])) == 0):
                insight_params = None  # 重置为空

        # 检查CDP执行器是否可用
        if cdp_executor is None:
            result = "错误: CDP执行器未正确初始化"
            log.info(f"PortalCampaignTargetTool 执行结果: {result}")
            return result

        # 从insight_params提取ID
        case_id = getattr(insight_params, 'campaign_case_id', None) if insight_params else None
        scene_id = getattr(insight_params, 'scene_id', None) if insight_params else None
        business_goal_id = getattr(insight_params, 'business_goal_id', None) if insight_params else None
        strategy_id = getattr(insight_params, 'strategy_id', None) if insight_params else None

        # 如果case_id为0或None，但有scene_id，尝试通过scene_id查找相关的案例
        if (not case_id or case_id == 0) and scene_id:
            case_id = None  # 重置case_id，让后续逻辑处理

        # 从insight_params提取strategy_ids
        strategy_ids = []
        if insight_params and hasattr(insight_params, 'strategies') and insight_params.strategies:
            for strategy in insight_params.strategies:
                if hasattr(strategy, 'ids') and strategy.ids:
                    for sid in strategy.ids:
                        if str(sid).isdigit():
                            strategy_ids.append(int(sid))

        # 构建SQL，分别查询对应的表
        all_results = []

        # 如果有case_id，查询案例信息
        if case_id and case_id != 0:
            case_sql = f"""
            SELECT 
              cc.id AS campaign_case_id,
              cc.name AS case_name,
              cc.project AS case_project,
              cc.description AS case_description
            FROM horizon_workspace_demo_3.t_campaign_case cc
            WHERE cc.id = {case_id}
            """
            
            # 添加重试机制
            max_retries = 3
            retry_count = 0

            while retry_count < max_retries:
                try:
                    case_result = cdp_executor.execute_query(case_sql)
                    break
                except Exception as e:
                    retry_count += 1
                    if retry_count >= max_retries:
                        case_result = f"SQL 执行失败：{e}"
                    else:
                        time.sleep(2)  # 等待2秒后重试

            if case_result and not case_result.startswith("SQL 执行失败"):
                # 解析 CSV 结果
                import csv
                import io
                csv_reader = csv.DictReader(io.StringIO(case_result))
                case_data = list(csv_reader)
                if case_data:
                    all_results.extend(case_data)

        # 如果有scene_id，查询场景信息
        if scene_id:
            scene_sql = f"""
            SELECT 
              s.id AS scene_id,
              s.name AS scene_name,
              s.type AS scene_type
            FROM horizon_workspace_demo_3.t_scenes s
            WHERE s.id = {scene_id}
            """
            # 添加重试机制
            max_retries = 3
            retry_count = 0

            while retry_count < max_retries:
                try:
                    scene_result = cdp_executor.execute_query(scene_sql)
                    break
                except Exception as e:
                    retry_count += 1
                    if retry_count >= max_retries:
                        scene_result = f"SQL 执行失败：{e}"
                    else:
                        time.sleep(2)  # 等待2秒后重试

            if scene_result and not scene_result.startswith("SQL 执行失败"):
                # 解析 CSV 结果
                import csv
                import io
                csv_reader = csv.DictReader(io.StringIO(scene_result))
                scene_data = list(csv_reader)
                if scene_data:
                    all_results.extend(scene_data)

        # 如果没有case_id但有scene_id，尝试通过场景查找相关信息
        if (not case_id or case_id == 0) and scene_id:
            scene_info_sql = f"""
            SELECT 
              s.id AS scene_id,
              s.name AS scene_name,
              s.type AS scene_type,
              s.description AS scene_description
            FROM horizon_workspace_demo_3.t_scenes s
            WHERE s.id = {scene_id}
            """

            # 添加重试机制
            max_retries = 3
            retry_count = 0

            while retry_count < max_retries:
                try:
                    scene_info_result = cdp_executor.execute_query(scene_info_sql)
                    break
                except Exception as e:
                    retry_count += 1
                    if retry_count >= max_retries:
                        scene_info_result = f"SQL 执行失败：{e}"
                    else:
                        time.sleep(2)  # 等待2秒后重试

            if scene_info_result and not scene_info_result.startswith("SQL 执行失败"):
                # 解析 CSV 结果
                import csv
                import io
                csv_reader = csv.DictReader(io.StringIO(scene_info_result))
                scene_info_data = list(csv_reader)
                if scene_info_data:
                    all_results.extend(scene_info_data)

        # 如果有business_goal_id，查询业务目标信息
        if business_goal_id:
            goal_sql = f"""
            SELECT 
              bg.id AS business_goal_id,
              bg.name AS business_goal_name,
              bg.goal_type AS business_goal_type
            FROM horizon_workspace_demo_3.t_goals bg
            WHERE bg.id = {business_goal_id} AND bg.goal_type = 'BUSINESS'
            """
            # 添加重试机制
            max_retries = 3
            retry_count = 0

            while retry_count < max_retries:
                try:
                    goal_result = cdp_executor.execute_query(goal_sql)
                    break
                except Exception as e:
                    retry_count += 1
                    if retry_count >= max_retries:
                        goal_result = f"SQL 执行失败：{e}"
                    else:
                        time.sleep(2)  # 等待2秒后重试

            if goal_result and not goal_result.startswith("SQL 执行失败"):
                # 解析 CSV 结果
                import csv
                import io
                csv_reader = csv.DictReader(io.StringIO(goal_result))
                goal_data = list(csv_reader)
                if goal_data:
                    all_results.extend(goal_data)

        if not all_results:
            result = "错误: 未提供任何有效的查询参数或未找到相关数据"
            log.info(f"PortalCampaignTargetTool 执行结果: {result}")
            return result

        # 构建合并结果
        json_result = {
            "data": all_results,
            "row_count": len(all_results)
        }

        # 处理结果
        result_dict = self._process_results(json_result, case_id, scene_id, business_goal_id)

        # 构建字符串格式的结果
        result_str = "## 营销活动目标分析\n\n"

        # 添加案例信息
        if result_dict.get("case_id"):
            result_str += f"### 📋 案例信息\n"
            result_str += f"- **案例ID**: {result_dict.get('case_id')}\n"
            result_str += f"- **案例名称**: {result_dict.get('case_name', 'N/A')}\n"
            result_str += f"- **案例描述**: {result_dict.get('case_description', 'N/A')}\n\n"

        # 添加场景信息
        if result_dict.get("scene_id"):
            result_str += f"### 🎯 场景信息\n"
            result_str += f"- **场景ID**: {result_dict.get('scene_id')}\n"
            result_str += f"- **场景名称**: {result_dict.get('scene_name', 'N/A')}\n"
            result_str += f"- **场景类型**: {result_dict.get('scene_type', 'N/A')}\n\n"

        # 添加业务目标信息
        if result_dict.get("business_goal_id"):
            result_str += f"### 🎯 业务目标\n"
            result_str += f"- **目标ID**: {result_dict.get('business_goal_id')}\n"
            result_str += f"- **目标名称**: {result_dict.get('business_goal_name', 'N/A')}\n"
            result_str += f"- **目标类型**: {result_dict.get('business_goal_type', 'N/A')}\n\n"

        # 添加查询参数信息
        result_str += f"### 📊 查询参数\n"
        result_str += f"- **策略ID**: {strategy_ids if strategy_ids else 'N/A'}\n"
        result_str += f"- **案例ID**: {case_id if case_id else 'N/A'}\n"
        result_str += f"- **场景ID**: {scene_id if scene_id else 'N/A'}\n"
        result_str += f"- **业务目标ID**: {business_goal_id if business_goal_id else 'N/A'}\n"
        result_str += f"- **查询结果数量**: {len(all_results)}\n\n"

        # 如果没有找到任何数据
        if not result_dict or (
                not result_dict.get("case_id") and not result_dict.get("scene_id") and not result_dict.get(
                "business_goal_id")):
            result_str += f"⚠️ **注意**: 未找到相关的营销活动目标数据。\n"
            result_str += f"当前参数：案例ID={case_id}, 场景ID={scene_id}, 业务目标ID={business_goal_id}\n"
            result_str += f"建议：请检查数据库中的相关表是否有数据，或者检查查询参数是否正确。\n"

        log.info(f"PortalCampaignTargetTool 执行结果: {result_str}")
        return result_str

    def _process_results(self, json_result: Dict, case_id: Optional[int], scene_id: Optional[int],
                         business_goal_id: Optional[int]) -> Dict:
        """处理查询结果"""
        data = json_result.get('data', [])
        if not data:
            return {"error": f"未找到相关数据"}

        # 初始化结果
        result = {}

        # 处理不同类型的数据
        for row in data:
            # 根据返回的字段判断数据类型
            if 'campaign_case_id' in row and row.get('campaign_case_id') is not None:
                # 案例数据
                result.update({
                    "case_id": row.get('campaign_case_id'),
                    "case_name": row.get('case_name'),
                    "case_description": row.get('case_description')
                })
            elif 'scene_id' in row and row.get('scene_id') is not None:
                # 场景数据
                result.update({
                    "scene_id": row.get('scene_id'),
                    "scene_name": row.get('scene_name'),
                    "scene_type": row.get('scene_type')
                })
            elif 'business_goal_id' in row and row.get('business_goal_id') is not None:
                # 业务目标数据
                result.update({
                    "business_goal_id": row.get('business_goal_id'),
                    "business_goal_name": row.get('business_goal_name'),
                    "business_goal_type": row.get('business_goal_type')
                })

        return result

    async def _arun(self, **kwargs: Any) -> str:
        result = self._run(**kwargs)
        return result
