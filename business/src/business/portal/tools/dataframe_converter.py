#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import pandas as pd
from typing import List, Dict, Any, Optional
from common import tools as my_tools

log = my_tools.get_logger()


class DataFrameConverter:
    """通用的JSON转DataFrame工具类"""

    @staticmethod
    def json_to_dataframe(response_text: str,
                          target_columns: Optional[List[str]] = None,
                          default_values: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """
        将多行JSON响应转换为DataFrame
        
        Args:
            response_text: API响应的文本内容
            target_columns: 目标列名列表，如果为None则自动推断
            default_values: 默认值字典，用于补齐缺失字段
            
        Returns:
            pd.DataFrame: 转换后的DataFrame
        """
        json_lines = response_text.strip().split('\n')
        all_data = []
        inferred_columns = set()

        # 第一遍：收集所有可能的列名
        for line in json_lines:
            if line.strip():
                try:
                    data = json.loads(line)
                    if data.get('code') == 'SUCCESS' and 'data' in data:
                        columns = data['data'].get('columns', [])
                        inferred_columns.update(columns)
                except json.JSONDecodeError:
                    continue

        # 如果没有指定目标列，使用推断的列
        if target_columns is None:
            target_columns = list(inferred_columns)

        # 第二遍：处理数据
        for line in json_lines:
            if line.strip():
                try:
                    data = json.loads(line)
                    if data.get('code') == 'SUCCESS' and 'data' in data:
                        columns = data['data'].get('columns', [])
                        row_data = data['data'].get('data', [])

                        if isinstance(row_data, list):
                            # 动态组装dict
                            row_dict = {col: row_data[i] for i, col in enumerate(columns) if i < len(row_data)}

                            # 补齐字段
                            fixed_row = {}
                            for col in target_columns:
                                if col in row_dict:
                                    fixed_row[col] = row_dict[col]
                                elif default_values and col in default_values:
                                    fixed_row[col] = default_values[col]
                                else:
                                    # 根据列名推断默认值
                                    if 'count' in col.lower():
                                        fixed_row[col] = 0
                                    elif 'level' in col.lower() or 'preference' in col.lower():
                                        fixed_row[col] = '未知'
                                    else:
                                        fixed_row[col] = None

                            all_data.append(fixed_row)

                except json.JSONDecodeError:
                    continue

        if all_data:
            df = pd.DataFrame(all_data, columns=target_columns)
            return df
        else:
            return pd.DataFrame(columns=target_columns)

    @staticmethod
    def safe_get_value(row_dict: Dict[str, Any], key: str, default: Any = None) -> Any:
        """安全获取字典值，支持多种可能的键名"""
        if key in row_dict:
            return row_dict[key]

        # 尝试常见的键名变体
        key_variants = {
            'user_count': ['count', 'count(distinct e.user_id)', 'user_count'],
            'user_tag_risk_level': ['risk_level', 'user_tag_risk_level'],
            'user_tag_product_value_preference': ['product_preference', 'user_tag_product_value_preference'],
            'resource_category': ['category', 'resource_category'],
            'resource_sub_category': ['sub_category', 'resource_sub_category']
        }

        if key in key_variants:
            for variant in key_variants[key]:
                if variant in row_dict:
                    return row_dict[variant]

        return default
