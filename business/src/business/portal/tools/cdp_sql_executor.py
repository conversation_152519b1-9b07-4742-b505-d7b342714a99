import csv
import io
from typing import Type, Any
import requests
import time
from common import tools as my_tools
from impala.dbapi import connect
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from framework.prompt import PromptFactory

log = my_tools.get_logger()

SQL_FIELD_DESC = PromptFactory.create_or_get_prompt(
    business="portal",
    business_cname="下一代产品 Demo",
    prompt_code="PORTAL_SQL_FIELD_DESC",
    prompt_name="Portal SQL 描述信息",
    prompt_desc="Portal SQL 描述信息",
    prompt_content=(
        "Impala SQL 语句，注意语法（比如不支持 TIMESTAMPADD 等函数），SQL 使用的表应该使用数据库名和表名，比如 horizon_default_1.users，"
        "数据库名命名规律："
        "1）horizon_<项目名>_<项目id> 表示项目的主表，用户、事件等表均在主表中；"
        "2）horizon_workspace_<项目名>_<项目id> 表示项目的外部表，该项目额外依赖的表；"
        "3）查看数据库列表可以使用 sql：show databases。"
    )
)

CDP_SQL_EXECUTOR_TOOL_DESC = PromptFactory.create_or_get_prompt(
    business="portal",
    business_cname="下一代产品 Demo",
    prompt_code="PORTAL_CDP_SQL_EXECUTOR_TOOL_DESC",
    prompt_name="Portal CDP SQL 执行工具描述",
    prompt_desc="Portal CDP SQL 执行工具描述",
    prompt_content=(
        "执行 CDP 的 SQL，注意 SQL 是 Impala SQL，请注意语法（比如不支持 TIMESTAMPADD 等函数），返回 csv 格式的数据结果。"
        "重要：在执行此工具前请先执行knowledge_querier工具，输入参数是查数需求，会返回SQL给你，如果SQL执行不通你再自己debug。"
        "使用注意事项：用户、事件表较大，请勿直接使用 SQL 拉取用户、事件表数据，建议使用统计 SQL 执行统计程序，"
        "对于比如 time 时间戳、用户id 等离散数据并且数据量大的字段，请注意统计方式，"
        "如果要查询明细，或者需要遍历表数据，则你必须使用 limit 或者使用 count 等方式先获取预览表格数据的条数，"
        "然后再统计查询，防止查询的数据过大，导致 LLM 上下文超限！"
    )
)


class _InnerModel(BaseModel):
    sql: str = Field(description=PromptFactory.render_prompt(SQL_FIELD_DESC))


class CDPSqlExecutorTool(BaseTool):
    name: str = "CDPSqlExecutorTool"
    description: str = PromptFactory.render_prompt(CDP_SQL_EXECUTOR_TOOL_DESC)
    args_schema: Type[BaseModel] = _InnerModel

    tenant_project_info: dict

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def _extract_host(self):
        host = self.tenant_project_info['cdp_host']
        if host.startswith('http://'):
            host = host.replace('http://', '')
        if host.startswith('https://'):
            host = host.replace('https://', '')
        host = host.strip().split(':')[0]
        return host

    def execute_query(self, sql: str) -> str:
        # 连接 HiveServer2
        conn = None
        try:
            host = self._extract_host()
            log.info(f"🔍 CDPSqlExecutorTool: 尝试连接到 {host}:8416")

            conn = connect(
                host=host,
                port=8416,
                auth_mechanism='PLAIN',
                user='admin',
                password=self.tenant_project_info['api_key']
            )
            log.info(f"🔍 CDPSqlExecutorTool: 连接成功，开始执行 SQL")

            cursor = conn.cursor()
            cursor.execute(sql)
            column_names = [desc[0] for desc in cursor.description]
            rows = cursor.fetchall()
            csv_buffer = io.StringIO()
            writer = csv.writer(csv_buffer)

            writer.writerow(column_names)  # 写列名
            writer.writerows(rows)  # 写数据

            csv_content = csv_buffer.getvalue()
            csv_buffer.close()
            if len(csv_content) > 8000:
                csv_content = csv_content[:8000] + '\n...内容过大，其余内容省略，请优化查询 SQL 语句'

            log.info(f"CDPSqlExecutorTool: SQL 执行成功，返回 {len(csv_content)} 字符")
            return csv_content or ''
        except Exception as e:
            log.error(f"SQL 执行失败：{e}")
            return f"SQL 执行失败：{e}，请特别注意 Impala 语法！"
        finally:
            if conn is not None:
                conn.close()

    def execute_query0(self, sql_query):
        """执行SQL查询"""
        api_url = f"{self.tenant_project_info['cdp_host']}/api/v3/analytics/v1/model/sql/query"
        headers = {
            'accept': 'application/json',
            'api-key': self.tenant_project_info['api_key'],
            'sensorsdata-project': self.tenant_project_info['project_name'],
            'Content-Type': 'application/json'
        }

        payload = {
            "sql": sql_query,
            "limit": "100",
            "request_id": f"llm_query_{int(time.time())}"
        }

        try:
            response = requests.post(api_url, headers=headers, json=payload, timeout=30)
            return response.json()
        except Exception as e:
            return f"SQL执行失败: {str(e)}"

    def _run(self, sql: str, **kwargs: Any) -> Any:
        """
        执行 CDP SQL 查询
        
        Args:
            sql: SQL查询语句
        """
        log.info(f"开始执行工具：name={self.name}, sql={sql}")
        try:
            # 执行查询
            response = self.execute_query(sql)
            log.info(f"工具执行结果：name={self.name}, result=无数据")
            return response
        except Exception as e:
            error_msg = f"查询执行失败: {str(e)}"
            log.warning(f"工具执行结果：name={self.name}, result={error_msg}")
            return error_msg

    async def _arun(self, sql: str, **kwargs: Any) -> str:
        return self._run(sql, **kwargs)
