#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
自然语言转SQL工具 - 只负责生成SQL
"""

from typing import Type, Any

from jinja2 import Template
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from framework.prompt import PromptFactory
from common import tools
from framework.agents.assistant import AssistantAgent
from framework.models import ModelsFactory

log = tools.get_logger()

# 系统提示词
SQL_GENERATION_SYSTEM_PROMPT = """你是一个专业的SQL查询专家，需要根据用户的自然语言需求生成准确的SQL查询语句。

## 数据库Schema：

### 1. 事件表 (events)
用户行为事件表，包含以下字段：
- user_id: 用户ID
- event: 事件类型，包括：
  - login_submitLoginInfo, login_result (登录事件)
  - payroll_page_view (代发专区事件)
  - product_detail_view, product_purchase, confirm_purchase, pruchase_result (产品购买事件)
  - Activity_notification (动帐流水事件)
- purchase_amount: 购买金额 (NUMBER类型)
- is_success: 是否成功 (STRING类型，值为'是'或'否')
- product_id: 产品代码
- product_variety: 产品种类

### 2. 用户属性表 (users)
用户标签表，包含以下字段：
- id: 用户ID
- user_tag_ssfh: 所属分行 (STRING类型)
- user_tag_payroll: 代发客户标识 (0/1)
- user_tag_khjzdj: 客户价值等级 (高价值/中价值/低价值)
- user_tag_risk_level: 风险等级
- user_tag_amount_of_financial_products_held: 理财产品持有金额

### 3. 产品属性表 (horizon_workspace_default_1.product_attribute)
产品信息表，包含以下字段：
- product_code: 产品代码 (产品唯一标识)
- product_name: 产品名称 (产品显示名称)
- min_purchase_amt: 起购金额 (NUMBER类型)
- product_net_value: 产品净值 (NUMBER类型，基金/理财产品)
- deposit_term: 存期 (STRING类型，存款产品，如"1个月"、"1年")
- deposit_rate: 利率 (STRING类型，存款产品，如"1.10%")
- precious_metal_amt: 贵金属金额 (NUMBER类型，贵金属产品)
- user_risk_level: 匹配的用户风险等级 (低风险/中低风险/中风险/中高风险/高风险)
- is_open_period: 是否在开放期内 (是/否)
- product_cycle: 产品周期 (STRING类型，理财产品，如"365天"、"14天")
- product_type: 产品类型 (理财/基金/存款/贵金属)
- has_balance_is_purchasable: 产品是否有余额/是否可购买 (是/否)

### 4. 分行网点数表 (horizon_workspace_default_1.ssfhwdsb)
分行信息表，包含以下字段：
- 所属分行(ssfh): 分行名称(北京/上海/广州/深圳/成都/等)
- 分行网点数(fhwds): 该分行的网点数量

## 表关联关系：
- events.user_id = users.id (用户事件与用户属性关联)
- events.product_id = product_attribute.product_code (产品事件与产品属性关联)
- users.user_tag_ssfh = ssfhwdsb.ssfh (用户所属分行与分行信息关联)

## 常用查询模式：
1. 代发客户筛选：WHERE u.user_tag_payroll = 1
2. 成功购买事件：WHERE e.event = 'pruchase_result' AND e.is_success = '是'
3. 登录事件：WHERE e.event IN ('login_submitLoginInfo', 'login_result')
4. 按分行分组：GROUP BY u.user_tag_ssfh
5. 用户统计：COUNT(DISTINCT e.user_id)
6. 金额统计：SUM(e.purchase_amount)

请根据用户需求生成准确的SQL查询语句。只返回SQL语句，不要其他解释。"""

async def get_sql_generation_system_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="portal",
        business_cname="下一代产品 Demo",
        prompt_code="PORTAL_NLP_TO_SQL_CDP_TOOL_SYSTEM_PROMPT",
        prompt_name="Portal 自然语言转SQL工具 - 系统提示词",
        prompt_desc="Portal 自然语言转SQL工具 - 系统提示词",
        prompt_content=SQL_GENERATION_SYSTEM_PROMPT,
    )
    return template

# 用户提示词模板
SQL_GENERATION_USER_PROMPT = """## 用户需求：{natural_language_query}

请根据用户需求生成准确的SQL查询语句。只返回SQL语句，不要其他解释："""

async def get_sql_generation_user_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="portal",
        business_cname="下一代产品 Demo",
        prompt_code="PORTAL_NLP_TO_SQL_CDP_TOOL_USER_PROMPT",
        prompt_name="Portal 自然语言转SQL工具 - 用户提示词",
        prompt_desc="Portal 自然语言转SQL工具 - 用户提示词",
        prompt_content=SQL_GENERATION_USER_PROMPT,
    )
    return template

class _InnerModel(BaseModel):
    natural_language_query: str = Field(description="自然语言查询需求")


class NaturalLanguageToSQLTool(BaseTool):
    """自然语言转SQL工具 - 只负责生成SQL，不执行查询"""

    name: str = "NaturalLanguageToSQLTool"
    description: str = "将自然语言查询转换为SQL语句"
    args_schema: Type[BaseModel] = _InnerModel

    llm: Any = None
    llm_available: bool = False

    def __init__(self, **kwargs):
        """
        初始化工具
        """
        super().__init__(**kwargs)

        # 尝试初始化LLM
        try:
            self.llm = ModelsFactory.get_llm()
            self.llm_available = True
            log.info("LLM初始化成功")
        except Exception as e:
            log.warning(f"LLM初始化失败: {str(e)}")
            self.llm_available = False

    async def generate_sql_with_llm(self, natural_language_query):
        """使用LLM生成SQL"""
        if not self.llm_available:
            raise Exception("LLM不可用，无法生成SQL")

        try:
            # 创建AssistantAgent
            agent = AssistantAgent(llm=self.llm, system_prompt= await get_sql_generation_system_prompt())

            # 构建用户提示词
            user_prompt = (await get_sql_generation_user_prompt()).render(natural_language_query=natural_language_query)

            # 异步调用LLM
            response = await agent.achat_and_save(prompt=user_prompt)

            # 提取SQL语句
            sql = response.strip()

            # 清理SQL语句
            if sql.startswith('```sql'):
                sql = sql[6:]
            if sql.startswith('```'):
                sql = sql[3:]
            if sql.endswith('```'):
                sql = sql[:-3]

            return sql.strip()

        except Exception as e:
            raise Exception(f"LLM生成SQL失败: {str(e)}")

    def _run(self, natural_language_query: str, **kwargs: Any) -> Any:
        return tools.asyncio_run(self.arun, natural_language_query=natural_language_query, **kwargs)

    async def _arun(self, natural_language_query: str, **kwargs: Any) -> str:
        """
        将自然语言查询转换为SQL语句

        Args:
            natural_language_query: 自然语言查询需求
        """
        log.info(f"开始执行工具：name={self.name}, natural_language_query={natural_language_query}")
        try:
            if not self.llm_available:
                return "LLM不可用，无法生成SQL"

            # 使用LLM生成SQL
            sql_query = await self.generate_sql_with_llm(natural_language_query)

            result = {
                "sql": sql_query,
                "natural_language_query": natural_language_query
            }
            log.info(f"工具执行结果：name={self.name}, result={result}")
            return result
        except Exception as e:
            error_msg = f"SQL生成失败: {str(e)}"
            log.warning(f"工具执行结果：name={self.name}, result={error_msg}")
            return error_msg
