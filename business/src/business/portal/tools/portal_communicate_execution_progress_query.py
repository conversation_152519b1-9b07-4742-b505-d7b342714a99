from typing import Any, List, Type
import requests
import time
import pandas as pd
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field
from common import tools

from apis.definition.portal.portal_bot_pb2 import InsightParams
# 添加项目路径
from business.portal.tools.dataframe_converter import DataFrameConverter
from framework.prompt import PromptFactory

log = tools.get_logger()

PORTAL_COMMUNICATE_EXECUTION_PROGRESS_TOOL_DESC=PromptFactory.create_or_get_prompt(
    business="portal",
    business_cname="下一代产品 Demo",
    prompt_code="PORTAL_COMMUNICATE_EXECUTION_PROGRESS_TOOL_DESC",
    prompt_name="Portal 沟通渠道执行进度工具描述",
    prompt_content="用于根据策略用例 ID 查询沟通渠道的触达执行情况，包含如下两个功能：1.分析外呼和人工跟进的执行情况。2.推荐相关产品以及活动"
)


class _InnerModel(BaseModel):
    execution_completeness_threshold: float = Field(default=90.0, description="触达后执行完整度阈值，默认90%")
    requirement_desc: str = Field(default="", description="本次分析的需求描述")


class PortalCommunicateExecutionProgressTool(BaseTool):
    name: str = "PortalCommunicateExecutionProgressTool"
    description: str = PromptFactory.render_prompt(PORTAL_COMMUNICATE_EXECUTION_PROGRESS_TOOL_DESC)
    args_schema: Type[BaseModel] = _InnerModel

    tenant_project_info: dict
    insight_params: InsightParams

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def _run(self, execution_completeness_threshold: float = 90.0, requirement_desc: str = "", **kwargs: Any) -> Any:
        insight_params = self.insight_params
        
        # 检查 insight_params 是否为空或无效
        if not self.insight_params:
            insight_params = None
        else:
            insight_params = self.insight_params
            
            # 检查是否是默认的空对象
            if (getattr(insight_params, 'start_time', '') == '' and 
                getattr(insight_params, 'end_time', '') == '' and 
                getattr(insight_params, 'level', 0) == 0 and 
                getattr(insight_params, 'scene_id', 0) == 0 and 
                getattr(insight_params, 'campaign_case_id', 0) == 0 and 
                len(getattr(insight_params, 'strategies', [])) == 0):
                insight_params = None  # 重置为空，使用默认值
        
        # 从insight_params提取strategy_ids
        strategy_ids = []
        if insight_params and hasattr(insight_params, 'strategies') and insight_params.strategies:
            for strategy in insight_params.strategies:
                if hasattr(strategy, 'ids') and strategy.ids:
                    for sid in strategy.ids:
                        if str(sid).isdigit():
                            strategy_ids.append(int(sid))

        # 如果没有从insight_params获取到，使用默认值
        if not strategy_ids:
            strategy_ids = [1]

        # 验证阈值参数
        if execution_completeness_threshold <= 0:
            execution_completeness_threshold = 90.0

        # API配置
        api_url = f"{self.tenant_project_info['cdp_host']}/api/v3/analytics/v1/model/sql/query"
        headers = {
            'accept': 'application/json',
            'api-key': self.tenant_project_info['api_key'],
            'sensorsdata-project': self.tenant_project_info['project_name'],
            'Content-Type': 'application/json'
        }

        # 构建用户偏好查询的strategy_ids字符串
        strategy_ids_str = ','.join([f"'{sid}'" for sid in strategy_ids])

        # 查询可用资源对照范围
        available_resources_query = """
        SELECT 
            resource_category_code,
            resource_sub_category_code,
            resource_sub_category,
            resource_category
        FROM horizon_workspace_demo_3.ai_demo_total_resource 
        ORDER BY resource_category_code, resource_sub_category_code
        """

        # 查询用户偏好数据 - 简化版本，直接查询用户表
        user_pref_query = """
        SELECT 
            COUNT(*) as user_count,
            user_tag_risk_level,
            user_tag_product_value_preference
        FROM horizon_demo_3.users 
        WHERE user_tag_risk_level IS NOT NULL 
          AND user_tag_product_value_preference IS NOT NULL
          AND user_tag_risk_level != '未知'
          AND user_tag_product_value_preference != '未知'
        GROUP BY user_tag_risk_level, user_tag_product_value_preference
        ORDER BY user_count DESC
        LIMIT 10
        """

        # 查询产品属性信息
        product_attribute_query = """
        SELECT 
            product_code,
            product_name,
            product_type,
            user_risk_level,
            has_balance_is_purchasable,
            is_open_period,
            min_purchase_amt
        FROM horizon_workspace_demo_3.product_attribute 
        WHERE has_balance_is_purchasable = '是' AND is_open_period = '是'
        ORDER BY product_type, product_code
        """
        # 定义策略ID字符串，用于SQL查询
        strategy_ids_str_for_resources = ','.join([str(sid) for sid in strategy_ids])
        
        # 查询策略用户组信息
        strategy_user_groups_query = f"""
        SELECT DISTINCT
            rur.strategy_id,
            rur.user_group_names
        FROM horizon_workspace_demo_3.ai_demo_resource_usage_03 rur
        WHERE rur.strategy_id IN ({strategy_ids_str_for_resources})
          AND rur.user_group_names IS NOT NULL
          AND rur.user_group_names != ''
        ORDER BY rur.strategy_id
        """
        # 查询所有策略已使用的资源（聚合查询）
        used_resources_query = f"""
        SELECT 
            rur.resource_category_code,
            rur.resource_sub_category_code,
            rtr.resource_sub_category,
            rtr.resource_category,
            rur.strategy_type,
            SUM(rur.outbound_call_plan_count) as total_plan_count,
            SUM(rur.outbound_call_complete_count) as total_complete_count,
            COUNT(DISTINCT rur.strategy_id) as strategy_count
        FROM horizon_workspace_demo_3.ai_demo_resource_usage_03 rur
        LEFT JOIN horizon_workspace_demo_3.ai_demo_total_resource rtr 
            ON rur.resource_category_code = rtr.resource_category_code 
            AND rur.resource_sub_category_code = rtr.resource_sub_category_code
        WHERE rur.strategy_id IN ({strategy_ids_str_for_resources})
        GROUP BY rur.resource_category_code, rur.resource_sub_category_code, rtr.resource_sub_category, rtr.resource_category, rur.strategy_type
        ORDER BY rur.resource_category_code, rur.resource_sub_category_code
        """

        try:
            # 执行公共查询（只执行一次）
            
            available_df = self._execute_query_to_dataframe(api_url, headers, available_resources_query)
            
            user_pref_df = self._execute_query_to_dataframe(api_url, headers, user_pref_query)
            
            product_df = self._execute_query_to_dataframe(api_url, headers, product_attribute_query)
            
            strategy_user_groups_df = self._execute_query_to_dataframe(api_url, headers, strategy_user_groups_query)
            
            # 动态构建用户偏好查询
            strategy_user_preferences_df = self._get_strategy_user_preferences(strategy_user_groups_df, api_url, headers)
            
            used_df = self._execute_query_to_dataframe(api_url, headers, used_resources_query)

            # 分析聚合触达后执行程度
            result = self._analyze_aggregated_execution_completeness(
                strategy_ids, used_df, available_df, user_pref_df,
                product_df, strategy_user_preferences_df, execution_completeness_threshold
            )

            log.info(f"返回结果: {result}")
            return result
        except Exception as e:
            error_msg = f"触达执行分析失败: {str(e)}"
            return error_msg

    def _execute_query_to_dataframe(self, api_url: str, headers: dict, sql_query: str) -> pd.DataFrame:
        """执行SQL查询并返回DataFrame，使用通用的DataFrameConverter，添加重试机制"""
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                payload = {
                    "sql": sql_query, 
                    "limit": "50", 
                    "request_id": f"debug_{int(time.time())}"
                }
                
                response = requests.post(api_url, headers=headers, json=payload)
                response.raise_for_status()
                response_text = response.text.strip()

                # 使用通用的DataFrameConverter
                df = DataFrameConverter.json_to_dataframe(response_text)
                return df
                
            except Exception as e:
                retry_count += 1
                
                if retry_count >= max_retries:
                    return pd.DataFrame()
                else:
                    time.sleep(2)  # 等待2秒后重试

    def _analyze_aggregated_execution_completeness(self, strategy_ids: List[int], used_df: pd.DataFrame,
                                                   available_df: pd.DataFrame, user_pref_df: pd.DataFrame,
                                                   product_df: pd.DataFrame, strategy_user_preferences_df: pd.DataFrame, 
                                                   threshold: float) -> str:
        """分析多个策略的聚合触达后执行完整度并生成统一报告"""

        result = ""

        # 1. 触达后执行度表格
        result += f"## 📊 触达后执行度\n\n"
        table_lines = ["| 类型 | 预期目标 | 实际回执 | 回执率 | 结论 |", "|---|---|---|---|---|"]

        if not used_df.empty:
            # 计算聚合外呼数据
            ai_call_stats = {'plan_count': 0, 'complete_count': 0}
            manual_follow_stats = {'plan_count': 0, 'complete_count': 0}
            
            for _, row in used_df.iterrows():
                plan_count = row.get('total_plan_count', 0)
                complete_count = row.get('total_complete_count', 0)
                resource_sub_category = row.get('resource_sub_category', '')
                
                if plan_count is not None and complete_count is not None:
                    plan_count = float(plan_count)
                    complete_count = float(complete_count)
                    
                    # 只统计AI外呼和人工跟进
                    if 'AI外呼' in resource_sub_category or '外呼' in resource_sub_category:
                        ai_call_stats['plan_count'] += plan_count
                        ai_call_stats['complete_count'] += complete_count
                    elif '人工跟进' in resource_sub_category or 'CRM' in resource_sub_category:
                        manual_follow_stats['plan_count'] += plan_count
                        manual_follow_stats['complete_count'] += complete_count
            
            # AI外呼统计
            if ai_call_stats['plan_count'] > 0:
                call_rate = (ai_call_stats['complete_count'] / ai_call_stats['plan_count'] * 100) if ai_call_stats['plan_count'] > 0 else 0
                call_conclusion = f"有提升空间，建议提升至{threshold}%以上。" if call_rate < threshold else "达标。"
                table_lines.append(f"| AI外呼 | {int(ai_call_stats['plan_count'])}人 | {int(ai_call_stats['complete_count'])}人 | {call_rate:.1f}% | {call_conclusion} |")
            
            # 人工跟进统计
            if manual_follow_stats['plan_count'] > 0:
                follow_rate = (manual_follow_stats['complete_count'] / manual_follow_stats['plan_count'] * 100) if manual_follow_stats['plan_count'] > 0 else 0
                follow_conclusion = f"有提升空间，建议提升至{threshold}%以上。" if follow_rate < threshold else "达标。"
                table_lines.append(f"| 人工跟进 | {int(manual_follow_stats['plan_count'])}人 | {int(manual_follow_stats['complete_count'])}人 | {follow_rate:.1f}% | {follow_conclusion} |")
            
            if ai_call_stats['plan_count'] == 0 and manual_follow_stats['plan_count'] == 0:
                table_lines.append(f"| 外呼/人工跟进 | - | - | - | 无外呼数据 |")
        else:
            table_lines.append(f"| 外呼/CRM | - | - | - | 未配置，无需统计 |")

        result += f"{chr(10).join(table_lines)}\n\n"

        # 2. 产品&活动推荐
        result += f"## 💡 产品&活动推荐\n\n"
        
        # 产品推荐表格
        product_recommendations = self._generate_product_recommendations_table(user_pref_df, product_df, used_df, strategy_user_preferences_df)
        result += product_recommendations
        
        # 活动推荐表格
        activity_recommendations = self._generate_activity_recommendations_table(available_df, used_df)
        result += activity_recommendations

        return result

    def _generate_product_recommendations_table(self, user_pref_df: pd.DataFrame, product_df: pd.DataFrame,
                                               used_df: pd.DataFrame, strategy_user_preferences_df: pd.DataFrame) -> str:
        """生成产品推荐表格"""
        result = "### 🏦 产品推荐\n\n"
        
        if not strategy_user_preferences_df.empty and not product_df.empty:
            # 构建产品推荐候选列表，按用户数量加权
            product_candidates = {}
            
            for idx, user_row in strategy_user_preferences_df.iterrows():
                risk_level = user_row.get('user_tag_risk_level', '')
                product_pref = user_row.get('user_tag_product_value_preference', '')
                user_count = user_row.get('user_count', 0)
                
                if risk_level and product_pref and user_count > 0:
                    # 1. 精确匹配
                    matching_products = product_df[
                        (product_df['user_risk_level'] == risk_level) & 
                        (product_df['product_type'] == product_pref)
                    ]
                    
                    # 2. 如果精确匹配没有结果，尝试模糊匹配
                    if len(matching_products) == 0:
                        # 风险等级映射
                        risk_mapping = {
                            '低风险': ['低风险', '低'],
                            '中风险': ['中风险', '中'],
                            '中高风险': ['中高风险', '中高'],
                            '高风险': ['高风险', '高']
                        }
                        
                        # 产品类型映射
                        product_mapping = {
                            '贵金属': ['贵金属', '黄金', '白银'],
                            '理财': ['理财', '理财产品'],
                            '基金': ['基金', '股票基金', '债券基金'],
                            '存款': ['存款', '定期存款', '活期存款']
                        }
                        
                        # 获取匹配的风险等级和产品类型
                        target_risk_levels = risk_mapping.get(risk_level, [risk_level])
                        target_product_types = product_mapping.get(product_pref, [product_pref])
                        
                        # 模糊匹配
                        risk_level_match = product_df['user_risk_level'].isin(target_risk_levels)
                        product_type_match = product_df['product_type'].isin(target_product_types)
                        
                        matching_products = product_df[risk_level_match & product_type_match]
                        
                        # 3. 如果模糊匹配也没有结果，尝试只按产品类型匹配
                        if len(matching_products) == 0:
                            matching_products = product_df[product_type_match]
                    
                    # 4. 为每个匹配的产品计算加权分数
                    for _, product in matching_products.iterrows():
                        product_name = product.get('product_name', '')
                        if product_name:
                            match_score = self._calculate_match_score(product, risk_level, product_pref)
                            # 加权分数 = 匹配分数 * 用户数量
                            weighted_score = match_score * user_count
                            
                            if product_name not in product_candidates:
                                product_candidates[product_name] = {
                                    'name': product_name,
                                    'type': product.get('product_type', ''),
                                    'risk_level': product.get('user_risk_level', ''),
                                    'min_amount': product.get('min_purchase_amt', ''),
                                    'total_score': 0,
                                    'total_users': 0,
                                    'matching_groups': []
                                }
                            
                            # 累加分数和用户数
                            product_candidates[product_name]['total_score'] += weighted_score
                            product_candidates[product_name]['total_users'] += user_count
                            product_candidates[product_name]['matching_groups'].append({
                                'risk_level': risk_level,
                                'product_pref': product_pref,
                                'user_count': user_count,
                                'match_score': match_score
                            })
            
            # 转换为推荐产品列表并按总分排序
            recommended_products = []
            for product_name, candidate in product_candidates.items():
                # 计算平均匹配分数
                avg_match_score = candidate['total_score'] / candidate['total_users'] if candidate['total_users'] > 0 else 0
                
                recommended_products.append({
                    'name': candidate['name'],
                    'type': candidate['type'],
                    'risk_level': candidate['risk_level'],
                    'min_amount': candidate['min_amount'],
                    'total_users': candidate['total_users'],
                    'avg_match_score': avg_match_score,
                    'matching_groups': candidate['matching_groups']
                })
            
            if recommended_products:
                # 按总用户数和平均匹配分数排序
                recommended_products.sort(key=lambda x: (x['total_users'], x['avg_match_score']), reverse=True)
                
                table_lines = ["| 推荐产品 | 类型 | 风险等级 | 起购金额 | 覆盖用户数 | 平均匹配度 |", "|---|---|---|---|---|---|"]
                for i, product in enumerate(recommended_products[:8]):  # 最多推荐8个产品
                    avg_match_score = product['avg_match_score']
                    match_level = "高" if avg_match_score >= 0.8 else "中" if avg_match_score >= 0.5 else "低"
                    
                    table_lines.append(f"| {product['name']} | {product['type']} | {product['risk_level']} | {product['min_amount']} | {product['total_users']} | {match_level} |")
                result += f"{chr(10).join(table_lines)}\n\n"
            else:
                result += f"暂无推荐产品\n\n"
        else:
            result += f"暂无推荐产品\n\n"
        
        return result

    def _generate_activity_recommendations_table(self, available_df: pd.DataFrame, used_df: pd.DataFrame) -> str:
        """生成活动推荐表格"""
        result = "### 🎁 活动推荐\n\n"
        
        if not available_df.empty:
            # 获取权益类型活动
            equity_activities = available_df[available_df['resource_category'] == '权益类型']['resource_sub_category'].tolist()
            used_activities = used_df[used_df['resource_category'] == '权益类型']['resource_sub_category'].tolist() if not used_df.empty else []
            available_activities = [a for a in equity_activities if a not in used_activities]
            
            if available_activities:
                table_lines = ["| 推荐活动 | 活动类型 | 适用场景 |", "|---|---|---|"]
                for activity in available_activities[:5]:  # 最多推荐5个活动
                    if '代发客户' in activity:
                        scenario = "代发客户活跃度提升"
                    elif '工资卡' in activity:
                        scenario = "工资卡客户绑卡激励"
                    elif '理财达标' in activity:
                        scenario = "理财客户达标激励"
                    else:
                        scenario = "通用客户激励"
                    
                    table_lines.append(f"| {activity} | 权益类型 | {scenario} |")
                
                result += f"{chr(10).join(table_lines)}\n\n"
            else:
                result += f"当前已使用所有可用活动类型\n\n"
        else:
            result += f"暂无可用活动资源\n\n"
        
        return result

    def _calculate_match_score(self, product: pd.Series, target_risk_level: str, target_product_type: str) -> float:
        """计算产品与用户偏好的匹配分数"""
        score = 0.0
        
        # 风险等级匹配分数 (40%)
        product_risk = product.get('user_risk_level', '')
        if product_risk == target_risk_level:
            score += 0.4  # 精确匹配
        elif any(risk in product_risk for risk in target_risk_level.split()):
            score += 0.3  # 部分匹配
        elif any(risk in target_risk_level for risk in product_risk.split()):
            score += 0.2  # 反向部分匹配
        
        # 产品类型匹配分数 (40%)
        product_type = product.get('product_type', '')
        if product_type == target_product_type:
            score += 0.4  # 精确匹配
        elif any(type_name in product_type for type_name in target_product_type.split()):
            score += 0.3  # 部分匹配
        elif any(type_name in target_product_type for type_name in product_type.split()):
            score += 0.2  # 反向部分匹配
        
        # 起购金额合理性分数 (20%)
        min_amount = product.get('min_purchase_amt', 0)
        try:
            min_amount = float(min_amount) if min_amount else 0
            if min_amount <= 10000:  # 低起购金额
                score += 0.2
            elif min_amount <= 100000:  # 中等起购金额
                score += 0.1
            else:  # 高起购金额
                score += 0.05
        except:
            score += 0.1  # 默认分数
        
        final_score = min(score, 1.0)  # 确保分数不超过1.0
        return final_score

    def _get_strategy_user_preferences(self, strategy_user_groups_df: pd.DataFrame, api_url: str, headers: dict) -> pd.DataFrame:
        """根据策略用户组动态构建用户偏好查询"""
        
        # 解析用户组名称
        all_user_groups = set()
        strategy_user_groups = {}
        
        for _, row in strategy_user_groups_df.iterrows():
            strategy_id = row.get('strategy_id')
            user_group_names_str = row.get('user_group_names', '')
            
            if strategy_id and user_group_names_str:
                try:
                    import json
                    user_groups_list = json.loads(user_group_names_str)
                    if isinstance(user_groups_list, list):
                        strategy_user_groups[strategy_id] = user_groups_list
                        all_user_groups.update(user_groups_list)
                except json.JSONDecodeError:
                    pass
        
        if not all_user_groups:
            return pd.DataFrame()
        
        # 动态构建UNION查询
        union_queries = []
        for user_group in all_user_groups:
            table_name = f"horizon_demo_3.user_group_{user_group}"
            union_queries.append(f"SELECT user_id FROM {table_name}")
        

        
        try:
            # 直接执行用户偏好统计查询
            preference_stats_sql = f"""
            SELECT 
                u.user_tag_risk_level,
                u.user_tag_product_value_preference,
                COUNT(DISTINCT u.id) as user_count
            FROM horizon_demo_3.users u
            INNER JOIN (
                {(" UNION ").join(union_queries)}
            ) ug ON u.id = ug.user_id
            WHERE u.user_tag_risk_level IS NOT NULL 
              AND u.user_tag_product_value_preference IS NOT NULL
              AND u.user_tag_risk_level != '未知'
              AND u.user_tag_product_value_preference != '未知'
            GROUP BY u.user_tag_risk_level, u.user_tag_product_value_preference
            ORDER BY user_count DESC
            """
            
            stats_df = self._execute_query_to_dataframe(api_url, headers, preference_stats_sql)
            return stats_df
                
        except Exception as e:
            return pd.DataFrame()

    async def _arun(self, **kwargs: Any) -> str:
        return self._run(**kwargs)
