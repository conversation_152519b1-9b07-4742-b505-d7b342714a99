from inspect import iscoroutinefunction
from typing import Type, Any, Callable, List, Dict, Literal, Optional
import json

from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from common import tools
from framework.agents.schema import ViewMessage
from framework.prompt import PromptFactory

log = tools.get_logger()

supported_chart_types = ['TABLE', 'CHART', 'ENTITY_RELATION']

COLUMN_INFORMATION_NAME_FIELD_DESC = PromptFactory.create_or_get_prompt(
    business="portal",
    business_cname="下一代产品 Demo",
    prompt_code="PORTAL_COLUMN_INFORMATION_NAME_FIELD_DESC",
    prompt_name="Portal 列信息 name 字段描述",
    prompt_desc="Portal 列信息 name 字段描述",
    prompt_content="列名，当 column='operation' 的时候，name 固定为'操作'"
)

COLUMN_INFORMATION_COLUMN_FIELD_DESC = PromptFactory.create_or_get_prompt(
    business="portal",
    business_cname="下一代产品 Demo",
    prompt_code="PORTAL_COLUMN_INFORMATION_COLUMN_FIELD_DESC",
    prompt_name="Portal 列信息 column 字段描述",
    prompt_content="列对应的字段名称，当 column='dimension' 的时候表示可以按照这一列的值下钻"
)

TABLE_DATA_COLUMNS_FIELD_DESC = PromptFactory.create_or_get_prompt(
    business="portal",
    business_cname="下一代产品 Demo",
    prompt_code="PORTAL_TABLE_DATA_COLUMNS_FIELD_DESC",
    prompt_name="Portal 表格数据 columns 字段描述",
    prompt_desc="Portal 表格数据 columns 字段描述",
    prompt_content="列信息，如果需要下钻分析，则需要添加两列：dimension 和 operation 即可"
)

TABLE_DATA_DATA_FIELD_DESC = PromptFactory.create_or_get_prompt(
    business="portal",
    business_cname="下一代产品 Demo",
    prompt_code="PORTAL_TABLE_DATA_DATA_FIELD_DESC",
    prompt_name="Portal 表格数据 data 字段描述",
    prompt_content=("行数据, 字典 key 为列信息的 column, value 是列值"
"如果可以继续下钻分析本行数据的某个维度，则需要行数据中存在 dimension 字段，"
                     "并且有个字段为 operation，值为 List 类型：['DRILL_DOWN']，"
                     "示例：[{\"dimension\":\"年龄段\", \"operation\":[\"DRILL_DOWN\"]}]")
    )

TABLE_DATA_OTHER_FEATURE_FIELD_DESC = PromptFactory.create_or_get_prompt(
    business="portal",
    business_cname="下一代产品 Demo",
    prompt_code="PORTAL_TABLE_DATA_OTHER_FEATURE_FIELD_DESC",
    prompt_name="Portal 表格数据 other_feature 字段描述",
    prompt_content="其他特征，OTHER_DRILL_DOWN 表示其他下钻维度，不需要下钻则不需要此字段"
)

TABLE_DATA_OTHER_DRILL_DOWN_DATA_FIELD_DESC = PromptFactory.create_or_get_prompt(
    business="portal",
    business_cname="下一代产品 Demo",
    prompt_code="PORTAL_TABLE_DATA_OTHER_DRILL_DOWN_DATA_FIELD_DESC",
    prompt_name="Portal 表格数据 other_drill_down_data 字段描述",
    prompt_content="其他下钻维度信息，字典格式：{'cname': '下钻维度'}"
)

CHART_DATA_TYPE_FIELD_DESC= PromptFactory.create_or_get_prompt(
    business="portal",
    business_cname="下一代产品 Demo",
    prompt_code="PORTAL_CHART_DATA_TYPE_FIELD_DESC",
    prompt_name="Portal 图表数据 type 字段描述",
    prompt_content="该图表支持的类型列表(List类型)，默认展示第一个类型，多个值表示允许切换到其他的 type 类型，可取值：TABLE/INTERVAL/LINE/PIE，分别表示表格、柱状图、折线图、饼图类型"
)

ENTITY_RELATION_DATA_ENTITY_FIELD_DESC=PromptFactory.create_or_get_prompt(
    business="portal",
    business_cname="下一代产品 Demo",
    prompt_code="PORTAL_ENTITY_RELATION_DATA_ENTITY_FIELD_DESC",
    prompt_name="Portal 实体关系数据 entity 字段描述",
    prompt_content="当前实体信息，一般指的是 AUDIENCE 客群"
)

ENTITY_RELATION_DATA_RELATION_FIELD_DESC=PromptFactory.create_or_get_prompt(
    business="portal",
    business_cname="下一代产品 Demo",
    prompt_code="PORTAL_ENTITY_RELATION_DATA_RELATION_FIELD_DESC",
    prompt_name="Portal 实体关系数据 relation 字段描述",
    prompt_content="关系实体信息，一般指的是： PLAN 运营计划 / CANVAS 流程画布"
)

INNER_MODEL_CHART_TYPE_FIELD_DESC=PromptFactory.create_or_get_prompt(
    business="portal",
    business_cname="下一代产品 Demo",
    prompt_code="PORTAL_INNER_MODEL_CHART_TYPE_FIELD_DESC",
    prompt_name="Portal 内部模型 chart_type 字段描述",
    prompt_content=f"图表类型，可选：{supported_chart_types}，分别对应表格/图表/实体关系",
)

INNER_MODEL_DISPLAY_DATA_FIELD_DESC=PromptFactory.create_or_get_prompt(
    business="portal",
    business_cname="下一代产品 Demo",
    prompt_code="PORTAL_INNER_MODEL_DISPLAY_DATA_FIELD_DESC",
    prompt_name="Portal 内部模型 display_data 字段描述",
    prompt_content=f"图表展示对应的数据，当 chart_type 分别为 表格/图表/实体关系 时，分别对应 table_data | chart_data | entity_relation_data 类型结构，三个字段选择其一即可"
)

DATA_RENDER_TOOL_NAME_FIELD_DESC=PromptFactory.create_or_get_prompt(
    business="portal",
    business_cname="下一代产品 Demo",
    prompt_code="PORTAL_DATA_RENDER_TOOL_NAME_FIELD_DESC",
    prompt_name="Portal 数据渲染工具名称",
    prompt_content=("数据前端渲染工具，渲染前需要仔细检查是否已经渲染过该资源，"
                    "如果已经渲染过则不要重复渲染，需要在每次渲染后仔细检查数据渲染的格式、数据、展示维度等是否正确，"
                    "如果不正确则需要重新渲染（但是不改变资源 id）")
)

class _ColumnInformation(BaseModel):
    name: str = Field(description=PromptFactory.render_prompt(COLUMN_INFORMATION_NAME_FIELD_DESC))
    column: str = Field(description=PromptFactory.render_prompt(COLUMN_INFORMATION_COLUMN_FIELD_DESC))


class _TableData(BaseModel):
    columns: List[_ColumnInformation] = Field(
        description=PromptFactory.render_prompt(TABLE_DATA_COLUMNS_FIELD_DESC)
    )
    data: List[Dict[str, Any]] = Field(
        description=PromptFactory.render_prompt(TABLE_DATA_DATA_FIELD_DESC)
    )
    other_feature: Optional[Literal['OTHER_DRILL_DOWN']] = Field(
        description=PromptFactory.render_prompt(TABLE_DATA_OTHER_FEATURE_FIELD_DESC),
        default=None
    )
    other_drill_down_data: Optional[List[Dict[str, Any]]] = Field(
        description=PromptFactory.render_prompt(TABLE_DATA_OTHER_DRILL_DOWN_DATA_FIELD_DESC),
        default=None
    )

    def check_data(self):
        if len(self.columns) == 0:
            raise ValueError("columns 不能为空")

        if len(self.columns) != max([len(d) for d in self.data]):
            raise ValueError("columns 长度必须等于 data 中每行数据的长度")

        column_names = set([d.column for d in self.columns])
        for d in self.data:
            for k, v in d.items():
                if k not in column_names:
                    raise ValueError("data 中存在未知的列：" + k)

        if self.other_drill_down_data and self.other_feature is None:
            raise ValueError("other_drill_down_data 不为空时，other_feature 必须设置！")

        if self.other_feature is not None and self.other_feature != 'OTHER_DRILL_DOWN':
            raise ValueError("other_feature 不为空时，必须设置必须为 OTHER_DRILL_DOWN")

        if self.other_drill_down_data:
            for d in self.other_drill_down_data:
                if 'cname' not in d:
                    raise ValueError("other_drill_down_data 中必须包含 cname 字段")


class _MeasureFormat(BaseModel):
    type: Literal['percent', 'number'] = Field(description="格式化类型，可选：percent 百分比、number 数字")
    precision: int = Field(description="精度，2 代表保留两位小数，0 代表整数", default=0)

    def check_data(self):
        if not self.type:
            raise ValueError("format 必须包含 type 字段")
        if self.type not in ['number', 'percent']:
            raise ValueError("format type 字段必须为 number 或 percent")
        if not isinstance(self.precision, int):
            raise ValueError("format 的 precision 必须为 int 类型")


class _Measure(BaseModel):
    name: str = Field(description="指标名称，示例：触达率")
    column: str = Field(description="指标对应的列名，示例：reach_rate")
    format: Optional[_MeasureFormat] = Field(description="可选：指标的格式化信息", default=None)
    threshold: Optional[Dict[str, Any]] = Field(description="可选：标准线，示例：{'value': 0.6}", default=None)

    def check_data(self):
        if self.threshold:
            if self.threshold.get('value', None) is None:
                raise ValueError("标准线必须包含 value 字段")
            if not isinstance(self.threshold.get('value'), (int, float)):
                raise ValueError("标准线 value 字段必须为数字")
        if self.format:
            self.format.check_data()


class _ChartData(BaseModel):
    type: List[Literal['TABLE', 'INTERVAL', 'LINE', 'PIE']] = Field(
        default=['TABLE'],
        description=PromptFactory.render_prompt(CHART_DATA_TYPE_FIELD_DESC)
    )
    columns: List[_ColumnInformation] = Field(description="列信息")
    by_field: _ColumnInformation = Field(description='该图表的维度字段列信息')
    data: List[Dict[str, Any]] = Field(description="行数据, 字典 key 为列信息的 column, value 是列值")
    measures: List[_Measure] = Field(description="该图表的指标信息")

    def check_data(self):
        if len(self.columns) == 0:
            raise ValueError("columns 不能为空")
        if len(self.columns) != max([len(d) for d in self.data]):
            raise ValueError("columns 长度必须等于 data 中每行数据的长度")

        column_names = set([d.column for d in self.columns])
        for d in self.data:
            for k, v in d.items():
                if k not in column_names:
                    raise ValueError("data 中存在未知的列：" + k)

        if self.by_field.column not in column_names:
            raise ValueError("by_field 中指定的列不存在：" + self.by_field.column)

        for measure in self.measures:
            if measure.column not in column_names:
                raise ValueError("measures 中指定的列不存在：" + measure.column)
            measure.check_data()


class _EntityInfo(BaseModel):
    id: int | str = Field(description="实体ID")
    type: Literal['AUDIENCE', 'PLAN', 'CANVAS'] = Field(description="实体类型", default="AUDIENCE")
    name: str = Field(description="实体名称")


class _EntityRelationData(BaseModel):
    entity: _EntityInfo = Field(description=PromptFactory.render_prompt(ENTITY_RELATION_DATA_ENTITY_FIELD_DESC))
    relation: List[_EntityInfo] = Field(description=PromptFactory.render_prompt(ENTITY_RELATION_DATA_RELATION_FIELD_DESC))

    def check_data(self):
        if not self.relation:
            raise ValueError("实体关系数据错误，关系实体信息不能为空")


class DisplayInputData(BaseModel):
    table_data: _TableData | None = Field(description="表格数据", default=None)
    chart_data: _ChartData | None = Field(description="图表数据", default=None)
    entity_relation_data: _EntityRelationData | None = Field(description="实体关系数据", default=None)


# 消息格式：https://doc.sensorsdata.cn/pages/viewpage.action?pageId=594353715
class _InnerModel(BaseModel):
    resource_id: int = Field(description="指定的资源ID")
    chart_type: str = Field(
        description=PromptFactory.render_prompt(INNER_MODEL_CHART_TYPE_FIELD_DESC),
        default="TABLE"
    )
    query_sql: str | None = Field(description="图表展示对应的 SQL 语句", default=None)
    display_data: DisplayInputData = Field(
        description=PromptFactory.render_prompt(INNER_MODEL_DISPLAY_DATA_FIELD_DESC),
    )


class DataRenderTool(BaseTool):
    name: str = "DataRenderTool"
    description: str = PromptFactory.render_prompt(DATA_RENDER_TOOL_NAME_FIELD_DESC)
    args_schema: Type[BaseModel] = _InnerModel

    # 渲染结果回调
    resource_callback: Callable[[int, Any], Any] | None = None

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def _format_table_data(self, display_data) -> dict:
        table_data = display_data
        table_data.check_data()
        return table_data.model_dump()

    def _format_chart_data(self, display_data) -> dict:
        chart_data = display_data
        chart_data.check_data()
        return chart_data.model_dump()

    def _format_entity_relation_data(self, display_data) -> dict:
        entity_relation_data = display_data
        entity_relation_data.check_data()
        return entity_relation_data.model_dump()

    def _check_and_get_result(
            self,
            chart_type: str,
            display_data: Any,
            query_sql: str | None = None,
            **kwargs: Any
    ) -> ViewMessage:
        if not chart_type:
            raise ValueError('图表类型 chart_type 不能为空')
        chart_type = chart_type.strip().upper()
        if not chart_type in supported_chart_types:
            raise ValueError(f"不支持的图表类型: {chart_type}")

        log.info(f"from_display_data: type={type(display_data)}, data={display_data}")

        if isinstance(display_data, BaseModel):
            display_data = display_data.model_dump_json()
        if not isinstance(display_data, str):
            display_data = json.dumps(display_data, ensure_ascii=False)
        if not isinstance(display_data, dict):
            display_data = json.loads(display_data)

        log.info(f"display_data={display_data}")
        display_data = DisplayInputData.model_validate_json(json.dumps(display_data, ensure_ascii=False))

        result = {}
        if chart_type == 'TABLE':
            result = self._format_table_data(display_data=display_data.table_data)
            result['sql'] = {'value': query_sql}
        elif chart_type == 'CHART':
            result = self._format_chart_data(display_data=display_data.chart_data)
        else:
            result = self._format_entity_relation_data(display_data=display_data.entity_relation_data)

        return ViewMessage(
            type=chart_type,
            content=json.dumps(result, ensure_ascii=False)
        )

    def _run(self, resource_id: int, **kwargs) -> str:
        try:
            result = self._check_and_get_result(**kwargs)
            self.display(resource_id, result)
            return f"已按照要求渲染和展示数据内容，resource_id={resource_id}，图表数据：{result.content}"
        except Exception as e:
            log.warning(f"render error.", e)
            return f"渲染失败，请检查数据格式是否正确: {str(e)}"

    async def _arun(self, resource_id: int, **kwargs: Any) -> str:
        try:
            result = self._check_and_get_result(**kwargs)
            await self.async_display(resource_id, result)
            return f"已按照要求渲染和展示数据内容，resource_id={resource_id}，图表数据：{result.content}"
        except Exception as e:
            log.warning(f"render error.", e)
            return f"渲染失败，请检查数据格式是否正确: {str(e)}"

    async def async_display(self, resource_id: int, content: ViewMessage):
        if content is None:
            return
        if self.resource_callback:
            try:
                if iscoroutinefunction(self.resource_callback):
                    await self.resource_callback(resource_id, content)
                else:
                    self.resource_callback(resource_id, content)
            except BaseException as e:
                log.warning('display callback error.', e)

    def display(self, resource_id: int, content: ViewMessage):
        if content is None:
            return
        if self.resource_callback:
            try:
                if iscoroutinefunction(self.resource_callback):
                    tools.asyncio_run(lambda: self.resource_callback(resource_id, content))
                else:
                    self.resource_callback(resource_id, content)
            except BaseException as e:
                log.warning('display callback error.', e)
