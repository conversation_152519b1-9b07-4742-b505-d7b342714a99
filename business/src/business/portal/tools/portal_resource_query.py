from typing import Type, Any, List
import requests
import time
import pandas as pd
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field
from common import tools

from apis.definition.portal.portal_bot_pb2 import InsightParams
from business.portal.tools.dataframe_converter import DataFrameConverter
from framework.prompt import PromptFactory

log = tools.get_logger()

PORTAL_RESOURCE_QUERY_TOOL_DESC=PromptFactory.create_or_get_prompt(
    business="portal",
    business_cname="下一代产品 Demo",
    prompt_code="PORTAL_RESOURCE_QUERY_TOOL_DESC",
    prompt_name="Portal 资源查询工具描述",
    prompt_content="用于查询当前分析策略的资源覆盖度，包括可用的资源、使用资源以及未用资源信息"
)


class _InnerModel(BaseModel):
    resource_coverage_threshold: float = Field(default=90.0, description="资源覆盖度阈值，默认90%")
    requirement_desc: str = Field(default="", description="本次分析的需求描述")


class PortalResourceQueryTool(BaseTool):
    name: str = "PortalResourceQueryTool"
    description: str = PromptFactory.render_prompt(PORTAL_RESOURCE_QUERY_TOOL_DESC)
    args_schema: Type[BaseModel] = _InnerModel

    tenant_project_info: dict
    insight_params: InsightParams

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def _run(self, resource_coverage_threshold: float = 90.0, requirement_desc: str = "", **kwargs: Any) -> Any:
        insight_params = self.insight_params
        
        # 检查 insight_params 是否为空或无效
        if not self.insight_params:
            insight_params = None
        else:
            insight_params = self.insight_params
            
            # 检查是否是默认的空对象
            if (getattr(insight_params, 'start_time', '') == '' and 
                getattr(insight_params, 'end_time', '') == '' and 
                getattr(insight_params, 'level', 0) == 0 and 
                getattr(insight_params, 'scene_id', 0) == 0 and 
                getattr(insight_params, 'campaign_case_id', 0) == 0 and 
                len(getattr(insight_params, 'strategies', [])) == 0):
                insight_params = None  # 重置为空，使用默认值
        
        # 从insight_params提取strategy_ids
        strategy_ids = []
        if insight_params and hasattr(insight_params, 'strategies') and insight_params.strategies:
            for strategy in insight_params.strategies:
                if hasattr(strategy, 'ids') and strategy.ids:
                    for sid in strategy.ids:
                        if str(sid).isdigit():
                            strategy_ids.append(int(sid))

        # 如果没有从insight_params获取到，使用默认值
        if not strategy_ids:
            strategy_ids = [1001]
        
        # 验证阈值参数
        if resource_coverage_threshold <= 0:
            resource_coverage_threshold = 90.0

        # API配置
        api_url = f"{self.tenant_project_info['cdp_host']}/api/v3/analytics/v1/model/sql/query"
        headers = {
            'accept': 'application/json',
            'api-key': self.tenant_project_info['api_key'],
            'sensorsdata-project': self.tenant_project_info['project_name'],
            'Content-Type': 'application/json'
        }

        try:
            # 1. 查询可用资源对照范围（直接查询现有表）
            available_resources_query = """
            SELECT 
                resource_category_code,
                resource_sub_category_code,
                resource_sub_category,
                resource_category
            FROM horizon_workspace_demo_3.ai_demo_total_resource 
            ORDER BY resource_category_code, resource_sub_category_code
            """

            # 2. 查询所有策略已使用的资源（聚合查询）
            strategy_ids_str = ','.join([str(sid) for sid in strategy_ids])
            used_resources_query = f"""
            SELECT 
                rur.resource_category_code,
                rur.resource_sub_category_code,
                rtr.resource_sub_category,
                rtr.resource_category,
                rur.strategy_type,
                COUNT(DISTINCT rur.strategy_id) as strategy_count
            FROM horizon_workspace_demo_3.ai_demo_resource_usage_03 rur
            LEFT JOIN horizon_workspace_demo_3.ai_demo_total_resource rtr 
                ON rur.resource_category_code = rtr.resource_category_code 
                AND rur.resource_sub_category_code = rtr.resource_sub_category_code
            WHERE rur.strategy_id IN ({strategy_ids_str})
            GROUP BY rur.resource_category_code, rur.resource_sub_category_code, rtr.resource_sub_category, rtr.resource_category, rur.strategy_type
            ORDER BY rur.resource_category_code, rur.resource_sub_category_code
            """

            # 执行查询并转换为DataFrame
            available_df = self._execute_query_to_dataframe(api_url, headers, available_resources_query)
            used_df = self._execute_query_to_dataframe(api_url, headers, used_resources_query)

            # 分析聚合资源覆盖度
            result = self._analyze_aggregated_resource_coverage(
                strategy_ids, available_df, used_df, resource_coverage_threshold
            )

            log.info(f"PortalResourceQueryTool 执行结果: {result}")
            return result
        except Exception as e:
            error_msg = f"资源覆盖度分析失败: {str(e)}"
            log.info(f"PortalResourceQueryTool 执行结果: {error_msg}")
            return error_msg

    def _execute_query_to_dataframe(self, api_url: str, headers: dict, sql_query: str) -> pd.DataFrame:
        """执行SQL查询并返回DataFrame，使用通用的DataFrameConverter，添加重试机制"""
        payload = {
            "sql": sql_query,
            "limit": "1000",
            "request_id": f"resource_coverage_{int(time.time())}"
        }

        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                response = requests.post(api_url, headers=headers, json=payload)
                response.raise_for_status()

                # 使用通用的DataFrameConverter
                response_text = response.text.strip()
                df = DataFrameConverter.json_to_dataframe(response_text)
                return df
            except Exception as e:
                retry_count += 1
                if retry_count >= max_retries:
                    return pd.DataFrame()
                else:
                    time.sleep(2)  # 等待2秒后重试

    def _analyze_aggregated_resource_coverage(self, strategy_ids: List[int], available_df: pd.DataFrame,
                                              used_df: pd.DataFrame, threshold: float) -> str:
        """分析多个策略的聚合资源覆盖度并生成统一报告"""

        if available_df.empty:
            result = f"❌ 未找到可用资源数据，请检查数据库连接和表结构"
            log.info(f"PortalResourceQueryTool 执行结果: {result}")
            return result

        # 构建资源使用情况分析
        result = f"## 资源使用覆盖度\n\n"
        
        # 分析已使用和未使用的资源
        used_resources_by_category = {}
        unused_resources_by_category = {}
        
        # 按资源类别分组
        for _, row in available_df.iterrows():
            category = row.get('resource_category', '')
            sub_category = row.get('resource_sub_category', '')
            
            if category not in used_resources_by_category:
                used_resources_by_category[category] = []
                unused_resources_by_category[category] = []
            
            # 检查是否被使用
            is_used = False
            if not used_df.empty:
                for _, used_row in used_df.iterrows():
                    if (used_row.get('resource_category') == category and 
                        used_row.get('resource_sub_category') == sub_category):
                        is_used = True
                        break
            
            if is_used:
                used_resources_by_category[category].append(sub_category)
            else:
                unused_resources_by_category[category].append(sub_category)
        
        # 生成分析建议
        analysis_points = []
        for category, unused_list in unused_resources_by_category.items():
            if unused_list:
                if category == '权益类型':
                    analysis_points.append(f"权益活动类内容应用不足，当前仅使用{', '.join(used_resources_by_category.get(category, []))}，建议加入{', '.join(unused_list[:2])}以提升用户吸引力")
                elif category == '产品类型':
                    analysis_points.append(f"金融产品类内容应用不足，可补充{', '.join(unused_list[:2])}以完善产品推荐")
                else:
                    analysis_points.append(f"{category}类内容应用不足，建议补充{', '.join(unused_list[:2])}")
        
        # 输出分析建议
        for i, point in enumerate(analysis_points, 1):
            result += f"{i}. {point}\n"
        
        result += "\n"
        
        # 生成资源对比表格
        result += f"| 资源类型 | 已使用资源 | 未使用资源 |\n"
        result += f"|---|---|---|\n"
        
        for category in used_resources_by_category.keys():
            used_list = used_resources_by_category.get(category, [])
            unused_list = unused_resources_by_category.get(category, [])
            
            used_str = ', '.join(used_list) if used_list else '无'
            unused_str = ', '.join(unused_list) if unused_list else '无'
            
            result += f"| {category} | {used_str} | {unused_str} |\n"
        
        result += "\n"

        # 计算总体覆盖度
        total_available = len(available_df)
        total_used = len([row for _, row in available_df.iterrows() 
                         for _, used_row in used_df.iterrows() 
                         if (row.get('resource_category') == used_row.get('resource_category') and 
                             row.get('resource_sub_category') == used_row.get('resource_sub_category'))])
        
        overall_coverage = (total_used / total_available * 100) if total_available > 0 else 0
        
        result += f"📈 总体资源覆盖度: {overall_coverage:.1f}%\n\n"
        
        # 根据阈值判断覆盖度是否达标
        if overall_coverage >= threshold:
            result += f"✅ 总体资源覆盖度{overall_coverage:.1f}%已达到阈值{threshold}%，资源使用情况良好。\n\n"
        else:
            result += f"🌱 总体资源覆盖度{overall_coverage:.1f}%低于阈值{threshold}%，建议进一步提升资源使用效率。\n\n"

        return result

    async def _arun(self, **kwargs: Any) -> str:
        return self._run(**kwargs)
