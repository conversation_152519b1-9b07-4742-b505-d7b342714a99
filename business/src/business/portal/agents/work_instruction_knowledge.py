import json
from typing import Dict, List, Generator, Any, AsyncGenerator, Callable

from jinja2 import Template
from langchain_core.messages import BaseMessage, HumanMessage
from onnxruntime.capi.onnxruntime_pybind11_state import RuntimeException
from pydantic import BaseModel

from business.config import KNOWLEDGE_DATA_PATH
from business.knowledge_factory import KnowledgeFactory
from business.portal.tools import KnowledgeRenderTool
from business.scenario.agents.knowledge_agent import KnowledgeAgent
from framework.prompt import PromptFactory
from core.models import KnowledgebaseModel
from core.service.knowledgebase_service import KnowledgebaseService
from framework.agents.autogen import ParticipantAgent, ReactManagerAgent, AutoGenWorkflow, ReActParticipantAgent
from framework.agents.autogen.react_manager import COMMON_BACKGROUND_KNOWLEDGE
from framework.agents.schema import Conversation, Role
from framework.knowledge import SimpleRag, KnowledgeBase, RagClient
from framework.models import BaseLLM

from common import tools

log = tools.get_logger()

TOPIC_EXTRACT_SYSTEM_PROMPT = """你是一个思路指南解析助手，你将得到一篇思路描述，你需要告知思路描述中描述的思路所解决的问题的主题。
主题是涵盖思路适用场景的一句话描述，比如：

```json
{
    "topics": ["分析企业内获客成本较高的思路", "..."]
}
```

你需要仔细分析文档内容，然后按照上述的格式，在一个 json 对象中给出主题内容。

回复注意事项：
  1. 你必须将主题内容放在一个 markdown 代码块中，并且你的回复中只能有这一个 markdown 代码块！
  2. 你必须先思考，然后按照上述格式回复主题内容；
  3. 你回复的主题不能超过 5 个！
"""


async def get_topic_extract_system_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="portal",
        business_cname="下一代产品 Demo",
        prompt_code="PORTAL_WORK_INSTRUCTION_TOPIC_EXTRACT_SYSTEM_PROMPT",
        prompt_name="Portal 工作方法指示 - 主题抽取系统提示词",
        prompt_desc="Portal 工作方法指示 - 主题抽取系统提示词",
        prompt_content=TOPIC_EXTRACT_SYSTEM_PROMPT,
    )
    return template


BACKGROUND_KNOWLEDGE = f"""{COMMON_BACKGROUND_KNOWLEDGE}

策略用例（Campaign Case）：即一组由运营计划、流程画布、资源位等组合而成的，用于解决某个特定场景的一组运营策略，
一个策略用例中可能包含了多个运营计划、流程画布、资源位等运营方法和手段。
"""


async def get_background_knowledge_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="portal",
        business_cname="下一代产品 Demo",
        prompt_code="PORTAL_WORK_INSTRUCTION_BACKGROUND_KNOWLEDGE_PROMPT",
        prompt_name="Portal 工作方法指示 - 背景知识",
        prompt_desc="Portal 工作方法指示 - 背景知识",
        prompt_content=BACKGROUND_KNOWLEDGE,
    )
    return template


async def extract_additional_extract_topic(document: str, llm: BaseLLM) -> List[str]:
    """额外抽取主题，主要抽取当前流程的名称、适用场景"""
    topics = []
    prompt = get_topic_extract_system_prompt().render()
    messages = [
        {"role": "system", "content": prompt},
        {"role": "user", "content": f"文档内容如下：\n\n{document}"}
    ]
    for i in range(3):
        try:
            response = llm.complete(input=messages, stream=False)
            messages.append({"role": "assistant", "content": response.content})
            code = tools.extract_code_from_text(response.content)
            if not code:
                raise RuntimeException("你必须将主题 json 包含在 markdown 代码块中！")
            topics = json.loads(code)['topics']
            if topics:
                return topics
            else:
                raise RuntimeException("无法识别到回复中的 topics")
        except BaseException as e:
            log.warning(f"extract additional topic error. retry={i}", e)
            messages.append({"role": "user", "content": f"你的回复错误：{e}，请重新回复！"})
    return topics


default_entity_types = {
    "name": "工作流程名称，名称需要覆盖工作流程的适用场景"
}


async def get_or_init_mind_map_knowledge(organization_id: str, project_id: int) -> RagClient:
    knowledgebase_name = f"work_mind_map@{organization_id}@{project_id}"
    client = await KnowledgeFactory.async_get_knowledge_base_client(name=knowledgebase_name)
    if client:
        return client

    client_config = {
        "cname": f"场景思路指南@{organization_id}@{project_id}",
        "name": knowledgebase_name,
        "description": "工作思路说明，必须优先检索此工作思路说明，然后根据工作思路说明再让其他 role 检索对应的思路中的细化内容！",
        "base_dir": str(
            KNOWLEDGE_DATA_PATH / 'customers' / organization_id / f"{project_id}_work_mind_map"
        ),
        "index_type": "SIMPLE",
        "config": json.dumps({}),
        "custom_knowledge_class": "business.portal.agents.work_instruction_knowledge.WorkMindMapKnowledge",
    }
    kb_service = KnowledgebaseService()
    log.info(f"Start create knowledgebase {client_config['cname']}")
    await kb_service.save_knowledgebase(knowledgebase=KnowledgebaseModel(**client_config))
    client = await  KnowledgeFactory.async_get_knowledge_base_client(name=knowledgebase_name)
    if client:
        return client
    else:
        raise RuntimeError(f"无法初始化知识库 {knowledgebase_name}, project={organization_id}/{project_id}")


class WorkMindMapKnowledge(SimpleRag):
    """
    场景思路指南
    """

    def __init__(
            self,
            kb: KnowledgeBase,
            entity_types: Dict[str, str] | None = None,
            retrieve_documents_only: bool = True,
            **kwargs,
    ):
        if not entity_types:
            entity_types = default_entity_types
        super().__init__(
            kb=kb,
            entity_types=entity_types,
            retrieve_documents_only=retrieve_documents_only,
            additional_extract_topics=[extract_additional_extract_topic],
            chunk_size=-1,  # 不做 chunk
            columns_to_embed=['summary', 'topics'],  # 只按照主题和摘要匹配检索
            **kwargs
        )


async def get_or_init_other_knowledge(
        organization_id: str, project_id: int,
        name: str, cname: str, description: str
) -> RagClient:
    knowledgebase_name = name
    client = await KnowledgeFactory.async_get_knowledge_base_client(name=knowledgebase_name)
    if client:
        return client

    client_config = {
        "cname": cname,
        "name": knowledgebase_name,
        "description": description,
        "base_dir": str(
            KNOWLEDGE_DATA_PATH / 'customers' / organization_id / name
        ),
        "index_type": "SIMPLE",
        "config": json.dumps({
            "entity_types": {
                "concept": "概念，指的是一些特定领域的概念"
            },
            "pre_knowledge": "",
            "query_limit": 5,
            "max_talk_round": 2,
            "score_limit": 0.4,
            "additional_retrieve_requirement": "",
            "retrieve_documents_only": False,
            "chunk_size": 2000
        }, ensure_ascii=False),
    }
    kb_service = KnowledgebaseService()
    log.info(f"Start create knowledgebase {cname}")
    await kb_service.save_knowledgebase(knowledgebase=KnowledgebaseModel(**client_config))
    client = await KnowledgeFactory.async_get_knowledge_base_client(name=knowledgebase_name)
    if client:
        return client
    else:
        raise RuntimeError(f"无法初始化知识库 {name}/{cname}, project={organization_id}/{project_id}")


class WorkInstructionAgent(ParticipantAgent):
    """工作方法指示，需要把工作思路放在 resource 里面"""

    def __init__(
            self,
            name: str,
            description: str,
            tenant_info: dict | None = None,
            callback: Callable[[Any], Any] | None = None,
            output_as_final_answer: bool = False,
            result_as_resource: bool = False,  # 是否将结果当成资源（对话内使用）
            **kwargs,
    ):
        super().__init__(name=name, description=description, **kwargs)
        self.tenant_info = tenant_info
        self.workflow = None
        self.callback = callback
        self.output_as_final_answer = output_as_final_answer
        self.result_as_resource = result_as_resource
        self.kwargs = kwargs

    async def get_or_init_agents(self) -> List[ParticipantAgent]:
        results = []
        organization_name = self.tenant_info['tenant_cname']
        organization_id = self.tenant_info['organization_id']
        project_id = self.tenant_info['project_id']

        mind_map_rag_client = await get_or_init_mind_map_knowledge(
            organization_id=organization_id,
            project_id=project_id
        )
        results.append(KnowledgeAgent(
            name='work_mind_map_role',
            description=mind_map_rag_client.description,
            rag_client=mind_map_rag_client
        ))

        # name, cname, description
        other_knowledge_names = [
            (f"preset_msjdffl@{organization_id}@{project_id}", f"{organization_name}_描述解读方法论", "描述解读方法论"),
            (f"preset_gyffl@{organization_id}@{project_id}", f"{organization_name}_归因方法论", "归因方法论"),
            (f"preset_jjfal@{organization_id}@{project_id}", f"{organization_name}_解决方案类", "解决方案类"),
            (f"preset_zdffffl@{organization_id}@{project_id}", f"{organization_name}_诊断分析方法论", "诊断分析方法论"),
        ]
        for name, cname, description in other_knowledge_names:
            other_knowledge_client = await get_or_init_other_knowledge(
                organization_id=organization_id,
                project_id=project_id,
                name=name,
                cname=cname,
                description=description
            )
            results.append(KnowledgeAgent(
                name=name,
                description=other_knowledge_client.description,
                rag_client=other_knowledge_client
            ))

        render_tool = KnowledgeRenderTool()
        render_agent = ReActParticipantAgent(
            name="knowledge_render_tool",
            description="知识数据可视化渲染角色，负责将知识渲染成可展示的内容。如果最终检索出来的知识有效，则尽量使用该角色渲染，需要手动指定渲染的资源 id（来源于上文或者重新生成）",
            tools=[render_tool],
            llm=self.llm,
            max_iterations=5,
        )
        results.append(render_agent)
        render_tool.resource_callback = lambda resource_id, data: self.resources.update(
            {resource_id: data}
        )

        return results

    async def _init_workflow(self):
        if self.workflow is not None:
            return

        agents = await self.get_or_init_agents()
        manager = ReactManagerAgent(
            name="work_instruction_dispatch_manager",
            llm=self.llm,
            background_knowledge=await get_background_knowledge_prompt(),
        )

        self.workflow = AutoGenWorkflow(
            manager_agent=manager,
            participant_agents=agents,
            callback=self.callback,
            max_talk_round=5,
            num_concurrent_runs=1,
            stream=self.kwargs.get('stream', True),
            think_progress=True,
            output_as_final_answer=self.output_as_final_answer,
        )

    def run(self) -> BaseMessage:
        """运行 agent 步骤"""
        return tools.asyncio_run(lambda: self.arun())

    def stream_run(self) -> Generator[BaseMessage, Any, Any]:
        yield self.run()

    async def arun(self) -> BaseMessage:
        """运行 agent 步骤"""
        history = self.get_history()
        history.append(HumanMessage(content=f"你是 {self.name}，请按照要求检索并渲染内容", name='system'))
        if len(history) > 10:
            history = history[-10:]

        messages = []
        for message in history:
            if not isinstance(message, HumanMessage):
                messages.append(Conversation(role=Role.ASSISTANT, content=message.content))
            else:
                messages.append(Conversation(role=Role.USER, content=message.content))
        log.info(f'history: {messages}')

        await self._init_workflow()

        response = await self.workflow.run_with_history(messages)
        log.info(f'Work instruction reporter response: {response.content}')

        # 返回渲染内容
        resources = self.resources.copy()
        if not response:
            response = "未检索到内容"
            return self._convert_message(message={'role': 'assistant', 'content': response, 'name': self.name})[0]

        if self.result_as_resource:
            response = f"已完成检索，检索资源：resource_ids={resources.keys()}"
        else:
            new_resources = {}
            for k, v in resources.items():
                new_resources[k] = v.model_dump() if isinstance(v, BaseModel) else v
            response = json.dumps([v for v in new_resources.values()], ensure_ascii=False)

        return self._convert_message(message={'role': 'assistant', 'content': response, 'name': self.name})[0]

    async def astream_run(self) -> AsyncGenerator[Any, Any]:
        response = await self.arun()
        yield response
