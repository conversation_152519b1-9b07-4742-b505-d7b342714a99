from typing import Any, AsyncGenerator, Generator

from langchain_core.messages import BaseMessage, HumanMessage

from common import tools
from framework.agents.assistant import AssistantAgent
from framework.models import BaseLLM
from framework.agents.autogen import (
    ParticipantAgent
)

log = tools.get_logger()


class CampaignOptimizeAgent(ParticipantAgent):
    """策略优化 agent，遵循固定流程，先检索后优化和回复"""

    def __init__(
            self,
            llm: BaseLLM,
            tenant_info: dict | None = None,
            **kwargs,
    ):
        kwargs = {
                     "llm": llm,
                 } | kwargs
        super().__init__(**kwargs)
        self.tenant_info = tenant_info
        self.kwargs = kwargs

    def run(self) -> BaseMessage:
        """运行 agent 步骤"""
        return tools.asyncio_run(lambda: self.arun())

    def stream_run(self) -> Generator[BaseMessage, Any, Any]:
        yield self.run()

    async def arun(self) -> BaseMessage:
        """运行 agent 步骤"""
        history = self.get_history().copy()
        history.append(HumanMessage(content="请根据需求，结合前面的内容，给出优化建议"))
        if len(history) > 15:
            history = history[-15:]

        agent = AssistantAgent(llm=self.llm)
        await agent.async_add_history(history)
        response = await agent.achat_and_save()

        return self._convert_message(message={'role': 'assistant', 'content': response, 'name': self.name})[0]

    async def astream_run(self) -> AsyncGenerator[Any, Any]:
        response = await self.arun()
        yield response
