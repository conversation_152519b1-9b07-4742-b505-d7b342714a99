from inspect import iscoroutinefunction
from typing import Generator, Any, AsyncGenerator, Callable

from jinja2 import Template
from langchain_core.messages import BaseMessage
import re
from common import tools
from framework.agents.assistant import AssistantAgent
from framework.agents.autogen import ParticipantAgent, ManagerAgent
from framework.agents.schema import ViewMessage
from framework.prompt import PromptFactory

log = tools.get_logger()

SYSTEM_PROMPT = """
# 职责
你正在一个角色扮演游戏中，你擅长根据其他角色的讨论内容，撰写最终报告。
你需要仔细理解其他角色的讨论内容，并撰写最终的报告。
你撰写的最终报告是能直接面向用户的，图文并茂的报告，用户只能看到你的报告内的信息，看不到其他角色回复的内容，也不知道其他角色的存在！所以不要使用任何代词等指代其他角色的回复内容！

其他的角色可能的回复包括：
  - 1. 普通文本，其他角色的讨论发言内容，或者角色的执行结果
  - 2. 资源，角色可能会给出图表、页面等资源，此时角色会告知资源的 id

# 回复要求
  1. 你需要直接根据内容撰写报告，不要对用户暴露你正在游戏中。也不要在回复内容中出现发言角色的名字！
  2. 禁止回复敏感内容，禁止回复其他角色未提及的内容。
  3. 你应该直接回复，禁止精简需要回复的内容！
  4. 你应该尽可能的在需要的地方展示其他角色渲染后的图表、表格等资源，尽量使用资源引用的方式来展示资源，而不是重新复述资源内容！
  5. 如果需要展示资源，则你必须使用占位符来替代需要展示的资源（如下所示）！禁止使用其他非展示资源的格式来提及资源！
  6. 禁止使用诸如代词等方式来指代资源、数据等，而是应该直接将原始的资源、数据等在需要的时候回复出来！
  7. 「图文并茂」的「图」指的是可展示的资源，而不是图片！

**资源展示要求：**
如果需要展示资源，则你必须将其他角色给出的资源id放在<|display_resource|>...<|end_resource|>中间，比如：

...
<|display_resource|>
resource_id=123
<|end_resource|>
...

资源展示注意事项：
  1. resource_id 必须是其他角色已经明确成功生成了的 resource_id，禁止使用比如渲染失败了的 resource_id；
  2. 每一个 <|display_resource|> 只能展示一个 resource_id，展示对个 resource_id 请使用多个 <|display_resource|>...<|end_resource|> 展示；
  3. <|display_resource|> 和 <|end_resource|> 中间是固定的格式：resource_id=<real_resource_id>，禁止在 <|display_resource|>...<|end_resource|> 中间加入其他的内容！
  3. 你不能回复用户“资源”这个概念，用户无法理解“资源”，使用正常的回复语气回复用户即可，在需要的地方展示即可，无需提示“资源”！
  4. <|display_resource|> 和 <|end_resource|> 是两个不可拆分的 token，请回复的时候注意不要遗漏的尖括号！

**资源展示示例：**  
错误的资源展示示例（中间不是 resource_id=xxx 的格式！）：  
<|display_resource|>
- **海口**: 78笔，总金额1662453元
- **合肥**: 67笔，总金额1161211元
- **沈阳**: 80笔，总金额1662893元
- **北京**: 67笔，总金额1569096元
<|end_resource|>

正确的资源展示示例：  
<|display_resource|>
resource_id=123
<|end_resource|>

请你撰写报告：
***
"""


async def get_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="portal",
        business_cname="下一代产品 Demo",
        prompt_code="PORTAL_REPORT_GENERATE_PROMPT",
        prompt_name="Portal 报告生成 Agent 提示词",
        prompt_desc="Portal 报告生成 Agent 提示词",
        prompt_content=SYSTEM_PROMPT,
    )
    return template


RESOURCE_START_TOKEN = "<|display_resource|>"
RESOURCE_END_TOKEN = "<|end_resource|>"
RESOURCE_ID_START = "resource_id="


class ReportGenerateParticipantAgent(ParticipantAgent):
    def __init__(self, manager: ManagerAgent, report_callback: Callable[[Any], Any], **kwargs):
        self.manager = manager
        self.report_callback = report_callback
        super().__init__(**kwargs)

        self.proxy_agent: AssistantAgent | None = None

        # 存储报告内容
        self.last_response = None

    async def _init_proxy_agent(self):
        if self.proxy_agent is not None:
            return
        self.proxy_agent = AssistantAgent(
            llm=self.llm,
            system_prompt=await get_prompt(),
        )

    def run(self) -> BaseMessage:
        if self.proxy_agent is None:
            tools.asyncio_run(self._init_proxy_agent)

        history = self.get_history()
        response = self.proxy_agent.chat(prompts=history)
        self.add_history(history=[{'role': 'assistant', 'name': self.name, 'content': response}])
        self.last_response = response
        return self._convert_message(message={'role': 'assistant', 'content': response, 'name': self.name})[0]

    async def arun(self) -> BaseMessage:
        await self._init_proxy_agent()
        history = self.get_history()
        response = await self.proxy_agent.achat(prompts=history)
        await self.async_add_history(history=[{'role': 'assistant', 'name': self.name, 'content': response}])
        self.last_response = response
        return self._convert_message(message={'role': 'assistant', 'content': response, 'name': self.name})[0]

    def stream_run(self) -> Generator[BaseMessage, Any, Any]:
        response = self.run()
        yield response

    async def astream_run(self) -> AsyncGenerator[BaseMessage, Any]:
        response = await self.arun()
        yield response

    def _check_and_get_resource(self, message: str) -> list:
        """检查文本片段中是否有资源引用"""
        if RESOURCE_START_TOKEN in message and RESOURCE_END_TOKEN in message:
            log.info(f"parse resource from message: {message}")
        else:
            return [ViewMessage(type='MARKDOWN', content=message)]

        def _load_resource_as_view_message(resource_str: str):
            try:
                resource_id = int(resource_str.split("=")[1].strip())
                all_resources = self.manager.collect_resources()
                log.info(f"all_resources: {all_resources}")
                resource_detail = all_resources.get(resource_id, None)
                if resource_detail is None:
                    resource_detail = ViewMessage(type='MARKDOWN', content=f'\n```\nNo resource id={resource_id}\n```')
                return resource_detail
            except Exception as e:
                log.error(f'load resource error: {e}')
                return ViewMessage(type='MARKDOWN', content=resource_str)

        start_escaped = re.escape(RESOURCE_START_TOKEN)
        end_escaped = re.escape(RESOURCE_END_TOKEN)
        pattern = f"{start_escaped}(.*?){end_escaped}"

        text_results = []
        matches = list(re.finditer(pattern, message, re.DOTALL))
        last_end = 0
        for match in matches:
            if match.start() > last_end:
                text_results.append(message[last_end:match.start()])
            text_results.append(match.group(1).strip())
            last_end = match.end()
        if last_end < len(message):
            text_results.append(message[last_end:])

        results = []
        for txt in text_results:
            if txt.strip().startswith(RESOURCE_ID_START):
                results.append(_load_resource_as_view_message(txt.strip()))
            else:
                results.append(ViewMessage(type='MARKDOWN', content=txt))

        results = [r if isinstance(r, ViewMessage) else ViewMessage(type='MARKDOWN', content=r) for r in results]
        return results

    async def get_report(self):
        callback = self.report_callback

        if callback is None:
            log.warning('callback is None.')
            return

        if not self.last_response:
            self.last_response = "无报告内容"

        log.info(f"{'*' * 50}\n原始报告内容：{self.last_response}")
        contents = self._check_and_get_resource(self.last_response)
        log.info(f"{'*' * 50}\n替换后报告内容：{contents}")

        if callback:
            try:
                if iscoroutinefunction(callback):
                    for content in contents:
                        await callback(content)
                else:
                    for content in contents:
                        callback(content)
            except BaseException as e:
                log.warning('callback error.', e)
