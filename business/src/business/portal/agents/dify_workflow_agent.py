from typing import Generator, Any, AsyncGenerator

from langchain_core.messages import BaseMessage

from business.scenario.tools import DifyWorkflowTool
from framework.agents.autogen import ParticipantAgent
from framework.agents.schema import Conversation
from common import tools as my_tools

log = my_tools.get_logger()


class DifyWorkflowParticipantAgent(ParticipantAgent):
    def __init__(
            self,
            scene: str,
            dify_api_base: str,
            dify_api_key: str,
            input_history: list[Conversation],
            **kwargs
    ):
        super().__init__(**kwargs)

        self.tool = DifyWorkflowTool(
            dify_api_base=dify_api_base,
            dify_api_key=dify_api_key,
            scene=scene
        )
        self.input_history = input_history

    def run(self) -> BaseMessage:
        """运行 agent 步骤，使用用户输入查询，不做任何改写"""
        try:
            # messages = [f"{h.role.name}: {h.content}" for h in self.input_history] # 只使用原始用户输入检索
            messages = [f"{h.type}: {h.content}" for h in self.get_history()]  # 使用推理过程语句
            response = self.tool._run(query='\n'.join(messages))
        except Exception as e:
            response = str(e)

        log.info(f"Retrieval from dify: {response}")
        return self._convert_message(message={'role': 'assistant', 'content': response, 'name': self.name})[0]

    async def arun(self) -> BaseMessage:
        return self.run()

    def stream_run(self) -> Generator[BaseMessage, Any, Any]:
        response = self.run()
        yield response

    async def astream_run(self) -> AsyncGenerator[BaseMessage, Any]:
        response = self.run()
        yield response
