from typing import Generator, Any, AsyncGenerator
from langchain_core.messages import BaseMessage
from framework.agents.autogen import AutoGenWorkflow
from framework.agents.autogen.complex_participant import ComplexParticipantAgent
from common import tools

log = tools.get_logger()


class WorkRunnerAgent(ComplexParticipantAgent):
    """流程执行 agent"""

    def __init__(
            self,
            workflow: AutoGenWorkflow,
            **kwargs,
    ):
        super().__init__(workflow=workflow, **kwargs)

    def run(self) -> BaseMessage:
        """运行 agent 步骤"""
        return tools.asyncio_run(lambda: self.arun())

    def stream_run(self) -> Generator[BaseMessage, Any, Any]:
        yield self.run()

    async def arun(self) -> BaseMessage:
        """运行 agent 步骤"""
        return await super().arun()

    async def astream_run(self) -> AsyncGenerator[Any, Any]:
        response = await self.arun()
        yield response
