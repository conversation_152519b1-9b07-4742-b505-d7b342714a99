from typing import Any, As<PERSON><PERSON>enerator, Generator, Callable

from langchain_core.messages import BaseMessage, HumanMessage

from common import tools
from framework.models import BaseLLM
from framework.agents.autogen import (
    ParticipantAgent, ReactManagerAgent, AutoGenWorkflow, ReActParticipantAgent
)
from framework.prompt import PromptFactory
from ..prompts.cdp_tables_description import get_cdp_tables_description
from ..tools import CDPSqlExecutorTool
from ..tools.data_render import DataRenderTool
from ...scenario.agents.knowledge_agent import KnowledgeAgent

log = tools.get_logger()

common_prompt_params = {
    "business": "portal",
    "business_cname": "下一代产品 Demo",
}


class DataAnalyseAgent(ParticipantAgent):
    """查数分析 agent"""

    def __init__(
            self,
            llm: BaseLLM,
            query_limit=5,
            max_talk_round=4,
            score_limit=0.4,
            tenant_info: dict | None = None,
            custom_knowledge: dict | None = None,
            display_callback: Callable[[Any], Any] | None = None,
            **kwargs,
    ):
        kwargs = {
                     "llm": llm,
                     "query_limit": query_limit,
                     "max_talk_round": max_talk_round,
                     "score_limit": score_limit,
                 } | kwargs
        super().__init__(**kwargs)
        self.tenant_info = tenant_info
        self.industry = self.tenant_info.get('industry')
        self.custom_knowledge = custom_knowledge
        self.render_callback = display_callback
        self.kwargs = kwargs
        self._workflow = None

    def run(self) -> BaseMessage:
        """运行 agent 步骤"""
        return tools.asyncio_run(lambda: self.arun())

    def stream_run(self) -> Generator[BaseMessage, Any, Any]:
        yield self.run()

    async def arun(self) -> BaseMessage:
        """运行 agent 步骤"""
        history = self.get_history().copy()
        history.append(HumanMessage(content="请根据需求检索知识库并在需要的时候通过 SQL 查询数据和回复结果"))
        if len(history) > 15:
            history = history[-15:]

        await self._init_workflow(**self.kwargs)

        response = await self._workflow.run_with_history(history)
        response = response.content
        log.info(f'workflow result: {response}')
        return self._convert_message(message={'role': 'assistant', 'content': response, 'name': self.name})[0]

    async def astream_run(self) -> AsyncGenerator[Any, Any]:
        response = await self.arun()
        yield response

    async def _init_workflow(self, **kwargs):
        if self._workflow is not None:
            return
        tenant_info = self.tenant_info.copy()
        agents = []

        # 通用知识库
        common_knowledge_agent = await KnowledgeAgent.common_knowledge_agent(llm=self.llm)
        if common_knowledge_agent:
            agents.append(common_knowledge_agent)

        # 行业知识库
        industry_knowledge_agent = await KnowledgeAgent.industry_knowledge_agent(
            llm=self.llm,
            industry=self.industry,
        )
        if industry_knowledge_agent:
            agents.append(industry_knowledge_agent)

        # 客户知识库
        customer_knowledge_agent = await KnowledgeAgent.customer_knowledge_agent(
            llm=self.llm,
            tenant_info=tenant_info,
            custom_knowledge=self.custom_knowledge,
        )
        if customer_knowledge_agent:
            agents.append(customer_knowledge_agent)

        agents.append(
            ReActParticipantAgent(
                name="sql_runner",
                description="执行数据查询的 SQL，请注意 SQL 必须符合 Impala SQL 语法",
                system_prompt_background=await get_cdp_tables_description(),
                tools=[CDPSqlExecutorTool(tenant_project_info=tenant_info)],
                llm=self.llm
            )
        )
        agents.append(
            ReActParticipantAgent(
                name="data_render",
                description="查询结果数据前端渲染工具",
                tools=[DataRenderTool(callback=self.render_callback)],
                llm=self.llm
            )
        )

        manager_generate_prompt = await PromptFactory.async_create_or_get_prompt(
            **common_prompt_params,
            prompt_code="PORTAL_DATA_ANALYSE_MANAGER_AGENT",
            prompt_name="Portal 数据分析角色的 Manager 提示词",
            prompt_desc="Portal 数据分析角色的 Manager 提示词",
            prompt_content="你需要尽量检索有用的知识库内容，在必要的时候需要通过 SQL 查询数据并实时渲染正确的数据",
        )
        manager = ReactManagerAgent(
            name="retrieval_manager",
            llm=self.llm,
            generate_prompt=manager_generate_prompt,
        )

        self._workflow = AutoGenWorkflow(
            manager_agent=manager,
            need_extract_real_query=True,  # 需要重新提取问题
            participant_agents=agents,
            max_talk_round=10,
            num_concurrent_runs=1,
            stream=kwargs.get('stream', True),
            think_progress=True,
            output_as_final_answer=False,
        )
