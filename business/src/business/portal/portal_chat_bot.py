import json
from typing import List, Callable
from jinja2 import Template

from apis.definition.portal.portal_bot_pb2 import InsightParams, PortalMessage
from common.tools import extract_code_from_text
from framework.agents.assistant import AssistantAgent
from framework.agents.autogen import (
    ReactManagerAgent, AutoGenWorkflow, ReActParticipantAgent, ParticipantAgent
)
from framework.agents.autogen.react_manager import COMMON_BACKGROUND_KNOWLEDGE
from framework.agents.schema import Conversation, Role, ViewMessage
from common import tools as my_tools
from .agents.campaign_optimize_agent import CampaignOptimizeAgent
from .agents.dify_workflow_agent import DifyWorkflowParticipantAgent
from framework.prompt import PromptFactory
from .agents.report_generate_agent import ReportGenerateParticipantAgent

from .base_bot import BasicPortalBot
from .prompts.cdp_tables_description import get_cdp_tables_description
from .constants import DIFY_BASE_UTL, KNOWLEDGE_WORKFLOW_API_KEY
from .tools import (
    PortalCampaignTargetTool,
    PortalExecutionProgressQueryTool,
    PortalResourceQueryTool,
    PortalCommunicateExecutionProgressTool,
    DataRenderTool, CDPSqlExecutorTool, KnowledgeRenderTool
)

log = my_tools.get_logger()

common_prompt_params = {
    "business": "portal",
    "business_cname": "下一代产品 Demo",
}

USER_SELECTOR_REACT_PROMPT_1 = """
# 回复格式
你必须按照以下格式回复（Thought/Choose/Do 三个都只能在你的回复中出现一次！）：
Thought: 当前问题已经解决了 xx，为了解决 xx 问题，我需要 xx 角色来做 xx 事情
Choose: 角色名称，角色名称只能从 {{ participants }} 中选一个
Do: 角色需要做的事情，比如：请你继续分析用户的意图

三个步骤的含义如前面的系统级别要求所示！

**如果用户给出了执行计划，则：**
  - 你需要严格按照执行计划指定合适的角色执行
  - 如果角色列表中存在 report_generate_role，则必须指定 report_generate_role 根据执行计划和其他角色执行的结果来生成最终的报告，不准让 reporter 角色生成报告！
  - report_generate_role 需要根据其他角色的结果汇总报告内容，所以 report_generate_role 一般会在其他角色之后执行！
  - 角色列表中如果没有 report_generate_role 角色，则禁止生成报告，而是使用正常回复！
  - 如果需要 report_generate_role 给出报告，则：
    - 你必须明确告知 report_generate_role 最终生成的报告包含那些章节，以及报告脉络，并且要求 report_generate_role 严格按照你告知的章节生成报告！
    - 在 report_generate_role 生成合适的报告，并检查通过后，你需要让 reporter 复述最后的报告内容。

**如果用户没有给出执行计划，则：**
  - 如果知识库有相关信息，则按照知识库检索结果回复用户，否则你需要自行分析问题，并最大程度上回复用户的问题。
  - 如果回复的内容中有依赖检索出来的知识，你应该尽可能让其他角色将检索出来的知识渲染成知识展示样式（注意这里是知识展示渲染），并且在最终回复的时候需要展示渲染结果。
  - 最终需要 reporter 详细回复用户，不需要回复报告。

**其他的你必须遵守的要求：**
  - 如果用户没有指定时间，则默认时间为上个月
  - 对于知识、图表的渲染，必须选取合适的角色来渲染，你不能直接自己渲染
  - 一些图表的约定：
    - 1) 对于趋势图等，一般是按照时间维度展示趋势；
    - 2) 对比图一般展示聚合汇总后的对比；
    - 3) 对于检索到的或者思考过程中发现的需要被展示的下钻维度信息，需要使用 data_render_role 渲染成下钻的展示样式，并在最终回复的时候展示渲染结果
    - 4) 如果对于检索到的内容，同时需要渲染成「知识样式」和「图表样式」（比如检索出来的下钻维度信息），则你需要分别逐个指定对应的角色进行渲染。
  - 确保回复内容有理有据，基于实际数据，如果中间用到了数据，应该说明数据出处。
  - 优先选择最能解决当前问题的专业角色。
  - 对于查数分析出来的的数据，如果是对比、趋势等需要展示图表的类型，你应该尽可能在回复中展示图表！中间用到的数据，应该尽可能说明数据来源以增加用户信任程度。
  - 如果有渲染的图表内容，你必须在每次渲染完成后仔细检查渲染的结果数据是否正确，不正确则需要重新渲染（重新渲染不修改资源id）。
    - 图表渲染不对的情况：数据错误、重复生成等。
  - 如果要给用户回复渲染的知识展示，则渲染的知识必须在回复的开头处给出。
  - 要求 reporter 不要重复展示同一个资源！

请按照上述格式分析并回复：
"""


async def get_user_selector_react_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        **common_prompt_params,
        prompt_code="PORTAL_MAIN_MANAGER_ROLE_SELECTOR_REACT_PROMPT",
        prompt_name="Portal 主机器人角色选择指南",
        prompt_desc="Portal 主机器人角色选择指南",
        prompt_content=USER_SELECTOR_REACT_PROMPT_1,
    )
    return template


BACKGROUND_KNOWLEDGE = COMMON_BACKGROUND_KNOWLEDGE + """

场景/子场景：用户自行创建的符合自身业务需求的、代表业务的集合。

业务目标：用户自行创建的、代表业务目标的集合。

策略用例（Campaign Case）：即一组由运营计划、流程画布、资源位等组合而成的，用于解决某个特定场景的一组运营策略，
一个策略用例中可能包含了多个运营计划、流程画布、资源位等运营方法和手段。

当前工作的项目信息：
{{project_information}}

当前时间：
{{curr_time}}

"""


async def get_background_knowledge_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        **common_prompt_params,
        prompt_code="PORTAL_MAIN_MANAGER_BACKGROUND_KNOWLEDGE_PROMPT",
        prompt_name="Portal 主机器人背景知识",
        prompt_desc="Portal 主机器人背景知识",
        prompt_content=BACKGROUND_KNOWLEDGE,
    )
    return template


RECOMMEND_QUESTION_PROMPT = """
根据用户对话记录，推荐最多三个用户可能想继续问的问题。

为了推荐问题，你可能需要以下背景知识：
""" + COMMON_BACKGROUND_KNOWLEDGE + """

**回复要求**：
你必须使用字符串的 json list 格式回复推荐问题，并且将推荐的问题包含在 markdown 代码块中，比如：
```json
[
  "问题一",
  "问题二",
  "问题三"
]
```
具体回复的问题内容，需要根据实际情况替换。
"""


async def get_recommend_question_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        **common_prompt_params,
        prompt_code="PORTAL_MAIN_MANAGER_RECOMMEND_QUESTION_PROMPT",
        prompt_name="Portal 推荐用户可能想继续问的问题",
        prompt_desc="Portal 推荐用户可能想继续问的问题",
        prompt_content=RECOMMEND_QUESTION_PROMPT,
    )
    return template


REPORT_CRITIC_PROMPT = """# 职责
你是一个专业的报告审核员，你需要仔细理解用户需求、执行计划，并审核报告是否与执行计划要求的报告一致。

# 报告审核要求
要求如下：
    - 报告必须是根据执行计划以及其他角色执行的结果来生成的，禁止在报告中出现猜测的数据、错误渲染的资源等内容！
    - 报告是根据其他角色的执行结果汇总的报告内容！报告中涉及的数据应该尽量详细！
    - 报告应该是图文并茂的，在必要的分析点展示图或者表格类型的数据信息，「图」指的是聊天历史中出现的各种渲染成功的资源。
    - 禁止使用语言化描述比如 “整体分析：整体分析数据”，因为用户看不到「整体分析数据」的具体数据！！
    - 报告应该尽可能的在需要的地方展示其他角色渲染后的图表、表格等资源，尽量使用资源引用的方式来展示资源，而不是重新复述资源内容！
    - 报告的章节，以及报告脉络与执行计划相符。
    - 报告中使用多层级的 markdown 标题结构（使用#号标题），以让报告结构更清晰。
    - 报告中引用的资源是通过类似 <|display_resource|>...<|end_resource|> 这种格式的占位符给出！
    - <|display_resource|> 和 <|end_resource|> 中间是固定的格式：resource_id=<real_resource_id>，禁止在 <|display_resource|>...<|end_resource|> 中间加入其他的内容！
    - 禁止重复展示同一个资源或者渲染失败的资源！
    - 禁止以非正确占位符的格式提及资源！

**资源展示占位符示例：**  
错误的资源展示占位符示例（中间不是 resource_id=xxx 的格式！）：  
<|display_resource|>
- **海口**: 78笔，总金额1662453元
- **合肥**: 67笔，总金额1161211元
- **沈阳**: 80笔，总金额1662893元
- **北京**: 67笔，总金额1569096元
<|end_resource|>

正确的资源展示示例：  
<|display_resource|>
resource_id=123
<|end_resource|>

# 回复内容
  - **如果被审核的报告符合要求**则你直接回复报告符合要求即可。
  - 否则指出报告存在的问题，并且要求重新给出报告！
"""


async def get_report_critic_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        **common_prompt_params,
        prompt_code="REPORT_CRITIC_PROMPT",
        prompt_name="Portal 报告评价提示词",
        prompt_desc="Portal 报告评价提示词",
        prompt_content=REPORT_CRITIC_PROMPT,
    )
    return template


class PortalChatBot(BasicPortalBot):
    def __init__(
            self,
            insight_strategy: InsightParams | None = None,
            insight_goal: InsightParams | None = None,
            capability: PortalMessage.Capability | None = None,
            **kwargs,
    ):
        """
        通用 Portal 机器人
        """
        kwargs = kwargs | {"name": "portal_common_bot"}
        super().__init__(**kwargs)
        log.info(f"PortalChatBot params: \ninsight_strategy={insight_strategy}\ninsight_goal={insight_goal}")

        self.insight_strategy = insight_strategy
        self.insight_goal = insight_goal
        self.capability = capability
        self.workflow = None

        self.max_token_limit = 128000

    async def async_chat(self, history: list[Conversation]) -> Conversation:
        log.info(
            f'start chat to {self.name}:\n'
            f'insight_strategy = {self.insight_strategy},\n'
            f'insight_goal = {self.insight_goal},\n'
            f'chat_session={self.chat_session_info}'
        )

        if len(history) > 10:
            history = history[-10:]

        result_getter = await self._init_multi_agent(history=history)

        log.info(f'chat to {self.name}. [history = {history}]')
        messages = []
        for message in history:
            if message.role == Role.ASSISTANT:
                messages.append({'role': 'assistant', 'content': message.content})
            else:
                messages.append({'role': 'user', 'content': message.content})
        log.info(f'history: {history}')

        response = await self.workflow.run_with_history(messages)

        if result_getter is not None:
            await result_getter()

        messages.append({"role": "assistant", "content": response.content})
        await self._recommend_questions(messages)

        return Conversation(
            role=Role.ASSISTANT,
            content=response.content
        )

    async def _recommend_questions(self, messages: List[dict]):
        if not messages:
            return
        if len(messages) > 4:
            messages = messages[-4:]

        agent = AssistantAgent(
            llm=self.llm,
            system_prompt=await get_recommend_question_prompt(),
        )
        await agent.async_add_history(messages)

        ask_prompt = await PromptFactory.async_create_or_get_prompt(
            **common_prompt_params,
            prompt_code="PORTAL_QUESTION_RECOMMEND_ASK_PROMPT",
            prompt_name="Portal 猜你想问用户提示词",
            prompt_desc="Portal 猜你想问用户提示词",
            prompt_content="请按要求格式推荐我下一步可能想问的问题：",
        )
        response = await agent.achat(prompts=PromptFactory.render_prompt(ask_prompt))
        log.info(f"Recommend agent output：{response}")
        code = extract_code_from_text(response)
        if not code:
            return

        try:
            questions = json.loads(code)
            if questions:
                await self.send_message(
                    message_type="RECOMMEND_QUESTION",
                    content=json.dumps(questions, ensure_ascii=False)
                )
        except BaseException as e:
            log.warning(f"Failed to parse code when recommend question: {e}")

    async def _init_multi_agent(self, history: list[Conversation]) -> Callable[[], None] | None:
        """初始化，返回结果获取方法或者空"""
        if self.workflow is not None:
            return None

        log.info("查询租户/项目信息")
        tenant_info = await self.get_tenant_project_info(chat_session_info=self.chat_session_info)
        if not tenant_info:
            log.error('获取项目信息失败，请先配置项目信息！')
            raise Exception('获取项目信息失败，请先配置项目信息！')

        insight_params = None
        scene = "data_analyse"
        if self.insight_strategy and self._is_valid_insight_params(self.insight_strategy):
            scene = "insight_strategy"
            insight_params = self.insight_strategy
        elif self.insight_goal and self._is_valid_insight_params(self.insight_goal):
            scene = "insight_goal"
            insight_params = self.insight_goal

        log.info(f"history length={len(history)}, scene={scene}")

        agents = []
        if scene in ["insight_strategy", "insight_goal"]:
            log.info("初始化目标查询 Agent")
            tool = PortalCampaignTargetTool(insight_params=insight_params, tenant_project_info=tenant_info)
            campaign_target_role_description = await PromptFactory.async_create_or_get_prompt(
                **common_prompt_params,
                prompt_code="campaign_target_role_description",
                prompt_name="目标查询 Agent 调用说明",
                prompt_desc="目标查询 Agent 调用说明",
                prompt_content=tool.description,
            )
            agents.append(
                ReActParticipantAgent(
                    name="campaign_target_role",
                    description=campaign_target_role_description,
                    tools=[tool],
                    llm=self.llm,
                    max_token_limit=self.max_token_limit,
                )
            )
        if scene in ["insight_strategy", "insight_goal"]:
            log.info("初始化目标执行进度 Agent")
            tool = PortalExecutionProgressQueryTool(insight_params=insight_params, tenant_project_info=tenant_info)
            campaign_execution_progress_role_description = await PromptFactory.async_create_or_get_prompt(
                **common_prompt_params,
                prompt_code="campaign_execution_progress_role_description",
                prompt_name="目标执行进度查询 Agent 调用说明",
                prompt_desc="目标执行进度查询 Agent 调用说明",
                prompt_content=tool.description,
            )
            agents.append(
                ReActParticipantAgent(
                    name="campaign_execution_progress_role",
                    description=campaign_execution_progress_role_description,
                    tools=[tool],
                    llm=self.llm,
                    max_token_limit=self.max_token_limit,
                )
            )
        if scene in ["insight_strategy", "insight_goal"]:
            log.info("初始化资源查询 Agent")
            tool = PortalResourceQueryTool(insight_params=insight_params, tenant_project_info=tenant_info)
            campaign_resource_query_role_description = await PromptFactory.async_create_or_get_prompt(
                **common_prompt_params,
                prompt_code="campaign_resource_query_role_description",
                prompt_name="资源查询 Agent 调用说明",
                prompt_desc="资源查询 Agent 调用说明",
                prompt_content=tool.description,
            )
            agents.append(
                ReActParticipantAgent(
                    name="campaign_resource_query_role",
                    description=campaign_resource_query_role_description,
                    tools=[tool],
                    llm=self.llm,
                    max_token_limit=self.max_token_limit,
                )
            )
        if scene in ["insight_strategy", "insight_goal"]:
            log.info("初始化渠道执行查询 Agent")
            tool = PortalCommunicateExecutionProgressTool(
                insight_params=insight_params,
                tenant_project_info=tenant_info
            )
            campaign_communicate_execution_progress_role_description = await PromptFactory.async_create_or_get_prompt(
                **common_prompt_params,
                prompt_code="campaign_communicate_execution_progress_role_description",
                prompt_name="渠道执行查询 Agent 调用说明",
                prompt_desc="渠道执行查询 Agent 调用说明",
                prompt_content=tool.description,
            )
            agents.append(
                ReActParticipantAgent(
                    name="campaign_communicate_execution_progress_role",
                    description=campaign_communicate_execution_progress_role_description,
                    tools=[tool],
                    llm=self.llm,
                    max_token_limit=self.max_token_limit,
                )
            )
        if scene in ["insight_strategy", "insight_goal"]:
            log.info("初始化优化建议 Agent")
            campaign_optimize_advise_role_description = await PromptFactory.async_create_or_get_prompt(
                **common_prompt_params,
                prompt_code="campaign_optimize_advise_role_description",
                prompt_name="优化建议 Agent 调用说明",
                prompt_desc="优化建议 Agent 调用说明",
                prompt_content="根据当前情况，给出当前分析的场景/子场景/业务目标/策略用例等的优化建议",
            )
            agents.append(
                CampaignOptimizeAgent(
                    name="campaign_optimize_advise_role",
                    description=campaign_optimize_advise_role_description,
                    llm=self.llm,
                    tenant_info=tenant_info,
                    max_token_limit=self.max_token_limit,
                )
            )

        if scene == "data_analyse":
            log.info("初始化 Dify 知识查询 Agent")
            knowledge_querier_description = await PromptFactory.async_create_or_get_prompt(
                **common_prompt_params,
                prompt_code="knowledge_querier_description",
                prompt_name="Dify 知识查询 Agent 调用说明",
                prompt_desc="Dify 知识查询 Agent 调用说明",
                prompt_content=(
                    "数据查询相关知识检索角色，当接收到需要进行查数的任务时，必须强制先使用该角色获取相关知识。本角色可以提供的服务为："
                    "1）将用户的自然语言查数需求翻译为sql；"
                    "2）提供指标的可下钻维度入参为用户的具体查询需求，例如：这个月代发客群的MAU如何、MAU有哪些可以下钻的维度等"
                ),
            )
            agents.append(
                DifyWorkflowParticipantAgent(
                    name="knowledge_querier",
                    scene="",  # 查数分析不传这个参数，先让大模型发挥
                    dify_api_base=DIFY_BASE_UTL,
                    dify_api_key=KNOWLEDGE_WORKFLOW_API_KEY,
                    description=knowledge_querier_description,
                    llm=self.llm,
                    input_history=history,
                    max_token_limit=self.max_token_limit,
                )
            )

        log.info("初始化 SQL 查询 Agent")
        data_querier_description = await PromptFactory.async_create_or_get_prompt(
            **common_prompt_params,
            prompt_code="data_querier_description",
            prompt_name="SQL 查询 Agent 调用说明",
            prompt_desc="SQL 查询 Agent 调用说明",
            prompt_content=("使用 SQL 查询数据，如果其他地方查不到的数据，尝试使用本角色，"
                            "使用本角色之前，必须先使用 knowledge_querier 角色检索出来一个参考的 SQL 做参考"),
        )
        agents.append(
            ReActParticipantAgent(
                name="data_querier",
                system_prompt_background=await get_cdp_tables_description(),
                description=data_querier_description,
                tools=[CDPSqlExecutorTool(tenant_project_info=tenant_info)],
                llm=self.llm,
                max_iterations=10,
                max_token_limit=self.max_token_limit,
            )
        )

        # 数据渲染
        log.info("初始化数据渲染 Agent")
        data_render_tool = DataRenderTool()
        data_render_role_description = await PromptFactory.async_create_or_get_prompt(
            **common_prompt_params,
            prompt_code="data_render_role_description",
            prompt_name="数据渲染 Agent 调用说明",
            prompt_desc="数据渲染 Agent 调用说明",
            prompt_content=("数据图表、指标维度可视化渲染角色，负责将数据以表格、柱状图、折线图、饼图、下钻分析的指标维度等形式进行可视化展示，"
                            "请指明需要按照什么维度等展示渲染内容，如果要展示，请指定该展示资源的资源 id"),
        )
        render_agent = ReActParticipantAgent(
            name="data_render_role",
            description=data_render_role_description,
            tools=[data_render_tool],
            llm=self.llm,
            max_iterations=20,
            max_token_limit=self.max_token_limit,
        )
        data_render_tool.resource_callback = lambda resource_id, data: render_agent.resources.update(
            {resource_id: data}
        )
        agents.append(render_agent)

        # 知识渲染
        log.info("初始化知识渲染 Agent")
        knowledge_render_tool = KnowledgeRenderTool()
        knowledge_render_role_description = await PromptFactory.async_create_or_get_prompt(
            **common_prompt_params,
            prompt_code="knowledge_render_role_description",
            prompt_name="知识渲染 Agent 调用说明",
            prompt_desc="知识渲染 Agent 调用说明",
            prompt_content="分析思路整理和知识渲染角色（不能渲染图表），将 knowledge_querier 查询出来的知识整理成本次分析的分析思路，并渲染成可展示的样式，如果需要展示，请指定该展示资源的资源 id",
        )
        knowledge_render_agent = ReActParticipantAgent(
            name="knowledge_render_role",
            description=knowledge_render_role_description,
            tools=[knowledge_render_tool],
            llm=self.llm,
            max_iterations=5,
            max_token_limit=self.max_token_limit,
        )
        knowledge_render_tool.resource_callback = lambda resource_id, data: knowledge_render_agent.resources.update(
            {
                resource_id: ViewMessage(
                    type=data.type,
                    content=json.dumps([json.loads(data.content)], ensure_ascii=False)
                )
            }
        )
        agents.append(knowledge_render_agent)

        background_knowledge = PromptFactory.render_prompt(
            await get_background_knowledge_prompt(),
            project_information='\n'.join([
                "  - project_name:" + str(tenant_info.get('project_name', '')),
                "  - project_id:" + str(tenant_info.get('project_id', '')),
                "  - organization_id:" + str(tenant_info.get('organization_id', '')),
            ])
        )
        manager = ReactManagerAgent(
            name="execution_manager",
            llm=self.llm,
            generate_prompt=await get_user_selector_react_prompt(),
            background_knowledge=background_knowledge,
            max_token_limit=self.max_token_limit,
        )

        # 只有在一开始的对话中才需要报告
        return_result = None
        with_report_agent = False
        if len(history) <= 2 and (scene in ["insight_strategy", "insight_goal"]):
            report_generate_role_description = await PromptFactory.async_create_or_get_prompt(
                **common_prompt_params,
                prompt_code="report_generate_role_description",
                prompt_name="报告整理 Agent 调用说明",
                prompt_desc="报告整理 Agent 调用说明",
                prompt_content="报告整理角色，当最终需要给用户回复报告的时候，需要使用该角色生成图文并茂的报告内容，注意，报告中引用的资源是使用的占位符",
            )
            report_generate_agent = ReportGenerateParticipantAgent(
                name="report_generate_role",
                description=report_generate_role_description,
                manager=manager,
                llm=self.llm,
                report_callback=self.callback,
                max_token_limit=self.max_token_limit,
            )
            return_result = report_generate_agent.get_report
            agents.append(report_generate_agent)
            with_report_agent = True

            report_critic_role_description = await PromptFactory.async_create_or_get_prompt(
                **common_prompt_params,
                prompt_code="report_critic_role_description",
                prompt_name="报告审核 Agent 调用说明",
                prompt_desc="报告审核 Agent 调用说明",
                prompt_content="报告审核角色，当 report_generate_role 生成报告后，必须让该角色进行审核，审核不通过则需要重新生成报告！",
            )
            report_critic_agent = ParticipantAgent(
                name="report_critic_role",
                description=report_critic_role_description,
                system_prompt=await get_report_critic_prompt(),
                llm=self.llm,
                max_token_limit=self.max_token_limit,
            )
            agents.append(report_critic_agent)

        self.workflow = AutoGenWorkflow(
            manager_agent=manager,
            participant_agents=agents,
            callback=self.callback if not with_report_agent else None,
            max_talk_round=15,
            num_concurrent_runs=1,
            stream=self.kwargs.get('stream', True),
            think_progress=True,
            output_as_final_answer=True,
        )
        return return_result

    def _is_valid_insight_params(self, insight_params) -> bool:
        """检查 insight_params 是否包含有效数据"""
        if not insight_params:
            return False

        # 检查是否有任何有效字段
        has_valid_fields = (
                getattr(insight_params, 'start_time', '') != '' or
                getattr(insight_params, 'end_time', '') != '' or
                getattr(insight_params, 'level', 0) != 0 or
                getattr(insight_params, 'scene_id', 0) != 0 or
                getattr(insight_params, 'campaign_case_id', 0) != 0 or
                len(getattr(insight_params, 'strategies', [])) > 0
        )

        return has_valid_fields

    def _is_empty_insight_params(self, insight_params) -> bool:
        """检查 insight_params 是否是空对象"""
        if not insight_params:
            return True

        return (
                getattr(insight_params, 'start_time', '') == '' and
                getattr(insight_params, 'end_time', '') == '' and
                getattr(insight_params, 'level', 0) == 0 and
                getattr(insight_params, 'scene_id', 0) == 0 and
                getattr(insight_params, 'campaign_case_id', 0) == 0 and
                len(getattr(insight_params, 'strategies', [])) == 0
        )
