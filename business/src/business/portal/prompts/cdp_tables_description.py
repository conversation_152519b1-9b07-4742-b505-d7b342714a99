from jinja2 import Template

from framework.prompt import PromptFactory

CDP_TABLES_DESCRIPTION = """
相关数据库及数据表说明：
## 数据库Schema：
### 1. 事件表 (horizon_default_1.events)
用户行为事件表，包含以下字段：
  - user_id: 用户ID
  - event: 事件类型，包括：
  - login_submitLoginInfo, login_result (登录相关事件)
    - payroll_page_view (代发专区事件)
    - product_detail_view, product_purchase, confirm_purchase, pruchase_result (产品购买相关事件)
    - Activity_notification (动帐流水事件)
  - purchase_amount: 购买金额 (NUMBER类型)
  - is_success: 是否成功 (STRING类型，值为'是'或'否')
  - product_id: 产品代码
  - product_variety: 产品种类
 - time: 事件发生时间(Timestamp)
### 2. 用户属性表 (horizon_default_1.users)
用户标签表，包含以下字段：
  - id: 用户ID
  - user_tag_ssfh: 所属分行 (STRING类型)
  - user_tag_payroll: 代发客户标识 (0/1)
  - user_tag_khjzdj: 客户价值等级 (高价值/中价值/低价值)
  - user_tag_risk_level: 风险等级
  - user_tag_amount_of_financial_products_held: 理财产品持有金额
### 3. 产品属性表 (horizon_workspace_default_1.product_attribute)
产品信息表，包含以下字段：
  - product_code: 产品代码 (产品唯一标识)
  - product_name: 产品名称 (产品显示名称)
  - min_purchase_amt: 起购金额 (NUMBER类型)
  - product_net_value: 产品净值 (NUMBER类型，基金/理财产品)
  - deposit_term: 存期 (STRING类型，存款产品，如'1个月'、'1年')
  - deposit_rate: 利率 (STRING类型，存款产品，如'1.10%')
  - precious_metal_amt: 贵金属金额 (NUMBER类型，贵金属产品)
  - user_risk_level: 匹配的用户风险等级 (低风险/中低风险/中风险/中高风险/高风险)
  - is_open_period: 是否在开放期内 (是/否)
  - product_cycle: 产品周期 (STRING类型，理财产品，如'365天'、'14天')
  - product_type: 产品类型 (理财/基金/存款/贵金属)
  - has_balance_is_purchasable: 产品是否有余额/是否可购买 (是/否)
### 4. 分行网点数表 (horizon_workspace_default_1.ssfhwdsb)
分行信息表，包含以下字段：
  - 所属分行(ssfh): 分行名称(北京/上海/广州/深圳/成都/等)
  - 分行网点数(fhwds): 该分行的网点数量
## 表关联关系：
    - events.user_id = users.id (用户事件与用户属性关联)
    - events.product_id = product_attribute.product_code (产品事件与产品属性关联)
    - users.user_tag_ssfh = ssfhwdsb.ssfh (用户所属分行与分行信息关联)

"""

async def get_cdp_tables_description() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="portal",
        business_cname="下一代产品 Demo",
        prompt_code="PORTAL_CDP_TABLES_DESCRIPTION",
        prompt_name="Portal CDP 表结构描述",
        prompt_desc="Portal CDP 表结构描述",
        prompt_content=CDP_TABLES_DESCRIPTION,
    )
    return template
