import json
from pathlib import Path
from typing import Callable, List, Literal

import requests

from framework import config
from common import tools
from framework.agents.schema import Conversation, Role
from framework.knowledge.rag_client import RagClient, KnowledgeBase
from framework.models import BaseLLM, ModelsFactory

log = tools.get_logger()


class DifyRag(RagClient):
    """适配 dify api 的 rag"""

    def __init__(
            self,
            kb: KnowledgeBase,
            api_host: str,
            api_key: str,
            dify_knowledgebase_id: int,
            llm: BaseLLM = ModelsFactory.get_llm(),
            max_token_limit: int = int(config.llm_config['input_token_limit'] * 0.8),
            query_limit=20,
            max_talk_round=4,
            score_limit=0.45,
            additional_retrieve_requirement: str | None = None,
            callback: Callable[[str], None] = None,
            **kwargs,
    ):
        """
        api_host: dify 的知识库 host，比如：http://**********/v1
        additional_retrieve_requirement: 额外的检索要求
        """
        super().__init__(kb, **kwargs)
        self.api_host = api_host
        self.api_key = api_key
        self.dify_knowledgebase_id = dify_knowledgebase_id

        self.callback = callback
        self.llm = llm
        self.query_limit = query_limit
        self.additional_retrieve_requirement = additional_retrieve_requirement

        self.score_limit = score_limit
        self.max_token_limit = max_token_limit
        self.max_talk_round = max_talk_round
        self.retrieve_client = None

    def _callback(self, content: str):
        log.info(content)
        if self.callback:
            self.callback(content)

    @property
    def output_dir(self) -> Path | None:
        self._callback("dify rag output_dir not supported.")
        return None

    async def build_index(
            self,
            index_mode: Literal['reindex', 'resume', 'update'] = 'reindex',
            **kwargs
    ):
        self._callback("dify rag build index not supported.")

    async def search(self, history: List[Conversation], **kwargs) -> Conversation:
        url = f"{self.api_host}/datasets/{self.dify_knowledgebase_id}/retrieve"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        query = history[-1].content
        if self.additional_retrieve_requirement:
            query = f"{self.additional_retrieve_requirement}\n\n{query}"
        response = requests.post(
            url=url,
            headers=headers,
            json={
                "query": query
            }
        ).json()
        if not 'records' in response:
            log.warning(f"dify rag search failed: {response}")
            return Conversation(role=Role.ASSISTANT, content=json.dumps(response, ensure_ascii=False))

        content = []
        for record in response['records']:
            content.append(record['content'])

        return Conversation(role=Role.ASSISTANT, content="\n".join(content))

    async def simple_search(self, history: List[Conversation], **kwargs) -> Conversation:
        return await self.search(history=history, **kwargs)

    def add_input_file(self, input_file: Path):
        self._callback("dify rag add input file not supported.")

    def clean_input_files(self):
        self._callback("dify rag clean input files not supported.")

    def add_additional_file(self, input_file: Path, new_file_name: str):
        self._callback("dify rag add additional file not supported.")
