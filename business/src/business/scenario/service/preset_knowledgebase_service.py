import json

from business.config import K<PERSON><PERSON><PERSON>DGE_DATA_PATH
from core.db.transaction import transactional
from core.models import KnowledgebaseModel
from core.service.knowledgebase_service import KnowledgebaseService
from framework import config


class PresetKnowledgebaseService(KnowledgebaseService):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def _get_customer_knowledgebase_name(self, organization_id: str, project_id: int) -> str:
        name = f"customer_{organization_id}@{project_id}"
        return name

    async def construct_customer_knowledgebase_config(self, organization_id: str, project_id: int) -> dict:
        """从数据库获取租户信息、拼接知识库配置信息"""
        name = self._get_customer_knowledgebase_name(organization_id=organization_id, project_id=project_id)
        cname = f"客户知识库_{organization_id}@{project_id}"
        kb = await self.get_knowledgebase_by_name(name=name)
        if kb:
            return kb.dict()
        return {
            'name': name,
            'cname': cname,
            'base_dir': str(KNOWLEDGE_DATA_PATH / 'customers' / organization_id / str(project_id)),
            'index_type': 'PRESET_CUSTOMER',
            'custom_knowledge_class': 'business.scenario.customer_rag_client.CustomerRagClient',
            'config': json.dumps({
                'organization_id': organization_id,
                'project_id': project_id,
                'max_token_limit': int(config.llm_config['input_token_limit'] * 0.8),
                'query_limit': 20,
                'max_talk_round': 5,
                'score_limit': 0.45,
            }),
        }

    @transactional
    async def create_customer_knowledgebase(self, organization_id: str, project_id: int, **kwargs) -> dict | None:
        existed_kb = await self.get_customer_knowledgebase(organization_id=organization_id, project_id=project_id)
        if existed_kb:
            raise ValueError('客户知识库已存在')
        kb_model = await self.construct_customer_knowledgebase_config(
            organization_id=organization_id,
            project_id=project_id,
        )
        kb = await self.save_knowledgebase(knowledgebase=KnowledgebaseModel(**kb_model))
        return kb.dict()

    @transactional
    async def get_customer_knowledgebase(self, organization_id: str, project_id: int, **kwargs) -> dict | None:
        name = self._get_customer_knowledgebase_name(organization_id=organization_id, project_id=project_id)
        kb = await self.get_knowledgebase_by_name(name=name)
        return kb.dict() if kb else None

    def _get_sensors_knowledgebase_name(self, product: str, version: str) -> str:
        name = f"sensors_{product}@{version}"
        return name

    async def construct_sensors_knowledgebase_config(self, product: str, version: str) -> dict:
        name = self._get_sensors_knowledgebase_name(product=product, version=version)
        cname = f"神策知识库_{product}@{version}"
        kb = await self.get_knowledgebase_by_name(name=name)
        if kb:
            return kb.dict()
        return {
            'name': name,
            'cname': cname,
            'base_dir': str(KNOWLEDGE_DATA_PATH / 'sensors' / product / version),
            'index_type': 'PRESET_SENSORS',
            'custom_knowledge_class': 'business.scenario.sensors_knowledgebase_simple.SensorsKnowledgebaseSimple',
            'config': json.dumps({
                'product': product,
                'version': version,
                'max_token_limit': int(config.llm_config['input_token_limit'] * 0.8),
                'query_limit': 5,
                'max_talk_round': 2,
                'score_limit': 0.45,
            }),
        }

    @transactional
    async def create_sensors_knowledgebase(self, product: str, version: str, **kwargs) -> dict | None:
        existed_kb = await self.get_sensors_knowledgebase(product=product, version=version)
        if existed_kb:
            raise ValueError(f'神策知识库已存在 product: {product}, version: {version}')
        kb_model = await self.construct_sensors_knowledgebase_config(product=product, version=version)
        kb = await self.save_knowledgebase(knowledgebase=KnowledgebaseModel(**kb_model))
        return kb.dict()

    @transactional
    async def get_sensors_knowledgebase(self, product: str, version: str, **kwargs) -> dict | None:
        name = self._get_sensors_knowledgebase_name(product=product, version=version)
        kb = await self.get_knowledgebase_by_name(name=name)
        return kb.dict() if kb else None

    def _get_industry_knowledgebase_name(self, industry: str) -> str:
        name = f"industry_{industry}"
        return name

    async def construct_industry_knowledgebase_config(self, industry: str, industry_cname: str) -> dict:
        name = self._get_industry_knowledgebase_name(industry=industry)
        cname = industry_cname
        kb = await self.get_knowledgebase_by_name(name=name)
        if kb:
            return kb.dict()
        return {
            'name': name,
            'cname': cname,
            'base_dir': str(KNOWLEDGE_DATA_PATH / 'industry' / industry),
            'index_type': 'PRESET_INDUSTRY',
            'custom_knowledge_class': 'business.scenario.industry_knowledgebase.IndustryKnowledgebase',
            'config': json.dumps({
                'industry': industry,
                'max_token_limit': int(config.llm_config['input_token_limit'] * 0.8),
                'query_limit': 5,
                'max_talk_round': 2,
                'score_limit': 0.45,
            }),
        }

    @transactional
    async def create_industry_knowledgebase(self, industry: str, industry_cname: str, **kwargs) -> dict | None:
        existed_kb = await self.get_industry_knowledgebase(industry=industry, industry_cname=industry_cname)
        if existed_kb:
            raise ValueError(f'行业已存在 industry: {industry}, industry_cname: {industry_cname}')
        kb_model = await self.construct_industry_knowledgebase_config(industry=industry, industry_cname=industry_cname)
        kb = await self.save_knowledgebase(knowledgebase=KnowledgebaseModel(**kb_model))
        return kb.dict()

    @transactional
    async def get_industry_knowledgebase(self, industry: str, industry_cname: str, **kwargs) -> dict | None:
        name = self._get_industry_knowledgebase_name(industry=industry)
        kb = await self.get_knowledgebase_by_name(name=name)
        return kb.dict() if kb else None

    @transactional
    async def init_default_preset_knowledgebase(self, **kwargs):
        _default_knowledgebase_list = [
            {
                'name': 'common',
                'cname': '通用知识库',
                'base_dir': str(KNOWLEDGE_DATA_PATH / 'common'),
                'index_type': 'PRESET_COMMON',
                'custom_knowledge_class': 'business.scenario.common_knowledgebase.CommonKnowledgebase',
                'config': json.dumps({
                    'max_token_limit': int(config.llm_config['input_token_limit'] * 0.8),
                    'query_limit': 20,
                    'max_talk_round': 4,
                    'score_limit': 0.45,
                }),
            }
        ]
        for k in _default_knowledgebase_list:
            kb = KnowledgebaseModel(**k)
            await self.save_knowledgebase(kb)
