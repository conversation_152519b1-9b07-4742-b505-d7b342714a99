from typing import Dict

from framework.knowledge import SimpleRag
from business.scenario.kgconfigs.common_knowledge_configs import (
    KNOWLEDGE_BASE_COMMON_ENTITY_TYPES
)


def _get_entity_types() -> Dict[str, str]:
    return KNOWLEDGE_BASE_COMMON_ENTITY_TYPES


def _get_pre_knowledge() -> str:
    return ""


class CommonKnowledgebase(SimpleRag):
    def __init__(self, **kwargs):
        entity_types = _get_entity_types()
        pre_knowledge = _get_pre_knowledge()
        super().__init__(
            entity_types=entity_types,
            pre_knowledge=pre_knowledge,
            **kwargs
        )
