import json
from typing import Type
import requests
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from common import tools

log = tools.get_logger()


class _InnerModel(BaseModel):
    query: str = Field(description="知识库接收的用户查询，比如xx数据分析的思路等")


class DifyWorkflowTool(BaseTool):
    name: str = "DifyWorkflowTool"
    description: str = "调用 dify 的 workflow 工具，用于查询知识"
    args_schema: Type[BaseModel] = _InnerModel

    # 示例：http://**********/v1
    dify_api_base: str
    dify_api_key: str

    # 取值：insight_strategy / insight_goal / data_analyse
    scene: str = ""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def _run(self, query: str, **kwargs) -> str:
        try:
            url = f"{self.dify_api_base}/workflows/run"
            headers = {
                "Authorization": f"Bearer {self.dify_api_key}",
                "Content-Type": "application/json",
            }
            response = requests.post(
                url,
                headers=headers,
                json={
                    "inputs": {
                        "query": query,
                        "intent": self.scene
                    },
                    "response_mode": "blocking",
                    "user": "portal-test"
                }
            )
            result = response.json()
            log.info(f"Got dify result: {result}")
            result = result['data']['outputs']
            return json.dumps(result, ensure_ascii=False, indent=4)
        except Exception as e:
            # 打印异常
            log.error(f"Dify 调用失败: {e}", exc_info=True)
            return f"Dify 调用失败：{e}"

    async def _arun(self, **kwargs) -> str:
        return self._run(**kwargs)
