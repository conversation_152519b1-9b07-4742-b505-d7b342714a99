from typing import Dict, Callable

from framework.knowledge import SimpleRag, KnowledgeBase
from framework.models import ModelsFactory, BaseLLM
from framework import config
from business.scenario.kgconfigs.industry_knowledge_configs import (
    KNOWLEDGE_BASE_INDUSTRY_ENTITY_TYPES
)


def _get_entity_types() -> Dict[str, str]:
    return KNOWLEDGE_BASE_INDUSTRY_ENTITY_TYPES


def _get_pre_knowledge() -> str:
    return ""


class IndustryKnowledgebase(SimpleRag):
    def __init__(
            self,
            industry: str,
            **kwargs
    ):
        entity_types = _get_entity_types()
        pre_knowledge = _get_pre_knowledge()
        self.industry = industry
        super().__init__(
            entity_types=entity_types,
            pre_knowledge=pre_knowledge,
            **kwargs
        )
