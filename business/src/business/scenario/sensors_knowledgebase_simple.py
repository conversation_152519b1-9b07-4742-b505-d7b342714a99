from typing import Dict

from framework.knowledge import SimpleRag
from business.scenario.kgconfigs.sensors_introduce import (
    SENSORS_INTRODUCE,
    SA_INTRODUCE,
    SDH_INTRODUCE,
    SF_INTRODUCE
)
from business.scenario.kgconfigs.sensors_knowledge_configs import (
    KNOWLEDGE_BASE_SENSORS_ENTITY_TYPES,
)


def _get_introduce() -> str:
    return SENSORS_INTRODUCE + '\n\n' + SA_INTRODUCE + '\n\n' + SDH_INTRODUCE + '\n\n' + SF_INTRODUCE


def _get_entity_extraction_types() -> Dict[str, str] | None:
    return KNOWLEDGE_BASE_SENSORS_ENTITY_TYPES


class SensorsKnowledgebaseSimple(SimpleRag):
    def __init__(
            self,
            product: str,
            version: str,
            **kwargs
    ):
        self.product = product
        self.version = version
        entity_types = _get_entity_extraction_types()
        super().__init__(
            pre_knowledge=_get_introduce(),
            entity_types=entity_types,
            **kwargs
        )
