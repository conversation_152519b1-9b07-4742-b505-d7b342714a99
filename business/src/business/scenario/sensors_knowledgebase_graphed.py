from typing import Dict, Callable

from common import tools
from framework.knowledge import Sensors<PERSON>raphRag, KnowledgeBase
from framework.models import ModelsFactory, BaseLLM
from framework import config
from business.scenario.kgconfigs.sensors_introduce import (
    SENSORS_INTRODUCE,
    SA_INTRODUCE,
    SDH_INTRODUCE,
    SF_INTRODUCE
)
from business.scenario.kgconfigs.sensors_knowledge_configs import (
    KNOWLEDGE_BASE_SENSORS_RELATION_TYPES,
    KNOWLEDGE_BASE_SENSORS_ENTITY_TYPES,
    COMMUNITY_REPORT,
    ENTITY_EXTRACTION,
    SUMMARIZE_DESCRIPTIONS, get_sensors_knowledge_community_report, get_sensors_knowledge_entity_extraction,
    get_sensors_knowledge_summarize_descriptions
)


def _get_introduce() -> str:
    return SENSORS_INTRODUCE + '\n\n' + SA_INTRODUCE + '\n\n' + SDH_INTRODUCE + '\n\n' + SF_INTRODUCE


def _get_prompts() -> Dict[str, str]:
    return {
        'community_report': tools.asyncio_run(get_sensors_knowledge_community_report).render(),
        'entity_extraction': tools.asyncio_run(get_sensors_knowledge_entity_extraction).render(),
        'summarize_descriptions': tools.asyncio_run(get_sensors_knowledge_summarize_descriptions).render()
    }


def _get_entity_extraction_types() -> Dict[str, str] | None:
    return KNOWLEDGE_BASE_SENSORS_ENTITY_TYPES


def _get_relation_extraction_types() -> Dict[str, str]:
    return KNOWLEDGE_BASE_SENSORS_RELATION_TYPES


class SensorsKnowledgebaseGraphed(SensorsGraphRag):
    def __init__(
            self,
            product: str,
            version: str,
            **kwargs
    ):
        index_prompts = _get_prompts()
        self.product = product
        self.version = version
        entity_types = _get_entity_extraction_types()
        relation_types = _get_relation_extraction_types()
        super().__init__(
            index_prompts=index_prompts,
            entity_types=entity_types,
            relation_types=relation_types,
            **kwargs
        )
