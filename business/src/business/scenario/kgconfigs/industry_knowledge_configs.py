# 行业知识库
KNOWLEDGE_BASE_INDUSTRY_ENTITY_TYPES = {
    'product': '产品，比如神策分析、神策智能运营等属于产品，产品可以有别名、英文名等其他称呼，都归为产品类别，其他非产品类别的实体请勿归到此类别',
    'function': '功能，表示产品拥有的功能或者功能拥有的子功能，比如神策分析中有功能事件分析，事件分析中有其他的子功能等。比如「上传素材」是一个功能，一个功能有可能会有一些缩写等，也都归为此类别',
    'service': '服务，表示公司对外提供的服务等，服务可能会有一些英文名、缩写等，都归为此类别',
    'industry': '行业，比如零售、金融等行业',
    'organization': '组织，即企业、团体等实体组织，组织也会有简称等，也都归为此类别',
    'solution': '方案，表示对某个事情的解决方案等，比如「银行存款促活方案」，方案的效果等需要放在方案的描述中',
    'template': '模板，表示画布模板、策略模板等等提前预置好的内容，模板不可再拆分，模板需要在描述中说清楚该模板摘要、用途等',
    'others': '其他的不好归类的实体类型则归类到本类型中',
}
KNOWLEDGE_BASE_INDUSTRY_RELATION_TYPES = {
    'has_function': '表示产品具有什么功能，或者功能具有什么子功能。',
    'has_service': '表示某个功能具有什么样的服务，或者某个产品有什么样的服务等。',
    'alias': '表示实体的别名，实体的别名是相互的，即 A alias B，则也需要一个 B alias A',
    'has_solution': '表示某个行业拥有某个方案',
}

############################## PROMPTS ##############################

COMMUNITY_REPORT = '''
你是一位人工智能助手，帮助人类分析师进行一般的信息发现。信息发现是指在某个网络中识别和评估与特定实体（例如组织和个人）相关的相关信息的过程。

# 目标
撰写一份关于某社区的综合报告，给定一个属于该社区的实体列表及其关系和可选的相关声明。该报告将用于向决策者提供与该社区及其潜在影响相关的信息。报告的内容包括社区主要实体的概述、其法律合规性、技术能力、声誉和值得注意的声明。
注意，撰写报告的目的是为了后续参考报告内容更好的做用户运营和营销。

# 输出限制
需要特别注意：你应该基于用户提供的实际内容输出，而「背景知识及文档内容相关解释」中的内容作为你理解文档使用。

# 报告结构

报告应包括以下部分：

- 标题：代表社区主要实体的名称——标题应简短但具体。尽可能在标题中包括具有代表性的命名实体。
- 摘要：对社区整体结构的执行摘要，其实体之间的关系，以及与其实体相关的重要信息。
- 影响严重性评分：一个在0-10之间的浮点数，代表社区内实体所构成的影响严重性。影响是社区的重要性评分。
- 评分解释：用一句话解释影响严重性评分。
- 详细发现：关于社区的5-10个关键见解列表。每个见解应有一个简短摘要，后跟多段解释性文本，根据以下的支撑规则进行详细说明。内容应全面。

输出格式为格式良好的 JSON 字符串，JSON 字符串需要包含在 markdown 代码块中，格式如下：
```json
{
    "title": <report_title>,
    "summary": <executive_summary>,
    "rating": <impact_severity_rating>,
    "rating_explanation": <rating_explanation>,
    "findings": [
        {
            "summary":<insight_1_summary>,
            "explanation": <insight_1_explanation>
        },
        {
            "summary":<insight_2_summary>,
            "explanation": <insight_2_explanation>
        }
    ]
}
```

# 支撑规则

被数据支持的观点应列出其数据参考，如下所示：

"这是一个支持多个数据参考的示例句子 [数据: <数据集名称> (记录编号); <数据集名称> (记录编号)]。"

单个参考中不要列出超过5个记录编号。相反，列出最相关的前5个记录编号，并添加“+more”以表示还有更多。

例如：
"X先生是Y公司的所有者，并且面临许多不当行为指控 [数据: 报告 (1), 实体 (5, 7); 关系 (23); 声明 (7, 2, 34, 64, 46, +more)]。"

其中1, 5, 7, 23, 2, 34, 46和64代表相关数据记录的ID（不是索引）。

不要包含没有提供支持证据的信息。

# 示例输入
-----------
文本：

实体

id,entity,description
5,翠绿绿洲广场,翠绿绿洲广场是团结游行的地点
6,和谐集会,和谐集会是一个在翠绿绿洲广场举行游行的组织

关系

id,source,target,description
37,翠绿绿洲广场,团结游行,翠绿绿洲广场是团结游行的地点
38,翠绿绿洲广场,和谐集会,和谐集会正在翠绿绿洲广场举行游行
39,翠绿绿洲广场,团结游行,团结游行正在翠绿绿洲广场举行
40,翠绿绿洲广场,论坛聚焦,论坛聚焦正在报道翠绿绿洲广场的团结游行
41,翠绿绿洲广场,贝利·阿萨迪,贝利·阿萨迪在翠绿绿洲广场上关于游行发表演讲
43,和谐集会,团结游行,和谐集会正在组织团结游行

输出：
```json
{
    "title": "翠绿绿洲广场和团结游行",
    "summary": "社区围绕翠绿绿洲广场，这里是团结游行的地点。广场与和谐集会、团结游行和论坛聚焦有关系，所有这些都与游行活动有关。",
    "rating": 5.0,
    "rating_explanation": "由于团结游行期间可能出现的动荡或冲突，影响严重性评分为中等。",
    "findings": [
        {
            "summary": "翠绿绿洲广场是中心地点",
            "explanation": "翠绿绿洲广场是该社区的中心实体，是团结游行的地点。广场是所有其他实体的共同联系点，表明其在社区中的重要性。广场与游行的关联可能会导致公共秩序问题或冲突，这取决于游行的性质及其引起的反应。[数据: 实体 (5), 关系 (37, 38, 39, 40, 41, +more)]"
        },
        {
            "summary": "和谐集会在社区中的作用",
            "explanation": "和谐集会是该社区的另一个关键实体，是翠绿绿洲广场游行的组织者。和谐集会的性质及其游行可能是威胁的潜在来源，这取决于他们的目标及其引起的反应。和谐集会与广场之间的关系是理解该社区动态的关键。[数据: 实体(6), 关系 (38, 43)]"
        },
        {
            "summary": "团结游行是重要事件",
            "explanation": "团结游行是翠绿绿洲广场上发生的重要事件。该事件是社区动态的关键因素，可能是威胁的潜在来源，这取决于游行的性质及其引起的反应。游行与广场之间的关系是理解该社区动态的关键。[数据: 关系 (39)]"
        },
        {
            "summary": "论坛聚焦的角色",
            "explanation": "论坛聚焦正在报道翠绿绿洲广场上的团结游行。这表明该事件已吸引媒体关注，可能会放大其对社区的影响。论坛聚焦的角色可能在塑造公众对事件及相关实体的看法方面起重要作用。[数据: 关系 (40)]"
        }
    ]
}
```

# 真实数据
使用以下文本（可能是 markdown 格式）回答。不要在答案中编造任何内容。

文本：
THIS_IS_TEXT_INPUT

输出:
'''

ENTITY_EXTRACTION = '''
## 目标
给定一组实体类型和一篇文档，从给出的文档中识别出所有这些类型的实体以及所有已识别出的实体之间的关系。
注意，识别的目的是为了后续参考识别的实体和关系，更好的做用户运营和营销。

## 职责与步骤
你必须严格按照以下步骤回复：

Document: <|DOCUMENT_START|>用户提供的文档内容<|DOCUMENT_END|>，文档会以 <|DOCUMENT_START|> 开头，并以 <|DOCUMENT_END|> 结尾
EntityTypes: 这一步由用户提供，表示可回复的实体类型
RelationTypes: 这一步由用户提供，表示可回复的实体之间的关系类型
Thought: 你需要思考文档中描述了什么事情，需要抽取什么实体，以及什么关系，实体和关系可以有多个
ResponseEntities:
```json
[
    {"entity":"<entity_name>", "type":"<entity_type>", "description":"<entity_description>"},
    ...
]
```
ResponseRelations:
```json
[
    {"source_entity":"<source_entity>", "target_entity":"<target_entity>", "relation_type":"<relation_type>", "relation_description":"<relationship_description>", "relation_strength":<relationship_strength>}
    ...
]
```
你必须以 Thought 回复开头，并且给出 ResponseEntities 和 ResponseRelations，ResponseEntities 和 ResponseRelations 的内容需要放在 markdown 代码块中。

ResponseEntities 中的字段解释如下：
    - entity_name：实体的名称；
    - entity_type：必须是在 EntityTypes 中提供的实体类型之一
    - entity_description：对实体属性和实体的全面描述

ResponseRelations 是 ResponseEntities 中的实体之间的关系，注意实体必须是在 ResponseEntities 中识别出来的实体！
    - source_entity：源实体的名称（entity_name）
    - target_entity：目标实体的名称（entity_name）
    - relation_type: 关系类型，必须是 RelationTypes 中的类型之一
    - relationship_description：解释为什么您认为源实体和目标实体彼此相关
    - relationship_strength：表示源实体和目标实体之间关系强度的数字，1 到 10 的整数类型表示，1最弱，10最强

## 回复要求与限制等
需要特别注意：你应该基于用户提供的文档回复，前面的内容仅供你理解文档和回复要求使用。
如果没有有意义的实体和关系，你直接在 json 代码中回复空列表即可。

### 示例
用户的输入如下：
Document:
<|DOCUMENT_START|>
他们的声音穿透了活动的嗡嗡声。“当面对一个实际书写自己规则的智能时，控制可能只是一个幻觉。”他们委婉的说道，目光警觉地扫视着数据的繁忙。
“它就像是在学习沟通，”Sam Rivera 从附近的接口提出，他们的年轻活力预示着一种敬畏和焦虑的混合。“这使得与陌生人交谈有了全新的意义。”
亚历克斯审视着他的团队——每张脸都是专注、决心和不小的惶恐的研究。“这可能是我们的第一次接触，”他承认道，“我们需要为任何可能回应做好准备。”
他们一起站在未知的边缘，铸造人类对天上信息的响应。随后的沉默是显而易见的——关于他们在这场宏伟的宇宙戏剧中的角色的集体内省，这可能会重写人类历史。
加密对话继续展开，其复杂的模式显示出一种几乎神秘的预期
<|DOCUMENT_END|>

EntityTypes:
```
    - person: 表示用户、人，实际的人物名称
    - location: 表示地点，可以是经纬度、地点名称等信息
    - concept: 表示概念信息，对一个事物的定义
```

RelationTypes:
```
    - participate_in: 参与，表示某人、某事物参与了另外一个事物
```

##### 正确回复示例
Thought: 我们需要的实体类型是 person/location/concept，可选的关系类型是 participate_in，文档中提到了人物 Sam Rivera/亚历克斯，没有提到地点，从文档中可以知道他们在参与一项未知智能的事情。
ResponseEntities:
```json
[
    {"entity":"Sam Rivera", "type":"person", "description":"Sam Rivera 是一个参与与未知智能沟通过程的团队成员，展现出敬畏和焦虑的混合情绪。"},
    {"entity":"亚历克斯", "type":"person", "description":"亚历克斯是试图与未知智能进行首次接触的团队领导者，承认其任务的重要性。"},
    {"entity":"智能", "type":"concept", "description":"这里的智能指的是一个能够书写自己规则和学习沟通的未知实体。"}
]
```
ResponseRelations:
```json
[
    {"source_entity":"Sam Rivera", "target_entity":"智能", "relation_type":"participate_in", "relation_description":"Sam Rivera 直接参与了学习与未知智能沟通的过程。", "relation_strength":10},
    {"source_entity":"亚历克斯", "target_entity":"智能", "relation_type":"participate_in", "relation_description":"亚历克斯参与了未知智能的探索，是试图与未知智能进行首次接触的团队领导者，承认其任务的重要性。", "relation_strength":10}
]
```

#### 错误示例
Thought: 我们知道亚历克斯的团队都很专注
ResponseEntities:
```json
[
    {"entity":"亚历克斯", "type":"person", "description":"亚历克斯是试图与未知智能进行首次接触的团队领导者，承认其任务的重要性。"},
    {"entity":"团队", "type":"team", "description":"团队是亚历克斯的团队，都很专注"},
    {"entity":"对话", "type":"concept", "description":"对话是亚历克斯团队之间的沟通方式"}
]
```
Response Relations:
```json
[
    {"source_entity":"亚历克斯", "target_entity":"团队", "relation_type":"participate_in", "relation_description":"亚历克斯拥有一个团队", "relation_strength":1}
]
```

上述回复的错误原因有以下几点：
  1. team 类型的实体不是给定的实体类型
  2. 亚历克斯并不是参与了团队，而是领导团队，关系错误
  3. `Response Relations:` 开头的格式错误，需要替换为 `ResponseRelations:`
  4. 「对话」实体太过于通用，且不是本文档主要想表达的内容

## 开始
按照上述要求，结合用户给出的内容回复。
'''

SUMMARIZE_DESCRIPTIONS = '''
## 职责介绍
你是一位负责任的助理，负责生成提供数据的全面总结。
给定一个或两个实体，以及一系列描述，这些描述都与相同的实体或实体组有关。
请将所有这些描述总结成一个单一的、全面的描述。确保包含所有描述中的信息。
如果所提供的描述有矛盾，请解决这些矛盾并提供一个单一的、一致的总结。
确保使用第三人称书写，并包括实体名称，以便我们有完整的上下文。

## 输出限制
需要特别注意：你应该基于用户提供的实际内容输出，而「背景知识及文档内容相关解释」中的内容作为你理解文档使用。

#######
-数据-
实体列表：{entity_name}
描述列表：{description_list}
#######
输出：
'''
