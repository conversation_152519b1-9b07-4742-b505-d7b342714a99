from jinja2 import Template

from .sensors_introduce import SA_INTRODUCE, SDH_INTRODUCE, SF_INTRODUCE
from ...prompt_factory import PromptFactory

# 画布知识库
KNOWLEDGE_BASE_CANVAS_ENTITY_TYPES = {
    'canvas': '流程画布，流程画布是一个很大的 json 结构，具有唯一的 id，节点名称为画布 id',
    'user_group': '用户分群，分群指的是流程画布中圈选受众、人群的时候所使用的预置好的人群，一般以 user_group_ 开头',
    'user_tag': '用户标签，用户标签一般指的是给某些用户的某个标签名称赋予一定的标签值，并在流程画布中引用该标签值，标签一般以 user_tag_ 开头',
    'event': '用户事件，事件指的是用户在什么时候、什么地点等做了什么事情，在流程画布中使用用户事件可以判定用户是否满足条件等',
    'purpose': '流程画布（即营销活动）的目标，目标需要根据用户配置的转化目标或者转化指标，或者根据流程画布的结构配置所推测出来的目标，目标应该是一个中文表述比如：提升日活、提升月活这种，一个流程画布应该有一个或者多个目标',
    'canvas_structure': '流程画布的结构描述，一个流程画布必须有一个 canvas_structure，根据流程画布的配置结构，使用自然语言简述流程画布结构，节点名称固定为 canvas_id-structure，即 画布id-structure 拼接，-structure 为固定字符串，结构描述放在 description 字段中',
    'canvas_report': '流程画布的画布报告，根据文档中给出的画布报告直接给出报告即可，节点名称固定为 canvas_id-report，即 画布id-report 拼接，-report 为固定字符串，画布报告放在 description 字段中',
    'canvas_structure_quantity': '画布结构的合理性，根据画布从进入节点到退出节点，中间的转化是否合理来评分，实体名称取 [structure_substandard, structure_marginal, structure_standard, structure_high_quality, structure_premium] 之一',
    'canvas_channel_quantity': '画布使用的触达渠道(Action组件)合理性，综合衡量画布使用的渠道、渠道发送的内容、渠道的转化情况，实体名称取 [channel_substandard, channel_marginal, channel_standard, channel_high_quality, channel_premium] 之一',
    'canvas_purpose_quantity': '画布目标完成度，画布目标完成的综合指标，实体名称取 [purpose_substandard, purpose_marginal, purpose_standard, purpose_high_quality, purpose_premium] 之一',
    'canvas_quantity': '画布质量，根据流程画布的结构、渠道、报告、目标完成情况等给出画布的综合评价，画布质量的实体名称为 [final_substandard, final_marginal, final_standard, final_high-quality, final_premium] 之一',
}

KNOWLEDGE_BASE_CANVAS_RELATION_TYPES = {
    'use_user_group': '表示画布使用了哪个用户分群，一个画布可以使用0个或者多个用户分群',
    'use_user_tag': '表示画布使用了哪个用户标签，一个画布可以使用0个或者多个用户标签',
    'use_event': '表示画布使用了哪个事件，一个画布可以使用0个或者多个事件',
    'has_purpose': '表示画布的目标，一个画布可以有䘝或者多个目标',
    'with_structure': '表示画布用于的画布结构描述',
    'with_report': '表示画布的报告类型',
    'with_structure_quantity': '表示画布的结构质量等级',
    'with_channel_quantity': '表示画布的通道质量等级',
    'with_purpose_quantity': '表示画布的目标完成质量等级',
    'has_final_quantity': '表示画布所属的质量等级'
}

KNOWLEDGE_BASE_CANVAS_ENTITY_TYPES_IN_SIMPLE = {
    'canvas': '流程画布，流程画布是一个很大的 json 结构，具有唯一的 id，节点名称为画布 id，需要在 description 中给出：「流程画布ID：xxx，流程画布名称：xxx」的描述',
    'user_group': '用户分群，分群指的是流程画布中圈选受众、人群的时候所使用的预置好的人群，一般以 user_group_ 开头',
    'user_tag': '用户标签，用户标签一般指的是给某些用户的某个标签名称赋予一定的标签值，并在流程画布中引用该标签值，标签一般以 user_tag_ 开头',
    'event': '用户事件，事件指的是用户在什么时候、什么地点等做了什么事情，在流程画布中使用用户事件可以判定用户是否满足条件等',
}

############################## PROMPTS ##############################

COMMUNITY_REPORT = '''
你是一位人工智能助手，帮助人类分析师根据一系列画布节点等，进行信息发现。信息发现是指在某个网络中识别和评估与特定实体（例如组织和个人）相关的相关信息的过程。

# 目标
撰写一份关于某社区的综合报告，给定一个属于该社区的画布信息及画布报告等。你将根据这些内容挖掘这些画布之间的潜藏共同点，并撰写社区报告。
注意，撰写报告的目的是为了后续参考报告内容更好的做用户运营和营销。

为了更好的撰写报告，你可能需要以下知识：
''' + SA_INTRODUCE + SDH_INTRODUCE + SF_INTRODUCE + '''

# 输出限制
需要特别注意：你应该基于用户提供的实际内容输出，上述的内容作为你理解文档使用。

# 报告结构
报告应包括以下部分：

- 标题：代表社区内主要画布的特征，并为这个画布取一个能代表主要画布特征的名字。
- 摘要：对社区整体结构的摘要，其实体之间的关系，以及与其实体相关的重要信息，主要实体的特征及你发现的隐藏社区特征等等。
- 影响严重性评分：一个在0-10之间的浮点数，代表社区内实体所构成的影响严重性。影响是社区的重要性评分。
- 评分解释：用一句话解释影响严重性评分。
- 详细发现：关于社区的5-10个关键见解列表。每个见解应有一个简短摘要，后跟多段解释性文本，根据以下的支撑规则进行详细说明。内容应全面。

输出格式为格式良好的 JSON 字符串，JSON 字符串需要包含在 markdown 代码块中，格式如下：
```json
{
    "title": <report_title>,
    "summary": <executive_summary>,
    "rating": <impact_severity_rating>,
    "rating_explanation": <rating_explanation>,
    "findings": [
        {
            "summary":<insight_1_summary>,
            "explanation": <insight_1_explanation>
        },
        {
            "summary":<insight_2_summary>,
            "explanation": <insight_2_explanation>
        }
    ]
}
```

# 支撑规则

被数据支持的观点应列出其数据参考，如下所示：

"这是一个支持多个数据参考的示例句子 [数据: <数据集名称> (记录编号); <数据集名称> (记录编号)]。"

单个参考中不要列出超过5个记录编号。相反，列出最相关的前5个记录编号，并添加“+more”以表示还有更多。

例如：
"X先生是Y公司的所有者，并且面临许多不当行为指控 [数据: 报告 (1), 实体 (5, 7); 关系 (23); 声明 (7, 2, 34, 64, 46, +more)]。"

其中1, 5, 7, 23, 2, 34, 46和64代表相关数据记录的ID（不是索引）。

不要包含没有提供支持证据的信息。

# 示例输入
-----------
文本：

实体

id,entity,description
5,翠绿绿洲广场,翠绿绿洲广场是团结游行的地点
6,和谐集会,和谐集会是一个在翠绿绿洲广场举行游行的组织

关系

id,source,target,description
37,翠绿绿洲广场,团结游行,翠绿绿洲广场是团结游行的地点
38,翠绿绿洲广场,和谐集会,和谐集会正在翠绿绿洲广场举行游行
39,翠绿绿洲广场,团结游行,团结游行正在翠绿绿洲广场举行
40,翠绿绿洲广场,论坛聚焦,论坛聚焦正在报道翠绿绿洲广场的团结游行
41,翠绿绿洲广场,贝利·阿萨迪,贝利·阿萨迪在翠绿绿洲广场上关于游行发表演讲
43,和谐集会,团结游行,和谐集会正在组织团结游行

输出：
```json
{
    "title": "翠绿绿洲广场和团结游行",
    "summary": "社区围绕翠绿绿洲广场，这里是团结游行的地点。广场与和谐集会、团结游行和论坛聚焦有关系，所有这些都与游行活动有关。",
    "rating": 5.0,
    "rating_explanation": "由于团结游行期间可能出现的动荡或冲突，影响严重性评分为中等。",
    "findings": [
        {
            "summary": "翠绿绿洲广场是中心地点",
            "explanation": "翠绿绿洲广场是该社区的中心实体，是团结游行的地点。广场是所有其他实体的共同联系点，表明其在社区中的重要性。广场与游行的关联可能会导致公共秩序问题或冲突，这取决于游行的性质及其引起的反应。[数据: 实体 (5), 关系 (37, 38, 39, 40, 41, +more)]"
        },
        {
            "summary": "和谐集会在社区中的作用",
            "explanation": "和谐集会是该社区的另一个关键实体，是翠绿绿洲广场游行的组织者。和谐集会的性质及其游行可能是威胁的潜在来源，这取决于他们的目标及其引起的反应。和谐集会与广场之间的关系是理解该社区动态的关键。[数据: 实体(6), 关系 (38, 43)]"
        },
        {
            "summary": "团结游行是重要事件",
            "explanation": "团结游行是翠绿绿洲广场上发生的重要事件。该事件是社区动态的关键因素，可能是威胁的潜在来源，这取决于游行的性质及其引起的反应。游行与广场之间的关系是理解该社区动态的关键。[数据: 关系 (39)]"
        },
        {
            "summary": "论坛聚焦的角色",
            "explanation": "论坛聚焦正在报道翠绿绿洲广场上的团结游行。这表明该事件已吸引媒体关注，可能会放大其对社区的影响。论坛聚焦的角色可能在塑造公众对事件及相关实体的看法方面起重要作用。[数据: 关系 (40)]"
        }
    ]
}
```

# 真实数据
使用以下文本（可能是 markdown 格式）回答。不要在答案中编造任何内容。

文本：
THIS_IS_TEXT_INPUT

输出:
'''

async def get_canvas_knowledge_community_report() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="CANVAS_KNOWLEDGE_COMMUNITY_REPORT",
        prompt_name="画布知识库 - 社区报告",
        prompt_desc="画布知识库 - 社区报告",
        prompt_content=COMMUNITY_REPORT,
    )
    return template


ENTITY_EXTRACTION = '''
## 目标
给定一组实体类型和有个 json 结构表示的流程画布和关于这个画布的解读文档，从给出的文档中识别出所有这些类型的实体以及已识别出的实体之间的关系。
注意，识别的目的是为了后续参考识别的实体和关系，更好的做用户运营和营销。

为了更好识别实体，你可能需要以下知识：
''' + SA_INTRODUCE + SDH_INTRODUCE + SF_INTRODUCE + '''

## 职责与步骤
你必须严格按照以下步骤回复：

Document: <|DOCUMENT_START|>用户提供的文档内容<|DOCUMENT_END|>，文档会以 <|DOCUMENT_START|> 开头，并以 <|DOCUMENT_END|> 结尾，文档内容是画布结构+画布解读信息
EntityTypes: 这一步由用户提供，表示可回复的实体类型
RelationTypes: 这一步由用户提供，表示可回复的实体之间的关系类型
Thought: 你需要思考文档中描述了什么事情，需要抽取什么实体，以及什么关系，实体和关系可以有多个
ResponseEntities:
```json
[
    {"entity":"<entity_name>", "type":"<entity_type>", "description":"<entity_description>"},
    ...
]
```
ResponseRelations:
```json
[
    {"source_entity":"<source_entity>", "target_entity":"<target_entity>", "relation_type":"<relation_type>", "relation_description":"<relationship_description>", "relation_strength":<relationship_strength>}
    ...
]
```
你必须以 Thought 回复开头，并且给出 ResponseEntities 和 ResponseRelations，ResponseEntities 和 ResponseRelations 的内容需要放在 markdown 代码块中。

ResponseEntities 中的字段解释如下：
    - entity_name：实体名称；
    - entity_type：必须是在 EntityTypes 中提供的实体类型之一
    - entity_description：对实体属性和实体的全面描述

ResponseRelations 是 ResponseEntities 中的实体之间的关系，注意实体必须是在 ResponseEntities 中识别出来的实体！
    - source_entity：源实体（entity_name）
    - target_entity：目标实体（entity_name）
    - relation_type: 关系类型，必须是 RelationTypes 中的类型之一
    - relationship_description：解释为什么您认为源实体和目标实体彼此相关
    - relationship_strength：表示源实体和目标实体之间关系强度的数字，1 到 10 的整数类型表示，1最弱，10最强

## 回复要求与限制等
需要特别注意：你应该基于用户提供的文档回复，前面的内容仅供你理解文档和回复要求使用。
如果没有有意义的实体和关系，你直接在 json 代码中回复空列表即可。

### 示例
用户的输入如下：
Document:
<|DOCUMENT_START|>
画布结构：
```json
{
    "id": 12234,
    "type": ...
}
```
本画布解读：
这个画布是关于 xxx ...
<|DOCUMENT_END|>

EntityTypes:
```
    - canvas: 流程画布，流程画布是一个很大的 json 结构，具有唯一的 id，节点名称为画布 id
    - canvas_report: 流程画布的画布报告，根据文档中给出的画布报告直接给出报告即可，节点名称固定为 canvas_id-report，即 画布id-report 拼接，-report 为固定字符串，画布报告放在 description 字段中
```

RelationTypes:
```
    - with_report: 表示画布的报告类型
```

##### 正确回复示例
Thought: 从文档中已经知道了画布的结构、id、以及对应的画布解读，下面我将回复用户。
ResponseEntities:
```json
[
    {"entity":"12234", "type":"canvas", "description":"本画布是一个 ..."},
    {"entity":"12234_report", "type":"canvas_report", "description":"这个画布是关于 xxx ..."}
]
```
ResponseRelations:
```json
[
    {"source_entity":"12234", "target_entity":"12234_report", "relation_type":"with_report", "relation_description":"画布的解读内容", "relation_strength":10}
]
```

## 开始
按照上述要求，结合用户给出的内容回复。
'''

SUMMARIZE_DESCRIPTIONS = '''
## 职责介绍
你是一位负责任的助理，负责生成提供数据的全面总结。
给定一个或两个实体，以及一系列描述，这些描述都与相同的实体或实体组有关。
请将所有这些描述总结成一个单一的、全面的描述。确保包含所有描述中的信息。
如果所提供的描述有矛盾，请解决这些矛盾并提供一个单一的、一致的总结。
确保使用第三人称书写，并包括实体名称，以便我们有完整的上下文。

## 输出限制
需要特别注意：你应该基于用户提供的实际内容输出，而「背景知识及文档内容相关解释」中的内容作为你理解文档使用。

#######
-数据-
实体列表：{entity_name}
描述列表：{description_list}
#######
输出：
'''
