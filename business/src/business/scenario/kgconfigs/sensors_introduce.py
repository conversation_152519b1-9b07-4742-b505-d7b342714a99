from jinja2 import Template

from framework.prompt import PromptFactory

SENSORS_INTRODUCE = """
神策数据是一家面向企业提供软件服务的软件公司，其软件产品包括：
1. 神策数界平台(Sensors Data Horizon，简称 SDH 或者 SPS)，用户数据引擎，即用户数据平台(CDP)
2. 神策分析 (Sensors Analytics，简称 SA)产品
3. 神策智能运营(Sensors Focus，简称 SF), 依赖 CDP 来完成自动化营销
这些产品是可向客户售卖的产品，这些产品帮助客户来洞察他们的用户，并对他们的用户做智能化营销等。
"""

async def get_sensors_introduce_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="SENSORS_INTRODUCE_PROMPT",
        prompt_name="神策产品介绍",
        prompt_desc="神策产品介绍",
        prompt_content=SENSORS_INTRODUCE,
    )
    return template

SA_INTRODUCE = """
SA，即神策分析（Sensors Analytics），是神策数据可售卖的数据分析产品。其基本功能包括了数据接入、数据存储（CDP）、数据分析、指标洞察等。
SA 提供了丰富的埋点SDK，能让客户在自己的产品上对用户发生的事件、用户的基本属性等进行埋点和上报。
SA 提供了丰富的分析功能，比如事件分析、用户路径分析、归因分析等，客户可以使用这些分析对他们的产品和用户进行深入的分析和洞察。
SA 除了提供这些分析以外，还提供了报表、概览、业务集市、指标平台等，客户可以使用这些功能进行指标口径的管理，以及根据业务需求将不同的图表组合成看板等。
SA 还提供了渠道追踪的功能，用于追踪用户的点击、查看来源渠道等。
SA 还提供了诸如权限管理、系统设置等基础功能。
"""

async def get_sa_introduce_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="SA_INTRODUCE_PROMPT",
        prompt_name="神策分析产品介绍",
        prompt_desc="神策分析产品介绍",
        prompt_content=SA_INTRODUCE,
    )
    return template

SDH_INTRODUCE = """
SDH，即神策数界(Sensors Data Horizon，简称 SDH 或者 SPS)，用户数据引擎，即用户数据平台(CDP)，功能包括：
    - 数据管理，主要管理的数据包括：
        - profile: 用户基础信息，存储于 users 表中，比如用户的姓名、年龄、地域等；
        - event: 用户事件行为信息，比如用户在什么时间、什么地点、点击了哪个按钮等等行为记录，存储与 events 表；
        - items: 企业的商品或者物品信息，存储与 items 表。
        - 外部表 / 多实体等
    - 标签管理：通过 SQL、自定义规则等方式计算用户的标签值
    - 分群管理：通过 SQL、自定义规则等方式计算用户的群体分类
SDH 复用 SA 的权限管理等基础模块。
"""

async def get_sdh_introduce_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="SDH_INTRODUCE_PROMPT",
        prompt_name="神策数界产品介绍",
        prompt_desc="神策数界产品介绍",
        prompt_content=SDH_INTRODUCE,
    )
    return template

SF_INTRODUCE = """
SF，即神策智能运营(Sensors Focus，简称 SF), 依赖 CDP 来完成自动化营销，其主要包含了以下模块功能：
    - 流程画布：简称「画布」，企业可以自主配置用户的流转节点，在各个节点的判定规则（比如是否做了某个事件等），并通过触达通道给用户发送营销信息等；
    - 运营计划：简称「计划」，通过简单的配置即可实现批量的、简单的营销触达，注意流程画布和运营计划是两个功能；
    - 资源位：包括比如弹窗、物品推荐、banner 等营销方式；
    - 微信运营：常见的微信公众号、小程序、微信裂变等玩法（需要注意：微信公众号的群发等可以通过运营计划、流程画布完成）。
其他的比如触达通道管理、全局勿扰设置等等在营销的时候需要用到的功能，均归属于 SF。
SF 也复用 SA 的权限管理、数据埋点等基础部分。
"""

async def get_sf_introduce_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="SF_INTRODUCE_PROMPT",
        prompt_name="神策智能运营产品介绍",
        prompt_desc="神策智能运营产品介绍",
        prompt_content=SF_INTRODUCE,
    )
    return template
