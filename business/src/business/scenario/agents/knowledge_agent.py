from typing import Generator, Any, AsyncGenerator
from langchain_core.messages import BaseMessage, HumanMessage

from business.knowledge_factory import KnowledgeFactory
from business.scenario.service.preset_knowledgebase_service import PresetKnowledgebaseService
from framework.agents.autogen import ParticipantAgent
from framework.agents.schema import Conversation, Role
from framework.knowledge import RagClient
from common import tools
from framework.models import BaseLLM

log = tools.get_logger()


class KnowledgeAgent(ParticipantAgent):
    """知识库检索 agent"""

    def __init__(
            self,
            name: str,
            description: str,
            rag_client: RagClient,
            **kwargs,
    ):
        if not description:
            description = rag_client.description

        super().__init__(name=name, description=description, **kwargs)
        self.retrieve_client = rag_client

    @classmethod
    async def common_knowledge_agent(cls, llm: BaseLLM, **kwargs):
        """获取通用知识库 agent"""
        common_knowledge = await KnowledgeFactory.async_get_knowledge_base_client(
            name='common',
        )
        kwargs = {
                     "name": "common_knowledge_retrieval_role",
                     "description": "用于检索营销通用知识库，知识库内包含一些通用营销知识，需要明确指定检索哪方面知识",
                     "llm": llm,
                     "rag_client": common_knowledge,
                 } | kwargs
        if common_knowledge:
            return cls(**kwargs)

    @classmethod
    async def industry_knowledge_agent(cls, llm: BaseLLM, industry: str, **kwargs):
        industry_knowledge = await KnowledgeFactory.async_get_knowledge_base_client(
            name=f"industry_{industry}",
        )
        kwargs = {
                     "name": "industry_knowledge_retrieval_role",
                     "description": f"用于检索当前行业的营销行业知识库，知识库内包含一些该行业的营销知识，需要明确指定检索哪方面知识，当前行业：{industry}",
                     "llm": llm,
                     "rag_client": industry_knowledge,
                 } | kwargs
        if industry_knowledge:
            return cls(**kwargs)

    @classmethod
    async def customer_knowledge_agent(
            cls, llm: BaseLLM, tenant_info: dict, custom_knowledge: dict | None = None, **kwargs
    ):
        organization_id = tenant_info['organization_id']
        project_id = tenant_info['project_id']
        service = PresetKnowledgebaseService()

        kb_id = None
        exist_knowledgebase = await service.get_customer_knowledgebase(
            organization_id=organization_id,
            project_id=project_id,
        )
        log.info(f"exist_knowledgebase: {exist_knowledgebase}")

        if not exist_knowledgebase:
            await service.create_customer_knowledgebase(
                organization_id=organization_id,
                project_id=project_id,
            )
            exist_knowledgebase = await service.get_customer_knowledgebase(
                organization_id=organization_id,
                project_id=project_id,
            )
            kb_id = exist_knowledgebase['id']
        else:
            kb_id = exist_knowledgebase['id']

        rag_client_kwargs = kwargs.copy()
        rag_client_kwargs = rag_client_kwargs | {
            "custom_knowledge": custom_knowledge,
            "id": kb_id
        }
        customer_knowledgebase = await KnowledgeFactory.async_get_knowledge_base_client(**rag_client_kwargs)

        kwargs = {
                     "name": "customer_knowledge_retrieval_role",
                     "description": "用于检索当前客户知识库，知识库内包含当前客户的历史计划/画布/策略，以及检索 CDP 环境的事件、用户属性、标签分群元数据信息等，需要明确指定需要检索什么内容",
                     "llm": llm,
                     "rag_client": customer_knowledgebase,
                 } | kwargs
        if customer_knowledgebase:
            return cls(**kwargs)

    def run(self) -> BaseMessage:
        """运行 agent 步骤"""
        return tools.asyncio_run(lambda: self.arun())

    def stream_run(self) -> Generator[BaseMessage, Any, Any]:
        yield self.run()

    async def arun(self) -> BaseMessage:
        """运行 agent 步骤"""
        history = self.get_history()
        history.append(HumanMessage(content=f"你是 {self.name}，请按照要求检索内容", name='system'))
        if len(history) > 10:
            history = history[-10:]

        messages = []
        for message in history:
            if not isinstance(message, HumanMessage):
                messages.append(Conversation(role=Role.ASSISTANT, content=message.content))
            else:
                messages.append(Conversation(role=Role.USER, content=message.content))
        log.info(f'history: {messages}')
        result = await self.retrieve_client.simple_search(messages)
        response = result.content
        return self._convert_message(message={'role': 'assistant', 'content': response, 'name': self.name})[0]

    async def astream_run(self) -> AsyncGenerator[Any, Any]:
        response = await self.arun()
        yield response
