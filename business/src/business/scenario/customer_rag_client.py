import json
import shutil
from pathlib import Path
from typing import Dict, List, Literal, Callable

from jinja2 import Template
from langchain_core.messages import BaseMessage
from tenacity import retry, stop_after_attempt, retry_if_exception_type, wait_fixed

from framework.prompt import PromptFactory
from common import tools
from core.service.tenant_project_service import TenantProjectService
from framework import config
from business.focus.core.canvas_insight.insight_client import StrategyInsight<PERSON>lient
from business.focus.core.prompts.canvas_introduce import CANVAS_INTRODUCE, get_canvas_introduce_prompt
from framework.agents.schema import Conversation, Role
from framework.models import BaseLLM, ModelsFactory
from framework.knowledge.base import KnowledgeBase
from framework.knowledge.simplerag.simple_rag_client import SimpleRag
from framework.knowledge.rag_client import RagClient
from framework.agents.autogen import (
    ParticipantAgent, AutoGenWorkflow, ReactManagerAgent
)

from framework.tools.sensors_metadata import SensorsMetadataTool
from business.scenario.kgconfigs import DEFAULT_ENTITY_TYPES
from business.scenario.kgconfigs.canvas_knowledge_configs import K<PERSON>OWLEDGE_BASE_CANVAS_ENTITY_TYPES_IN_SIMPLE, \
    get_canvas_knowledge_community_report
from business.scenario.kgconfigs.sensors_introduce import SENSORS_INTRODUCE, SA_INTRODUCE, SDH_INTRODUCE, SF_INTRODUCE, \
    get_sensors_introduce_prompt, get_sdh_introduce_prompt, get_sa_introduce_prompt, get_sf_introduce_prompt

log = tools.get_logger()

MANAGER_PROMPT = """
# 职责描述
你正在一个角色扮演游戏中，这个游戏旨在尽最大努力给用户的话题提供参考，参与角色如下：
{{role_and_descriptions}}

# 背景知识及文档内容相关解释
为了能更好的玩这个游戏，你需要以下背景知识：
{{background_knowledge}}

# 输出格式
你需要按照以下格式回复：
```
Thought: 当前问题已经解决了 xx，为了解决 xx 问题，我需要 xx 角色来做 xx 事情
Choose: 角色名称，角色名称只能从 {{participants}} 中选一个
Do: 角色需要做的事情，比如：请你继续分析用户的意图
```
你必须始终以 `Thought: ` 开头，Thought 中你需要思考当前提供的内容是否足够回答用户问题，以及需要谁做什么等。
不允许使用 markdown 代码块标记回复！如果回复中包含代码信息，则可以使用 markdown 代码块标记。
如果你所选的角色已经回复了你，则他们会加上他们自己的回复，比如：Do Response: xxx。

# 输出结果的步骤
你需要不断重复上述格式步骤，直至检索到足够的信息为止。需要注意的是，其他角色的发言通常不全（或者会用局部信息欺骗你），
为了保证用户能获取足量的信息，你应该尽可能的让他们发言，直到无法得到新的信息为止。

注意事项：
  - 只有 reporter 才能最终回复用户，其他角色都不能跟用户直接沟通，所以需要最终回复的内容请明确告知 reporter。
  - reporter 不能执行检索、任务分配等任务，reporter 只能将其他角色的回复结果转述给用户！
  - reporter 是最终用来回复用户的角色，只有在需要终止上述思考步骤的时候才需要，如果不终止上述步骤，则不要指定 reporter 角色。
  - 如果其他角色已经无法解决问题，或者出现了很多重复或者无意义的讨论，你应该直接让 reporter 收集其他角色的结果并回复用户。
  - 你不能从对话内容中获取角色名称！

# 用户对话记录与需求
仔细阅读下面的对话记录，并从中识别到用户(user/human)最终的需求，然后按照上述格式解决用户需求。
***
{{chat_history}}

{{query_prompt}}

开始：
"""

async def get_customer_rag_manager_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="CUSTOMER_RAG_MANAGER_PROMPT",
        prompt_name="客户知识库检索机器人 - 管理者提示词",
        prompt_desc="客户知识库检索机器人 - 管理者提示词",
        prompt_content=MANAGER_PROMPT,
    )
    return template


METADATA_ENTITY_TYPES = {
    'user_property': '用户属性的元数据定义，用户属性的定义中，name 通常为直接的字段名，比如：$city',
    'user_tag': '用户标签的元数据定义，用户标签的 name 通常以 user_tag_ 开头，用户标签使用方法与用户属性相同，是通过一定方式计算出来的属性',
    'event': '事件元数据定义，在事件的元数据定义中，name 通常为 events. 开头，事件是表示用户行为的记录数据',
    'user_group': '用户分群的元数据定义，用户分群指的是将某些有共同特征的用户集合到一个群组，该群组就是分群，分群的 name 通常以 user_segment 或者 user_group 开头',
}
METADATA_PRE_KNOWLEDGE = SENSORS_INTRODUCE + SA_INTRODUCE + SDH_INTRODUCE + """
给定的数据有四种类型，为以下四种类型之一：
""" + '\n'.join(['  - ' + desc for desc in METADATA_ENTITY_TYPES.values()]) + """

需要注意的是：有一些特殊的、预置的字段你不需要在提取的时候特殊处理：
  - 事件元数据中的 $app_version/$city/$manufacturer/$model/$os/$os_version/$screen_height/$screen_width/$wifi/time/$receive_time/login_id/anonymous_id/identities/project/time_free 字段均为所有事件都有的字段，不需要特殊提取

而有些类型的某些信息需要重点关注：
  - user_property 中的字段类型及字段含义，非常重要！
  - user_tag 中的字段类型、来源、定义（规则或者SQL等）及含义非常重要！
  - event 的事件含义和重要的事件属性
  - user_group 中的分群名称、含义及定义规则所表达的意思非常重要！
"""

async def get_metadata_pre_knowledge() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="METADATA_PRE_KNOWLEDGE_PROMPT",
        prompt_name="客户知识库检索机器人 - 元数据预知识提示词",
        prompt_desc="客户知识库检索机器人 - 元数据预知识提示词",
        prompt_content=METADATA_PRE_KNOWLEDGE,
    )
    return template

STRATEGY_QUANTITY_PROMPT = """# 职责描述
请结合流程画布的结构配置，使用自然语言描述画布的质量。
画布质量包括：
    1. 画布结构的合理性：是根据画布从进入节点到退出节点，中间的转化是否合理来评分，画布结构可以是：不合理、一般、合理、很合理、非常合理 共5个等级描述
    2. 画布使用的触达渠道(Action组件)质量：综合衡量画布使用的渠道、渠道发送的内容、渠道的转化情况，可以是：低质量、一般、标准、高质量、极高质量 共5个等级描述
    3. 画布目标的完成度：画布目标完成的综合指标，可以是：完全未完成、少部分完成、中等完成（即完成接近一半）、大部分完成、全部完成 共5个等级描述
    4. 画布的综合质量：根据流程画布的结构、渠道、报告、目标完成情况等给出画布的综合评价，可以是：极低质量、低质量、标准质量、高质量、极高质量 共5个等级描述

# 为了更好的理解画布，你可能需要以下知识
{{pre_knowledge}}

# 回复要求
你应该始终以 `Think:` 开头，在 `Think:` 中，你需要逐步思考画布的结构，以及给出画布的上述四个指标等级。
在你思考完成以后，你需要先回复一个 `Answer:`，然后再逐行给出上述四个维度的描述。

每个画布都必须包括上述四个维度的质量描述，一个合理的回复如下：
Think: 画布是一个 xxx 画布，我们首先需要知道画布的结构，画布的结构为 xxx，目标为 xxx，...，从中可以得出 xxx ...
Answer:
画布的结构合理性一般，原因是 xxx
画布的触达渠道质量较高，等级为高质量，原因是 xxx
画布的目标完成度为大部分完成，原因是 xxx
画布的综合质量为标准质量，原因是 xxx

# 开始
根据以下画布的配置信息和解读报告，给出质量描述：
***
{{canvas_info}}
"""

async def get_strategy_quantity_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="STRATEGY_QUANTITY_PROMPT",
        prompt_name="客户知识库检索机器人 - 画布质量提示词",
        prompt_desc="客户知识库检索机器人 - 画布质量提示词",
        prompt_content=STRATEGY_QUANTITY_PROMPT,
    )
    return template

@retry(
    stop=stop_after_attempt(5),
    retry=retry_if_exception_type(BaseException),
    wait=wait_fixed(60),
    reraise=True,
)
def _call_llm(llm: BaseLLM, system_prompt: str) -> str:
    return llm.complete(input={'role': 'system', 'content': system_prompt}).content


def _extract_strategy_purpose(text: str, llm: BaseLLM) -> List[str]:
    """抽取画布目标主题"""
    prompt = ("请结合流程画布的结构配置和解读，给出当前流程画布（即营销活动）的目标。"
              "目标需要根据用户配置的转化目标或者转化指标，或者根据流程画布的结构配置所推测出来的目标，"
              "目标应该是一个中文表述比如：提升日活、提升月活这种，一个流程画布应该有一个或者多个目标。"
              f"\n\n流程画布信息：\n{text}"
              "\n\n回复要求：\n你应该直接回复流程画布的目标，如果流程画布有多个目标，则你应该分多行回复。"
              "\n除了流程画布目标以外，你不能回复其他任何内容！")
    response = _call_llm(llm=llm, system_prompt=prompt)
    response = response.split('\n')
    log.info(f'extract strategy purpose: {response}')
    return ['画布目标：' + r for r in response if r.strip()]


def _extract_strategy_structure(text: str, llm: BaseLLM) -> List[str]:
    """抽取画布结构描述"""
    prompt = ("请结合流程画布的结构配置，使用自然语言按照步骤描述流程画布。"
              "流程画布的结构描述示例："
              "\n1. 画布为定时单次画布，圈选受众为三天内未登录过APP的用户\n2. 用户完成了APP登录事件，则...\n3. ..."
              "\n你应该直接回复流程画布的结构，除了流程画布结构以外，你不能回复其他任何内容！"
              f"\n\n流程画布信息：\n{text}")
    response = _call_llm(llm=llm, system_prompt=prompt)
    log.info(f'extract strategy structure: {response}')
    return ["画布结构描述：\n" + response]


def _extract_strategy_quantity(text: str, llm: BaseLLM) -> List[str]:
    """抽取画布质量描述"""

    template = tools.asyncio_run(get_strategy_quantity_prompt)
    sensors_introduce = tools.asyncio_run(get_sensors_introduce_prompt).render()
    sa_introduce = tools.asyncio_run(get_sa_introduce_prompt).render()
    sdh_introduce = tools.asyncio_run(get_sdh_introduce_prompt).render()
    sf_introduce = tools.asyncio_run(get_sf_introduce_prompt).render()
    canvas_introduce = tools.asyncio_run(get_canvas_introduce_prompt).render()
    prompt = template.render(
        pre_knowledge=sensors_introduce + sa_introduce + sdh_introduce + sf_introduce + canvas_introduce,
        canvas_info=text,
    )
    response = _call_llm(llm=llm, system_prompt=prompt)
    if 'Answer:' in response:
        response = response.split('Answer:')[1]
    response = response.split('\n')
    log.info(f'extract strategy quantity: {response}')
    return [r for r in response if r.strip()]


class SubRagClient(RagClient):
    def __init__(self, name: str, description: str, rag_client: RagClient, **kwargs):
        kwargs = kwargs | rag_client.kwargs
        super().__init__(kb=rag_client.knowledge_base, **kwargs)
        self.rag_client = rag_client
        self.name = name
        self.description = description
        self.id = rag_client.knowledge_base.id

    async def build_index(self, index_mode: Literal['reindex', 'resume', 'update'] = 'reindex', **kwargs):
        return await self.rag_client.build_index(index_mode=index_mode, **kwargs)

    async def search(self, history: List[Conversation], **kwargs) -> Conversation:
        return await self.rag_client.search(history=history, **kwargs)

    async def simple_search(self, history: List[Conversation], **kwargs) -> Conversation:
        return await  self.rag_client.simple_search(history=history, **kwargs)


class RagParticipantAgent(ParticipantAgent):
    """包装 rag 类型"""

    def __init__(
            self,
            rag_client: SubRagClient,
            **kwargs,
    ):
        super().__init__(name=rag_client.name, description=rag_client.description, **kwargs)
        self.rag_client = rag_client

    def run(self) -> BaseMessage:
        """运行 rag 步骤"""
        return tools.asyncio_run(lambda: self.arun())

    async def arun(self) -> BaseMessage:
        """运行 rag 步骤"""
        history = self.get_history()
        result = await self.rag_client.simple_search(history=[self._basemessage_to_conversation(_) for _ in history])
        result = result.content
        return self._convert_message(message={'role': 'assistant', 'content': result, 'name': self.name})[0]


class CustomerRagClient(RagClient):
    """
    客户知识库获取，客户知识库混合了多种类型的知识库而重新封装的知识库
    技术方案：https://doc.sensorsdata.cn/pages/viewpage.action?pageId=565420578
    """

    def __init__(
            self,
            organization_id: str,
            project_id: int,
            custom_knowledge: Dict[int | str, str] | None = None,
            llm: BaseLLM = ModelsFactory.get_llm(),
            max_token_limit: int = int(config.llm_config['input_token_limit'] * 0.8),
            query_limit=20,
            max_talk_round=5,
            score_limit=0.45,
            callback: Callable[[str], None] = None,
            **kwargs
    ):
        """
        客户知识库
        """
        super().__init__(**kwargs)
        self.organization_id = organization_id
        self.project_id = project_id

        if custom_knowledge is None:
            custom_knowledge = {}
        self.custom_knowledge = custom_knowledge
        self.llm = llm
        self.max_token_limit = max_token_limit
        self.query_limit = query_limit
        self.max_talk_round = max_talk_round
        self.score_limit = score_limit
        self.callback = callback

        self._retrieve_workflow = None

    @property
    def custom_rag_clients(self) -> List[SubRagClient]:
        """
        获取 RAG Clients
        返回值：kb_id -> (description, client)
        """
        clients = []
        for kb_id, kb_desc in self.custom_knowledge.items():
            kb_id = 100000 + kb_id
            rag_client = SubRagClient(
                name=f'role_{kb_id}',
                description=kb_desc,
                rag_client=SimpleRag(
                    kb=KnowledgeBase(
                        id=kb_id,
                        base_dir=str(self.base_path.joinpath(str(kb_id)).absolute()),
                        name='',
                        cname=''
                    ),
                    entity_types=DEFAULT_ENTITY_TYPES,
                    llm=self.llm,
                    max_token_limit=self.max_token_limit,
                    query_limit=self.query_limit,
                    max_talk_round=self.max_talk_round,
                    score_limit=self.score_limit,
                    callback=self.callback,
                    retrieve_documents_only=True,
                )
            )
            clients.append(rag_client)
        return clients

    async def _get_tenant_project(self) -> dict | None:
        if hasattr(self, '_tenant_project'):
            return self._tenant_project
        result = await TenantProjectService().get_tenant_project(
            organization_id=self.organization_id,
            project_id=self.project_id
        )
        self._tenant_project = result.dict() if result else None
        return self._tenant_project

    async def reload_customer_metadata(self):
        """同步客户元数据"""
        tenant_project = await self._get_tenant_project()
        if not tenant_project:
            raise RuntimeError("Project not found.")

        tool = SensorsMetadataTool(
            cdp_host=tenant_project['cdp_host'],
            project_id=self.project_id,
            project_name=tenant_project['project_name'],
            api_key=tenant_project['api_key'],
        )
        rag_client = self.customer_metadata_rag_client
        rag_client.clean_input_files()

        temp_path = self.base_path / "metadata"
        if not temp_path.exists():
            temp_path.mkdir(parents=True, exist_ok=True)

        # 用户属性
        user_properties = tool.run(tool_input={'query_mode': 'query_user_fields'})
        log.info(f'queried user properties: {user_properties}')
        if not isinstance(user_properties, list):
            raise RuntimeError("Query user properties error.")
        for prop in user_properties:
            file = temp_path.joinpath(f'user_property_{prop["name"]}.json.txt')
            file.write_text(data=json.dumps(prop, indent=4, ensure_ascii=False), encoding='utf-8')
            rag_client.add_input_file(file)

        # 事件及事件属性
        events = tool.run(tool_input={'query_mode': 'query_events_schemas'})
        log.info(f'queried events: {events}')
        if not isinstance(events, list):
            raise RuntimeError("Query events error")
        event_names = [e['name'] for e in events]
        events_properties = tool.run(tool_input={'query_mode': 'query_event_fields', 'event_names': event_names})
        log.info(f'queried event properties: {events_properties}')
        events_properties_map = dict([(e['event_name'], e['properties']) for e in events_properties])
        for event in events:
            event_name = event['name']
            props = events_properties_map.get(event_name, None)
            event['properties'] = props
            file = temp_path.joinpath(f'event_{event_name}.json.txt')
            file.write_text(data=json.dumps(event, indent=4, ensure_ascii=False), encoding='utf-8')
            rag_client.add_input_file(file)

        # 分群
        user_groups = tool.run(tool_input={'query_mode': 'query_user_groups'})
        log.info(f'queried user groups: {user_groups}')
        if not isinstance(user_groups, list):
            raise RuntimeError("Query user groups error.")
        for user_group in user_groups:
            file = temp_path.joinpath(f'{user_group["name"]}.json.txt')
            file.write_text(data=json.dumps(user_group, indent=4, ensure_ascii=False), encoding='utf-8')
            rag_client.add_input_file(file)

    @property
    def customer_metadata_rag_client(self) -> SubRagClient:
        """
        元数据 RAG client
        """
        kwargs = self.kwargs | {
            "kb": KnowledgeBase(
                id=1,
                base_dir=str(self.base_path.joinpath('metadata').absolute()),
                name='',
                cname=''
            ),
            "llm": self.llm,
            "entity_types": METADATA_ENTITY_TYPES,
            "pre_knowledge":  tools.asyncio_run(get_metadata_pre_knowledge),
            "additional_retrieve_requirement": '请尽量返回所检索的元数据结果（事件名称、属性名称、描述等），并且让其他角色也尽量返回元数据结果！',
            "max_token_limit": self.max_token_limit,
            "query_limit": self.query_limit,
            "max_talk_round": min(self.max_talk_round, 2),
            "score_limit": self.score_limit,
            "callback": self.callback,
            "retrieve_documents_only": True,
        }
        rag_client = SubRagClient(
            name='query_cdp_metadata',
            description="查询神策数据平台（CDP）的用户属性、事件及事件属性、标签、分群等元数据的定义，支持根据需求查询特定的元数据或者查询全部元数据",
            rag_client=SimpleRag(**kwargs)
        )
        return rag_client

    @property
    def _strategy_index_client(self) -> StrategyInsightClient:
        return StrategyInsightClient(
            base_dir=f'/tmp/{self.organization_id}/{self.project_id}',
            regenerate=True,
            llm=self.llm,
            callback=self.callback,
        )

    async def reload_strategy_data(self):
        index_client = self._strategy_index_client
        if index_client.base_path.exists():
            shutil.rmtree(index_client.base_path)
            index_client.base_path.mkdir(parents=True, exist_ok=True)

        tenant_project = await self._get_tenant_project()
        if not tenant_project:
            raise RuntimeError("Project not found.")

        await index_client.insight(
            host=tenant_project['cdp_host'],
            token=tenant_project['api_key'],
            organization=tenant_project['organization_id'],
            project=tenant_project['project_name'],
            strategy_ids="ALL",
        )
        temp_path = index_client.get_insight_report_dir(
            organization=tenant_project['organization_id'],
            project=tenant_project['project_name'],
        )
        rag_client = self.customer_strategy_rag_client
        for file in Path(temp_path).glob('**/*'):
            if file.is_file() and file.match('*.txt'):
                rag_client.add_input_file(file)

    @property
    def customer_strategy_rag_client(self) -> SubRagClient:
        """
        历史策略 RAG client
        """

        kwargs = self.kwargs | {
            "kb": KnowledgeBase(
                id=1,
                base_dir=str(self.base_path.joinpath('focus_strategies').absolute()),
                name='',
                cname=''
            ),
            "llm": self.llm,
            "entity_types": tools.asyncio_run(get_canvas_knowledge_community_report).render(),
            "pre_knowledge": SA_INTRODUCE + SDH_INTRODUCE + SF_INTRODUCE,
            "additional_extract_topics": [
                _extract_strategy_purpose,
                _extract_strategy_structure,
                _extract_strategy_quantity
            ],
            "additional_retrieve_requirement": '请你尽量返回包含画布id的原始信息，也让其他角色尽量返回原始信息，返回结果中尽量要给出画布的id，以及根据我的需求对他们的分析效果等',
            "max_token_limit": self.max_token_limit,
            "query_limit": min(self.query_limit, 8),
            "max_talk_round": self.max_talk_round,
            "score_limit": self.score_limit,
            "callback": self.callback,
            "retrieve_documents_only": False,
        }
        rag_client = SubRagClient(
            name='query_history_strategies',
            description="查询智能运营的历史策略（流程画布），可以根据需求或者指定条件查询，支持按照策略质量高中低查询",
            rag_client=SimpleRag(**kwargs)
        )
        return rag_client

    @property
    def history_strategy_agent(self) -> ParticipantAgent:
        """客户历史策略库 agent"""
        return RagParticipantAgent(
            rag_client=self.customer_strategy_rag_client,
            llm=self.llm,
        )

    @property
    def all_sub_rag_clients(self) -> List[SubRagClient]:
        all_clients = []
        rag_clients = self.custom_rag_clients
        if rag_clients:
            all_clients.extend(rag_clients)
        all_clients.append(self.customer_metadata_rag_client)
        all_clients.append(self.customer_strategy_rag_client)
        return all_clients

    def _init_retrieve_workflow(self):
        if self._retrieve_workflow is not None:
            return
        all_sub_rag_clients = self.all_sub_rag_clients

        agents = [
            RagParticipantAgent(rag_client=client, llm=self.llm)
            for client in all_sub_rag_clients
        ]

        kwargs = self.kwargs | {
            "name": "customer_manager",
            "llm": self.llm,
            "system_prompt": tools.asyncio_run(get_customer_rag_manager_prompt),
        }
        manager = ReactManagerAgent(**kwargs)

        self._retrieve_workflow = AutoGenWorkflow(
            manager_agent=manager,
            participant_agents=agents,
            need_extract_real_query=True,
            max_talk_round=self.max_talk_round,
            step_callback=self.callback,
        )

    async def get_web_pages(self):
        """
        抓取网页内容
        """
        pass

    async def build_index(
            self,
            index_mode: Literal['reindex', 'resume', 'update'] = 'reindex',
            **kwargs
    ):
        custom_rag_clients = self.custom_rag_clients
        if custom_rag_clients:
            for rag_client in custom_rag_clients:
                log.info(f'{index_mode} build knowledge index: {self.organization_id}:{self.project_id}')
                await rag_client.build_index(index_mode=index_mode, **kwargs)

        # CDP 元数据库 TODO 暂时没有做定时任务同步，而是索引的时候自动同步一次
        log.info('Start build metadata index.')
        await self.reload_customer_metadata()
        await self.customer_metadata_rag_client.build_index(index_mode='reindex', **kwargs)

        # 历史策略库
        await self.reload_strategy_data()
        await self.customer_strategy_rag_client.build_index(index_mode='reindex', **kwargs)

    async def search(self, history: List[Conversation], **kwargs) -> Conversation:
        return await self.simple_search(history=history)

    async def simple_search(self, history: List[Conversation], **kwargs) -> Conversation:
        self._init_retrieve_workflow()

        messages = []
        for message in history:
            if message.role == Role.ASSISTANT:
                messages.append({'role': 'assistant', 'content': message.content})
            else:
                messages.append({'role': 'user', 'content': message.content})
        result = await self._retrieve_workflow.run_with_history(history=messages)
        return Conversation(role=Role.ASSISTANT, content=result.content)
