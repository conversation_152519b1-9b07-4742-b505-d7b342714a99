import importlib
import json
from typing import Callable, Any, Dict

from common import tools
from core.service.knowledgebase_service import KnowledgebaseService
from framework.knowledge import SimpleRag, SensorsGraphRag, KnowledgeBase
from framework.models import BaseLLM, ModelsFactory
from framework.knowledge.rag_client import RagClient

log = tools.get_logger()
_cache: Dict[str, RagClient] = {}

kb_service = KnowledgebaseService()


class KnowledgeFactory:

    @classmethod
    async def async_get_knowledge_base_client(
            cls,
            id: int | None = None,
            name: str | None = None,
            llm: BaseLLM = ModelsFactory.get_llm(),
            callback: Callable[[Any], Any] = None,
            **kwargs,
    ) -> RagClient | None:
        if id is None and name is None:
            raise RuntimeError('id or name must be set')

        knowledgebase_model = None
        if id is not None:
            knowledgebase_model = await kb_service.get_knowledgebase(id=id)
        elif name is not None:
            knowledgebase_model = await kb_service.get_knowledgebase_by_name(name=name)

        if not knowledgebase_model:
            log.warning(f'Knowledgebase not found. id={id}, name={name}')
            return None

        knowledgebase_model = knowledgebase_model.dict()
        cache_key = f'{knowledgebase_model["name"]}@{knowledgebase_model["version"]}'
        if cache_key in _cache:
            return _cache[cache_key]

        config_map = knowledgebase_model['config']
        if config_map:
            config_map = json.loads(config_map)
        else:
            config_map = {}

        if not kwargs:
            kwargs = {}

        kb_basic_info = KnowledgeBase(
            id=knowledgebase_model['id'],
            name=knowledgebase_model['name'],
            cname=knowledgebase_model['cname'],
            base_dir=knowledgebase_model['base_dir'],
        )
        kwargs = kwargs | {
            'kb': kb_basic_info,
            'index_type': knowledgebase_model['index_type'],
            'custom_knowledge_class': knowledgebase_model['custom_knowledge_class'],
            'description': knowledgebase_model['description'],
            'llm': llm,
            'callback': callback,
        }
        kwargs = config_map | kwargs

        client = KnowledgeFactory._get_knowledge_base_client(**kwargs)
        _cache[cache_key] = client
        return client

    @classmethod
    def get_knowledge_base_client(
            cls,
            id: int | None = None,
            name: str | None = None,
            llm: BaseLLM = ModelsFactory.get_llm(),
            callback: Callable[[Any], Any] = None,
            **kwargs,
    ) -> RagClient | None:
        return tools.asyncio_run(
            cls.async_get_knowledge_base_client,
            id=id,
            name=name,
            llm=llm,
            callback=callback,
            **kwargs,
        )

    @classmethod
    def _get_knowledge_base_client(
            cls,
            custom_knowledge_class: str | None = None,
            index_type: str | None = None,
            **kwargs,
    ) -> RagClient:
        if custom_knowledge_class:
            log.info(f'使用自定义知识库类：{custom_knowledge_class}')
            class_name_parts = custom_knowledge_class.split('.')
            module_name = '.'.join(class_name_parts[:-1])
            class_name = class_name_parts[-1]
            module = importlib.import_module(module_name)
            knowledgebase_class = getattr(module, class_name)
            return knowledgebase_class(**kwargs)

        if not index_type:
            index_type = 'SIMPLE'

        match index_type:
            case 'SIMPLE':
                return SimpleRag(**kwargs)
            case 'GRAPH':
                return SensorsGraphRag(**kwargs)
            case 'PRESET':
                raise RuntimeError('PRESET knowledgebase type MUST have a custom knowledge class implement.')
            case _:
                raise RuntimeError(f'Unsupported knowledgebase type {index_type}')
