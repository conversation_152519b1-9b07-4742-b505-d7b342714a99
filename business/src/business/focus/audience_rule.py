from typing import List, Dict, Optional
from typing import Literal

from pydantic import BaseModel, Field
from enum import Enum


class PropertyFilterFunction(Enum):
    IS_SET = "is_set"
    NOT_SET = "not_set"
    """属性筛选函数"""
    IS_TRUE = "is_true"
    IS_FALSE = "is_false"
    "为假"
    LESS = "less"
    "小于"
    EQUAL = "equal"
    "等于"
    GREATER = "greater"
    "大于"
    NOT_EQUAL = "not_equal"
    "不等于"
    BETWEEN = "between"
    "在某个区间内, 全闭区间"
    CONTAIN = "contain"
    "包含"
    NOT_CONTAIN = "not_contain"
    "不包含"
    IS_EMPTY = "is_empty"
    "为空"
    IS_NOT_EMPTY = "is_not_empty"
    "不为空"
    RLIKE = "rlike"
    "正则匹配"
    NOT_RLIKE = "not_rlike"
    "正则不匹配"
    IN = "include"
    "集合包含"
    NOT_IN = "not_include"
    "集合不包含"
    RELATIVE_BETWEEN = "relative_between"
    "相对时间区间, 全闭区间"
    RELATIVE_AFTER = "relative_after"
    "相对时间之后"
    RELATIVE_BEFORE = "relative_before"
    "相对时间之前"


class PropertyFilterCondition(BaseModel):
    """属性筛选条件"""
    field: str = Field(...,
                       description='属性字段, 用户属性: user.属性名称, eg: user.age; 用户分群: user.分群名称, eg: user.user_group_123; 事件属性: event.事件名称.事件属性名称 eg: event.$appPay.amount')
    function: PropertyFilterFunction = Field(...,
                                             description='筛选函数, 与 field 的数据类型强相关, 不同数据类型函数取值不同')
    params: Optional[List[str]] = Field(default=None,
                                        description='筛选参数, 与 function 强相关, function 决定了有多少个参数')


class PropertyFilter(BaseModel):
    """属性筛选"""
    relation: Literal["and", "or"] = Field(..., description='规则关系，表示多个规则之间的关系，and 表示 且，or 表示或')
    conditions: List[PropertyFilterCondition] = Field(..., description='属性筛选条件')


class AudienceProfileRule(PropertyFilter):
    """用户属性筛选规则"""


class EventMeasureAggregator(Enum):
    """事件指标聚合函数
    GENERAL: 次数
    UNIQUE: 事件分布的天数
    SUM: 总和
    AVG: 平均值
    MAX: 最大值
    MIN: 最小值"""
    GENERAL = "general"
    """次数"""
    UNIQUE = "unique"
    """事件分布的天数"""
    SUM = "sum"
    """总和"""
    AVG = "avg"
    """平均值"""
    MAX = "max"
    """最大值"""
    MIN = "min"
    """最小值"""


class AudienceEventMeasure(BaseModel):
    """事件指标"""
    event: str = Field(..., description='事件名称')
    aggregator: EventMeasureAggregator = Field(..., description='聚合函数')
    aggr_field: Optional[str] = Field(default=None,
                                      description='用于聚合的事件属性(eg: event.$pay.price), 当聚合函数为 general(次数)/unique(分布天数) 时，aggr_field 无值')
    filter: Optional[PropertyFilter] = Field(default=None, description='事件属性筛选条件')


class AudienceTimeRange(BaseModel):
    """时间范围"""
    function: str = Field(..., description='时间筛选函数')
    params: List[str] = Field(default=None,
                              description='时间参数, 与 function 强相关, function 决定了有多少个参数')


class EventMeasureFilterFunction(Enum):
    """事件指标过滤函数
    LESS: 小于
    EQUAL: 等于
    GREATER: 大于
    NOT_EQUAL: 不等于
    GREATER_EQUAL: 大于等于
    LESS_EQUAL: 小于等于
    BETWEEN: 在某个区间内, 左闭右闭区间
    TOP_PERCENT: 前百分之几
    BOTTOM_PERCENT: 后百分之几
    RANK_PERCENT_BETWEEN: 排名在百分之几之间
    TOP_N: 前几名
    BOTTOM_N: 后几名
    RANK_N_BETWEEN: 排名在第几名到第几名之间
    """
    LESS = "less"
    EQUAL = "equal"
    GREATER = "greater"
    NOT_EQUAL = "not_equal"
    GREATER_EQUAL = "greater_equal"
    LESS_EQUAL = "less_equal"
    BETWEEN = "between"
    TOP_PERCENT = "top_percent"
    BOTTOM_PERCENT = "bottom_percent"
    RANK_PERCENT_BETWEEN = "rank_percent_between"
    TOP_N = "top_n"
    BOTTOM_N = "bottom_n"
    RANK_N_BETWEEN = "rank_n_between"


class AudienceEventMeasureCondition(BaseModel):
    """单个事件筛选条件"""
    measure: AudienceEventMeasure = Field(..., description='事件指标')
    time_range: AudienceTimeRange = Field(..., description='时间范围')
    function: EventMeasureFilterFunction = Field(..., description='指标过滤函数')
    params: List[str] = Field(..., description='指标过滤函数参数, 与 function 强相关, function 决定了有多少个参数')


class AudienceEventCondition(BaseModel):
    """事件筛选条件"""
    event: str = Field(..., description='事件名称')
    filter: Optional[PropertyFilter] = Field(default=None, description='事件属性筛选条件')


class AudienceEventRule(BaseModel):
    """事件筛选规则"""
    relation: Literal["and", "or"] = Field(..., description='规则关系，表示多个规则之间的关系，and 表示 且，or 表示或')
    events: List[AudienceEventMeasureCondition] = Field(..., description='事件筛选条件')


class SingleAudienceEventSequenceRule(BaseModel):
    """单个事件序列筛选规则"""
    time_range: AudienceTimeRange = Field(..., description='时间范围')
    steps: List[AudienceEventCondition] = Field(..., description='事件序列')


class AudienceEventSequenceRule(BaseModel):
    """事件序列筛选规则"""
    relation: Literal["and", "or"] = Field(..., description='规则关系，表示多个规则之间的关系，and 表示 且，or 表示或')
    multi_sequence_rules: List[SingleAudienceEventSequenceRule] = Field(..., description='事件筛选条件')


class AudienceRule(BaseModel):
    """受众规则"""
    select_all: bool = Field(default=True, description='是否表示全部用户, 若为 true 则 rule_content 字段无意义')
    relation: Literal["and", "or"] = Field(default="and",
                                           description='规则关系，表示多个规则之间的关系，and 表示 且，or 表示或')
    profile_rule: Optional[AudienceProfileRule] = Field(default=None, description='用户属性筛选规则')
    event_rule: Optional[AudienceEventRule] = Field(default=None, description='事件筛选规则')
    event_sequence_rule: Optional[AudienceEventSequenceRule] = Field(default=None, description='事件序列筛选规则')
