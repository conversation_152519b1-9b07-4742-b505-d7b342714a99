from common import tools as my_tools
from framework.agents.schema import Conversation, Role
from framework.models import BaseLLM, ModelsFactory
from .basic import BasicCanvasBot
from ...core.canvas_optimizer.agents.canvas_optimize_agent import CanvasOptimizeAgent
from ...tenant import ProjectInfo, CdpInfo

log = my_tools.get_logger()


class CanvasOptimizeBot(BasicCanvasBot):
    """画布策略优化单Agent版机器人"""

    def __init__(
            self,
            model_client: BaseLLM = None,
            stream: bool = False,
            **kwargs
    ):
        super().__init__(model_client=model_client, **kwargs)
        self.llm = model_client or ModelsFactory.get_llm(scenario='single_aud_rule_parse')
        self.stream = stream
        self.agent = CanvasOptimizeAgent(
            llm=self.llm,
            project_info=ProjectInfo(
                org_id=self.chat_session_info["organization_id"],
                project_name=self.chat_session_info["project_name"],
                project_id=self.chat_session_info["project_id"]
            ),
            cdp_info=CdpInfo(
                cdp_host=self.chat_session_info["cdp_host"],
                api_key=self.chat_session_info["api_key"]
            )
        )
        log.info("init optimize bot success")

    async def async_chat(self, history: list[Conversation]) -> Conversation:
        # 保留最近10条对话历史
        trimmed_history = history[-10:] if len(history) > 10 else history

        # 转换对话格式
        messages = []
        for conv in trimmed_history:
            role = "assistant" if conv.role == Role.ASSISTANT else "user"
            messages.append({"role": role, "content": conv.content})
        log.info(f'history: {messages}')

        # 直接调用优化Agent
        await self.send_status(status='PROCESSING', status_desc='优化中...', keeping=False)
        try:
            response = self.agent.astream_chat(prompts=messages)
            async for chunk in response:
                await self.send_message(message_type='MARKDOWN', content=chunk)
            log.info(f'final answer: {response}')
            await self.send_status(status='SUCCESS', status_desc='优化完成', keeping=True)
        except Exception as e:
            log.error(f'optimize error: {e}')
            response = f'优化失败：{e}'
            await self.send_status(status='ERROR', status_desc='优化失败', keeping=False)
        return Conversation(role=Role.ASSISTANT, content=response)
