import asyncio
import json
from typing import Optional

from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from pydantic import BaseModel, Field

from business.scenario.agents.knowledge_agent import KnowledgeAgent
from framework.agents.schema import Conversation, Role
from framework.models import BaseLLM, ModelsFactory
from .basic import BasicCanvasBot
from business.focus.core.prompts.canvas.canvas_predict_prompts import (
    PREDICT_CONTENT_PARSE_PROMPT,
    PREDICT_AUDIENCE_PROMPT, PREDICT_CONVERT_PROMPT, PREDICT_REPORTER_PROMPT, CANVAS_IDEA_CHECK_PROMPT,
    get_canvas_idea_check_prompt, get_canvas_predict_reporter_prompt, get_canvas_predict_convert_prompt,
    get_canvas_predict_audience_prompt, get_canvas_predict_content_parse_prompt
)
from framework.agents.assistant import AssistantAgent
from framework.tools.llm_retry import pydantic_chat_with_retry
from common import tools as my_tools
from ...core.canvas_common.tools.strategy_info import StrategyInfo

log = my_tools.get_logger()


class PredictStrategyInfo(BaseModel):
    type: str = Field(default=None, description="策略类型")
    strategy_id: Optional[str | int] = Field(default=None, description="策略id")
    strategy_result: Optional[dict] = Field(default=None, description="策略设计的完整结构")
    strategy_idea: Optional[str] = Field(default=None, description="策略思路")


class CanvasIdeaCheckResult(BaseModel):
    strategy_idea_check: bool = Field(default=None, description="策略是否符合要求")
    inquiry_words: Optional[str] = Field(default=None, description="信息不足时, 向用户询问的话语")


class CanvasPredictBot(BasicCanvasBot):
    def __init__(
            self,
            model_client: BaseLLM = None,
            stream: bool = False,
            **kwargs
    ):
        super().__init__(**kwargs)

        if not model_client:
            self.llm = ModelsFactory.get_llm(scenario='canvas_predict')
        else:
            self.llm = model_client
        self.stream = stream
        log.info("init predict bot success")

    async def async_chat(self, history: list[Conversation]) -> Conversation:
        await self.send_status(status='PROCESSING', status_desc='解析预测信息...', keeping=False)
        predict_strategy_info = await self._parse_predict_target(history)

        await self.send_status(status='PROCESSING', status_desc='正在预测中...', keeping=False)
        result = "无法识别要预测的画布信息, 请重试"
        if predict_strategy_info.strategy_id:
            result = await self._predict_canvas(predict_strategy_info.strategy_id)
        elif predict_strategy_info.strategy_result:
            result = await self._predict_canvas_design(predict_strategy_info.strategy_result)
        elif predict_strategy_info.strategy_idea:
            result = await self._predict_canvas_idea(predict_strategy_info.strategy_idea)

        await self.send_message(message_type="MARKDOWN", content=result)
        await self.send_status(status='SUCCESS', status_desc='预测完成', keeping=True)
        return Conversation(role=Role.ASSISTANT, content=result)

    async def _predict_canvas_idea(self, strategy_idea: str) -> str:
        # CASE: 用户描述思路, 调用策略设计生成画布设计信息
        canvas_idea_check_prompt_template = await get_canvas_idea_check_prompt()
        canvas_idea_check_prompt = canvas_idea_check_prompt_template.render()
        check_result, retry_times = await pydantic_chat_with_retry(
            llm=self.llm,
            output_format=CanvasIdeaCheckResult,
            with_sys_prompt_messages=[SystemMessage(
                content=canvas_idea_check_prompt),
                HumanMessage(content=strategy_idea)]
        )
        if check_result.strategy_idea_check:
            return await self._predict_by_similar_canvas(strategy_idea)
        else:
            return f"缺少关键信息, 无法预测效果, {check_result.inquiry_words}"

    async def _predict_canvas_design(self, canvas_design: dict) -> str:
        # CASE: 有明确的画布设计信息
        return await self._predict_by_similar_canvas(json.dumps(canvas_design, ensure_ascii=False))

    async def _insight(self, canvas_info) -> str:
        agent = AssistantAgent(llm=self.llm)
        return await agent.achat_and_save(prompt=f"请使用自然语言描述以下画布的结构及配置信息：\n\n{canvas_info}")

    async def _predict_canvas(self, canvas_id) -> str:
        canvas_info = {}
        try:
            canvas_info = StrategyInfo(
                host=self.chat_session_info['cdp_host'],
                token=self.chat_session_info['api_key'],
                organization_id=self.chat_session_info['organization_id'],
                project_name=self.chat_session_info['project_name'],
                api_key=self.chat_session_info['api_key']
            ).get_info(canvas_id)
        except Exception as e:
            log.warn(f"get canvas info failed, treat as not found. [canvas_id='{canvas_id}']]", exc_info=e)
        if not canvas_info.get('canvas_info'):
            return f"未找到ID为 {canvas_id} 的计划/画布"
        # TODO 获取画布相关元数据(包括事件、属性、通道)翻译画布内容
        #  目前由于 OpenAPI 拿不到相关数据, 所以跳过翻译的步骤, 直接使用原始画布数据匹配
        return await self._predict_by_similar_canvas(json.dumps(canvas_info, ensure_ascii=False))

    async def _predict_by_similar_canvas(self, canvas_info: str) -> str:
        log.info("查询租户/项目信息")
        tenant_info = await self.get_tenant_project_info(chat_session_info=self.chat_session_info)
        if not tenant_info:
            log.error('获取项目信息失败，请先配置项目信息！')
            raise Exception('获取项目信息失败，请先配置项目信息！')

        agent = await KnowledgeAgent.customer_knowledge_agent(
            llm=self.llm,
            tenant_info=tenant_info,
            max_talk_round=1,
            retrieve_documents_only=True,
            custom_knowledge=self.custom_knowledge_id_and_desc
        )
        customer_rag_client = agent.retrieve_client

        canvas_description = await self._insight(canvas_info)
        similar_canvas_content = await customer_rag_client.simple_search(history=[
            Conversation(
                role=Role.USER,
                content=f"阅读下面画布配置描述：\n{canvas_description}\n\n仔细阅读上面的画布配置描述，并找出相似配置的画布，你需要给出画布的详细信息、指标配置等信息."
            )
        ])
        similar_canvas_content = similar_canvas_content.content
        if not similar_canvas_content:
            similar_canvas_content = "未找到类似目标受众的画布"

        predict_audience_prompt_template = await get_canvas_predict_audience_prompt()
        predict_audience_prompt = predict_audience_prompt_template.render(
            canvas_design=canvas_info,
            similar_canvas_content=similar_canvas_content
        )
        predict_convert_prompt_template = await get_canvas_predict_convert_prompt()
        predict_convert_prompt = predict_convert_prompt_template.render(
            canvas_design=canvas_info,
            similar_canvas_content=similar_canvas_content
        )
        predict_audience_task = asyncio.create_task(
            self.llm.acomplete(input=SystemMessage(content=predict_audience_prompt)))
        predict_convert_task = asyncio.create_task(
            self.llm.acomplete(input=SystemMessage(content=predict_convert_prompt)))
        predict_audience_result = await predict_audience_task
        log.info(f"finished to predict audience. [result='{predict_audience_result}']")
        predict_convert_result = await predict_convert_task
        log.info(f"finished to predict convert. [result='{predict_convert_result}']")
        predict_reporter_prompt_template = await get_canvas_predict_reporter_prompt()
        predict_reporter_prompt = predict_reporter_prompt_template.render()
        reporter_messages = [SystemMessage(content=predict_reporter_prompt),
                             HumanMessage(content=f"请为以下策略进行效果预测: {canvas_info}"),
                             AIMessage(content=f"以下是相似配置的画布: {similar_canvas_content}"),
                             predict_audience_result,
                             predict_convert_result]
        result = await self.llm.acomplete(input=reporter_messages)
        log.info(f"finished to predict canvas. [result='{result}']")
        return result.content

    async def _parse_predict_target(self, history: list[Conversation]) -> PredictStrategyInfo:
        """
        解析预测目标
        :param history: 聊天记录
        :return: str 表示预测目标
        """
        # 识别预测的画布信息, 拿到明确的画布id 或 画布设计信息
        if len(history) > 10:
            history = history[-10:]

        predict_content_parse_prompt_template = await get_canvas_predict_content_parse_prompt()
        messages = [{'role': 'system', 'content': predict_content_parse_prompt_template.render()}]
        for message in history:
            if message.role == Role.ASSISTANT:
                messages.append({'role': 'assistant', 'content': message.content})
            else:
                messages.append({'role': 'user', 'content': message.content})
        log.info(f'parse canvas design for predict. [history={history}]')
        result, retry_times = await pydantic_chat_with_retry(llm=self.llm,
                                                             output_format=PredictStrategyInfo,
                                                             with_sys_prompt_messages=messages,
                                                             retry_times=3)
        log.info(f'finished to parse canvas design. [result={result.model_dump_json()}, retry_times={retry_times}]')
        return result
