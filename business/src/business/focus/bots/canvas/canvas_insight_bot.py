import json

from jinja2 import Template

from framework.prompt import PromptFactory
from common import tools as my_tools
from common.tools import extract_code_from_text

from framework.agents.assistant import AssistantAgent
from framework.agents.schema import Conversation, Role
from .basic import BasicCanvasBot
from ...core.canvas_insight.insight_client import StrategyInsightClient

log = my_tools.get_logger()

SYSTEM_PROMPT = """阅读用户给出的对话内容，从中提取出需要分析的策略(画布) id 来回复用户。
注意：策略 id 是 int 数字类型，用户给出的对话内容可能包含多个当前需要分析的策略 id，如果有多个，则需要全部提取出来。
如果当前没有需要分析的策略 id，则你需要给出提示，让用户提供策略id开始洞察。

**回复格式要求**

你需要将需要分析的策略 id 放在一个 markdown 代码块中，如下所示：
```json
[1, 2, 3, 4]
```
你的回复中最多只允许有一个 markdown 代码块，如果没有策略 id，则你不需要回复 markdown 代码块而是直接给出输入策略 id 的提示。
"""

async def get_canvas_insight_system_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="CANVAS_INSIGHT_SYSTEM_PROMPT",
        prompt_name="画布策略洞察机器人 - 系统提示词",
        prompt_desc="画布策略洞察机器人 - 系统提示词",
        prompt_content=SYSTEM_PROMPT,
    )
    return template

DEFAULT_USER_PROMPT = """对话内容是下面 <context></context> 中的部分：
<context>
{{contents}}
</context>

请你直接回复 id 信息：
"""

async def get_canvas_insight_user_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="CANVAS_INSIGHT_USER_PROMPT",
        prompt_name="画布策略洞察机器人 - 用户提示词",
        prompt_desc="画布策略洞察机器人 - 用户提示词",
        prompt_content=DEFAULT_USER_PROMPT,
    )
    return template

class CanvasInsightBot(BasicCanvasBot):
    def __init__(
            self,
            stream: bool = False,
            **kwargs,
    ):
        super().__init__(**kwargs)
        self.stream = stream
        self.insight_client = StrategyInsightClient(
            regenerate=True,
            llm=self.llm,
            # callback=self.send_status,
            **kwargs,
        )
        log.info("init insight bot success")

    async def _identify_ids(self, history: list[Conversation]) -> list[int] | str:
        system_prompt_template = await get_canvas_insight_system_prompt()
        agent = AssistantAgent(llm=self.llm, system_prompt=system_prompt_template.render())
        contents = []
        for h in history:
            contents.append(f"{h.role.name}: {h.content}")
        default_user_prompt_template = await get_canvas_insight_user_prompt()
        user_prompt = default_user_prompt_template.render(contents='\n'.join(contents))
        response = await agent.achat_and_save(prompt=user_prompt)
        code = extract_code_from_text(response)
        if not code:
            return response
        try:
            return json.loads(code)
        except Exception as e:
            log.error(f'parse code error: {e}')
            return "无法识别策略id，请重新给出~"

    async def async_chat(self, history: list[Conversation]) -> Conversation:
        await self.send_status(status='PROCESSING', status_desc='正在识别策略 id ...', keeping=False)
        strategy_id_list = await self._identify_ids(history)
        if isinstance(strategy_id_list, str):
            await self.send_status(status='ERROR', status_desc='策略 id 识别失败', keeping=True)
            return Conversation(role=Role.ASSISTANT, content=strategy_id_list)

        log.info(f'insight canvas ids: {strategy_id_list}')

        if not strategy_id_list:
            result = '没有找到有效的策略 id，请重新输入！'
        else:
            await self.send_status(status='PROCESSING', status_desc='正在解读数据...', keeping=False)
            result = await self.insight_client.insight(
                host=self.chat_session_info['cdp_host'],
                token=self.chat_session_info['api_key'],
                organization=self.chat_session_info['organization_id'],
                project=self.chat_session_info['project_name'],
                strategy_ids=strategy_id_list,
            )
            log.info(f'insight answer: {len(result)}')
            await self.send_status(status='SUCCESS', status_desc='数据解读完成', keeping=True)

        await self.send_message(message_type="MARKDOWN", content=result)
        return Conversation(role=Role.ASSISTANT, content=result)
