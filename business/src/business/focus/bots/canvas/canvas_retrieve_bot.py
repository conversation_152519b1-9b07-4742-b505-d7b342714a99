from business.scenario.agents.knowledge_agent import KnowledgeAgent
from common import tools as my_tools
from framework.agents.schema import Conversation, Role
from .basic import BasicCanvasBot

log = my_tools.get_logger()


class CanvasRetrieveBot(BasicCanvasBot):
    def __init__(
            self,
            query_limit=5,
            max_talk_round=2,
            score_limit=0.3,
            **kwargs,
    ):
        super().__init__(**kwargs)
        self.query_limit = query_limit
        self.max_talk_round = max_talk_round
        self.score_limit = score_limit

        if 'custom_knowledge' not in kwargs:
            kwargs['custom_knowledge'] = self.custom_knowledge_id_and_desc

        self.retrieve_client = None
        log.info("init retrieve bot success")

    async def async_chat(self, history: list[Conversation]) -> Conversation:
        # 保留最近10条对话历史
        if len(history) > 10:
            history = history[-10:]

        # 转换对话格式
        messages = []
        for message in history:
            if message.role == Role.ASSISTANT:
                messages.append({'role': 'assistant', 'content': message.content})
            else:
                messages.append({'role': 'user', 'content': message.content})
        log.info(f'history: {history}')

        await self._init_retrieval_client()

        # 调用知识库检索
        await self.send_status(status='PROCESSING', status_desc='正在检索中...', keeping=False)
        try:
            result = await self.retrieve_client.simple_search(history=history)
            log.info(f'retrieve answer: {result}')
            await self.send_status(status='SUCCESS', status_desc='检索完成', keeping=True)
            await self.send_message(message_type="MARKDOWN", content=result.content)
        except Exception as e:
            log.error(f'retrieve error: {e}')
            result = f'检索失败：{e}'
            await self.send_status(status='ERROR', status_desc='检索失败', keeping=False)
        return Conversation(role=Role.ASSISTANT, content=result)

    async def _init_retrieval_client(self):
        if self.retrieve_client is not None:
            return

        log.info("查询租户/项目信息")
        tenant_info = await self.get_tenant_project_info(chat_session_info=self.chat_session_info)
        if not tenant_info:
            log.error('获取项目信息失败，请先配置项目信息！')
            raise Exception('获取项目信息失败，请先配置项目信息！')

        agent = await KnowledgeAgent.customer_knowledge_agent(
            llm=self.llm,
            tenant_info=tenant_info,
            query_limit=self.query_limit,
            max_talk_round=self.max_talk_round,
            score_limit=self.score_limit,
            **self.kwargs
        )

        self.retrieve_client = agent.retrieve_client
