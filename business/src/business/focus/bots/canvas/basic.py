from typing import Dict
from typing import Callable, Any

from core.service.tenant_project_service import TenantProjectService
from framework.models import BaseLLM, ModelsFactory
from framework.bot import Bo<PERSON>
from apis.definition.knowledge_base_pb2 import KnowledgeBaseInfo


class BasicCanvasBot(Bot):
    def __init__(
            self,
            name: str,
            chat_session_info: dict | None = None,
            model_client: BaseLLM = None,
            knowledge_base_infos: dict | None = None,
            callback: Callable[[Any], Any] = None,
            **kwargs,
    ):
        super().__init__(name=name, callback=callback, **kwargs)
        self.chat_session_info = chat_session_info
        if not model_client:
            self.llm = ModelsFactory.get_llm(scenario='default')
        else:
            self.llm = model_client

        self.knowledge_base_infos = knowledge_base_infos

    @property
    def custom_knowledge_id_and_desc(self) -> Dict[int | str, str] | None:
        if not self.knowledge_base_infos:
            return None
        result = {}
        for k_info in self.knowledge_base_infos:
            description = f"{k_info.get('knowledge_base_name', '-')}: {k_info.get('remark', '-')}，使用场景："
            scenarios = k_info.get('scenario', KnowledgeBaseInfo.KnowledgeBaseScenario.values())
            for scenario in scenarios:
                if str(scenario) == 'SUPPLY_INFO':
                    description += '补充基本信息参考; '
                elif str(scenario) == 'STRATEGY_DESIGN_SUGGESTION':
                    description += '策略设计和推荐参考; '
                elif str(scenario) == 'STRATEGY_INSIGHT':
                    description += '策略解读参考; '
            result[k_info.get('knowledge_base_id')] = description
        return result

    async def get_tenant_project_info(self, chat_session_info: dict | None) -> dict | None:
        if not chat_session_info:
            raise Exception('chat_session_info is required')
        service = TenantProjectService()
        tenant_project_info = await service.get_tenant_project(
            organization_id=chat_session_info['organization_id'],
            project_id=chat_session_info['project_id']
        )
        if not tenant_project_info:
            return None
        return tenant_project_info.dict()
