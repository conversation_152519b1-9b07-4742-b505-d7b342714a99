from langchain_core.messages import BaseMessage

from business.scenario.agents.knowledge_agent import KnowledgeAgent
from common import tools as my_tools

from business.focus.core.canvas_creator.agents.canvas_draft_agent import CanvasDraftAgent
from business.focus.core.canvas_creator.agents.canvas_draft_critic_agent import CanvasDraftCriticAgent
from business.focus.core.canvas_creator.translator import translate_canvas_draft
from business.focus.core.canvas_creator.prompts.canvas_create_react_prompts import CANVAS_CREATE_REACT_MANAGER_PROMPT, \
    get_canvas_create_react_manager_prompt
from framework.agents.autogen import ReactManagerAgent, AutoGenWorkflow
from google.protobuf import json_format
from apis.definition.canvas_draft_pb2 import CanvasDraft
from .basic import BasicCanvasBot
from framework.agents.schema import Conversation, Role

MANAGER_NAME = "manager"

log = my_tools.get_logger()


class CanvasCreateBot(BasicCanvasBot):
    def __init__(
            self,
            stream: bool = False,
            **kwargs,
    ):
        super().__init__(**kwargs)

        # 直接获取，让 llm 少回复一些，加快速度
        self.canvas_draft_record = []
        self.stream = stream
        self.workflow = None
        log.info("init create bot success")

    async def async_chat(self, history: list[Conversation]) -> Conversation:
        try:
            await self._init_multi_agent()
            return await self.async_chat0(history)
        except Exception as e:
            log.error(f'translate canvas draft error', e)
            await self.send_status(status='ERROR', status_desc='画布生成错误', keeping=True)
            return Conversation(role=Role.ASSISTANT, content=f'画布生成错误！')

    async def async_chat0(self, history: list[Conversation]) -> Conversation:
        if len(history) > 10:
            history = history[-10:]

        messages = []
        for message in history:
            if message.role == Role.ASSISTANT:
                messages.append({'role': 'assistant', 'content': message.content})
            else:
                messages.append({'role': 'user', 'content': message.content})
        log.info(f'history: {history}')

        # 先生成总体草稿
        await self.send_status(status='PROCESSING', status_desc='正在生成草稿...', keeping=False)
        generate_response = await self.workflow.run_with_history(history=messages)
        await self.send_status(status='SUCCESS', status_desc='草稿生成完成', keeping=True)

        if isinstance(generate_response, BaseMessage):
            generate_response = generate_response.content.strip()

        if isinstance(generate_response, str) and generate_response.endswith('TERMINATE'):
            generate_response = generate_response[:-len('TERMINATE')]

        result = None
        if self.canvas_draft_record:
            result = self.canvas_draft_record[-1]
        else:
            return Conversation(role=Role.ASSISTANT, content=generate_response)

        log.info(f"canvas_draft: {result}")

        # 逐个组件翻译
        await self.send_status(status='PROCESSING', status_desc='正在翻译画布', keeping=False)
        canvas = my_tools.extract_code_from_text(result)

        canvas = await translate_canvas_draft(llm=self.llm, canvas_draft=canvas)
        canvas = json_format.Parse(
            text=canvas,
            message=CanvasDraft(),
            ignore_unknown_fields=True,
        )
        log.info(f'canvas_detail: \n{canvas}\n\n')

        # 输出最终草稿
        await self.send_message(
            message_type="CANVAS",
            content=json_format.MessageToJson(canvas, preserving_proto_field_name=True, ensure_ascii=False),
        )
        await self.send_status(status='SUCCESS', status_desc='画布生成完成', keeping=True)
        return Conversation(role=Role.ASSISTANT, content=canvas)

    async def _init_multi_agent(self):
        if self.workflow is not None:
            return

        log.info("查询租户/项目信息")
        tenant_info = await self.get_tenant_project_info(chat_session_info=self.chat_session_info)
        if not tenant_info:
            log.error('获取项目信息失败，请先配置项目信息！')
            raise Exception('获取项目信息失败，请先配置项目信息！')

        kwargs = self.kwargs.copy()
        kwargs = kwargs | {'custom_knowledge': self.custom_knowledge_id_and_desc}

        agents = [
            CanvasDraftAgent(canvas_list=self.canvas_draft_record),
            CanvasDraftCriticAgent(),
        ]

        customer_knowledge_agent = await KnowledgeAgent.customer_knowledge_agent(
            llm=self.llm,
            tenant_info=tenant_info,
            name="customer_metadata_retrieval",
            description="用于检索客户可使用的用户属性、事件、事件属性、标签、分群等信息，在创建画布草稿需要用到的时候，请给明确的检索要求",
            **kwargs
        )
        if customer_knowledge_agent:
            agents.append(customer_knowledge_agent)

        manager = ReactManagerAgent(
            name=MANAGER_NAME,
            llm=self.llm,
            system_prompt=await get_canvas_create_react_manager_prompt(),
        )

        self.workflow = AutoGenWorkflow(
            manager_agent=manager,
            participant_agents=agents,
            callback=self.callback,
            max_talk_round=10,
            num_concurrent_runs=1,
            think_progress=False,
            stream=self.stream
        )
