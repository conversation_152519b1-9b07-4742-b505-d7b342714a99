from business.scenario.agents.knowledge_agent import KnowledgeAgent
from common import tools as my_tools

from framework.agents.autogen import ReactManagerAgent, AutoGenWorkflow
from .basic import BasicCanvasBot
from framework.agents.schema import Conversation, Role
from ...core.canvas_designer.agents.canvas_design_critic_agent import CanvasDesignCriticAgent
from ...core.canvas_designer.agents.canvas_requirement_check_agent import RequirementCheckAgent
from ...core.canvas_designer.agents.canvas_structure_generate_agent import CanvasStructureGenerateAgent
from ...core.canvas_designer.prompts.canvas_design_react_prompts import CANVAS_DESIGN_REACT_MANAGER_PROMPT
from ...core.canvas_designer.agents.common_knowledge_retriever import CommonKnowledgeRetrievalAgent
from ...core.canvas_designer.agents.custom_knowledge_retriever import CustomerKnowledgeRetrievalAgent
from ...core.canvas_designer.agents.industroy_knowledge_retriever import IndustryKnowledgeRetrievalAgent
from ...core.canvas_designer.prompts.canvas_design_react_prompts import CANVAS_DESIGN_REACT_MANAGER_PROMPT, \
    get_canvas_design_react_manager_prompt

log = my_tools.get_logger()
MANAGER_NAME = "manager"


class CanvasDesignBot(BasicCanvasBot):
    def __init__(
            self,
            stream: bool = True,
            industry: str = 'finance_banking',
            **kwargs,
    ):
        super().__init__(**kwargs)
        self.industry = industry
        self.stream = stream
        self.design_agent = None
        log.info("init design bot success")

    async def async_chat(self, history: list[Conversation]) -> Conversation:
        if len(history) > 10:
            history = history[-10:]

        messages = []
        for message in history:
            if message.role == Role.ASSISTANT:
                messages.append({'role': 'assistant', 'content': message.content})
            else:
                messages.append({'role': 'user', 'content': message.content})
        log.info(f'history: {history}')

        await self.send_status(status='PROCESSING', status_desc='设计中...', keeping=False)
        result = await self.workflow.run_with_history(history=messages)
        log.info(f'final answer: {result}')
        await self.send_status(status='SUCCESS', status_desc='完成', keeping=True)

        if self.design_agent.latest_design is not None:
            log.info(f'design: {self.design_agent.latest_design}')
            await self.send_message(message_type="CANVAS_DESIGN", content=self.design_agent.latest_design)

        return Conversation(role=Role.ASSISTANT, content=result.content)

    async def _init_multi_agent(self):
        if self.design_agent is not None:
            return

        log.info("查询租户/项目信息")
        tenant_info = await self.get_tenant_project_info(chat_session_info=self.chat_session_info)
        if not tenant_info:
            log.error('获取项目信息失败，请先配置项目信息！')
            raise Exception('获取项目信息失败，请先配置项目信息！')

        self.design_agent = CanvasStructureGenerateAgent()
        kwargs = self.kwargs.copy()
        kwargs = kwargs | {'custom_knowledge': self.custom_knowledge_id_and_desc}

        agents = []
        common_knowledge_agent = await KnowledgeAgent.common_knowledge_agent(
            llm=self.llm,
            name="common_retriever",
            description="通用知识库检索，负责检索一些通用的营销知识等内容，为流程画布的创建等提供理论基础支撑",
            **kwargs
        )
        if common_knowledge_agent is not None:
            agents.append(common_knowledge_agent)

        industry_knowledge_agent = await KnowledgeAgent.industry_knowledge_agent(
            llm=self.llm,
            industry=self.industry,
            name="industry_retriever",
            description="行业级别知识库检索角色，负责检索行业的通用做法、法律法规、行业不同场景的画布模板等行业相关的信息，为流程画布的创建等提供样例参考等"
        )
        if industry_knowledge_agent is not None:
            agents.append(industry_knowledge_agent)

        customer_description = "客户级别知识库检索角色，负责检索客户环境上面的 1）事件、事件属性、用户属性、历史画布策略；"
        custom_knowledge_id_and_desc = self.custom_knowledge_id_and_desc
        if custom_knowledge_id_and_desc:
            customer_description += f"2）{';'.join(custom_knowledge_id_and_desc.values())}"
        customer_knowledge_agent = await KnowledgeAgent.customer_knowledge_agent(
            llm=self.llm,
            tenant_info=tenant_info,
            name="custom_retriever",
            description=customer_description,
            **kwargs
        )
        if customer_knowledge_agent is not None:
            agents.append(customer_knowledge_agent)

        agents.extend([
            RequirementCheckAgent(),
            self.design_agent,
            CanvasDesignCriticAgent(),
        ])

        manager = ReactManagerAgent(
            name=MANAGER_NAME,
            llm=self.llm,
            system_prompt=await get_canvas_design_react_manager_prompt()
        )

        self.workflow = AutoGenWorkflow(
            manager_agent=manager,
            participant_agents=agents,
            callback=self.callback,
            max_talk_round=10,
            num_concurrent_runs=1,
            stream=self.stream,
            think_progress=True,
        )
