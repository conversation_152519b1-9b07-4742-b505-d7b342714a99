from jinja2 import Template

from framework.prompt import PromptFactory
from common import tools as my_tools
from framework.agents.assistant import AssistantAgent
from framework.agents.schema import Conversation, Role
from .canvas_retrieve_bot import CanvasRetrieveBot
from .canvas_predict_bot import CanvasPredictBot
from .canvas_optimize_bot import CanvasOptimizeBot
from .canvas_insight_bot import CanvasInsightBot
from .canvas_design_bot import CanvasDesignBot
from .canvas_create_bot import CanvasCreateBot
from .basic import BasicCanvasBot

log = my_tools.get_logger()

bots = [
    {
        "name": "canvas_retrieval_bot",
        "type": CanvasRetrieveBot,
        "description": ("策略检索机器人：专注于从历史策略库中检索符合用户需求的营销策略。"
                        "可根据用户提供的策略目标（如用户召回、提升活跃度）、受众特征（如年龄、消费习惯）、"
                        "触达渠道（如短信、APP 推送）、执行效果（如目标完成率）等其他关键特征，快速匹配并输出历史优质策略，"
                        "为用户提供策略参考和灵感启发，帮助用户借鉴过往成功经验。")
    },
    {
        "name": "canvas_insight_bot",
        "type": CanvasInsightBot,
        "description": ("数据解读机器人：专注于对营销策略执行过程中产生的数据进行深度分析和解读。"
                        "能够剖析数据趋势、挖掘数据背后的原因（如转化率波动因素、用户流失关键节点）、"
                        "识别数据异常情况，并将复杂的数据信息转化为通俗易懂的结论和建议，帮助用户理解策略效果，发现问题并找到优化方向。")
    },
    {
        "name": "canvas_design_bot",
        "type": CanvasDesignBot,
        "description": ("画布策略资讯和设计机器人：致力于为用户构思并制定营销策略框架，并提供一些画布思路的咨询服务。通过与用户深度交互，全面收集运营场景信息，"
                        "包括目标受众画像、运营目标（如提升转化率、扩大品牌影响力）、触达渠道组合、内容创意方向、时间规划等需求，"
                        "综合行业经验，生成一套完整的文字版营销策略方案，但不涉及具体流程画布的创建")
    },
    {
        "name": "canvas_create_bot",
        "type": CanvasCreateBot,
        "description": ("画布创建机器人：基于用户已有的营销策略方案（可由策略设计机器人 canvas_design_bot 输出或用户自行提供），"
                        "将文字版策略转化为可视化的流程画布配置。"
                        "通过解析策略中的受众筛选条件、触达节点、执行逻辑、分支规则等内容，自动生成可直接在系统中部署的流程画布，"
                        "帮助用户快速将策略落地到实际运营工具中，提升策略执行效率。"
                        "需要注意：用户必须明确说明开始创建画布或者创建策略（注意是策略创建或者画布创建，而不是策略设计或者画布设计），"
                        "才需要 canvas_create_bot 机器人来执行。")
    },
    {
        "name": "canvas_predict_bot",
        "type": CanvasPredictBot,
        "description": ("效果预测机器人：主要负责对用户已制定的营销策略进行效果预估。"
                        "基于用户提供的策略 ID、策略详细配置信息（如受众筛选条件、触达内容、推送时间）或策略文字描述，"
                        "结合历史数据，模拟策略在实际运营中的执行情况，预测策略的预期效果（如触达情况、目标完成情况等），"
                        "帮助用户提前评估策略可行性，为决策提供数据支撑。")
    },
    {
        "name": "canvas_optimize_bot",
        "type": CanvasOptimizeBot,
        "description": ("策略优化机器人：以提升现有营销策略效果为核心目标。"
                        "根据用户对现有策略的描述和优化需求（如提高用户参与度、降低营销成本），"
                        "或直接依据策略 ID 获取策略信息，从受众定位、内容设计、渠道选择、时间安排等多个维度进行分析诊断，"
                        "提出针对性的优化建议和调整方案，助力用户改进策略，实现更好的运营效果。")
    }
]

DEFAULT_PROMPT = """你是智能运营的意图识别总 Agent，负责精准理解用户需求，调用合适的 Bot 来执行任务。
用户输入可能围绕策略相关的各类需求，你需依据以下规则分析并在以下 Bot 中选择一个 Bot 来回复用户。
你最好能够直接判断用户意图，如果实在无法判断可以进行必要的询问和引导，最终选择选择合适的机器人。
```
{{bots}}
```

**你可能需要以下知识来辅助你选择机器人**
神策数据是一家专注于 CDP + MA 业务的企业，其为企业进行数据接入、建模以及对企业的用户进行二次营销。

神策数据在给企业进行数据建模的时候，遵循一定的建模模式，将企业的用户数据分为用户表 users 和事件表 events.
在神策分析中，我们使用事件模型（Event 模型）来描述用户在产品上的各种行为，这也是神策分析所有的接口和功能设计的核心依据。
简单来说，事件模型包括事件（Event）和用户（User）两个核心概念，在神策分析中，分别提供了接口供使用者上传和修改这两类相应的数据，在使用产品的各个功能时，这两类数据也可以分别或者贯通起来参与具体的分析和查询。对这两个概念，我们会在后文做具体的描述。
其中，User 存储在 users 表中，表示用户的属性，Event 存储在 events 表中，表示用户的行为记录。
比如：用户的年龄、性别等，存储在 users 表中，而用户的点击、浏览等存储在 events 表中。
用户的 Event 包括 5 个要素，分别为：
* Who：即参与这个事件的用户是谁。使用 distinct_id 来设置用户的唯一 ID
* When：即这个事件发生的实际时间。使用 time 字段来记录精确到毫秒的事件发生时间。
* Where：即事件发生的地点。使用者可以设置 properties 中的 $ip 属性，这样系统会自动根据 ip 来解析相应的省份和城市
* How：即用户从事这个事件的方式。包括用户使用的设备、使用的浏览器、使用的 App 版本等。
* What：描述用户所做的这个事件的具体内容。对于一个“购买”类型的事件，则可能需要记录的字段有：商品名称、商品类型、购买数量、购买金额、 付款方式等。

流程画布可以用来规划编排个性化的用户旅程，并自动化的执行各个阶段的营销动作。相比于多个独立的点阵式的营销活动的触达，流程画布保证了用户旅程流转的顺畅，以及用户体验的一致性。能够在合适的时间、给合适的人、用合适的触达通道、以合适的频次发送合适的内容，并回收每个营销阶段的执行数据用以衡量活动效果，打造运营闭环。
流程画布通过组合组件来编排用户旅程，实现对目标受众的圈选、流转的控制、营销内容的触达。同时支持设置全局的触达频控、勿扰设置保障用户体验，活动级别的目标设置追踪活动效果。
当一个用户进入了流程画布，用户就会在流程画布内根据各个组件配置等进行流转，比如配置了延迟器，用户就会在延迟器上等待一段时间才能向下流转等，用户需要满足组件配置的条件才会继续从该组件向下流转，否则用户会停在组件上直到满足条件为止。
流程画布是由流程画布基本信息（canvas_info）、组件信息（components）、组件之间的关联关系（component_relations）、画布监控指标（canvas_metrics）组成，用户从进入组件开始，在组件之间流转。流程画布最终是一个由组件构成的有向无环图。

**回复要求**
你需要直接回复机器人名称，不允许回复其他没内容！
"""

async def get_canvas_dispatch_default_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="CANVAS_DISPATCH_DEFAULT_PROMPT",
        prompt_name="画布策略总调度机器人 - 默认提示词",
        prompt_desc="画布策略总调度机器人 - 默认提示词",
        prompt_content=DEFAULT_PROMPT,
    )
    return template

DEFAULT_USER_PROMPT = """对话内容是下面 <context></context> 中的部分：
<context>
{{contents}}
</context>

请你直接回复 Bot 名称：
"""

async def get_canvas_dispatch_user_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="CANVAS_DISPATCH_USER_PROMPT",
        prompt_name="画布策略总调度机器人 - 用户提示词",
        prompt_desc="画布策略总调度机器人 - 用户提示词",
        prompt_content=DEFAULT_USER_PROMPT,
    )
    return template

class CanvasDispatchBot(BasicCanvasBot):
    def __init__(
            self,
            **kwargs,
    ):
        super().__init__(**kwargs)
        log.info("init dispatch bot success")

    async def _select_bot(self, history: list[Conversation]) -> dict | None:
        bot_infos = '\n'.join([f"{b['name']}: {b['description']}" for b in bots])
        system_prompt_template = await get_canvas_dispatch_default_prompt()
        system_prompt = system_prompt_template.render(bots=bot_infos)
        agent = AssistantAgent(llm=self.llm, system_prompt=system_prompt)

        contents = '\n'.join([f"{h.role.name}: {h.content}" for h in history])
        user_prompt_template = await get_canvas_dispatch_user_prompt()
        user_prompt = user_prompt_template.render(contents=contents)
        response = await agent.achat_and_save(prompt=user_prompt)
        bot_name = response.strip()
        for bot in bots:
            if bot['name'] == bot_name:
                return bot
        return None

    async def async_chat(self, history: list[Conversation]) -> Conversation:
        selected_bot = await self._select_bot(history)
        log.info(f"selected bot: {selected_bot}")

        if not selected_bot:
            await self.send_status(status='ERROR', status_desc='找不到对应的 Bot', keeping=True)
            return Conversation(role=Role.ASSISTANT, content='找不到对应的 Bot')

        bot = selected_bot['type'](
            name=self.name,
            chat_session_info=self.chat_session_info,
            callback=self.callback,
            custom_knowledge=self.custom_knowledge_id_and_desc,
            knowledge_base_infos=self.knowledge_base_infos,
            **self.kwargs
        )
        try:
            return await bot.async_chat(history=history)
        except Exception as e:
            await self.send_status(status='ERROR', status_desc=f'Chat Error: {e}', keeping=True)
            log.error(f"bot {bot.name} error.", e)
