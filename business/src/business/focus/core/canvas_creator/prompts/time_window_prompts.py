TIME_WINDOW_EXAMPLE = """{ // 该示例结构表示 15 分钟
  "value": 15, // 时间值
  "unit": "MINUTE" // 时间单位，可取值包括 MINUTE | HOUR | DAY
}"""

TIME_WINDOW_PROMPT = f"""## 目标及要求
***
我需要你根据给定的时间窗口描述信息，回复时间窗口 json 结构。

一个合法的时间窗口 json 结构示例及注释：
```json
{TIME_WINDOW_EXAMPLE}
```
注意：上述 json 中的注释仅供你理解数据结构字段含义使用，你的回复中不能带注释信息！

## 给定的时间窗口描述
***
__TIME_WINDOW_DESC__

注意，你回复的时间窗口结构必须与给定的描述语义完全一样！
你只能时间窗口结构，且你需要将 json 包含在 markdown 的代码块中。禁止回复除了时间窗口结构以外的其他任何内容！"""
