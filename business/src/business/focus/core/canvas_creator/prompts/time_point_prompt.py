TIME_POINT_PROMPT = """## 目标及要求
请你根据对时间点的描述信息，返回一个 TimePoint 的 json 结构。
你需要将 json 包含在 markdown 的代码块中。

一个合法的 TimePoint 结构示例及注释：
```json
{
    "type": "STATIC", // TimePoint 类型，取值包括：STATIC | RELATIVE | EXPRESSION，分别表示静态绝对时间、相对于某个事件的偏移时间、cron 表达式时间
    "static_time": "2025-02-03T12:00:00" // 静态时间的值，注意格式为 %Y-%m-%dT%H:%M:%S
}
```
一个 RELATIVE 类型的 TimePoint 结构示例及注释：
```json
{
    "type": "RELATIVE",
    "trunc_unit": "TRUNC_HOUR", // 对时间点进行取整，可取值范围是 TRUNC_UNIT_UNSPECIFIED | TRUNC_HOUR | TRUNC_DAY | TRUNC_MONTH | TRUNC_MONTH | TRUNC_YEAR，不给这个字段表示不做 trunc
    "relative_time": { // 示例中表示相对用户进入画布的时间，偏移 5 分钟
        "base_time": "$ENTRY_TIME", // 表示相对的时间点，$ENTRY_TIME 表示相对用户的进入时间
        "time_interval": {
            "size": 5, // unit 的数量
            "unit": "MINUTE"  // 可取值包括 MINUTE | HOUR | DAY，此处表示 5 分钟
        }
    }
}
```
一个 EXPRESSION 类型的 TimePoint 结构示例及注释：
```json
{
    "type": "EXPRESSION",
    "expression": "0 0 9 * * ?"
}
```

注意：上述 json 中的注释仅供你理解数据结构字段含义使用，你的回复中不能带注释信息！

请你根据要求等，给出符合要求的 json 定义。
"""

TIME_POINT_USER_PROMPT = """## 当前需要回复的 TimePoint 描述
***
__INPUT__


你回复的 json 结构：
"""
