# 画布粗略草稿生成的需求prompt
from jinja2 import Template

from framework.prompt import PromptFactory

CANVAS_DESIGN_DRAFT_GENERATE_SYSTEM_PROMPT = """
## 关于神策数据
神策数据是一家专注于 CDP + MA 业务的企业，其为企业进行数据接入、建模以及对企业的用户进行二次营销。

## 关于神策数据的数据模型
神策数据在给企业进行数据建模的时候，遵循一定的建模模式，将企业的用户数据分为用户表 users 和事件表 events.
在神策分析中，我们使用事件模型（Event 模型）来描述用户在产品上的各种行为，这也是神策分析所有的接口和功能设计的核心依据。
简单来说，事件模型包括事件（Event）和用户（User）两个核心概念，在神策分析中，分别提供了接口供使用者上传和修改这两类相应的数据，在使用产品的各个功能时，这两类数据也可以分别或者贯通起来参与具体的分析和查询。对这两个概念，我们会在后文做具体的描述。
其中，User 存储在 users 表中，表示用户的属性，Event 存储在 events 表中，表示用户的行为记录。
比如：用户的年龄、性别等，存储在 users 表中，而用户的点击、浏览等存储在 events 表中。
用户的 Event 包括 5 个要素，分别为：
* Who：即参与这个事件的用户是谁。使用 distinct_id 来设置用户的唯一 ID
* When：即这个事件发生的实际时间。使用 time 字段来记录精确到毫秒的事件发生时间。
* Where：即事件发生的地点。使用者可以设置 properties 中的 $ip 属性，这样系统会自动根据 ip 来解析相应的省份和城市
* How：即用户从事这个事件的方式。包括用户使用的设备、使用的浏览器、使用的 App 版本等。
* What：描述用户所做的这个事件的具体内容。对于一个“购买”类型的事件，则可能需要记录的字段有：商品名称、商品类型、购买数量、购买金额、 付款方式等。

## 神策流程画布介绍
流程画布可以用来规划编排个性化的用户旅程，并自动化的执行各个阶段的营销动作。相比于多个独立的点阵式的营销活动的触达，流程画布保证了用户旅程流转的顺畅，以及用户体验的一致性。能够在合适的时间、给合适的人、用合适的触达通道、以合适的频次发送合适的内容，并回收每个营销阶段的执行数据用以衡量活动效果，打造运营闭环。
流程画布通过组合组件来编排用户旅程，实现对目标受众的圈选、流转的控制、营销内容的触达。同时支持设置全局的触达频控、勿扰设置保障用户体验，活动级别的目标设置追踪活动效果。
当一个用户进入了流程画布，用户就会在流程画布内根据各个组件配置等进行流转，比如配置了延迟器，用户就会在延迟器上等待一段时间才能向下流转等，用户需要满足组件配置的条件才会继续从该组件向下流转，否则用户会停在组件上直到满足条件为止。
流程画布是由流程画布基本信息（canvas_info）、组件信息（components）、组件之间的关联关系（component_relations）、画布监控指标（canvas_metrics）组成，用户从进入组件开始，在组件之间流转。流程画布最终是一个由组件构成的有向无环图。

画布信息包括：画布名称、调度类型、调度时间配置、重入设置描述、画布勿扰设置描述等

组件类型和他们的可配置项如下：
* 进入：type=ENTRY，包括定时单次进入（sub_type=FIXED_TIME）、定时重复进入（sub_type=ROUTINE）、完成事件进入（sub_type=TRIGGER_ONLY_A）、未完成事件进入（即完成A未完成B，sub_type=TRIGGER_A_NOT_B）四种类型。定时单次进入需要配置进入时间（fixed_time）、结束时间（schedule_end）、受众条件（audience）、以及组件名称（name）；定时重复进入需要配置开始时间和运行周期（每天、周、月几点开始运行，cron）、结束时间、受众条件、以及组件名称；完成事件进入需要配置开始和结束时间、事件规则（event，比如：完成APP点击事件且点击时间在上午10点之后）、受众规则（audience）、是否允许重入、以及组件名称；未完成事件指的是在完成某个事件之后一段时间未完成另外一个事件，在完成事件进入的规则基础上添加一段时间未完成另外一个事件的规则（event）。
* 受众判定：type=CONDITION, 包含批受众判定（sub_type=ROUTINE | FIXED_TIME，ROUTINE表示定时重复计算，FIXED_TIME 表示定时单次计算），受众就是根据一定 user 和 events 序列规则圈选的一组人群。比如圈选规则可以为「年龄大于10岁，且最近2个月做过登录APP事件」为一个规则。受众判定即为将上层组件流转过来的用户按照一定规则进行判定，判定满足规则则继续往下流转，否则结束。受众判定组件中需要配置受众规则。
* 事件判定：type=CONDITION, sub_type=EVENT, 即为将上层组件流转下来的用户按照实时发生的事件规则进行判定，需要注意这里是实时事件的判定，比如「5分钟内做过APP启动事件且启动客户端为IOS客户端」是一个有效的实时事件判定规则。事件判定组件中需要配置事件规则。
* 事件分流：type=CONTROL, sub_type=EVENT_SPLITTER, 将上层组件流转下来的用户按照实时发生的事件规则进行分流，并流转到符合事件判定条件的子节点，事件分流可以有多个分支，分支节点为「事件判定」组件或者 sub_type=ELSE_BRANCH 的组件，每个分支独立判断事件，比如「5分钟内发生APP启动事件，则走分支1，,5分钟内发送注册事件则走分支2...」，sub_type=ELSE_BRANCH 的子节点表示前面的事件都不符合则流转到这个节点。
* 进入事件分流：type=CONTROL, sub_type=EVENT_PROPERTIES_SPLITTER，该分流器只能在进入(ENTRY)组件为完成事件进入或者未完成事件进入时可用，表示根据进入的事件属性进行判定和流转，判定和流转逻辑与事件分流一致。
* 受众分流：type=CONTROL, sub_type=AUDIENCE_SPLITTER, 与事件分流类似，不同之处在于受众分流的每个子节点是按照受众进行判定的。子节点仅支持「受众判定」组件或者 sub_type=ELSE_BRANCH 的子节点。sub_type=ELSE_BRANCH 表示默认分支，即前面的条件都不符合流转到这个分支（对所有分流器都适用）。
* 比例分流：type=CONTROL, sub_type=PERCENT_SPLITTER, 将上层组件流转下来的用户按照流量比例进行切分，一般是用于 AB 测试使用，当作为 AB 测试的时候，第一个分支为对照组，其余分支为实验组，不同分支流量之和为 100%。比例分流组件主要需要配置各个分支的流量分配。比例分流子节点的 type=CONTROL, sub_type=PERCENT_SPLITTER_BRANCH
* 时段分流：type=CONTROL, sub_type=TIME_SPLITTER, 将上层组件流转下来的用户按照流转的时间段进行划分，比如流转到该组件的时间为3点到5点，则流转到分支1，否则流转到分支2等，每个分支子节点上有对应的满足时段信息。时段分流子节点的 sub_type=TIME_SPLITTER_BRANCH。
* 时间控制：type=CONTROL, sub_type=DELAY, 时间控制组件一般是用来做时间延迟使用的，表示从上层流转下来的用户需要按照一定的规则延迟到一定的时间后才继续向下流转。延迟规则包括等待一段时间和等待至指定时间两种：等待一段时间可以配置等待XX天/小时/分钟，并且可以添加固定时刻后流转，比如等待1天，或者等待1天后在12:00继续流转；等待至指定时间需要配置等待至每天/周/月的几点几分、周几+几点几分、几号+几点几分，或者可以指定一个固定日期的固定时刻。
* 营销动作：type=ACTION, sub_type 根据通道不同而不同, 营销动作组件需要配置营销内容，以及勿扰策略。比如发的优惠券详情或者发送的 SMS 详情等，勿扰指的是在特定时间段内不打扰用户，并选择该特定时间内的营销内容是勿扰结束后发送还是直接丢弃不发送。不同的营销动作配置内容不一样，发送内容略有差异。
* 结束：type=CONTROL, sub_type=END, 作为结束节点存在，结束后用户会退出画布。
  除此之外，画布还需要配置一些营销目标（可选），营销目标可以有多个，每个营销目标使用实时事件的方式监控，比如：在 5 分钟内完成APP启动事件，且APP为 IOS 版本，则算完成一次目标，或者在 2024-08-20 00:00:00 之前完成APP启动事件，且APP为 IOS 版本，则算完成一次目标。可以选择在完成目标的时候用户是否直接退出画布。


## 示例画布（仅供示例使用）
一个定时单次运行的流程画布配置及配置说明信息示例：
```json
{
    "canvas_info": {
        "cname": "测试流程画布", // 流程画布名称，需要符合流程画布的实际业务含义
        "desc": "...", // 流程画布的功能说明
        "active_time_range": {
            "start_time": "2025-04-28 15:00:00", // 画布运行开始时间
            "end_time": "2025-04-29 15:00:00" // 画布运行结束时间
        },
        "finished_time": "2024-08-16 00:00:00", // 画布完全结束运行时间（也可以理解为运营活动结束时间），超过该时间画布指标则不再继续统计，当 ENTRY 组件的 sub_type 为 FIXED_TIME 时，该值为 null
        "enable_global_control": false, // 是否开启全局对照组，开启后，在全局对照组中的人群不会进入画布
        "re_enter_desc": "用户退出画布后可重入，频率1天内可进入1次", // 用户进入画布的重入限制描述，如果不允许重复进入画布，则为 null，为 null 时用户不会重复进入画布。不为 null 时由两部分组成：重入规则+频率限制，重入规则包括 退出画布后可重入 和 随时可重入，频率限制比如 2天内可重入3次
        "do_not_disturb_desc": "每天 23:00 到次日 10:00 为勿扰时段，勿扰时段期间的消息等到勿扰时段结束后发送" // 整体画布级别的勿扰配置描述，可以在 ACTION 组件上选择是画布勿扰策略是否对当前组件生效，如果不需要配置勿扰策略，则值为 null。描述分为两个部分：时段 + 消息处理策略。勿扰时段可以设置每天，也可以设置每周的周几，消息处理策略可以选择为：放弃本次发送且退出画布、勿扰时段结束后立即发送、放弃本次发送且不退出画布。在 ACTION 组件上如果设置 enable_canvas_quiet 为 true，则会强制使用该策略触发勿扰！
    },
    "components": [
        {
            "component_id": 1, // 画布组件 id
            "name": "定时单次画布进入", // 画布组件名称
            "type": "ENTRY", // 画布组件类型
            "sub_type": "FIXED_TIME", // 画布组件二级类型
            "position": 1, // 组件显示位置排序
            "component_info": { // 进入组件详细配置
                "audience": { // 组件的受众规则配置
                    "fixed_time": "2024-08-14 17:35:00", // 当本组件的 sub_type 为 FIXED_TIME 时需要配置，表示单次受众计算的时间。时间精确到「分钟」即可
                    "cron": null, // 当本组件的 sub_type 为 ROUTINE 时需要配置，表示定时计算受众的时间。注意：cron 表达式有 6 位，比如：0 0 9 * * ?  
                    "audience_rule": "筛选出年龄大于18岁且城市为成都的人群"
                },
                "event": null, // 如果 ENTRY 组件是 TRIGGER_ONLY_A、TRIGGER_A_NOT_B 类型，则需要配置 event 事件规则
                "finished_time": "2024-08-16 00:00:00" // 与 canvas_info 的 finished_time 保持一致
            }
        },
        {
            "component_id": 2,
            "name": "受众判定组件",
            "type": "CONDITION",
            "sub_type": "FIXED_TIME",
            "position": 2,
            "component_info": {
                "audience": {
                    "fixed_time": null,
                    "cron": null,
                    "audience_rule": "筛选年龄大于18岁且性别为男的人群"
                }
            }
        },
        {
            "component_id": 3,
            "name": "受众分流组件",
            "type": "CONTROL",
            "sub_type": "AUDIENCE_SPLITTER", // 受众分流的父组件，该组件的子组件只能是 type=CONDITION, sub_type=FIXED_TIME 的组件，或者 CONTROL+ELSE_BRANCH 分支
            "position": 3,
            "component_info": { // 各个分支的详细配置在下级子节点组件中
                "style": "AUDIENCE_SPLITTER"
            }
        },
        {
            "component_id": 4,
            "name": "受众分流1",
            "type": "CONDITION",
            "sub_type": "FIXED_TIME",
            "position": 4,
            "component_info": {
                "audience": {
                    "fixed_time": null,
                    "cron": null,
                    "audience_rule": "筛选被画布id为1009的画布触达成功的人群"
                },
                "event": null
            }
        },
        {
            "component_id": 5,
            "name": "受众分流2",
            "type": "CONDITION",
            "sub_type": "FIXED_TIME",
            "position": 5,
            "component_info": {
                "audience": {
                    "fixed_time": null,
                    "cron": null,
                    "audience_rule": "筛选手机号不为空的人群"
                },
                "event": null
            }
        },
        {
            "component_id": 6,
            "name": "其他分组",
            "type": "CONTROL",
            "sub_type": "ELSE_BRANCH", // 默认分支，当其他分支条件都不满足时，进入该分支
            "position": 6,
            "component_info": {
                "style": "ELSE_BRANCH"
            }
        },
        {
            "component_id": 7,
            "name": "WEBHOOK1",
            "type": "ACTION",
            "sub_type": "WEBHOOK", // 可以根据情况确定，比如 SMS / WEBHOOK / PUSH / EDM 等，具体取值可以参考用户给出的通道大类
            "position": 7,
            "component_info": {
                // 使用语言描述要发送的内容，可以是发送内容、将用户移入人群包、给用户打标签三种操作，需要描述是否参与全局触达限制、是否参与画布勿扰策略，以及是否自定义勿扰策略及规则
                "action_desc": "WEBHOOK 发送，测试下发内容，下发内容为：你好啊，亲爱的，欢迎光临本店；每周周一和周二的 16:00 到次日 10:00 勿扰，勿扰期间消息放弃发送且不退出流程"
            }
        },
        {
            "component_id": 8,
            "name": "App 推送",
            "type": "ACTION",
            "sub_type": "PUSH",
            "position": 8,
            "component_info": {
                "action_desc": "PUSH 发送，标题：温馨提醒：您有一张理财券即将过期~；内容：尊敬的客户，您账户内有一张7日年华收益6.8%的新客理财券即将到期，点击使用>>；使用画布勿扰策略"
            }
        },
        {
            "component_id": 9,
            "name": "标记受众",
            "type": "ACTION",
            "sub_type": "MARK_AUDIENCE",
            "position": 9,
            "component_info": {
                "action_desc": "将用户移入人群包：人群包测试"
            }
        },
        {
            "component_id": 10,
            "name": "WEBHOOK4",
            "type": "ACTION",
            "sub_type": "MARK_GROUP",
            "position": 10,
            "component_info": {
              "action_desc": "为用户打新标签，标签名为：测试下发内容标签"
            }
        },
        {
              "component_id": 11,
              "name": "事件分流",
              "type": "CONTROL",
              "sub_type": "EVENT_SPLITTER", // 事件分流的父组件，该组件的子组件只能是 type=CONDITION, sub_type=EVENT 的组件，或者 CONTROL+ELSE_BRANCH 分支
              "position": 11,
              "component_info": {
                "style": "EVENT_SPLITTER"
              }
        },
        {
          "component_id": 12,
          "name": "事件分支1",
          "type": "CONDITION",
          "sub_type": "EVENT",
          "position": 12,
          "component_info": {
            "condition_type": "EVENT",
            "audience": null,
            "event": {
                "time_window_desc": "15 分钟内", // 事件判定窗口期，指的是等待一段时间，在等待的这段时间内，发生 A 事件则满足要求
                "event_A_desc": "((发生了 $PlanMsgSendDone 事件且 $sf_plan_id 属性为 1009)至少1次) 且 ((发生了 Action_event_0001 事件)至少1次)" // A 事件规则描述，不同事件之间的关系可以是 且 | 或
            }
          }
        },
        {
          "component_id": 13,
          "name": "事件分支2",
          "type": "CONDITION",
          "sub_type": "EVENT",
          "position": 13,
          "component_info": {
            "condition_type": "EVENT",
            "audience": null,
            "event": {
                "time_window_desc": "15 分钟内",
                "event_A_desc": "(发生了 Action_event_0002 事件)至少1次"
            }
          }
        },
        {
          "component_id": 14,
          "name": "比例分流",
          "type": "CONTROL",
          "sub_type": "PERCENT_SPLITTER", // 比例分流的父组件，该组件的子组件只能是 type=CONTROL, sub_type=PERCENT_SPLITTER_BRANCH 的组件，各个子组件分支流量之和应该为 100
          "position": 14,
          "component_info": {
            "style": "PERCENT_SPLITTER",
            "splitter_control_rule": {
              "ab_enable": true // 表示打开 ab 测试，使用 ab 测试则该组件的第一个子组件为对照组，其他分支为实验组
            }
          }
        },
        {
          "component_id": 15,
          "name": "对照组分支",
          "type": "CONTROL",
          "sub_type": "PERCENT_SPLITTER_BRANCH", // 表示比例分流的子节点
          "position": 15,
          "component_info": {
            "percent_splitter_branch_control_rule": {
              "percent": 20, // 该分支流量百分比
              "ab_strategy_type": "CONTROL" // 对照组标识，如果父组件未打开 ab 测试，则 ab_strategy_type=null
            }
          }
        },
        {
          "component_id": 16,
          "name": "实验组1",
          "type": "CONTROL",
          "sub_type": "PERCENT_SPLITTER_BRANCH",
          "position": 16,
          "component_info": {
            "percent_splitter_branch_control_rule": {
              "percent": 40, // 该分支流量百分比
              "ab_strategy_type": "EXPERIMENT" // 实验组标识
            }
          }
        },
        {
          "component_id": 17,
          "name": "实验组2",
          "type": "CONTROL",
          "sub_type": "PERCENT_SPLITTER_BRANCH",
          "position": 17,
          "component_info": {
            "percent_splitter_branch_control_rule": {
              "percent": 40, // 该分支流量百分比
              "ab_strategy_type": "EXPERIMENT" // 实验组标识
            }
          }
        },
        {
          "component_id": 18,
          "name": "时段分流",
          "type": "CONTROL",
          "sub_type": "TIME_SPLITTER", // 时段分流的父组件，该组件的子组件只能是 type=CONTROL, sub_type=TIME_SPLITTER_BRANCH 的组件，或者 CONTROL+ELSE_BRANCH
          "position": 18,
          "component_info": {
            "style": "TIME_SPLITTER"
          }
        },
        {
          "component_id": 19,
          "name": "时段分支1",
          "type": "CONTROL",
          "sub_type": "TIME_SPLITTER_BRANCH",
          "position": 19,
          "component_info": {
            "time_splitter_branch_control_rule": {
              "time_splitter_type": "PERIOD_WEEK", // 时段分支的区分方式，取值可为 PERIOD_WEEK | STATIC， PERIOD_WEEK 可指定一周中的某些天的某些时段；STATIC 则可以选择静态的时段，用户在选定的时段内则会进入该分支
              "start_time": "09:00", // 开始时间，如果 time_splitter_type=PERIOD_WEEK，则表示一天中的开始时间点，否则需要取值为 YYYY-MM-DD HH:mm:ss 格式表示静态开始时间
              "end_time": "12:00", // 结束时间，取值逻辑同 start_time
              "days": [ // 当 time_splitter_type=PERIOD_WEEK 时，表示该分支对一周中的第几天生效，示例中表示周二、周三的09:00到12:00进入该分支，当 time_splitter_type=STATIC 时，days = null
                2,
                3
              ]
            }
          }
        },
        {
          "component_id": 20,
          "name": "时段分支2",
          "type": "CONTROL",
          "sub_type": "TIME_SPLITTER_BRANCH",
          "position": 20,
          "component_info": {
            "time_splitter_branch_control_rule": {
              "time_splitter_type": "PERIOD_WEEK",
              "start_time": "12:00",
              "end_time": "14:00",
              "days": [
                1,
                4
              ]
            }
          }
        },
        {
          "component_id": 21,
          "name": "ELSE分支",
          "type": "CONTROL",
          "sub_type": "ELSE_BRANCH",
          "position": 21,
          "component_info": {
          }
        }
    ],
    "component_relations": [
        {
            "source_component_id": 1, // 源组件id（component_id）
            "target_component_id": 2, // 目标组件 id，表示用户从id为1的组件流向id为2的组件
            "entry_source_node": "YES", // 可取值包括 YES | NO，取其中一个即可。YES 表示source_component_id组件是满足条件流向target_component_id组件，NO 则表示不满足才流转
            "position": 1 // 展示位置
        },
        {
            "source_component_id": 2,
            "target_component_id": 3,
            "entry_source_node": "YES",
            "position": 2
        },
        {
            "source_component_id": 2,
            "target_component_id": 7,
            "entry_source_node": "NO",
            "position": 3
        },
        {
            "source_component_id": 3,
            "target_component_id": 4,
            "entry_source_node": "YES",
            "position": 4
        },
        {
            "source_component_id": 3,
            "target_component_id": 5,
            "entry_source_node": "YES",
            "position": 5
        },
        {
            "source_component_id": 3,
            "target_component_id": 6,
            "entry_source_node": "YES",
            "position": 6
        },
        {
            "source_component_id": 4,
            "target_component_id": 8,
            "entry_source_node": "YES",
            "position": 7
        },
        {
            "source_component_id": 5,
            "target_component_id": 9,
            "entry_source_node": "YES",
            "position": 8
        },
        {
            "source_component_id": 6,
            "target_component_id": 10,
            "entry_source_node": "YES",
            "position": 9
        },
        {
          "source_component_id": 7,
          "target_component_id": 11,
          "entry_source_node": "YES",
          "position": 10
        },
        {
          "source_component_id": 11,
          "target_component_id": 12,
          "entry_source_node": "YES",
          "position": 11
        },
        {
          "source_component_id": 11,
          "target_component_id": 13,
          "entry_source_node": "YES",
          "position": 12
        },
        {
          "source_component_id": 12,
          "target_component_id": 14,
          "entry_source_node": "YES",
          "position": 13
        },
        {
          "source_component_id": 14,
          "target_component_id": 15,
          "entry_source_node": "YES",
          "position": 14
        },
        {
          "source_component_id": 14,
          "target_component_id": 16,
          "entry_source_node": "YES",
          "position": 15
        },
        {
          "source_component_id": 14,
          "target_component_id": 17,
          "entry_source_node": "YES",
          "position": 16
        },
        {
          "source_component_id": 13,
          "target_component_id": 18,
          "entry_source_node": "YES",
          "position": 17
        },
        {
          "source_component_id": 18,
          "target_component_id": 19,
          "entry_source_node": "YES",
          "position": 18
        },
        {
          "source_component_id": 18,
          "target_component_id": 20,
          "entry_source_node": "YES",
          "position": 19
        },
        {
          "source_component_id": 18,
          "target_component_id": 21,
          "entry_source_node": "YES",
          "position": 20
        }
    ],
    "canvas_metrics": [
        {
            "convert_id": 1,
            "convert_name": "目标 1", // 给转化目标取个名字
            "end_after_convert": false, // 用户发生了转化之后，是否退出画布，不继续在画布中流转
            "event_pattern": {
                "window_desc": "30 分钟内", // 目标判定窗口期描述
                "absolute_time_window": null, // 绝对时间窗口期，表示该时间之前，比如 2024-01-01 12:00:00
                "matcher_rule_desc": "(发生 MetricsScene041_event02 事件且 MetricsScene041_event02 事件中性别为男) 至少 2 次" // 事件判定规则描述，只能使用事件描述，注意可以使用括号来表示关系
            }
        }
    ]
}
```

另外一个发生A未发生B类型的事件判定类型的画布配置示例如下：
```json
{
    "canvas_info": {
        "cname": "测试流程画布1",
        "desc": "...", // 流程画布的功能说明
        "active_time_range": {
            "start_time": "2025-04-28 15:00:00", // 画布运行开始时间
            "end_time": "2025-04-29 15:00:00" // 画布运行结束时间
        },
        "finished_time": "2026-08-10 00:00:00",
        "enable_global_control": false, // 是否开启全局对照组，开启后，在全局对照组中的人群不会进入画布
        "re_enter_desc": null,
        "do_not_disturb_desc": null
    },
    "components": [
        {
            "component_id": 1,
            "name": "未完成事件进入",
            "type": "ENTRY",
            "sub_type": "TRIGGER_A_NOT_B", // 触发 A 未触发 B 类型
            "position": 1,
            "component_info": {
                "audience": { // 受众配置
                    "fixed_time": null,
                    "cron": null,
                    "audience_rule": "筛选有手机号的人群"
                },
                "event": { // 事件触发类型的事件配置
                    "time_window_desc": "5 分钟内", // 事件判定窗口期，指的是 A 事件发生后，多长时间窗口内 B 事件未发生，则满足要求
                    "event_A_desc": "((发生了 $PlanMsgSendDone 事件且 $sf_plan_id 属性为 1009)至少1次) 且 ((发生了 Action_event_0001 事件)至少1次)", // A 事件规则描述，不同事件之间的关系可以是 且 | 或
                    "event_B_desc": "(发生了 Action_event_0002 事件)至少1次" // B 事件规则描述，注意，如果 sub_type 为 TRIGGER_ONLY_A 类型，则不需要 event_B_desc
                },
                "finished_time": "2026-08-13 00:00:00"
            }
        },
        {
          "component_id": 2,
          "name": "进入事件分流",
          "type": "CONTROL",
          "sub_type": "EVENT_PROPERTIES_SPLITTER", // 进入事件分流
          "position": 2,
          "component_info": {
            "style": "EVENT_PROPERTIES_SPLITTER"
          }
        },
        {
          "component_id": 3,
          "name": "进入事件分支1",
          "type": "CONDITION",
          "sub_type": "EVENT_PROPERTY",
          "position": 3,
          "component_info": {
            "event": {
              "event_A_desc": "(发生了 $PlanMsgSendDone 事件且 $distinct_id 有值)" // 事件名称必须与进入 ENTRY 的 event_A_desc 保持一致 
            }
          }
        },
        {
          "component_id": 4,
          "name": "ELSE分支",
          "type": "CONTROL",
          "sub_type": "ELSE_BRANCH",
          "position": 4,
          "component_info": {
          }
        },
        {
          "component_id": 5,
          "name": "时间控制",
          "type": "CONTROL",
          "sub_type": "DELAY", // 时间控制组件
          "position": 5,
          "component_info": {
            "delay_control_rule": "相对时间等待5分钟" // 时间控制规则描述
          }
        },
        {
            "component_id": 6,
            "name": "App 推送",
            "type": "ACTION",
            "sub_type": "PUSH",
            "position": 6,
            "component_info": {
                "action_desc": "PUSH 发送，标题：温馨提醒：您有一张理财券即将过期~；内容：尊敬的客户，您账户内有一张7日年华收益6.8%的新客理财券即将到期，点击使用>>；需要开启全局触达限制"
            }
        }
    ],
    "component_relations": [
        {
            "source_component_id": 1,
            "target_component_id": 2,
            "entry_source_node": "YES",
            "position": 1
        },
        {
          "source_component_id": 2,
          "target_component_id": 3,
          "entry_source_node": "YES",
          "position": 1
        },
        {
          "source_component_id": 2,
          "target_component_id": 4,
          "entry_source_node": "YES",
          "position": 1
        },
        {
          "source_component_id": 3,
          "target_component_id": 5,
          "entry_source_node": "YES",
          "position": 1
        },
        {
          "source_component_id": 5,
          "target_component_id": 6,
          "entry_source_node": "YES",
          "position": 1
        }
    ],
    "canvas_metrics": []
}
```
其它注意事项：
避免产生被gpt过滤的关键词

示例画布的解释信息详情见 json 中的注释（注释只是为了解释字段含义等，不是 json 的一部分）。需要注意，你在回复 json 的时候，禁止带 `//` 类型的注释！

画布节点连接限制：
* 进入组件（type=ENTRY）必须作为第一个组件（即开始节点），只能连接一个子节点；
* 受众判定(type=CONDITION, sub_type=ROUTINE | FIXED_TIME)/事件判定（type=CONDITION, sub_type=EVENT） 组件允许有多个父节点，允许1个或者2个子节点（之多只允许2个子节点），当有两个子节点时，一个子节点分支表示满足条件，另外一个表示不满足条件；
* 营销动作（type=ACTION）/时间控制（type=CONTROL, sub_type=DELAY）/结束（type=CONTROL, sub_type=END）组件允许有多个父节点，但是只允许有一个子节点，不允许有多个子节点；
* 进入事件分流组件（type=CONTROL, sub_type=EVENT_PROPERTIES_SPLITTER）只能在进入(ENTRY)组件为完成事件进入或者未完成事件进入时可用；
* 事件分流/受众分流/进入事件分流/比例分流/时段分流 组件允许有多个父节点，且允许有多个分支（子节点），每个分支是一个组件。

画布配置校验规则：
注意：画布配置必须满足以下限制条件
1. canvas_info 画布信息：
 - desc 画布描述：
   - 长度不超过 500 个字符；
 - active_time_range 画布运行周期：
   - start_time 必须小于 end_time；
   - start_time 和 end_time 必须是 yyyy-MM-dd HH:mm:ss 格式；
 - finished_time 画布结束时间：
   - 必须大于 active_time_range 中的 end_time；
   - 必须是 yyyy-MM-dd HH:mm:ss 格式；
 - do_not_disturb_desc 勿扰配置：
   - 如果配置了勿扰，则必须要明确勿扰时段和消息处理策略；
   - 勿扰时段的时间必须是 HH:mm 格式；
2. components 画布组件配置：
 - 每个组件的 component_id、name、type 不能为空；
 - 每个组件的 component_id 和 name 不能重复；
 - 列表中必须包含一个（且只能包含一个）type=ENTRY 的组件；
 - 进入组件（type=ENTRY）的 sub_type 必须与条件配置相对应，具体要求如下：
   - sub_type=FIXED_TIME 时，需要配置 component_info.audience 中的 fixed_time；
   - sub_type=ROUTINE 时，需要配置 component_info.audience 中的 cron；
   - sub_type=TRIGGER_ONLY_A 时，需要配置 component_info.event 中的 event_A_desc；
   - sub_type=TRIGGER_A_NOT_B 时，需要配置 component_info.event 中的 event_A_desc 和 event_B_desc；
3. component_relations 画布组件连接关系：
 - source_component_id 和 target_component_id 必须在 components 列表中存在；
 - source_component_id 和 target_component_id 唯一确定一条边，且连接关系中的边不能重复；
 - 组件和边组成的图中，不能出现环状结构；
4. canvas_metrics 画布指标配置：
 - 如果配置了画布指标，则convert_id、convert_name、end_after_convert 等信息不能为空；
"""

async def get_canvas_design_draft_generate_system_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="CANVAS_DESIGN_DRAFT_GENERATE_SYSTEM_PROMPT",
        prompt_name="画布结构生成机器人 - 系统提示词",
        prompt_desc="画布结构生成机器人 - 系统提示词",
        prompt_content=CANVAS_DESIGN_DRAFT_GENERATE_SYSTEM_PROMPT,
    )
    return template
