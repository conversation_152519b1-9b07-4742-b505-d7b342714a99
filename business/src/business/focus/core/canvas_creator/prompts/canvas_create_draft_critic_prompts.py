# 画布粗略草稿评论 prompt
from jinja2 import Template

from framework.prompt import PromptFactory
from .canvas_create_draft_generate_prompts import CANVAS_DESIGN_DRAFT_GENERATE_SYSTEM_PROMPT, \
    get_canvas_design_draft_generate_system_prompt

CANVAS_DESIGN_DRAFT_CRITIC_SYSTEM_PROMPT = """# 职责描述
你是一个资深的运营人员，你精通互联网行业、零售行业、金融行业、游戏行业等全部的运营方法，
你需要根据用户的需求，结合你的行业经验，来判断当前让你判断的画布草稿是否合理。

# 一些背景知识及示例流程画布配置
canvas_design_draft_generate_system_prompt
***

# 重点审核内容
1. 必须将画布草稿包含在一个 markdown 代码块中，画布草稿是一个 json 表示的结构
2. 画布草稿的内容必须与初始的画布结构所描述的内容一致，画布草稿是画布结构的细化
3. 画布草稿的结构是合理的，符合前面北京知识中的结构描述
4. 是否有违反组件节点连接限制的问题
5. 各个组件、连接信息中的字段类型是否正确

# 回复要求
如果画布草稿的 json 数据合理，请直接告知画布结构合理，可以进行下一步即可。
如果画布结构存在与用户需求、原始的画布结构不一致的地方，则你需要详细描述出问题所在，并要求重新给出画布草稿。
"""

async def get_canvas_design_draft_critic_system_prompt() -> Template:
    prompt = await get_canvas_design_draft_generate_system_prompt()
    CANVAS_DESIGN_DRAFT_CRITIC_SYSTEM_PROMPT.replace("canvas_design_draft_generate_system_prompt", prompt.render())
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="CANVAS_DESIGN_DRAFT_CRITIC_SYSTEM_PROMPT",
        prompt_name="画布结构评论机器人 - 系统提示词",
        prompt_desc="画布结构评论机器人 - 系统提示词",
        prompt_content=CANVAS_DESIGN_DRAFT_CRITIC_SYSTEM_PROMPT,
    )
    return template
