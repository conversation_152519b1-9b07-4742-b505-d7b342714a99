EVENT_EXAMPLE = """事件规则举例说明如下：
***
{
  "relation": "OR",
  "match_rule_list": [
    { // 这个事件规则表示：(发生 $AppUnInstall 事件并且事件的 $lib 属性为 Java)至少1次
      "event_name": "$AppUnInstall", // 事件名
      "aggregator": "COUNT", // 计算方式
      "aggregate_value": "",
      "function": "LEAST", // 事件判定条件
      "param": [ // function 对应的参数
        "1"
      ],
      "filter": { // 对事件进行筛选，满足筛选条件的事件才能计数
        "relation": "and",
        "conditions": [
          {
            "field": "event.$AppUnInstall.$lib",
            "function": "equal",
            "params": [
              "Java"
            ]
          }
        ]
      },
      "relation": "OR"
    },
    { // 这个事件规则表示：(发生 $AppStart 事件)至少1次
      "event_name": "$AppStart",
      "aggregator": "COUNT",
      "aggregate_value": "",
      "function": "LEAST",
      "param": [
        "1"
      ],
      "relation": "OR"
    }
  ]
}
"""
