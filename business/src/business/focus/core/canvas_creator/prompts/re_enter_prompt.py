RE_ENTER_PROMPT = """## 目标及要求
请你根据一个「重入规则」的描述，将重入规则改写为下面的 json 定义结构：
示例结构：
```json
{
    "unlimited": true, // 表示是否为无限次重入
    "reentry_strategy": "REENTRY_ANY_TIME", // 可取值 REENTRY_NO | REENTRY_ANY_TIME | REENTRY_AFTER_EXITING，分别表示不允许重入，任何时间可重入，退出画布后可重入
    "reentry_limit": {
        "window": {
            "size": 1, // 表示 unit 的个数
            "unit": "DAY", // 可取值 MINUTE | HOUR | DAY | WEEK | MONTH | YEAR
        },
        "limit": 5 // 比如 size=1 且 unit=DAY, limit=5 就表示限制了1天内可重入5次
    }
}
```

注意：上述 json 中的注释仅供你理解数据结构字段含义使用，你的回复中不能带注释信息！
你需要将你回复的 json 包含在 markdown 的代码块中。

请你根据要求等，给出 json 定义。
"""

RE_ENTRY_USER_PROMPT = """重入规则描述：
***
__RE_ENTER_DESC__


你回复的 json 结构：
"""
