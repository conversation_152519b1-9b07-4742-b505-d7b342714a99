QUIET_HOUR_PROMPT = """## 目标及要求
请你根据画布配置信息，勿扰时段描述信息等，向我回复一个符合勿扰结构的 json。
你需要将 json 包含在 markdown 的代码块中。

一个合法的勿扰结构示例及注释：
```json
{ // 画布勿扰配置，即在画布上面的防打扰配置，可以在 ACTION 组件上选择是画布勿扰策略是否对当前组件生效
    "enable_do_not_disturb": true, // 是否开启防打扰，如果开启了才需要后面的字段
    "repeat_cycle": "DAY", // 勿扰重复策略，可取值包括：DAY | WEEK | MONTH，分别表示按天、按周、按月设置勿扰，当取值为 WEEK 时，unit_list 中相同的 day 只能出现一次
    "unit_list": [ // 当 repeat_cycle 为 DAY 的时候， unit_list 列表只能有一个列表项，当 repeat_cycle 为 WEEK/MONTH 时，unit_list 可以有多个列表项，每个列表项表示一天
        {
            "time_range": {
                "start_time": "23:00", // 如果 repeat_cycle 为 DAY，则允许当天勿扰和跨天勿扰，当天勿扰 start_time<end_time，跨天勿扰 start_time>end_time（即从第一天的start_time到第二天的end_time期间属于勿扰时段）
                "end_time": "08:00",
            }
            "day": null // 当 repeat_cycle 为 WEEK 时，day 表示一周中的第几天
        }
    ],
    "action_strategy": "DROP" // 勿扰期间的消息处理方式，可取值包括：DROP | DELAY | DROP_AND_NOT_FINISH，分别表示放弃发送且退出当前流程画布、延迟到勿扰时段结束发送、放弃发送且不退出当前流程画布
}
```
一个按周勿扰的配置示例（表示周一周二的晚上23点到第二天8点之间勿扰）如下：
```json
{
    "enable_do_not_disturb": true,
    "repeat_cycle": "WEEK",
    "unit_list": [
        {
            "time_range": {
                "start_time": "23:00",
                "end_time": "08:00",
            }
            "day": 1
        },
        {
            "time_range": {
                "start_time": "23:00",
                "end_time": "08:00",
            }
            "day": 2
        }
    ],
    "action_strategy": "DROP"
}
```
同理，如果是按月的勿扰，则 day 则表示的每月的哪一天。

注意：上述 json 中的注释仅供你理解数据结构字段含义使用，你的回复中不能带注释信息！
解释：勿扰时段表示在某个特定时间段内不对用户进行画布触达，以防止打扰用户。

请你根据要求等，给出勿扰时段的 json 定义。
"""

QUIET_USER_PROMPT = """## 当前的画布配置
***
```json
__CANVAS_DRAFT__
```

## 当前需要回复的勿扰时段描述
***
__QUIENT_DESC__


你回复的勿扰结构：
"""
