from typing import Optional, AsyncGenerator, Any, Generator, List

from langchain_core.messages import BaseMessage

from common import tools
from ..prompts.canvas_create_draft_generate_prompts import CANVAS_DESIGN_DRAFT_GENERATE_SYSTEM_PROMPT, \
    get_canvas_design_draft_generate_system_prompt
from framework.agents.autogen import ParticipantAgent
from framework.models import BaseLLM, ModelsFactory
from framework import config
from framework.config import model_config

log = tools.get_logger()


class CanvasDraftAgent(ParticipantAgent):
    """画布粗略草稿生成"""

    def __init__(
            self,
            llm: Optional[BaseLLM] = None,
            canvas_list: List[str] = None,
            **kwargs,
    ):
        scenario = 'default'
        llm_config = model_config[config.scenario_reference_model.get(scenario, 0)]
        if not llm:
            r_llm = ModelsFactory.get_llm(scenario=scenario)
        else:
            r_llm = llm

        prompt = tools.asyncio_run(get_canvas_design_draft_generate_system_prompt)
        kwargs = {
                     "description": "用于生成画布的草稿，需要画布草稿生成完成后才能进行画布草稿结构的翻译",
                     "name": "canvas_draft_generator",
                     "system_prompt": prompt,
                     "llm": r_llm,
                     "prompt_file_name": "canvas_draft_generator.json",
                     "max_token_limit": llm_config.get("input_token_limit", 1000 * 8),
                 } | kwargs
        super().__init__(**kwargs)
        self.canvas_list = canvas_list

    def run(self) -> BaseMessage:
        result = super().run()
        self.canvas_list.append(result.content)
        return result

    def stream_run(self) -> Generator[BaseMessage, Any, Any]:
        result = self.run()
        yield result

    async def arun(self) -> BaseMessage:
        result = await super().arun()
        self.canvas_list.append(result.content)
        return result

    async def astream_run(self) -> AsyncGenerator[BaseMessage, Any]:
        result = await self.arun()
        yield result
