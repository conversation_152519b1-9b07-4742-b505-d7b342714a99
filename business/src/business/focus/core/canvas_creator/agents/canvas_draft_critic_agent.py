from typing import Optional

from common import tools
from ..prompts.canvas_create_draft_critic_prompts import CANVAS_DESIGN_DRAFT_CRITIC_SYSTEM_PROMPT, \
    get_canvas_design_draft_critic_system_prompt
from framework.models import BaseLLM, ModelsFactory
from framework.agents.autogen import (
    ParticipantAgent
)
from framework import config
from framework.config import model_config

log = tools.get_logger()


class CanvasDraftCriticAgent(ParticipantAgent):
    """画布粗略草稿评论"""

    def __init__(
            self,
            llm: Optional[BaseLLM] = None,
            **kwargs,
    ):
        scenario = 'default'
        llm_config = model_config[config.scenario_reference_model.get(scenario, 0)]
        if not llm:
            r_llm = ModelsFactory.get_llm(scenario=scenario)
        else:
            r_llm = llm

        prompt = tools.asyncio_run(get_canvas_design_draft_critic_system_prompt)
        kwargs = {
                     "description": ("用于对canvas_draft_generator回复的画布草稿结构进行评价，"
                                     "canvas_draft_generator给出的画布草稿结构必须经过"
                                     "canvas_draft_generator评价通过之后才能进入下一环节"),
                     "name": "canvas_draft_critic",
                     "system_prompt": prompt,
                     "llm": r_llm,
                     "prompt_file_name": "canvas_draft_critic.json",
                     "max_token_limit": llm_config.get("input_token_limit", 1000 * 8),
                 } | kwargs
        super().__init__(**kwargs)
