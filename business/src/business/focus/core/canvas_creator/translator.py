# 画布粗略草稿翻译成真实的画布
import json
from typing import Any, List

from onnxruntime.capi.onnxruntime_pybind11_state import RuntimeException
from tenacity import retry, stop_after_attempt, retry_if_exception_type, wait_fixed

from common import tools as my_tools
from framework.models import BaseLLM
from common.tools import extract_code_from_text
from .prompts.quiet_hour_prompt import QUIET_HOUR_PROMPT, QUIET_USER_PROMPT
from .prompts.re_enter_prompt import RE_ENTRY_USER_PROMPT, RE_ENTER_PROMPT
from .prompts.time_point_prompt import TIME_POINT_USER_PROMPT, TIME_POINT_PROMPT
from .prompts.time_window_prompts import TIME_WINDOW_PROMPT
from ..audience.audience_agent import AudienceRuleCreateAgent, GenAudienceRuleFromNLDescTaskInput

retry_times = 3
log = my_tools.get_logger()


async def translate_canvas_draft(llm: BaseLLM, canvas_draft: str) -> str:
    """
    画布草稿翻译成真实的画布
    """
    log.info("begin to translate canvas draft.")
    canvas: dict = json.loads(canvas_draft)

    log.info("translate canvas info.")
    canvas_info = await _translate_canvas_info(llm=llm, canvas_info=canvas.pop('canvas_info'))
    canvas = canvas | canvas_info

    log.info("translate canvas components/edges.")
    component_nodes = canvas.pop('components')
    new_component_nodes = []
    for component_node in component_nodes:
        component_type = component_node.get('type', '').upper()

        if component_type == 'ENTRY':
            new_component_nodes.append(await _translate_enter_component(llm=llm, component_node=component_node))
        elif component_type == 'CONDITION':
            new_component_nodes.append(await _translate_condition_component(llm=llm, component_node=component_node))
        elif component_type == 'CONTROL':
            new_component_nodes.append(await _convert_control_component(llm=llm, component_node=component_node))
        elif component_type == 'ACTION':
            new_component_nodes.append(await _translate_action_component(llm=llm, component_node=component_node))
        else:
            raise RuntimeError(f"unknown component type: {component_type}")

    canvas['component_list'] = new_component_nodes
    canvas['component_edge_list'] = canvas.pop('component_relations')

    log.info("translate canvas target.")
    canvas['convert_target'] = await _translate_convert_target(
        llm=llm,
        convert_targets=canvas.pop('canvas_metrics', None)
    )

    # fix fields
    canvas = await _fix_static_time(canvas)

    log.info(f"end to translate canvas draft: {canvas}")
    return json.dumps(canvas, ensure_ascii=False)


async def _fix_static_time(obj: Any) -> Any:
    if not isinstance(obj, dict):
        return obj

    obj = json.dumps(obj, ensure_ascii=False)
    obj = json.loads(obj)

    if "type" in obj and "static_time" in obj and obj['type'].upper() == 'STATIC':
        static_time = obj['static_time']
        if not static_time.endswith('Z'):
            static_time = static_time + 'Z'
        obj['static_time'] = static_time
        return obj

    for k, v in obj.items():
        if isinstance(v, dict):
            obj[k] = await _fix_static_time(v)
        else:
            pass
    return obj


async def _translate_convert_target(llm: BaseLLM, convert_targets: List[dict] | None) -> dict:
    """
    画布指标翻译成真实的画布指标
    """
    if not convert_targets:
        return {}

    convert_rule_list = []
    for convert_rule in convert_targets:
        event_pattern = convert_rule.get('event_pattern', None)
        if not event_pattern:
            continue
        time_desc = event_pattern.get('window_desc', '')
        if not time_desc:
            time_desc = event_pattern.get('absolute_time_window', '')
        time_window = await _translate_time_window(llm, time_desc)
        match_rules = await _translate_event_rule(llm=llm, rule_desc=event_pattern.get('matcher_rule_desc', {}))
        convert_rule_list.append(
            {
                "convert_id": convert_rule.get('convert_id', 0),
                "convert_name": convert_rule.get('convert_name', '未知目标'),
                "end_after_convert": convert_rule.get('end_after_convert', False),
                "convert_window": time_window,
                "match_rule_list": match_rules
            }
        )

    return {"convert_rule_list": convert_rule_list}


async def _convert_control_component(llm: BaseLLM, component_node: dict) -> dict:
    log.info("start translate control component")
    component_sub_type = component_node.get('sub_type', '').upper()
    control_component = {
        "control_type": component_sub_type
    }
    new_component_node = {
        "component_id": component_node.get('component_id', -1),
        "cname": component_node.get('name', ''),
        "component_type": "CONTROL",
        "control_component": control_component
    }

    if component_sub_type in [
        'AUDIENCE_SPLITTER', 'EVENT_SPLITTER', 'EVENT_PROPERTIES_SPLITTER', 'PERCENT_SPLITTER', 'TIME_SPLITTER'
    ]:
        ab_enable = False
        splitter_control_rule = component_node['component_info'].get('splitter_control_rule', None)
        if splitter_control_rule is not None:
            tmp_ab_enable = str(splitter_control_rule.get('ab_enable', False))
            tmp_ab_enable = tmp_ab_enable.upper().strip()
            ab_enable = bool(tmp_ab_enable == "TRUE")
        control_component['splitter'] = {
            'ab_enable': ab_enable
        }
    elif component_sub_type == 'QUIET_HOUR':
        control_component['percent_branch'] = component_node['component_info']['percent_splitter_branch_control_rule']
    elif component_sub_type == 'TIME_SPLITTER_BRANCH':
        control_component['time_branch'] = component_node['component_info']['time_splitter_branch_control_rule']
    elif component_sub_type == 'DELAY':
        delay_control_rule = component_node['component_info']['delay_control_rule']
        control_component['delay'] = {
            "delay_config": await _translate_time_point(llm=llm, time_point_desc=delay_control_rule),
            "delay_end_action": None
        }

    return new_component_node


async def _translate_condition_component(llm: BaseLLM, component_node: dict) -> dict:
    log.info("start translate condition component")
    component_sub_type = component_node.get('sub_type', '').upper()

    match component_sub_type:
        case 'FIXED_TIME':
            component_sub_type = 'CONDITION_AUDIENCE_BATCH'
        case 'ROUTINE':
            component_sub_type = 'CONDITION_AUDIENCE_BATCH'
        case 'EVENT':
            component_sub_type = 'CONDITION_EVENT'
        case 'EVENT_PROPERTY':
            component_sub_type = 'CONDITION_EVENT'

    condition_component = {
        "condition_type": component_sub_type
    }
    new_component_node = {
        "component_id": component_node.get('component_id', -1),
        "cname": component_node.get('name', ''),
        "component_type": "CONDITION",
        "condition_component": condition_component
    }

    if component_sub_type in ['CONDITION_AUDIENCE_BATCH']:
        audience_rule = component_node.get("component_info").get("audience")
        audience = await _translate_audience(llm=llm, audience_rule=audience_rule)
        condition_component['audience_rule'] = audience
    elif component_sub_type == 'CONDITION_EVENT':
        condition_node = await _translate_event_condition(llm=llm, component_node=component_node)
        condition_component['event_rule'] = condition_node
    elif component_sub_type == 'EVENT_PROPERTY':
        condition_node = await _translate_event_property_condition(llm=llm, component_node=component_node)
        condition_component['event_rule'] = condition_node
    return new_component_node


async def _translate_action_component(llm: BaseLLM, component_node: dict) -> dict:
    log.info("start translate action component")
    new_component_node = {
        "component_id": component_node.get('component_id', -1),
        "cname": component_node.get('name', ''),
        "component_type": "ACTION",
        "action_component": component_node.get('component_info')
    }
    return new_component_node


async def _translate_event_property_condition(llm: BaseLLM, component_node: dict) -> dict:
    event_desc = component_node.get("component_info").get("event").get('event_A_desc')
    event_rule = await _translate_event_rule(llm=llm, rule_desc=event_desc)
    component_node.get("component_info").get("event")['event_do'] = event_rule
    return component_node


async def _translate_event_condition(llm: BaseLLM, component_node: dict) -> dict:
    event_do = component_node.get("component_info").get("event")
    time_window_desc = None
    if 'time_window_desc' in event_do:
        time_window_desc = event_do['time_window_desc']

    event_desc = event_do['event_A_desc']
    event_do_rule = await _translate_event_rule(llm=llm, rule_desc=event_desc)
    return {
        "match_window": time_window_desc,
        "event_rule": event_do_rule
    }


async def _translate_enter_component(llm: BaseLLM, component_node: dict) -> dict:
    log.info("start translate entry component")
    component_sub_type = component_node.get('sub_type', '').upper()
    entry_component = {
        "entry_type": component_sub_type,
        "audience_rule": None,
        "do_entry_rule": None,
        "not_do_entry_rule": None
    }
    new_component_node = {
        "component_id": component_node.get('component_id', -1),
        "cname": component_node.get('name', ''),
        "component_type": "ENTRY",
        "entry_component": entry_component,
    }

    audience_rule = component_node['component_info']['audience']
    audience_rule = await _translate_audience(llm=llm, audience_rule=audience_rule)
    entry_component['audience_rule'] = audience_rule | {'user_group_rule': audience_rule}

    if component_sub_type not in ['TRIGGER_ONLY_A', 'TRIGGER_A_NOT_B']:
        return new_component_node

    # 进入事件规则
    event_obj = component_node['component_info']['event']
    event_rule = await _translate_event_rule(llm=llm, rule_desc=event_obj['event_A_desc'])
    entry_component['do_entry_rule'] = {
        "event_rule": event_rule
    }

    # 翻译 time window
    time_window = event_obj.get('time_window_desc', None)
    if component_sub_type == 'TRIGGER_A_NOT_B':
        time_window = await _translate_time_window(llm=llm, time_window=time_window)
        entry_component['do_entry_rule']['match_window'] = time_window

    # 翻译 event_B_desc
    if component_sub_type == 'TRIGGER_A_NOT_B':
        event_not_do = await _translate_event_rule(llm=llm, rule_desc=event_obj.get('event_B_desc', None))
        if event_not_do:
            entry_component['not_do_entry_rule'] = {
                "match_window": time_window,
                "event_rule": event_not_do
            }
    return new_component_node


async def _translate_time_window(llm: BaseLLM, time_window: str) -> dict | None:
    if not time_window:
        return None
    messages = [
        {"role": "system", "content": TIME_WINDOW_PROMPT.replace("__TIME_WINDOW_DESC__", time_window)},
        {"role": "user", "content": "请按要求的格式和内容等给出时间窗口结果："}
    ]
    result = await _llm_chat(llm=llm, messages=messages)
    try:
        time_window = json.loads(extract_code_from_text(result))
        return time_window
    except Exception as e:
        log.warning(f"json load time_window error.", e)
        return None


async def _translate_event_rule(llm: BaseLLM, rule_desc: str) -> dict | None:
    if not rule_desc:
        return None
    prompt = rule_desc
    agent = AudienceRuleCreateAgent(
        project_info=None,
        llm=llm,
    )
    try:
        result = await agent.generate_audience_rule_from_nl_desc(
            task_input=GenAudienceRuleFromNLDescTaskInput(
                nl_audience_rule=prompt
            ),
        )
        log.info(f"event_rule: {result}")
        if result.generated:
            if result.audience_rule.event_rule is not None:
                return json.loads(result.audience_rule.event_rule.model_dump_json())
            elif result.audience_rule.event_sequence_rule is not None:
                return json.loads(result.audience_rule.event_sequence_rule.model_dump_json())
            else:
                return {}
        else:
            raise RuntimeException(result.invalid_descriptions)
    except BaseException as e:
        log.warning(f"translate event rule failed, use default rule.", e)
        return {}


async def _translate_audience(llm: BaseLLM, audience_rule: dict) -> dict:
    fixed_time_str = audience_rule.get('fixed_time')
    cron = audience_rule.get('cron', '')

    if fixed_time_str:
        translated_time = await _translate_time_point(llm=llm, time_point_desc=fixed_time_str)
        fixed_time = await _fix_static_time(translated_time)
        audience_type = "AUDIENCE_ONETIME"
    else:
        fixed_time = ''
        audience_type = "AUDIENCE_ROUTINE"

    new_audience_rule = {
        "audience_type": audience_type,
        "fixed_time": fixed_time,
        "cron": cron,
    }
    audience_rule = audience_rule['audience_rule']

    agent = AudienceRuleCreateAgent(
        project_info=None,
        llm=llm,
    )
    try:
        result = await agent.generate_audience_rule_from_nl_desc(
            task_input=GenAudienceRuleFromNLDescTaskInput(
                nl_audience_rule=audience_rule
            ),
        )
        log.info(f"audience_rule: {result}")
        if result.generated:
            json_code = result.audience_rule.model_dump_json()
            audience_rule = json.loads(json_code)
            new_audience_rule['audience_rule'] = audience_rule | {'user_group_rule': audience_rule}
            return new_audience_rule
        else:
            raise RuntimeException(result.invalid_descriptions)
    except BaseException as e:
        log.warning(f"audience rule generate failed, use default rule.", e)
        new_audience_rule['audience_rule'] = {'select_all': True}
        return new_audience_rule


async def _translate_canvas_info(llm: BaseLLM, canvas_info: dict) -> dict:
    log.info("start translate canvas information.")
    quiet_hour_desc = canvas_info.get('do_not_disturb_desc', None)
    if quiet_hour_desc:
        canvas_info['do_not_disturb'] = await _translate_quiet_config(
            llm=llm,
            canvas_info=canvas_info,
            quiet_config_desc=quiet_hour_desc,
        )
    re_enter_desc = canvas_info.get('re_enter_desc', None)
    if re_enter_desc:
        canvas_info['re_enter'] = await _translate_re_enter_config(
            llm=llm,
            re_enter_desc=re_enter_desc,
        )

    active_time_range = canvas_info.get('active_time_range', None)
    if active_time_range:
        active_time_range = await _translate_time_range(
            llm=llm,
            time_range=active_time_range,
        )
        canvas_info['active_time_range'] = active_time_range

    canvas_info['finished_time'] = await _translate_time_point(
        llm=llm,
        time_point_desc=canvas_info.get('finished_time', None)
    )

    return canvas_info


async def _translate_re_enter_config(llm: BaseLLM, re_enter_desc: Any) -> dict:
    if not re_enter_desc:
        return {"reentry_strategy": "REENTRY_NO"}
    user_prompt = RE_ENTRY_USER_PROMPT.replace('__RE_ENTER_DESC__', re_enter_desc)
    messages = [
        {"role": "system", "content": RE_ENTER_PROMPT},
        {"role": "user", "content": user_prompt}
    ]

    for i in range(retry_times):
        response = await _llm_chat(llm=llm, messages=messages)
        messages.append({"role": "assistant", "content": response})
        response = my_tools.extract_code_from_text(response)
        try:
            response = json.loads(response)
            return response
        except Exception as e:
            log.warning(f"json load re-entry error. retry={i}", e)
            messages.append({"role": "user", "content": "你回复的格式不正确，请重新回复！"})
    raise RuntimeError('get re-entry config error.')


async def _translate_time_range(llm: BaseLLM, time_range: dict) -> dict:
    return {
        "start_time": await _translate_time_point(llm=llm, time_point_desc=time_range.get('start_time', None)),
        "end_time": await _translate_time_point(llm=llm, time_point_desc=time_range.get('end_time', None)),
    }


async def _translate_time_point(llm: BaseLLM, time_point_desc: Any) -> dict:
    user_prompt = TIME_POINT_USER_PROMPT.replace('__INPUT__', f"{time_point_desc}")
    messages = [
        {"role": "system", "content": TIME_POINT_PROMPT},
        {"role": "user", "content": user_prompt}
    ]

    for i in range(retry_times):
        response = await _llm_chat(llm=llm, messages=messages)
        messages.append({"role": "assistant", "content": response})
        response = my_tools.extract_code_from_text(response)
        try:
            response = json.loads(response)
            return response
        except Exception as e:
            log.warning(f"json load response error. retry={i}", e)
            messages.append({"role": "user", "content": "你回复的格式不正确，请重新回复！"})
    raise RuntimeError('get time point config error.')


async def _translate_quiet_config(llm: BaseLLM, canvas_info: dict, quiet_config_desc: Any) -> dict:
    user_prompt = QUIET_USER_PROMPT
    user_prompt = user_prompt.replace('__CANVAS_DRAFT__', json.dumps(canvas_info, indent=4, ensure_ascii=False))
    user_prompt = user_prompt.replace('__QUIENT_DESC__', quiet_config_desc)
    messages = [
        {"role": "system", "content": QUIET_HOUR_PROMPT},
        {"role": "user", "content": user_prompt}
    ]
    for i in range(retry_times):
        quiet_hour = await _llm_chat(llm=llm, messages=messages)
        messages.append({"role": "assistant", "content": quiet_hour})
        quiet_hour = my_tools.extract_code_from_text(quiet_hour)
        try:
            quiet_hour = json.loads(quiet_hour)
            return quiet_hour
        except Exception as e:
            log.warning(f"json load quiet_hour error. retry={i}", e)
            messages.append({"role": "user", "content": "你回复的格式不正确，请重新回复！"})
    raise RuntimeError('get quiet config error.')


@retry(
    stop=stop_after_attempt(retry_times),
    retry=retry_if_exception_type(BaseException),
    wait=wait_fixed(5),
    reraise=True,
)
async def _llm_chat(llm: BaseLLM, messages: list[dict]):
    response = await llm.acomplete(input=messages)
    return response.content
