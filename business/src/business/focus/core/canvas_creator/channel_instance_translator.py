import json
from typing import Any

from jinja2 import Template
from tenacity import stop_after_attempt, retry_if_exception_type, wait_fixed, retry

from business.context import request_cdp_context, request_project_context
from framework.prompt import PromptFactory
from common import tools
import requests

from framework.models import BaseLLM

select_prompt = """请根据当前触达通道的内容或者其他的描述，从下面的触达通道列表中选择一个最匹配的触达通道的 id。

触达通道列表：
```json
{{channel_instances}}
```

你必须回复触达通道列表中的 id 字段的值，并且将 id 的值包含在一个 markdown 代码块中，比如：
```
16
```
你的回复中只能最多有一个 markdown 代码块。
"""

async def get_channel_instance_select_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="CHANNEL_INSTANCE_SELECT_PROMPT",
        prompt_name="触达通道选择提示词",
        prompt_desc="触达通道选择提示词",
        prompt_content=select_prompt,
    )
    return template

async def translate_channel_instance(llm: BaseLLM, channel_instance_desc: Any) -> dict:
    channel_instance_desc = str(channel_instance_desc)
    selected_channel_instance = await _select_channel_instance(llm=llm, channel_instance_desc=channel_instance_desc)
    if not selected_channel_instance:
        return {}


async def _select_channel_instance(llm: BaseLLM, channel_instance_desc: str) -> dict | None:
    channel_instances = await _get_channel_instances()
    if not channel_instances:
        return {}

    prompt_template = await get_channel_instance_select_prompt()
    prompt = prompt_template.render(channel_instances=json.dumps(channel_instances, ensure_ascii=False, indent=4))
    messages = [
        {'role': 'system', 'content': prompt},
        {'role': 'user',
         'content': f"触达通道描述信息或者内容信息如下：\n{channel_instance_desc}\n\n请按要求回复通道id："}
    ]
    response = await _llm_chat(llm=llm, messages=messages)
    response = tools.extract_code_from_text(response)
    for channel_instance in channel_instances:
        if str(channel_instance['id']) == response:
            return channel_instance
    return None


async def _get_channel_instances():
    cdp_info = request_cdp_context.get()
    cdp_host = cdp_info.cdp_host
    url = f"{cdp_host}/api/v3/focus/v1/express-action-channel/channel/instance/query"
    response = requests.get(
        url=url,
        headers=await _get_openapi_headers(),
    ).json()['data']['channel_instance_informations']
    return response


async def _get_openapi_headers():
    cdp_info = request_cdp_context.get()
    project_context = request_project_context.get()
    headers = {
        'api-key': cdp_info.api_key,
        'sensorsdata-project': project_context.project_name,
        'X-Organization-Id': project_context.org_id
    }
    return headers


@retry(
    stop=stop_after_attempt(3),
    retry=retry_if_exception_type(BaseException),
    wait=wait_fixed(5),
    reraise=True,
)
async def _llm_chat(llm: BaseLLM, messages: list[dict]):
    response = await llm.acomplete(input=messages)
    return response.content
