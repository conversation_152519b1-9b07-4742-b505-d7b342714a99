from pathlib import Path
from typing import Callable, List

from llama_index.core.workflow import Workflow
from common import tools
from ..canvas_insight.schemas import StrategyIds
from framework.models import BaseLLM, ModelsFactory
from framework.config import knowledge_config
from .workflow import StrategyInsightWorkflow

log = tools.get_logger()


class StrategyInsightClient:
    """策略数据解读客户端"""

    def __init__(
            self,
            base_dir: str = knowledge_config['data_root'],
            regenerate: bool = False,
            llm: BaseLLM = ModelsFactory.get_llm(),
            callback: Callable[[str], None] = None,
            **kwargs,
    ):
        self.base_path = Path(base_dir, 'custom')
        self.regenerate = regenerate
        self.llm = llm
        self.callback = callback
        self.kwargs = kwargs

        if not self.base_path.exists():
            self.base_path.mkdir(parents=True, exist_ok=True)

    def _output_dir(self, organization: str, project: str) -> Path:
        output_dir = self.base_path.joinpath(organization, project, 'strategies')
        if not output_dir.exists():
            output_dir.mkdir(parents=True)
        return output_dir

    def _get_workflow(
            self,
            host: str,
            token: str,
            organization: str,
            project: str,
            strategy_ids: StrategyIds = "ALL",
    ) -> Workflow:
        return StrategyInsightWorkflow(
            host=host,
            token=token,
            organization=organization,
            project=project,
            strategy_ids=strategy_ids,
            llm=self.llm,
            output_dir=self._output_dir(organization, project),
            regenerate=self.regenerate,
            callback=self.callback,
        )

    async def insight(
            self,
            host: str,
            token: str,
            organization: str,
            project: str,
            strategy_ids: StrategyIds = "ALL",
    ) -> str:
        """执行策略数据解读"""
        flow = self._get_workflow(host, token, organization, project, strategy_ids)
        try:
            result = await flow.run()
            log.info(f'strategy insight result: {result}')
            return result
        except Exception as e:
            log.error(f'strategy insight error: {e}')
            return f"策略洞察失败：{e}"

    def get_insight_report_dir(self, organization: str, project: str) -> str:
        """获取策略解读报告目录"""
        return str(self._output_dir(organization, project).absolute())
