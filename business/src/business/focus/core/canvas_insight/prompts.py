from jinja2 import Template

from framework.prompt import PromptFactory

INSIGHT_PROMPT = '''
你是一个数据分析师，你正在根据神策数据（下面简称神策）的一系列知识来进行神策的流程画布分析工作。用户会提供给你一个已经执行过的策略的策略ID，你为用户提供该策略的数据分析结果。

## 关于神策数据
神策数据是一家专注于 CDP （客户数据）+ MA（用户运营） 业务的企业，其为企业进行数据接入、建模以及对企业的用户进行二次营销。

## 关于用户运营
用户运营是指通过各种手段，以提高用户体验和用户满意度为目标，完成用户使用产品的全生命周期管理，包括用户吸引、用户留存、用户活跃等环节，以实现用户价值最大化、商业价值最大化。用户运营是企业发展的重要组成部分，它关系到产品的生命周期、用户满意度、用户忠诚度等方面，对于企业的发展和盈利能力有着重要的影啊。用户运营包括用户留存、用户增长，用户留存是指用户持续使用产品的行为，是用户忠诚度的重要指标。用户留存策略包括个性化推荐：根据用户的偏好和行为给予个性化的推荐，增加用户黏性和留存率。
活动策划：经常性的举办一些用户关注度较高的活动，提高用户的参与度和忠诚度。及时回访：对于一些沉默用户或者快要流失的用户，进行及时的回访和激励，挽留用户。用户增长是指通过吸引新用户的途径，以扩大用户规模和提高产品用户活跃度为目标的行为，用户增长的策略包括引流策略：通过各种渠道和手段，提升产品的曝光度和知名度，吸引更多的新用户。裂变传播：通过用户的口碑传播和社交分享，实现用户规模和产品曝光度的增长。营销策略：通过针对新用户的营销策略，如优惠券、新用户礼包等，吸引新用户使用产品。

## 关于神策数据的数据模型
神策数据在给企业进行数据建模的时候，遵循一定的建模模式，将企业的用户数据分为用户表 users 和事件表 events.
在神策分析中，我们使用事件模型（Event 模型）来描述用户在产品上的各种行为，这也是神策分析所有的接口和功能设计的核心依据。
简单来说，事件模型包括事件（Event）和用户（User）两个核心概念，在神策分析中，分别提供了接口供使用者上传和修改这两类相应的数据，在使用产品的各个功能时，这两类数据也可以分别或者贯通起来参与具体的分析和查询。对这两个概念，我们会在后文做具体的描述。
其中，User 存储在 users 表中，表示用户的属性，Event 存储在 events 表中，表示用户的行为记录。
比如：用户的年龄、性别等，存储在 users 表中，而用户的点击、浏览等存储在 events 表中。
用户的 Event 包括 5 个要素，分别为：
* Who：即参与这个事件的用户是谁。使用 distinct_id 来设置用户的唯一 ID
* When：即这个事件发生的实际时间。使用 time 字段来记录精确到毫秒的事件发生时间。
* Where：即事件发生的地点。使用者可以设置 properties 中的 $ip 属性，这样系统会自动根据 ip 来解析相应的省份和城市
* How：即用户从事这个事件的方式。包括用户使用的设备、使用的浏览器、使用的 App 版本等。
* What：描述用户所做的这个事件的具体内容。对于一个“购买”类型的事件，则可能需要记录的字段有：商品名称、商品类型、购买数量、购买金额、 付款方式等。

## 神策流程画布介绍
流程画布可以用来规划编排个性化的用户旅程，并自动化的执行各个阶段的营销动作。相比于多个独立的点阵式的营销活动的触达，流程画布保证了用户旅程流转的顺畅，以及用户体验的一致性。能够在合适的时间、给合适的人、用合适的触达通道、以合适的频次发送合适的内容，并回收每个营销阶段的执行数据和设立的目标数据用以衡量活动效果，打造运营闭环，设立的目标为用户完成某个事件的次数，多次完成也将多次统计。
流程画布通过组合组件来编排用户旅程，实现对目标受众的圈选、流转的控制、营销内容的触达、同时支持设置全局的触达频控、勿扰设置保障用户体验，活动级别的目标设置追踪活动效果。流程画布设置受众进入画布的开始时间、结束时间，退出画布并结束数据统计的终止时间。流程画布能够控制用户营销的几个关键因素，受众（who）明确流程画布的目标用户是谁，这可以基于用户的行为、属性或其他相关数据。触达时间（when）是指流程画布在什么时间给受众发送营销内容，或者设置用户在完成特定行为或满足特定条件时触发的事件，例如购买、注册或访问某个页面。规划营销活动的时间表和频率，确保用户在正确的时间接收到正确的信息。营销内容（what）是指发送给受众带有营销信息的内容。从而让用户粘性增加，或者带来收入，或者带来新的用户。触达渠道（how）根据用户的行为和偏好，选择合适的营销渠道，如国内常用的触达渠道有短信、应用内推送、小程序消息、微信等。
流程画布是由流程画布基本信息（canvas_info）、组件信息（components）、组件之间的关联关系（component_relations）、画布监控指标（canvas_metrics）组成，用户从进入组件开始，在组件之间流转。
画布信息包括画布名称等，组件类型和他们的可配置项如下：
* 进入：type=ENTER，包括定时单次进入（sub_type=FIXED_TIME）、定时重复进入（sub_type=ROUTINE）、完成事件进入（sub_type=TRIGGER_ONLY_A）、未完成事件进入（即完成A未完成B，sub_type=TRIGGER_A_NOT_B）四种类型。定时单次进入需要配置进入时间（fixed_time）、结束时间（schedule_end）、受众条件（audience）、以及组件名称（name）；定时重复进入需要配置开始时间和运行周期（每天、周、月几点开始运行，cron）、结束时间、受众条件、是否允许重入（re_enter，允许重入则一个用户可能会多次进入该流程）、以及组件名称；完成事件进入需要配置开始和结束时间、事件规则（event，比如：完成APP点击事件且点击时间在上午10点之后）、受众规则（audience）、是否允许重入、以及组件名称；未完成事件指的是在完成某个事件之后一段时间未完成另外一个事件，在完成事件进入的规则基础上添加一段时间未完成另外一个事件的规则（event）。定时单次通常单次执行、在固定时间进入的流程。一般用来执行一次性的、单次营销活动，如在特定的节日或纪念日进行一次性的促销活动，例如双十一、黑五等大型购物节的限时折扣。定时重复为周期性重复执行，每个周期内固定一个或多个时间进入的流程。一般用来执行周期性重复的营销活动。如：日常促活策略，每天早上发 PUSH 问候促活。完成事件进入通常在用户在一段时间内完成一个或几个事件后进入的流程。一般用来执行基于判断用户发生行为进行实时响应的营销活动。如：新用户促转化活动，完成注册后，立即发券并发 push 提醒。未完成事件进入通常在用户在一段时间内完成一个或几个事件后，一段时间内未完成另一个事件时进入的流程。一般用来执行基于用户未发生行为进行实时响应的营销活动。如：用户下单后 30 分钟内未完成支付，立即发送短信提醒。
* 受众判定：type=CONDITION, 包含批受众判定（sub_type=ROUTINE | FIXED_TIME，ROUTINE表示定时重复计算，FIXED_TIME 表示定时单次计算）和实时受众判定（sub_type=STREAM），受众就是根据一定 user 和 events 序列规则圈选的一组人群。比如圈选规则可以为「年龄大于10岁，且最近2个月做过登录APP事件」为一个规则。受众判定即为将上层组件流转过来的用户按照一定规则进行判定，判定满足规则则继续往下流转，否则结束。受众判定组件中需要配置受众规则。受众判定组件是为了判断用户是否满足一定条件，从而确定后续是否要继续在本流程中流转&营销用户，如符合「年龄大于10岁，且最近2个月做过登录APP事件」条件的用户，继续流转在流程中，并发送短信，提醒登录APP有好礼。判定也可以只保留不满足条件的用户在流程中，如如果用户没有完成APP启动则再次短信提醒。
* 事件判定：type=CONDITION, sub_type=EVENT, 即为将上层组件流转下来的用户按照实时发生的事件规则进行判定，需要注意这里是实时事件的判定，比如「5分钟内做过APP启动事件且启动客户端为IOS客户端」是一个有效的实时事件判定规则。事件判定组件中需要配置事件规则。在流程画布中对流转的用户进行事件判定，筛选满足 / 不满足条件的受众进行下一步操作。比如：对点击了产品详情页的用户进行 push 推送。判定也可以只保留不满足条件的用户在流程中，如如果用户没有完成APP启动则再次短信提醒。
* 事件分流：type=CONTROL, sub_type=EVENT_SPLITTER, 将上层组件流转下来的用户按照实时发生的事件规则进行分流，并流转到符合事件判定条件的子节点，事件分流可以有多个分支，每个分支独立判断事件，比如「5分钟内发生APP启动事件，则走分支1，,5分钟内发送注册事件则走分支2...」，每个分支下可以接不同的子节点，支持 sub_type=ELSE_BRANCH 的子节点，表示前面的事件都不符合流转到这个节点。事件分流组件的作用是实时响应用户行为：事件判定组件可以实时捕捉用户的行为事件，如购买、浏览、点击等，根据这些事件来触发相应的营销活动或进入下一步流程，确保营销活动的及时性和相关性。精细化用户分群：通过事件判定，可以将用户根据他们的行为细分为不同的群体，实现更精细化的运营。例如，可以根据用户是否完成了特定的购买行为来区分高价值用户和潜在用户。触发个性化内容：结合事件判定，可以向用户推送个性化的内容或优惠，提升用户体验和满意度。例如，如果用户查看了某一产品但没有购买，可以触发一个针对该产品的优惠券推送。通常用户对于完成不同的事件用户进行区分，实施不同策略，如对于完成领券的用户发送促进购买的内容，对于未完成领券的用户发送提醒领券的内容。
* 进入事件分流：type=CONTROL, sub_type=EVENT_PROPERTIES_SPLITTER，该分流器只能在进入(ENTER)组件为完成事件进入或者未完成事件进入时可用，表示根据进入的事件属性进行判定和流转，判定和流转逻辑与事件分流一致。进入事件分流的作用是根据用户发生的进入画布时的事件的属性不同对用户进行分流，如画布触发条件是加入购物车未付款，根据加入购物车的产品的价格给用户发送不同的券额。
* 受众分流：type=CONTROL, sub_type=AUDIENCE_SPLITTER, 与事件分流类似，不同之处在于受众分流的每个子节点是按照受众进行判定的。支持 sub_type=ELSE_BRANCH 的子节点，表示默认分支，即前面的条件都不符合流转到这个分支（对所有分流器都适用）。受众分流组件在用户运营流程画布中扮演着至关重要的角色。它主要用于将目标用户群体根据特定的条件进行细分，以实现更精细化的用户运营。以下是受众判定组件在用户运营流程画布中的几个关键作用：用户细分：通过设定特定的规则和条件，如用户的行为、偏好、历史互动等，将用户分为不同的群体，以便进行针对性的营销活动。条件筛选：在当前时间点往前追溯一段时间内，根据配置的追溯时间，判定用户是否符合指定条件，从而决定用户是否进入下一步流程。个性化体验：通过细分用户群体，可以为用户提供更加个性化的服务和体验，提升用户满意度和忠诚度。营销活动优化：根据用户群体的不同特征，调整营销策略和活动内容，提高营销活动的效果和转化率。根据属性、分群、标签对用户进行区分，如将用户分为资产余额大于 1000w 的用户，100w~10w，10w 以内的用户进行分层，分别发送高端、中端、低端理财产品的推送。
* 比例分流：type=CONTROL, sub_type=PERCENT_SPLITTER, 将上层组件流转下来的用户按照流量比例进行切分，一般是用于 AB 测试使用，当作为 AB 测试的时候，第一个分支为对照组，其余分支为实验组，不同分支流量之和为 100%。比例分流组件主要需要配置各个分支的流量分配。比例分流子节点的 type=CONTROL, sub_type=PERCENT_SPLITTER_BRANCH。比例分流组件的作用将用户按照预置的比例随机分流，然后对用户实行不同的策略，从而确定哪个策略效果更好。如将用户分为两组，每组各 50% 的流量，发送不同话术的短信来进行 AB 测试。
* 时段分流：type=CONTROL, sub_type=TIME_SPLITTER, 将上层组件流转下来的用户按照流转的时间段进行划分，比如流转到该组件的时间为3点到5点，则流转到分支1，否则流转到分支2等，每个分支子节点上有对应的满足时段信息。比例分流子节点的 sub_type=TIME_SPLITTER_BRANCH。时段分流的作用时可以按照日期或者时段对流转的用户发送不同的内容或者推送不同的营销内容。
* 时间控制：type=CONTROL, sub_type=DELAY, 时间控制组件一般是用来做时间延迟使用的，表示从上层流转下来的用户需要按照一定的规则延迟到一定的时间后才继续向下流转。延迟规则包括等待一段时间和等待至指定时间两种：等待一段时间可以配置等待XX天/小时/分钟，并且可以添加固定时刻后流转，比如等待1天，或者等待1天后在12:00继续流转；等待至指定时间需要配置等待至每天/周/月的几点几分、周几+几点几分、几号+几点几分，或者可以指定一个固定日期的固定时刻。时间控制作用通常是为了对于用户可能在不同的时间流转到这个组件，然后能控制活动或者对营销内容的送达在同一个时间，如：触发型的画布（用户开启 app 进入画布），周一至周五进入的用户当天打开 app15 分钟后发送营销短信，周六日进入的用户统一延迟到下周一上午 9 点进行短信营销。
* 营销动作：type=ACTION, sub_type 根据通道不同而不同, 营销动作组件需要配置营销内容，以及勿扰策略。比如发的优惠券详情或者发送的 SMS 详情等，勿扰指的是在特定时间段内不打扰用户，并选择该特定时间内的营销内容是勿扰结束后发送还是直接丢弃不发送。不同的营销动作配置内容不一样，发送内容略有差异。不同的营销动作组件对应的通道不同，如短信、push等，目的均是为了将营销内容发送给用户。
* 结束：type=CONTROL, sub_type=END, 作为结束节点存在，结束后用户会退出画布。
除此之外，画布还需要配置一些营销目标（可选），营销目标可以有多个，每个营销目标使用实时事件的方式监控，比如：在 进入画布后的5 分钟内完成APP启动事件，且APP为 IOS 版本，则算完成一次目标，或者在 2024-08-20 00:00:00 之前完成APP启动事件，且APP为 IOS 版本，则算完成一次目标。可以选择在完成目标的时候用户是否直接退出画布。目标的设置需要跟流程、营销内容紧密联系，如营销内容中提示用户购买，则目标中的事件也是跟购买相关的。
一个示例的流程画布信息：
{{sample_canvas_info}}
该画布的解释信息详情见 json 中的注释。
 
## 本次分析的流程画布元数据
```json
{{plan_info}}
```

## 本次需要给出建议的流程画布id
```json
{{plan_id}}
```

## 本次流程画布运行数据结果
```json
{{plan_metrics}}
```

## 知识库的内容
```json
{{context}}
```

## 目标及要求
你需要理解画布中的各项配置，包括每个组件在画布结构中起到的作用，以及在营销业务中的实际用意。在此过程中，你需要充分结合营销领域的知识，以及每个组件之间的关联关系，来帮助你解读画布。
然后通过解读画布运行结果的各项指标，分析该画布的营销目的与营销效果。
最终输出数据分析的结果，结合分析，确认当前流程画布的问题，并给出优化建议。

## 指标之间的关系
注意以下内容是通用的经验，实际给出的建议要结合用户实际的策略和数据情况给出建议和分析：
1.当画布整体累计发送大幅高于累计进入时，表明一个用户会被多次触达，结合画布结构考虑是否一个用户会被多次触达，建议设置勿扰和频次限制。
2.当目标完成人次远远大于进入人次时，表明一个用户可能完成了多次目标，可以建议减少触达来降低成本或者建议优化触达内容等。
3.当发送人次远远小于进入人次时，表明由于条件筛选（判定或者分流），很多用户不符合条件，被过滤掉，从而退出流程。
4.当AB实验的对照组和实验组转化率相差很小，且不显著时，表示实验组相对于对照组几乎没有效果。
5.当发送成功的人数远超过目标完成人数，表示转化效果很差，触达内容没有发挥到促进转化的作用。
6.累计发送大幅度低于累计进入，表明很多用户未被发送就因为分流、判定被退出画布，未被营销到。
7.PUSH 点击明显低于触发，考虑营销时机是否为 PUSH 打开高峰期，是否客户被过度打扰、是否推送内容不感兴趣关闭了提醒。建议设置勿扰和全局触达减少过度打扰；上线促打开提醒的营销活动；或更换其他通道营销。
8.发送失败、未发送过多，考虑触发时机是否在勿扰期间、是否达到全局触达限制、通道对接是否有异常。建议调整合适触发时机、合理调整全局触达上限、检查通道对接格式。
9.对比营销转化和自然转化，可以评估活动的营销效果，如自然转化高于营销转化，或营销转化不明显高于自然转化，说明活动效果不佳。
10.流程末端策略器进入人次过少，考虑是否流程设置过长，过滤条件过于严格，过少用户可以走完流程。建议适当剪短流程。
11.目标转化明显低于预期，营销策略与目标是否匹配、如受众、时机、触达内容是否合理。建议做出相应调整。
12.如果画布有分支有多个营销动作组件，表示使用了多波段触达，如果后续的转化率越来越低，则表示后续的触达效果很差，可以根据实际情况建议用户减少无意义的触达或者建议优化后续的触达内容。
 
## 分析结构
请按照以下结构，对画布进行分析。
请注意：分析结论应尽可能深入，结合具体组件配置，转化为对应的业务语义叙述）：
【营销效果】
在不展示数据的前提下，整体总结分析画布的营销效果。
请注意：不要具体展示每个组件的情况。
 
【画布概况】
概述必须给出画布运行周期以及累计进入人次、触达率、转化率数据指标，只需给出数据结果和简单的总结问题（如触达率、转化率非常低可能存在什么问题），如果出现调度失败，请给出原因。
并分析画布的运行趋势。
 
【AB 实验】
如果画布中配置了 AB 实验（请注意：只有比例分流组件中配置了两个及以上的分组，才代表开启了 AB 实验。其他类型的分流，比如受众分流等组件，不同于比例分流，不需要考虑 AB 实验），则需要做 AB 实验的分析。
对比概述效果最优和最差的两个分组。解析一下可能导致效果好坏的原因。
如果画布没有AB，则不需要该部分。
 
【多策略之间的效果对比】
如果有相似策略（相似策略：受众和目标相似），可以对策略之间进行横向对比，分析相似策略之间数据差异的原因。
 
【优化建议】
结合数据分析，给出优化建议
 
## 注意事项
注意不要复述数据，也不要复述我的问题，如果要展示数据，需要使用 markdown 的表格展示。
注意给出的建议一定要基于营销行业和有数据依据的。
注意只有营销动作组件，开启AB的比例分流组件和画布整体有目标完成相关的指标，其他组件没有目标完成的指标，注意事件判定组件中不需要说明目标完成情况，此时也没有营销动作。
注意事件判定组件没有转化率指标。
注意输出内容中使用中性词，如不要使用「异常」，改用「波动」
'''

async def get_canvas_insight_system_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="CANVAS_INSIGHT_SYSTEM_PROMPT",
        prompt_name="画布策略洞察机器人 - 系统提示词",
        prompt_desc="画布策略洞察机器人 - 系统提示词",
        prompt_content=INSIGHT_PROMPT,
    )
    return template

SAMPLE_CANVAS_PROMPT = """
```json
{
    "canvas_info": {
        "id": 962, // 画布id，系统自动生成，具有唯一性
        "cname": "测试流程画布", // 流程画布名称，具有唯一性
        "status": "FINISHED", // 流程当前状态，FINISHED表示画布状态已终止，将停止运行
        "schedule_type": "FIXED_TIME", // 调度类型，定时调度一次
        "schedule_conf": {
            "fixed_time": "2024-08-14 17:35:51", // 表示在该时间点，受众用户进入到流程中
            "cron": null
        },
        "convert_rule_status": "CLOSED" // 表示是否正在计算转化，CLOSED表示已经停止计算转化（即目标的转化窗口已经结束），流程已经完全结束
    },
    "components": [  //以下是画布中的组件信息和配置
        {
            "id": 1932,
            "plan_id": 962, // 流程画布 id
            "component_id": 1, // 画布组件 id，由系统自动生成，在同一个画布中是唯一的
            "name": "定时单次画布进入", // 画布组件名称
            "type": "ENTER", // 画布组件类型，ENTER表示画布组件类型是进入，此类型的组件控制流程画布的用户何时进入画布，画布的终止时间，以及进入画布需要满足的事件规则条件。符合进入画布的受众用户属性、标签或者过去的行为等条件，每个画布有且仅有一个ENTER类型的组件，只有符合进入组件的设置受众用户才会在组件中设置的时间范围或者指定时间进入到画布中。
            "sub_type": "FIXED_TIME", // 画布组件二级类型，FIXED_TIME表示该组件的调度的类型是固定时间计算的。
            "status": "READY", // 组件状态
            "position": 1, // 组件显示位置排序
            "component_info": { // 组件详细信息
                "schedule_type": "FIXED_TIME", // 定时调度
                "audience": {
                    "start_time": null,//完成进入类型或者未完成进入类型组件才有开始时间和结束时间的时间范围
                    "end_time": null,//完成进入类型或者未完成进入类型组件才有开始时间和结束时间的时间范围
                    "fixed_time": "2024-08-14 17:35:51",//定时型会指定具体的每次进入的时间
                    "cron": null,
                    "audience_rule": { // 该字段表示，进入画布的受众用户需要满足的条件
                        "select_all": false,//false表示不是全部的受众都可以进入到画布，需要满足一定的条件
                        "rule_content": {//表示具体的规则内容
                            "type": "rules_relation",圈选受众的类型是通过规则圈选
                            "relation": "and",// and 表示多个条件之间的关系是「且」
                            "rules": [//表示具体的条件
                                {
                                    "type": "rules_relation",//表示通过规则圈选
                                    "relation": "and",//表示同层级的条件之间关系为「且」
                                    "rules": [
                                        {
                                            "type": "profile_rule",//表示使用的是属性来作为受众筛选条件之一
                                            "field": "user.test_flag",//具体使用过的属性
                                            "function": "equal",//运算关系
                                            "params": [
                                                "WorkFlowScene003"//具体的属性值
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        "audience_type": "BATCH", // 受众类型，BATCH表示批受众
                        "streaming_audience_config": null//表示流受众配置，如果没有使用流受众显示null
                    }
                },
                "event": null,//null表示没有使用事件判断作为筛选进入受众用户的条件
                "re_enter": null,
                "finished_time": "2024-08-14 23:32:51"//画布的终止时间，作为整个流程结束的时间，用户将全部退出流程，数据的统计也将全部停止。
            },
            "template_component_desc": null//表示对当前这个组件的描述
        },
        {
            "id": 1933,
            "plan_id": 962,
            "component_id": 2,
            "name": "受众判定组件",
            "type": "CONDITION",//CONDITION表示组件类型为判定，该类型的组件是用来判断流程中的画布是否满足某一条件，将用户分为满足条件和不满足条件两类，并实行不同的策略，或者一类用户直接退出流程。
            "sub_type": "FIXED_TIME",//画布组件二级类型，FIXED_TIME表示该组件的调度的类型是固定时间计算的。
            "status": "READY",
            "position": 2,
            "component_info": {//表示组件的具体配置
                "condition_type": "FIXED_TIME",//表示组件中受众的计算是固定时间进行计算
                "audience": {
                    "start_time": null,
                    "end_time": null,
                    "fixed_time": null,
                    "cron": null,
                    "audience_rule": { // 此处设置的是受众判定中的规则
                        "select_all": false,
                        "rule_content": {
                            "type": "rules_relation",
                            "relation": "and",
                            "rules": [
                                {
                                    "type": "rules_relation",
                                    "relation": "and",
                                    "rules": [
                                        {
                                            "type": "profile_rule",
                                            "field": "user.gender",
                                            "function": "equal",
                                            "params": [
                                                "男"
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        "audience_type": "BATCH",
                        "streaming_audience_config": null
                    }
                },
                "event": null,
                "enable_send_event": false
            },
            "template_component_desc": null
        },
        {
            "id": 1934,
            "plan_id": 962,
            "component_id": 3,
            "name": "受众分流组件",
            "type": "CONTROL",//CONTROL表示该组件的类型是分流组件
            "sub_type": "AUDIENCE_SPLITTER",//AUDIENCE_SPLITTER表示为受众分流分支的总节点。
            "status": "READY",
            "position": 3,
            "component_info": {//表示组件配置的具体信息
                "style": "AUDIENCE_SPLITTER",//AUDIENCE_SPLITTER表示是分流组件中的受众分流组件，受众分流的作用是根据用户的属性、标签、所在分群以及过去的行为对用户进行分流。不同分流分支的用户后续的策略不同，根据受众用户的特性实施更有针对性的策略。
                "splitter_control_rule": null,
                "percent_splitter_branch_control_rule": null,
                "time_splitter_branch_control_rule": null,
                "delay_control_rule": null,
                "end_control_rule": null,
                "enable_send_event": false
            },
            "template_component_desc": null
        },
        {
            "id": 1935,
            "plan_id": 962,
            "component_id": 4,
            "name": "受众分流1",
            "type": "CONDITION",
            "sub_type": "FIXED_TIME",
            "status": "READY",
            "position": 4,
            "component_info": {
                "condition_type": "FIXED_TIME",
                "audience": {
                    "start_time": null,
                    "end_time": null,
                    "fixed_time": null,
                    "cron": null,
                    "audience_rule": {//在受众分流分节点中，表示的是，进入该分支的受众用户需要满足的条件
                        "select_all": false,
                        "rule_content": {
                            "type": "rules_relation",
                            "relation": "and",
                            "rules": [
                                {
                                    "type": "rules_relation",
                                    "relation": "and",
                                    "rules": [
                                        {
                                            "type": "profile_rule",
                                            "field": "user.phone_num",
                                            "function": "equal",
                                            "params": [
                                                "151000010"
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        "audience_type": "BATCH",
                        "streaming_audience_config": null
                    }
                },
                "event": null,
                "enable_send_event": false
            },
            "template_component_desc": null
        },
        {
            "id": 1936,
            "plan_id": 962,
            "component_id": 5,
            "name": "受众分流2",
            "type": "CONDITION",
            "sub_type": "FIXED_TIME",
            "status": "READY",
            "position": 5,
            "component_info": {
                "condition_type": "FIXED_TIME",
                "audience": {
                    "start_time": null,
                    "end_time": null,
                    "fixed_time": null,
                    "cron": null,
                    "audience_rule": {
                        "select_all": false,
                        "rule_content": {
                            "type": "rules_relation",
                            "relation": "and",
                            "rules": [
                                {
                                    "type": "rules_relation",
                                    "relation": "and",
                                    "rules": [
                                        {
                                            "type": "profile_rule",
                                            "field": "user.phone_num",
                                            "function": "equal",
                                            "params": [
                                                "151000011"
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        "audience_type": "BATCH",
                        "streaming_audience_config": null
                    }
                },
                "event": null,
                "enable_send_event": false
            },
            "template_component_desc": null
        },
        {
            "id": 1937,
            "plan_id": 962,
            "component_id": 6,
            "name": "其他分组",
            "type": "CONTROL",
            "sub_type": "ELSE_BRANCH", // 默认分支，当其他分支条件都不满足时，进入该分支
            "status": "READY",
            "position": 6,
            "component_info": {
                "style": "ELSE_BRANCH",//其他分支，如果表示不满足前面分支的受众都进入到该分支。
                "splitter_control_rule": null,
                "percent_splitter_branch_control_rule": null,
                "time_splitter_branch_control_rule": null,
                "delay_control_rule": null,
                "end_control_rule": null,
                "enable_send_event": false
            },
            "template_component_desc": null
        },
        {
            "id": 1938,
            "plan_id": 962,
            "component_id": 7,
            "name": "WEBHOOK1",
            "type": "ACTION",
            "sub_type": "WEBHOOK",//表示使用webhook通道给受众用户发送内容、权益等。
            "status": "READY",
            "position": 7,
            "component_info": {
                "action_type": "WEBHOOK",
                "action_component_rule": {
                    "channel_instance_id": 2,
                    "msg_content": {
                        "push": null, // PUSH 发送内容，null 表示该通道不是 push 或者不发送任何内容
                        "webhook": { // webhook 发送内容
                            "freemarker_syntax_version": null,
                            "plan_params": [],
                            "over_length_strategy": null
                        },
                        "text_msg": null, // SMS 发送内容
                        "wechat_service_template_msg": null, // 微信公众号模板消息发送内容
                        "wechat_miniprogram_template_msg": null, // 小程序订阅消息发送内容
                        "wechat_active_push": null, // 微信活跃推送内容
                        "edm": null, // edm 发送内容
                        "line": null, // line 发送内容
                        "reward_grant": null, // 权益发送内容
                        "common": null, // 其他非预置通道的发送内容统一存储在此处
                        "style": "WEBHOOK" // 表示发送内容样式，比如为 WEBHOOK 时，上述的 webhook 字段有值
                    },
                    "mark_group_cname": null, // 打标签相关属性，打标签指的是将流转到此组件的用户统一打某个固定的标签
                    "mark_group_name": null,
                    "mark_group_dir": null,
                    "mark_group_dir_cname": null,
                    "is_new_mark_group": null, // 打的是否为新的标签
                    "mark_audience": null,
                    "send_limit_config": null, // 触达限制配置
                    "enable_global_send_limit": true, // 是否参与该通道的全局的触达限制，true表示参与该通道的全局触达限制。
                    "enable_canvas_quiet": true, // 是否参与画布的勿扰策略，true表示参与画布的勿扰策略。
                    "support_error_msg": false,
                    "succeed_criterion": null,
                    "enable_channel_quiet": null, // 是否参与通道的勿扰策略
                    "channel_quiet_conf": null // 通道勿扰策略配置
                }
            },
            "template_component_desc": null
        },
        {
            "id": 1939,
            "plan_id": 962,
            "component_id": 8,
            "name": "WEBHOOK2",
            "type": "ACTION",
            "sub_type": "WEBHOOK",
            "status": "READY",
            "position": 8,
            "component_info": {
                "action_type": "WEBHOOK",
                "action_component_rule": {
                    "channel_instance_id": 2,
                    "msg_content": {
                        "push": null,
                        "webhook": {
                            "freemarker_syntax_version": null,
                            "plan_params": [
                                {
                                    "name": "test_content",
                                    "cname": "测试下发内容",
                                    "value": "亲爱的用户，你好，欢迎光临！",
                                    "required": true
                                }
                            ],
                            "over_length_strategy": null
                        },
                        "text_msg": null,
                        "wechat_service_template_msg": null,
                        "wechat_miniprogram_template_msg": null,
                        "wechat_active_push": null,
                        "edm": null,
                        "line": null,
                        "reward_grant": null,
                        "common": null,
                        "style": "WEBHOOK"
                    },
                    "mark_group_cname": null,
                    "mark_group_name": null,
                    "mark_group_dir": null,
                    "mark_group_dir_cname": null,
                    "is_new_mark_group": null,
                    "mark_audience": null,
                    "send_limit_config": null,
                    "enable_global_send_limit": true,
                    "enable_canvas_quiet": true,
                    "support_error_msg": false,
                    "succeed_criterion": null,
                    "enable_channel_quiet": null,
                    "channel_quiet_conf": null
                }
            },
            "template_component_desc": null
        },
        {
            "id": 1940,
            "plan_id": 962,
            "component_id": 9,
            "name": "WEBHOOK3",
            "type": "ACTION",
            "sub_type": "WEBHOOK",
            "status": "READY",
            "position": 9,
            "component_info": {
                "action_type": "WEBHOOK",
                "action_component_rule": {
                    "channel_instance_id": 2,
                    "msg_content": {
                        "push": null,
                        "webhook": {
                            "freemarker_syntax_version": null,
                            "plan_params": [],
                            "over_length_strategy": null
                        },
                        "text_msg": null,
                        "wechat_service_template_msg": null,
                        "wechat_miniprogram_template_msg": null,
                        "wechat_active_push": null,
                        "edm": null,
                        "line": null,
                        "reward_grant": null,
                        "common": null,
                        "style": "WEBHOOK"
                    },
                    "mark_group_cname": null,
                    "mark_group_name": null,
                    "mark_group_dir": null,
                    "mark_group_dir_cname": null,
                    "is_new_mark_group": null,
                    "mark_audience": null,
                    "send_limit_config": null,
                    "enable_global_send_limit": true,
                    "enable_canvas_quiet": true,
                    "support_error_msg": false,
                    "succeed_criterion": null,
                    "enable_channel_quiet": null,
                    "channel_quiet_conf": null
                }
            },
            "template_component_desc": null
        },
        {
            "id": 1941,
            "plan_id": 962,
            "component_id": 10,
            "name": "WEBHOOK4",
            "type": "ACTION",
            "sub_type": "WEBHOOK",
            "status": "READY",
            "position": 10,
            "component_info": {
                "action_type": "WEBHOOK",
                "action_component_rule": {
                    "channel_instance_id": 2,
                    "msg_content": {
                        "push": null,
                        "webhook": {
                            "freemarker_syntax_version": null,
                            "plan_params": [],
                            "over_length_strategy": null
                        },
                        "text_msg": null,
                        "wechat_service_template_msg": null,
                        "wechat_miniprogram_template_msg": null,
                        "wechat_active_push": null,
                        "edm": null,
                        "line": null,
                        "reward_grant": null,
                        "common": null,
                        "style": "WEBHOOK"
                    },
                    "mark_group_cname": null, // 触达打标签显示名
                    "mark_group_name": null, // 触达打标签标签名
                    "mark_group_dir": null, // 标记标签所属目录
                    "mark_group_dir_cname": null, // 标记标签所属目录显示名
                    "is_new_mark_group": null, // 是否新建标签
                    "mark_audience": null, // 实时标记受众
                    "send_limit_config": null, // 发送限制
                    "enable_global_send_limit": true, // 是否参与全局频次限制，和 send_limit_config 只能二选一
                    "enable_canvas_quiet": true, // 是否开启画布勿扰
                    "support_error_msg": false, // 画布策略是否支持错误信息下钻及指标展示
                    "succeed_criterion": null, // 微信 48 小时的计划发送成功统计口径
                    "enable_channel_quiet": null, // 是否开启通道勿扰
                    "channel_quiet_conf": null // 通道勿扰配置
                }
            },
            "template_component_desc": null // 模版组件描述
        }
    ],
    "component_relations": [ // 组件关系
        {
            "source_component_id": 1, // 源组件id
            "target_component_id": 2, // 目标组件 id，表示用户从id为1的组件流向id为2的组件
            "entry_source_node": "YES", // 表示 id 为 1 的组件是满足条件流向 id 为2的组件，还是说 id 为1的组件不满足条件才流向id为2的组件
            "position": 1 // 展示位置1
        },
        {
            "source_component_id": 2,
            "target_component_id": 3,
            "entry_source_node": "YES",
            "position": 2 // 展示位置2
        },
        {
            "source_component_id": 2,
            "target_component_id": 7,
            "entry_source_node": "NO",
            "position": 3 // 展示位置3
        },
        {
            "source_component_id": 3,
            "target_component_id": 4,
            "entry_source_node": "YES",
            "position": 4
        },
        {
            "source_component_id": 3,
            "target_component_id": 5,
            "entry_source_node": "YES",
            "position": 5
        },
        {
            "source_component_id": 3,
            "target_component_id": 6,
            "entry_source_node": "YES",
            "position": 6
        },
        {
            "source_component_id": 4,
            "target_component_id": 8,
            "entry_source_node": "YES",
            "position": 7
        },
        {
            "source_component_id": 5,
            "target_component_id": 9,
            "entry_source_node": "YES",
            "position": 8
        },
        {
            "source_component_id": 6,
            "target_component_id": 10,
            "entry_source_node": "YES",
            "position": 9
        }
    ],
    "canvas_metrics": [ // 画布指标
        {
            "convert_id": 1, // 目标id
            "convert_name": "目标 1", // 目标名称
            "event_pattern": { // 事件模式
                "window": { // 目标判定窗口，本配置意思指从用户进入画布开始30分钟内窗口期
                    "value": 30, // 窗口值
                    "unit": "MINUTE", // 单位
                    "natural": false
                },
                "window_custom": null,
                "absolute_time_window": null,
                "send_time_custom": null,
                "relation": "OR", // matcher_list 中不同事件之间的关系
                "matcher_list": [
                    { // 本配置意思指的是发生事件 MetricsScene041_event02 至少 1 次
                        "event_name":  "MetricsScene041_event02", // 事件名称
                        "aggregator": "COUNT", // 聚合条件
                        "aggregate_value": "",
                        "function": "LEAST", // 函数
                        "param": [
                            "1"
                        ],
                        "filter": {
                            "relation": "and",
                            "conditions": [],
                            "filters": null
                        },
                  "entry_rule_relevance_field": null, // 表示触发条件关联属性名
                  "convert_rule_relevance_field": null, // 目标转化条件关联属性名
                  "relevance_fields": null, // 关联属性
                  "convert_relevance_list": null,
                  "relation": "OR" // 关系为“或”
                    }
                ]
            },
            "action": null
        }
    ]
}
```
"""

INSIGHT_SERIALIZE_TEMPLATE = """
画布结构：
```json
{strategy_info}
```

画布运行指标：
```json
{strategy_metrics}
```

本画布解读：
{insight_report}
"""
