import json
from pathlib import Path
from typing import Optional, Callable, List

import requests
from llama_index.core.workflow import Workflow, step, StartEvent, StopEvent
from llama_index.core.workflow.service import ServiceManager
from tenacity import retry, stop_after_attempt, retry_if_exception_type, wait_fixed
from inspect import iscoroutinefunction

from common import tools
from framework.models import BaseLLM
from framework.data.document import SensorsDocMetadata, SensorsDocsMetadata
from .prompts import *
from ..canvas_common.tools.strategy_info import StrategyInfo
from .schemas import (
    NextStrategyStepEvent, LoadHistoryStepEvent, GetInfoStepEvent,
    InsightStepEvent, StoreStepEvent, IndexStepEvent, StrategyIds
)

log = tools.get_logger()


@retry(
    stop=stop_after_attempt(5),
    retry=retry_if_exception_type(BaseException),
    wait=wait_fixed(10),
    reraise=True,
)
def _call_llm(llm: BaseLLM, system_prompt: str) -> str:
    return llm.complete(input={'role': 'system', 'content': system_prompt}).content


class StrategyInsightWorkflow(Workflow):
    """
    策略数据解读 workflow
    """

    def __init__(
            self,
            llm: BaseLLM,
            host: str,
            token: str,
            organization: str,
            project: str,
            output_dir: Path,
            strategy_ids: StrategyIds = "ALL",
            regenerate: bool = False,
            timeout: Optional[float] = 24 * 60 * 60,
            disable_validation: bool = False,
            verbose: bool = False,
            service_manager: Optional[ServiceManager] = None,
            num_concurrent_runs: Optional[int] = None,
            callback: Callable[[str], None] | None = None,
            **kwargs,
    ):
        super().__init__(
            timeout=timeout, disable_validation=disable_validation,
            verbose=verbose, service_manager=service_manager,
            num_concurrent_runs=num_concurrent_runs
        )
        self.llm = llm
        self.output_dir = output_dir
        if not self.output_dir.exists():
            self.output_dir.mkdir(parents=True, exist_ok=True)
        self.regenerate = regenerate
        self.strategy_info_client = StrategyInfo(host, token, organization, project, api_key=token)
        self.organization = organization
        self.project = project
        self._strategy_ids = strategy_ids
        if isinstance(self._strategy_ids, list):
            self._strategy_ids = sorted(self._strategy_ids, reverse=True)
        self.last_insight_report = None
        self.callback = callback
        self.kwargs = kwargs

    async def _callback(self, content: str):
        try:
            log.info(content)
            if not self.callback:
                return
            if iscoroutinefunction(self.callback):
                await self.callback(content)
            else:
                self.callback(content)
        except BaseException as e:
            log.warning('callback error.', e)

    @property
    def metadata_path(self) -> Path:
        return Path(self.output_dir, 'metadata.json')

    @property
    def data_meta(self) -> SensorsDocsMetadata:
        if not self.metadata_path.exists():
            return SensorsDocsMetadata(batch_id=0, documents=[])
        try:
            text = self.metadata_path.read_text(encoding='utf-8')
            return SensorsDocsMetadata.model_validate(json.loads(text))
        except BaseException as e:
            log.warning('get metadata error.', e)
            return SensorsDocsMetadata(batch_id=0, documents=[])

    def _get_history_insight(self, strategy_id: int) -> str | None:
        doc = self.data_meta.get_doc(id=str(strategy_id))
        if doc:
            report = Path(self.output_dir, doc.file_name)
            if report.exists():
                mark = '本画布解读：'
                content = report.read_text(encoding='utf-8')
                start_index = content.find(mark)
                return content[start_index + len(mark):].strip() if start_index != -1 else None
        return None

    @property
    def strategy_ids(self) -> List[int]:
        if isinstance(self._strategy_ids, list):
            return self._strategy_ids

        # load all strategies
        temp_strategy_ids = []
        request_url = f'{self.strategy_info_client.host}/api/v3/focus/v1/web/canvas/list'
        headers = {
            'api-key': self.strategy_info_client.api_key,
            'sensorsdata-project': self.strategy_info_client.project_name,
            'X-Organization-Id': self.strategy_info_client.organization_id
        }
        for i in range(1000):
            response = requests.post(
                url=request_url,
                headers=headers,
                json={
                    "page_filter": {
                        "limit": 1000,
                        "offset": i,
                        "total": 0
                    }
                })
            response = response.json()
            if 'SUCCESS' != response['code'].upper():
                log.error(f'get canvas list error. {response}')
                break
            canvases = response['data']['canvases']
            if len(canvases) == 0:
                break
            temp_strategy_ids.extend([can['id'] for can in canvases])
        self._strategy_ids = list(set(temp_strategy_ids))
        return self._strategy_ids

    @step
    async def start(
            self, event: StartEvent | NextStrategyStepEvent
    ) -> LoadHistoryStepEvent | GetInfoStepEvent | StopEvent:
        """开始构建"""
        if isinstance(event, StartEvent):
            log.info(f'start strategy insight workflow. {event.dict()}')
            await self._callback(f'开始进行数据解读, 共包含 {len(self.strategy_ids)} 个策略.')

        if not self.strategy_ids:
            self.metadata_path.write_text(self.data_meta.model_dump_json(indent=4), encoding='utf-8')
            await self._callback('全部策略解读完成')
            return StopEvent(result=self.last_insight_report)

        strategy_id = self.strategy_ids.pop()
        await self._callback(f'开始解读策略 id:{strategy_id}')
        if not self.regenerate:
            return GetInfoStepEvent(strategy_id=strategy_id)
        else:
            return LoadHistoryStepEvent(strategy_id=strategy_id)

    @step
    async def load_history(self, event: LoadHistoryStepEvent) -> NextStrategyStepEvent | GetInfoStepEvent:
        log.info(f'load history insight. {event.dict()}')
        await self._callback('加载历史解读报告...')
        insight_report = self._get_history_insight(event.strategy_id)
        if insight_report:
            self.last_insight_report = insight_report
            await self._callback(f'策略已经解读过，加载成功')
            return NextStrategyStepEvent()
        else:
            await self._callback(f'未找到历史报告，开始解读')
            return GetInfoStepEvent(**(event.dict()))

    @step
    async def get_info(self, event: GetInfoStepEvent) -> InsightStepEvent | NextStrategyStepEvent:
        log.info(f'get strategy info. {event.dict()}')
        await self._callback('加载策略数据...')
        strategy_info = self.strategy_info_client.get_info(event.strategy_id)

        if not strategy_info['canvas_info']:
            await self._callback('策略数据为空, 跳过')
            return NextStrategyStepEvent(**(event.dict()))
        else:
            await self._callback('策略数据加载完成')
            return InsightStepEvent(strategy_info=strategy_info, **(event.dict()))

    @step
    async def insight(self, event: InsightStepEvent) -> StoreStepEvent | NextStrategyStepEvent:
        log.info(f'generate strategy insight. {event.dict()}')
        await self._callback('执行数据解读...')

        try:
            system_prompt_template = await get_canvas_insight_system_prompt()
            insight_report = _call_llm(
                llm=self.llm,
                system_prompt=system_prompt_template.render(
                    sample_canvas_info=SAMPLE_CANVAS_PROMPT,
                    plan_info=json.dumps(event.strategy_info['canvas_info'], ensure_ascii=False, indent=4),
                    plan_id=event.strategy_id,
                    plan_metrics=json.dumps(event.strategy_info['canvas_metrics'], ensure_ascii=False, indent=4),
                    context=''
                ))
            if insight_report:
                await self._callback(f'数据解读完成')
            return StoreStepEvent(insight_report=insight_report, **(event.dict()))
        except BaseException as e:
            log.error('failed to generate strategy insight', e)
            await self._callback(f'数据解读失败')

        return NextStrategyStepEvent()

    @step
    async def store(self, event: StoreStepEvent) -> IndexStepEvent:
        log.info(f'store strategy insight result. {event.dict()}')

        strategy_path = Path(self.output_dir, f'strategy_insight_report_{event.strategy_id}.txt')
        strategy_path.write_text(INSIGHT_SERIALIZE_TEMPLATE.format(
            strategy_info=json.dumps(event.strategy_info['canvas_info'], ensure_ascii=False, indent=4),
            strategy_metrics=json.dumps(event.strategy_info['canvas_metrics'], ensure_ascii=False, indent=4),
            insight_report=event.insight_report,
        ))
        return IndexStepEvent(file=strategy_path, **(event.dict()))

    @step
    async def index(self, event: IndexStepEvent) -> NextStrategyStepEvent:
        log.info(f'update result file index. {event.dict()}')

        self.last_insight_report = event.insight_report
        data_meta = self.data_meta
        doc = SensorsDocMetadata(
            batch_id=data_meta.batch_id + 1,
            id=str(event.strategy_id),
            title=event.strategy_info['canvas_info']['cname'],
            file_name=event.file.name
        )
        data_meta.upsert_doc(doc)
        self.metadata_path.write_text(data_meta.model_dump_json(indent=4), encoding='utf-8')
        return NextStrategyStepEvent()
