import time
from pathlib import Path
from typing import Union, List, Any

from llama_index.core.workflow import Event

StrategyIds = Union[List[int], "ALL"]


class BaseEvent(Event):
    def __init__(self, start_time: int = time.time(), **kwargs):
        super().__init__(start_time=start_time, **kwargs)


class NextStrategyStepEvent(BaseEvent):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class LoadHistoryStepEvent(BaseEvent):
    def __init__(self, strategy_id: int, **kwargs):
        super().__init__(strategy_id=strategy_id, **kwargs)


class GetInfoStepEvent(BaseEvent):
    def __init__(self, strategy_id: int, **kwargs):
        super().__init__(strategy_id=strategy_id, **kwargs)


class InsightStepEvent(GetInfoStepEvent):
    def __init__(self, strategy_info: Any, **kwargs):
        super().__init__(strategy_info=strategy_info, **kwargs)


class StoreStepEvent(InsightStepEvent):
    def __init__(self, insight_report: str | None = None, **kwargs):
        super().__init__(insight_report=insight_report, **kwargs)


class IndexStepEvent(StoreStepEvent):
    def __init__(self, file: Path, **kwargs):
        super().__init__(file=file, **kwargs)
