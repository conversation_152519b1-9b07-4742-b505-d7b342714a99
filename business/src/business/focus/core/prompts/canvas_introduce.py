from jinja2 import Template

from framework.prompt import PromptFactory

CANVAS_INTRODUCE = """
### 神策流程画布介绍
流程画布可以用来规划编排个性化的用户旅程，并自动化的执行各个阶段的营销动作。相比于多个独立的点阵式的营销活动的触达，流程画布保证了用户旅程流转的顺畅，以及用户体验的一致性。能够在合适的时间、给合适的人、用合适的触达通道、以合适的频次发送合适的内容，并回收每个营销阶段的执行数据用以衡量活动效果，打造运营闭环。
流程画布通过组合组件来编排用户旅程，实现对目标受众的圈选、流转的控制、营销内容的触达。同时支持设置全局的触达频控、勿扰设置保障用户体验，活动级别的目标设置追踪活动效果。
流程画布是由流程画布基本信息（canvas_info）、组件信息（components）、组件之间的关联关系（component_relations）、画布监控指标等组成，用户从进入组件开始，在组件之间流转。
画布信息包括画布名称等，组件类型和他们的可配置项如下：
* 进入：type=ENTER，包括定时单次进入（sub_type=FIXED_TIME）、定时重复进入（sub_type=ROUTINE）、完成事件进入（sub_type=TRIGGER_ONLY_A）、未完成事件进入（即完成A未完成B，sub_type=TRIGGER_A_NOT_B）四种类型。定时单次进入需要配置进入时间（fixed_time）、结束时间（schedule_end）、受众条件（audience）、以及组件名称（name）；定时重复进入需要配置开始时间和运行周期（每天、周、月几点开始运行，cron）、结束时间、受众条件、以及组件名称；完成事件进入需要配置开始和结束时间、事件规则（event，比如：完成APP点击事件且点击时间在上午10点之后）、受众规则（audience）、是否允许重入、以及组件名称；未完成事件指的是在完成某个事件之后一段时间未完成另外一个事件，在完成事件进入的规则基础上添加一段时间未完成另外一个事件的规则（event）。
* 受众判定：type=CONDITION, 包含批受众判定（sub_type=ROUTINE | FIXED_TIME，ROUTINE表示定时重复计算，FIXED_TIME 表示定时单次计算），受众就是根据一定 user 和 events 序列规则圈选的一组人群。比如圈选规则可以为「年龄大于10岁，且最近2个月做过登录APP事件」为一个规则。受众判定即为将上层组件流转过来的用户按照一定规则进行判定，判定满足规则则继续往下流转，否则结束。受众判定组件中需要配置受众规则。
* 事件判定：type=CONDITION, sub_type=EVENT, 即为将上层组件流转下来的用户按照实时发生的事件规则进行判定，需要注意这里是实时事件的判定，比如「5分钟内做过APP启动事件且启动客户端为IOS客户端」是一个有效的实时事件判定规则。事件判定组件中需要配置事件规则。
* 事件分流：type=CONTROL, sub_type=EVENT_SPLITTER, 将上层组件流转下来的用户按照实时发生的事件规则进行分流，并流转到符合事件判定条件的子节点，事件分流可以有多个分支，分支节点为「事件判定」组件或者 sub_type=ELSE_BRANCH 的组件，每个分支独立判断事件，比如「5分钟内发生APP启动事件，则走分支1，,5分钟内发送注册事件则走分支2...」，sub_type=ELSE_BRANCH 的子节点表示前面的事件都不符合则流转到这个节点。
* 进入事件分流：type=CONTROL, sub_type=EVENT_PROPERTIES_SPLITTER，该分流器只能在进入(ENTER)组件为完成事件进入或者未完成事件进入时可用，表示根据进入的事件属性进行判定和流转，判定和流转逻辑与事件分流一致。
* 受众分流：type=CONTROL, sub_type=AUDIENCE_SPLITTER, 与事件分流类似，不同之处在于受众分流的每个子节点是按照受众进行判定的。子节点仅支持「受众判定」组件或者 sub_type=ELSE_BRANCH 的子节点。sub_type=ELSE_BRANCH 表示默认分支，即前面的条件都不符合流转到这个分支（对所有分流器都适用）。
* 比例分流：type=CONTROL, sub_type=PERCENT_SPLITTER, 将上层组件流转下来的用户按照流量比例进行切分，一般是用于 AB 测试使用，当作为 AB 测试的时候，第一个分支为对照组，其余分支为实验组，不同分支流量之和为 100%。比例分流组件主要需要配置各个分支的流量分配。比例分流子节点的 type=CONTROL, sub_type=PERCENT_SPLITTER_BRANCH
* 时段分流：type=CONTROL, sub_type=TIME_SPLITTER, 将上层组件流转下来的用户按照流转的时间段进行划分，比如流转到该组件的时间为3点到5点，则流转到分支1，否则流转到分支2等，每个分支子节点上有对应的满足时段信息。时段分流子节点的 sub_type=TIME_SPLITTER_BRANCH。
* 时间控制：type=CONTROL, sub_type=DELAY, 时间控制组件一般是用来做时间延迟使用的，表示从上层流转下来的用户需要按照一定的规则延迟到一定的时间后才继续向下流转。延迟规则包括等待一段时间和等待至指定时间两种：等待一段时间可以配置等待XX天/小时/分钟，并且可以添加固定时刻后流转，比如等待1天，或者等待1天后在12:00继续流转；等待至指定时间需要配置等待至每天/周/月的几点几分、周几+几点几分、几号+几点几分，或者可以指定一个固定日期的固定时刻。
* 营销动作：type=ACTION, sub_type 根据通道不同而不同, 营销动作组件需要配置营销内容，以及勿扰策略。比如发的优惠券详情或者发送的 SMS 详情等，勿扰指的是在特定时间段内不打扰用户，并选择该特定时间内的营销内容是勿扰结束后发送还是直接丢弃不发送。不同的营销动作配置内容不一样，发送内容略有差异。
* 结束：type=CONTROL, sub_type=END, 作为结束节点存在，结束后用户会退出画布。
  除此之外，画布还需要配置一些营销目标（可选），营销目标可以有多个，每个营销目标使用实时事件的方式监控，比如：在 5 分钟内完成APP启动事件，且APP为 IOS 版本，则算完成一次目标，或者在 2024-08-20 00:00:00 之前完成APP启动事件，且APP为 IOS 版本，则算完成一次目标。可以选择在完成目标的时候用户是否直接退出画布。

一个定时单次运行的流程画布配置及配置说明信息示例如下：
```json
{
    "canvas_info": {
        "cname": "测试流程画布", // 流程画布名称，需要符合流程画布的实际业务含义
        "schedule_type": "FIXED_TIME", // 调度类型，取值包括 FIXED_TIME | ROUTINE | TRIGGER_ONLY_A | TRIGGER_A_NOT_B
        "schedule_conf": {
            "fixed_time": "2024-08-14 17:35:51", // 表示在该时间点开始调度运行，当 schedule_type 为 FIXED_TIME 时，有值
            "cron": null // 当 schedule_type 为 ROUTINE 时，该字段为定时重复运行的 cron 表达式
        },
        "schedule_start": null, // 画布开始调度的时间，即画布开始运行时间，当 schedule_type 为 FIXED_TIME 时，该值为 null
        "schedule_end": null, // 画布结束运行时间，超过该时间画布指标则不再继续统计，当 schedule_type 为 FIXED_TIME 时，该值为 null
        "re_enter": { // 用户进入画布的重入限制，如果不重复，则为 null
            "type": "AFTER_EXITING", // 重入规则，取值包括 AFTER_EXITING | ANY_TIME，分别表示用户退出当前画布后可以再次进入画布，和用户随时可进入画布
            "value": 1, // 重入限制的时间窗口数值，与时间窗口单位一起表示时间窗口，比如 1 DAY
            "unit": "DAY", // 重入限制的时间窗口单位，取值包括 MINUTE | HOUR | DAY | WEEK | MONTH
            "limit": 10 // 在时间窗口内可以重入多少次的限制，此处表示：用户在1天内最多可以重入画布10次。
        },
        "quiet_hour": { // 画布勿扰配置，即在画布上面的防打扰配置，可以在 ACTION 组件上选择是画布勿扰策略是否对当前组件生效
            "repeat_cycle": "DAY", // 勿扰重复策略，可取值包括：DAY | WEEK，分别表示按天、按周设置勿扰
            "quiet_hour": [
                { // 表示从当天 23:00 到第二天 08:00 属于勿扰时段
                    "start_time": "23:00",
                    "end_time": "08:00",
                    "day": null // 当 repeat_cycle 为 WEEK 时，day 表示一周中的第几天
                }
            ],
            "action": "DROP" // 勿扰期间的消息处理方式，可取值包括：DROP | DELAY | DROP_AND_NOT_FINISH，分别表示放弃发送且退出当前流程画布、延迟到勿扰时段结束发送、放弃发送且不退出当前流程画布
        }
    },
    "components": [
        {
            "component_id": 1, // 画布组件 id
            "name": "定时单次画布进入", // 画布组件名称
            "type": "ENTER", // 画布组件类型
            "sub_type": "FIXED_TIME", // 画布组件二级类型
            "position": 1, // 组件显示位置排序
            "component_info": { // 进入组件详细配置
                "schedule_type": "FIXED_TIME", // 定时调度
                "audience": { // 组件的受众规则配置
                    "start_time": null, // 开始调度时间，与 canvas_info 中的 schedule_start 保持一致
                    "end_time": null, // 结束调度时间，与 canvas_info 中的 schedule_end 保持一致
                    "fixed_time": "2024-08-14 17:35:51", // 当 canvas_info 的 schedule_type 为 FIXED_TIME 时有值，表示受众计算时间，与 canvas_info.schedule_conf.fixed_time 保持一致
                    "cron": null, // 与 canvas_info.schedule_conf.cron 保持一致
                    "audience_rule": { // 受众人群的圈选规则，示例中规则表示用户（user）的 test_flag 属性等于 WorkFlowScene003，且一天内发生过（$PlanConverted >= 1次 或 Audience001_event01 >= 2次），且一天内依次发生过 $AppUnInstall 和 ABcanvas012_event01 事件
                        "select_all": false, // false 表示非全部人都为受众，为 true 表示所有人都是受众，当为 true 时，下面 rule_content 为 null
                        "rule_content": { // 圈选规则
                            "type": "rules_relation", // 固定值为 rules_relation
                            "relation": "and", // 表示 rules 中的规则之间的关系，取值包括 and | or
                            "rules": [
                                { // 「用户属性」类型的圈选规则，如果不需要则为空
                                    "type": "profile_rule", // 表示为用户属性类规则
                                    "relation": "and", // 表示 rules 中的规则之间的关系，取值包括 and | or
                                    "rules": [ // rules 中包含  rules 表示是递归嵌套结构，可以使用递归嵌套自由组合各个 rule 的 and/or 关系等
                                        {
                                            "type": "profile_rule",
                                            "field": "user.test_flag", // user.xxx 表示用户的属性
                                            "function": "equal", // 表示用户的属性与 params 之间的关系为等于，其他取值包括：equal | least | greater | less | less_equal | not_equal 分别表示等于、大于等于（最少）、大于、小于、小于等于、不等于
                                            "params": [ // 表示 field function 的值，比如 test_flag 属性等于 WorkFlowScene003，params 有多个值表示 field 的值可以为多个值中的其中一个
                                                "WorkFlowScene003"
                                            ]
                                        }
                                    ]
                                },
                                { // 「事件」类型的圈选规则，如果不需要则为空
                                    "type": "rules_relation",
                                    "relation": "and",
                                    "rules": [
                                        {
                                            "type": "rules_relation",
                                            "relation": "or", // 表示 rule 中的规则为 "或" 关系
                                            "rules": [
                                                { // 表示发生1天内发生了 $PlanConverted 事件至少1次
                                                    "measure": { // 表示事件的结构，仅 event_name 字段有效，其他的都是固定值
                                                        "aggregator": "general", // measure 中的固定值 general
                                                        "field": "", // measure 中的固定值
                                                        "type": "event_measure", // measure 中的固定值
                                                        "event_name": "$PlanConverted" // 具体的事件名称
                                                    },
                                                    "type": "event_rule", // 表示为事件规则
                                                    "time_function": "relative_time", // 取值包括：relative_time | absolute_time，分别表示相对当前的时间，绝对时间
                                                    "time_params": [ // 当 time_function 为 absolute_time 时，则 time_params 为两个绝对时间点，格式 YYYY-MM-DD
                                                        "1 day"
                                                    ],
                                                    "function": "least", // 表示事件的发生关系，取值包括：equal | least | greater | less | less_equal | not_equal | top_n | absolute_between 分别表示事件的发生次数等于、大于等于（最少）、大于、小于、小于等于、不等于、topN、在xxx之间 params 次
                                                    "params": [ // 当 function 为 absolute_between 时，params 有两个值（日期时间类型，精确到分钟，YYYY-MM-DD HH:mm），表示在这两个值之间
                                                        1.0
                                                    ],
                                                    "filters": [ // 可选的事件筛选器结构，表示事件满足该筛选器条件才能被 function 计算
                                                        {
                                                            "type": "filters_relation", // 固定字段取值
                                                            "relatin": "and", // 表示 subfilters 的各个 filter 取值关系
                                                            "subfilters": [
                                                                {
                                                                    "field": "event.$PlanConverted.$sf_plan_id", // 事件属性名，如 event.$PlanConverted.$sf_plan_id，其中：event 为固定前缀，$PlanConverted 表示当前事件，$sf_plan_id 为具体的属性名称
                                                                    "type": "filter", // 固定值为 filter
                                                                    "function": "equal", // 表示属性 field 与值 params 的关系，取值包括：equal | least | greater | less | less_equal | not_equal | absolute_between | is_set | not_set | is_null | not_null 分别表示事件的属性等于、大于等于（最少）、大于、小于、小于等于、不等于、在xxx之间、是否有值、是否为空等
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                },
                                                { // 表示发生1天内发生了 $PlanConverted 事件至少2次
                                                    "measure": {
                                                        "aggregator": "general",
                                                        "field": "",
                                                        "type": "event_measure",
                                                        "event_name": "Audience001_event01"
                                                    },
                                                    "type": "event_rule",
                                                    "time_function": "absolute_time",
                                                    "time_params": [
                                                        "2024-08-10",
                                                        "2024-08-15"
                                                    ],
                                                    "function": "least",
                                                    "params": [
                                                        2.0
                                                    ],
                                                    "filters": [] // 可选的事件筛选器
                                                }
                                            ]
                                        }
                                    ]
                                },
                                { // 「事件序列」类型的圈选规则，表示用户在一段时间内依次发生过一系列事件，如果不需要则为空
                                    "type": "rules_relation",
                                    "relation": "and",
                                    "rules": [
                                        {
                                            "type": "event_sequence_rule",
                                            "time_function": "relative_time",
                                            "time_params": [
                                                "1 day"
                                            ],
                                            "steps": [
                                                {
                                                    "event": "$AppUnInstall"
                                                },
                                                {
                                                    "event": "ABcanvas012_event01",
                                                    "filters": []
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        "audience_type": "BATCH" // 受众类型，批受众，固定值
                    }
                },
                "event": null, // 如果 ENTER 组件是 TRIGGER_ONLY_A、TRIGGER_A_NOT_B 类型，则需要配置 event 事件规则
                "finished_time": "2024-08-14 23:32:51"
            }
        },
        {
            "component_id": 2,
            "name": "受众判定组件",
            "type": "CONDITION",
            "sub_type": "FIXED_TIME",
            "position": 2,
            "component_info": {
                "audience": {
                    "start_time": null,
                    "end_time": null,
                    "fixed_time": null,
                    "cron": null,
                    "audience_rule": { // 该规则表示 user.gender 即用户性别为男的人
                        "select_all": false,
                        "rule_content": {
                            "type": "rules_relation",
                            "relation": "and",
                            "rules": [
                                {
                                    "type": "rules_relation",
                                    "relation": "and",
                                    "rules": [
                                        {
                                            "type": "profile_rule",
                                            "field": "user.gender",
                                            "function": "equal",
                                            "params": [
                                                "男"
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        "audience_type": "BATCH"
                    }
                }
            }
        },
        {
            "component_id": 3,
            "name": "受众分流组件",
            "type": "CONTROL",
            "sub_type": "AUDIENCE_SPLITTER", // 受众分流的父组件
            "position": 3,
            "component_info": { // 各个分支的详细配置在下级子节点组件中
                "style": "AUDIENCE_SPLITTER"
            }
        },
        {
            "component_id": 4,
            "name": "受众分流1",
            "type": "CONDITION",
            "sub_type": "FIXED_TIME",
            "position": 4,
            "component_info": {
                "audience": {
                    "start_time": null,
                    "end_time": null,
                    "fixed_time": null,
                    "cron": null,
                    "audience_rule": {
                        "select_all": false,
                        "rule_content": {
                            "type": "rules_relation",
                            "relation": "and",
                            "rules": [
                                {
                                    "type": "rules_relation",
                                    "relation": "and",
                                    "rules": [
                                        {
                                            "type": "profile_rule",
                                            "field": "user.phone_num",
                                            "function": "equal",
                                            "params": [
                                                "151000010"
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        "audience_type": "BATCH"
                    }
                },
                "event": null
            }
        },
        {
            "component_id": 5,
            "name": "受众分流2",
            "type": "CONDITION",
            "sub_type": "FIXED_TIME",
            "position": 5,
            "component_info": {
                "audience": {
                    "start_time": null,
                    "end_time": null,
                    "fixed_time": null,
                    "cron": null,
                    "audience_rule": {
                        "select_all": false,
                        "rule_content": {
                            "type": "rules_relation",
                            "relation": "and",
                            "rules": [
                                {
                                    "type": "rules_relation",
                                    "relation": "and",
                                    "rules": [
                                        {
                                            "type": "profile_rule",
                                            "field": "user.phone_num",
                                            "function": "equal",
                                            "params": [
                                                "151000011"
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        "audience_type": "BATCH"
                    }
                },
                "event": null
            }
        },
        {
            "component_id": 6,
            "name": "其他分组",
            "type": "CONTROL",
            "sub_type": "ELSE_BRANCH", // 默认分支，当其他分支条件都不满足时，进入该分支
            "position": 6,
            "component_info": {
                "style": "ELSE_BRANCH"
            }
        },
        {
            "component_id": 7,
            "name": "WEBHOOK1",
            "type": "ACTION",
            "sub_type": "WEBHOOK", // 可以根据情况确定，比如 SMS / WEBHOOK / PUSH / EDM 等，具体取值可以参考用户给出的通道大类
            "position": 7,
            "component_info": {
                "action_type": "WEBHOOK", // 与 sub_type 保持一致
                "action_component_rule": {
                    "channel_instance_id": 2,
                    "msg_content": {
                        "push": null, // PUSH 发送内容，null 表示该通道不是 push 或者不发送任何内容
                        "webhook": { // webhook 发送内容
                            "freemarker_syntax_version": null,
                            "plan_params": [
                                {
                                    "name": "test_content",
                                    "cname": "测试下发内容",
                                    "value": "亲爱的用户，你好，欢迎光临！",
                                    "required": true
                                }
                            ],
                            "over_length_strategy": null
                        },
                        "text_msg": null, // SMS 发送内容
                        "wechat_service_template_msg": null, // 微信公众号模板消息发送内容
                        "wechat_miniprogram_template_msg": null, // 小程序订阅消息发送内容
                        "wechat_active_push": null, // 微信活跃推送内容
                        "edm": null, // edm 发送内容
                        "line": null, // line 发送内容
                        "reward_grant": null, // 权益发送内容
                        "common": null, // 其他非预置通道的发送内容统一存储在此处
                        "style": "WEBHOOK" // 表示发送内容样式，比如为 WEBHOOK 时，上述的 webhook 字段有值
                    },
                    "mark_group_cname": null, // 打标签相关属性，打标签指的是将流转到此组件的用户统一打某个固定的标签
                    "mark_group_name": null,
                    "mark_group_dir": null,
                    "mark_group_dir_cname": null,
                    "is_new_mark_group": null, // 打的是否为新的标签
                    "mark_audience": null,
                    "send_limit_config": null, // 触达限制配置
                    "enable_global_send_limit": true, // 是否参与该通道的全局的触达限制
                    "enable_canvas_quiet": true, // 是否参与画布的勿扰策略，为 true 表示画布的勿扰策略（quiet_hour）对当前的组件也生效
                    "support_error_msg": false,
                    "succeed_criterion": null,
                    "enable_channel_quiet": true, // 是否参与通道的勿扰策略
                    "channel_quiet_conf": { // 通道勿扰策略配置，即在通道上面的防打扰配置,与画布勿扰配置结构一样
                        "repeat_cycle": "DAY",
                        "quiet_hour": [
                            {
                                "start_time": "23:00",
                                "end_time": "08:00",
                                "day": null
                            }
                        ],
                        "action": "DROP"
                    }
                }
            }
        },
        {
            "component_id": 8,
            "name": "App 推送",
            "type": "ACTION",
            "sub_type": "PUSH",
            "position": 8,
            "component_info": {
                "action_type": "PUSH",
                "action_component_rule": {
                    "channel_instance_id": 3,
                    "msg_content": {
                        "push": { // 示例 PUSH 发送内容，不同通道的发送内容不一样，以用户提供的结构为准
                            "freemarker_syntax_version": null,
                            "title": "温馨提醒：您有一张理财券即将过期~",
                            "content": "尊敬的客户，您账户内有一张7日年华收益6.8%的新客理财券即将到期，点击使用>>",
                            "landing_type": "LINK",
                            "notification_icon": null,
                            "advanced_setting": {
                                "channel_id": "",
                                "enable": null,
                                "notification_style": {
                                    "style": "DEFAULT",
                                    "big_text": "",
                                    "big_picture": ""
                                },
                                "notification_tip": {
                                    "sound_type": "DEFAULT",
                                    "custom_sound": "",
                                    "vibrate": 1,
                                    "light": 1
                                },
                                "manufacturer": {
                                    "enable": false,
                                    "skip_quota": true,
                                    "enabled_manufacturer": [],
                                    "jiguang_distribution": "secondary_push",
                                    "getui_android": null,
                                    "third_party_channel": null
                                },
                                "message_ttl": {
                                    "enable": false,
                                    "ttl_second": 86400,
                                    "unit": "DAY"
                                },
                                "ios": {
                                    "sub_title": "",
                                    "badge_number": "",
                                    "thread_id": "",
                                    "notification_tip": {
                                        "sound_type": "NONE",
                                        "custom_sound": null,
                                        "vibrate": null,
                                        "light": null
                                    }
                                }
                            },
                            "over_length_strategy": "FULL",
                            "inapp_message": null
                        },
                        "webhook": null,
                        "text_msg": null,
                        "wechat_service_template_msg": null,
                        "wechat_miniprogram_template_msg": null,
                        "wechat_active_push": null,
                        "edm": null,
                        "line": null,
                        "reward_grant": null,
                        "common": null,
                        "style": "PUSH"
                    },
                    "webhook": null,
                    "text_msg": null,
                    "wechat_service_template_msg": null,
                    "wechat_miniprogram_template_msg": null,
                    "wechat_active_push": null,
                    "edm": null,
                    "line": null,
                    "reward_grant": null,
                    "common": null,
                    "style": "PUSH"
                },
                "mark_group_cname": null,
                "mark_group_name": null,
                "mark_group_dir": null,
                "mark_group_dir_cname": null,
                "is_new_mark_group": null,
                "mark_audience": null,
                "send_limit_config": null,
                "enable_global_send_limit": true,
                "enable_canvas_quiet": true,
                "support_error_msg": false,
                "succeed_criterion": null,
                "enable_channel_quiet": null,
                "channel_quiet_conf": null
            }
        },
        {
            "component_id": 9,
            "name": "WEBHOOK3",
            "type": "ACTION",
            "sub_type": "WEBHOOK",
            "position": 9,
            "component_info": {
                "action_type": "WEBHOOK",
                "action_component_rule": {
                    "channel_instance_id": 2,
                    "msg_content": {
                        "push": null,
                        "webhook": {
                            "freemarker_syntax_version": null,
                            "plan_params": [],
                            "over_length_strategy": null
                        },
                        "text_msg": null,
                        "wechat_service_template_msg": null,
                        "wechat_miniprogram_template_msg": null,
                        "wechat_active_push": null,
                        "edm": null,
                        "line": null,
                        "reward_grant": null,
                        "common": null,
                        "style": "WEBHOOK"
                    },
                    "mark_group_cname": null,
                    "mark_group_name": null,
                    "mark_group_dir": null,
                    "mark_group_dir_cname": null,
                    "is_new_mark_group": null,
                    "mark_audience": null,
                    "send_limit_config": null,
                    "enable_global_send_limit": true,
                    "enable_canvas_quiet": true,
                    "support_error_msg": false,
                    "succeed_criterion": null,
                    "enable_channel_quiet": null,
                    "channel_quiet_conf": null
                }
            }
        },
        {
            "component_id": 10,
            "name": "WEBHOOK4",
            "type": "ACTION",
            "sub_type": "WEBHOOK",
            "position": 10,
            "component_info": {
                "action_type": "WEBHOOK",
                "action_component_rule": {
                    "channel_instance_id": 2,
                    "msg_content": {
                        "push": null,
                        "webhook": {
                            "freemarker_syntax_version": null,
                            "plan_params": [],
                            "over_length_strategy": null
                        },
                        "text_msg": null,
                        "wechat_service_template_msg": null,
                        "wechat_miniprogram_template_msg": null,
                        "wechat_active_push": null,
                        "edm": null,
                        "line": null,
                        "reward_grant": null,
                        "common": null,
                        "style": "WEBHOOK"
                    },
                    "mark_group_cname": null,
                    "mark_group_name": null,
                    "mark_group_dir": null,
                    "mark_group_dir_cname": null,
                    "is_new_mark_group": null,
                    "mark_audience": null,
                    "send_limit_config": null,
                    "enable_global_send_limit": true,
                    "enable_canvas_quiet": true,
                    "support_error_msg": false,
                    "succeed_criterion": null,
                    "enable_channel_quiet": null,
                    "channel_quiet_conf": null
                }
            }
        }
    ],
    "component_relations": [
        {
            "source_component_id": 1, // 源组件id（component_id）
            "target_component_id": 2, // 目标组件 id，表示用户从id为1的组件流向id为2的组件
            "entry_source_node": "YES", // 表示 id 为 1 的组件是满足条件流向 id 为2的组件，还是说 id 为1的组件不满足条件才流向id为2的组件
            "position": 1 // 展示位置
        },
        {
            "source_component_id": 2,
            "target_component_id": 3,
            "entry_source_node": "YES",
            "position": 2
        },
        {
            "source_component_id": 2,
            "target_component_id": 7,
            "entry_source_node": "NO",
            "position": 3
        },
        {
            "source_component_id": 3,
            "target_component_id": 4,
            "entry_source_node": "YES",
            "position": 4
        },
        {
            "source_component_id": 3,
            "target_component_id": 5,
            "entry_source_node": "YES",
            "position": 5
        },
        {
            "source_component_id": 3,
            "target_component_id": 6,
            "entry_source_node": "YES",
            "position": 6
        },
        {
            "source_component_id": 4,
            "target_component_id": 8,
            "entry_source_node": "YES",
            "position": 7
        },
        {
            "source_component_id": 5,
            "target_component_id": 9,
            "entry_source_node": "YES",
            "position": 8
        },
        {
            "source_component_id": 6,
            "target_component_id": 10,
            "entry_source_node": "YES",
            "position": 9
        }
    ],
    "canvas_metrics": [
        {
            "convert_id": 1,
            "convert_name": "目标 1",
            "event_pattern": {
                "window": { // 目标判定窗口，本配置意思指从用户进入画布开始30分钟内窗口期
                    "value": 30,
                    "unit": "MINUTE",
                    "natural": false
                },
                "window_custom": null,
                "absolute_time_window": null,
                "send_time_custom": null,
                "relation": "OR", // matcher_list 中不同事件之间的关系
                "matcher_list": [
                    { // 本配置意思指的是发生事件 MetricsScene041_event02 至少 1 次
                        "event_name": "MetricsScene041_event02",
                        "aggregator": "COUNT",
                        "aggregate_value": "",
                        "function": "LEAST",
                        "param": [
                            "1"
                        ],
                        "filter": {
                            "relation": "and",
                            "conditions": [],
                            "filters": null
                        },
                        "entry_rule_relevance_field": null,
                        "convert_rule_relevance_field": null,
                        "relevance_fields": null,
                        "convert_relevance_list": null,
                        "relation": "OR"
                    }
                ]
            },
            "action": null
        }
    ]
}
```

另外一个发生A未发生B类型的事件判定类型的画布配置示例如下：
```json
{
    "canvas_info": {
        "cname": "测试流程画布1",
        "schedule_type": "TRIGGER_A_NOT_B",
        "schedule_conf": null,
        "schedule_start": "2024-08-07 10:25:00",
        "schedule_end": "2026-08-10 00:00:00.000000",
        "re_enter": null,
        "quiet_hour": null
    },
    "components": [
        {
            "component_id": 1,
            "name": "未完成事件进入",
            "type": "ENTER",
            "sub_type": "TRIGGER_A_NOT_B", // 触发 A 未触发 B 类型
            "position": 1,
            "component_info": {
                "schedule_type": "TRIGGER_A_NOT_B",
                "audience": { // 受众配置
                    "start_time": "2024-08-07 10:25:00",
                    "end_time": "2026-08-10 00:00:00",
                    "fixed_time": null,
                    "cron": null,
                    "audience_rule": {
                        "select_all": false,
                        "rule_content": {
                            "type": "rules_relation",
                            "relation": "and",
                            "rules": [
                                {
                                    "type": "rules_relation",
                                    "relation": "and",
                                    "rules": [
                                        {
                                            "type": "profile_rule",
                                            "field": "user.test_flag",
                                            "function": "equal",
                                            "params": [
                                                "zhangsan"
                                            ],
                                            "cname": "test_flag",
                                            "icon": ""
                                        }
                                    ]
                                }
                            ]
                        },
                        "audience_type": "BATCH",
                        "streaming_audience_config": null
                    }
                },
                "event": { // 事件触发类型的事件配置
                    "start_time": "2024-08-07 10:25:00",
                    "end_time": "2026-08-10 00:00:00",
                    "time_window": { // 事件判定窗口期
                        "value": 5,
                        "unit": "MINUTE",
                        "natural": false
                    },
                    "custom_time_window": null,
                    "event_do": { // 触发 A 的 A 事件配置，此处配置为发生事件 creaty_event 至少 1 次
                        "window": null,
                        "window_custom": null,
                        "absolute_time_window": null,
                        "send_time_custom": null,
                        "relation": "OR",
                        "matcher_list": [
                            {
                                "event_name": "creaty_event",
                                "aggregator": "COUNT",
                                "aggregate_value": "",
                                "function": "LEAST",
                                "param": [
                                    "1"
                                ],
                                "filter": {
                                    "relation": "and",
                                    "conditions": [],
                                    "filters": null
                                },
                                "entry_rule_relevance_field": null,
                                "convert_rule_relevance_field": null,
                                "relevance_fields": null,
                                "convert_relevance_list": null,
                                "relation": "OR"
                            }
                        ]
                    },
                    "event_then_not_do": { // 未发生事件 B 的 B 事件配置，此处为未发生 Action003_event01 至少 1 次
                        "window": null,
                        "window_custom": null,
                        "absolute_time_window": null,
                        "send_time_custom": null,
                        "relation": "OR",
                        "matcher_list": [
                            {
                                "event_name": "Action003_event01",
                                "aggregator": "COUNT",
                                "aggregate_value": "",
                                "function": "LEAST",
                                "param": [
                                    "1"
                                ],
                                "filter": {
                                    "relation": "and",
                                    "conditions": [],
                                    "filters": null
                                },
                                "entry_rule_relevance_field": null,
                                "convert_rule_relevance_field": null,
                                "relevance_fields": null,
                                "convert_relevance_list": null,
                                "relation": "OR"
                            }
                        ]
                    },
                    "relevance_fields": null,
                    "event_property_references": null
                },
                "re_enter": null,
                "finished_time": "2026-08-13 00:00:00"
            }
        },
        {
            "component_id": 2,
            "name": "WEBHOOK1",
            "type": "ACTION",
            "sub_type": "WEBHOOK",
            "position": 2,
            "component_info": {
                "action_type": "WEBHOOK",
                "action_component_rule": {
                    "channel_instance_id": 2,
                    "msg_content": {
                        "push": null,
                        "webhook": {
                            "freemarker_syntax_version": null,
                            "plan_params": [
                                {
                                    "name": "test_content",
                                    "cname": "测试下发内容",
                                    "value": "亲爱的用户，你好，欢迎光临！",
                                    "required": true
                                }
                            ],
                            "over_length_strategy": null
                        },
                        "text_msg": null,
                        "wechat_service_template_msg": null,
                        "wechat_miniprogram_template_msg": null,
                        "wechat_active_push": null,
                        "edm": null,
                        "line": null,
                        "reward_grant": null,
                        "common": null,
                        "style": "WEBHOOK"
                    },
                    "mark_group_cname": null,
                    "mark_group_name": null,
                    "mark_group_dir": null,
                    "mark_group_dir_cname": null,
                    "is_new_mark_group": null,
                    "mark_audience": null,
                    "send_limit_config": null,
                    "enable_global_send_limit": true,
                    "enable_canvas_quiet": true,
                    "support_error_msg": false,
                    "succeed_criterion": null,
                    "enable_channel_quiet": null,
                    "channel_quiet_conf": null
                }
            }
        }
    ],
    "component_relations": [
        {
            "source_component_id": 1,
            "target_component_id": 2,
            "entry_source_node": "YES",
            "position": 1
        }
    ],
    "canvas_metrics": []
}
```

画布的解释信息详情见 json 中的注释。

画布的运行指标是画布的运行统计结果数据，比如画布的目标转化情况、各个策略器的进入情况等。
画布的运行指标包含以下字段： 
* phase：指标名称，指标名称可枚举，其取值和含义为：
  * AUDIENCED: 载入受众时，被读取到，标记为受众.
  * W_ENTER_ABORT_FOR_REENTER_LIMIT: 未进入计划/画布 - 进入次数达到上限，仅例行/单次计划会记录相关指标
  * ENTER: 进入画布
  * W_SEND_ABORT_FOR_QUIET_STRATEGY / W_SEND_ABORT_FOR_QUIET_STRATEGY_BY_SENDER: 勿扰退出
  * W_SEND_ABORT_FOR_QUIET_STRATEGY_NOT_EXIT / W_SEND_ABORT_FOR_QUIET_STRATEGY_NOT_EXIT_BY_SENDER: 勿扰不退出，但是也不发送消息
  * SEND_DELAY_FOR_QUIET_STRATEGY / SEND_DELAY_FOR_QUIET_STRATEGY_BY_SENDER: 勿扰延时发送（过了勿扰时段后再继续发送）
  * W_SEND_DISCARD_FOR_PLAN_PENDING: 画布待运行导致的消息过滤
  * W_SEND_DISCARD_FOR_PLAN_TRIAL_MODE: 画布测试模式导致的消息过滤
  * W_SEND_DISCARD_FOR_PLAN_FINISHED: 画布终止导致的消息过滤
  * W_SEND_DISCARD_FOR_PLAN_SUSPEND: 画布暂停导致的消息过滤
  * W_SEND_DISCARD_FOR_PROJECT_BLACKLIST: 项目黑名单导致不发送
  * W_SEND_ABORT_FOR_QUIETLY_CONVERTED: 未发送消息 - 目标转化已完成（在延时、勿扰期间，用户自主转化）
  * W_SEND_ABORT_FOR_MSG_LIMIT: 未发送消息 - 发送次数达到上限
  * W_SEND_ABORT_FOR_DELAY: 未发送消息 - 系统延迟导致放弃发送
  * W_SEND_ABORT_FOR_EXCEPTION: 未发送消息 - 发送发生异常
  * W_SEND_ABORT_FOR_SEND_ID_NULL: 未发送消息 - 用户不存在发送 ID
  * W_SEND_ABORT_FOR_SEND_ID_DUPLICATE: 未发送消息 - 用户发送 ID 重复
  * W_SEND_ABORT_FOR_USER_SET: 未发送消息 - 用户设置不发送
  * MSG_SEND: 满足发送条件
  * MSG_SEND_DISTINCT: 根据 sendId 去重
  * TRIGGERED: 触发定义，在 PlanMsgSendDone 之前的瞬间.
  * MSG_SEND_DONE: 消息已发送完成（无论发生成功还是失败）
  * ARRIVED: 消息已送达，比如 微信、短信（梦网）等的回执
  * MSG_SEND_SUCCESS: 回执，保留. 微信专用.
  * TRIGGERED_SUCCESS: 发送成功
  * FLITER_BY_PLAN_BLACKLIST: 画布黑名单导致的过滤
  * TRIGGERED_FAILED: 发送失败
  * W_MSG_FAILED_FOR_WX_SYSTEM_FAILED: 微信回执 - 发送失败
  * W_MSG_FAILED_FOR_WX_USER_BLOCK: 微信回执 - 用户拒收
  * W_MSG_SEND_PARTIAL_SUCCESS: 微信回执 - 部分发送成功
  * CONVERTED: 营销策略目标转化
  * CONVERTED_NATURAL: 自然转化（未发送消息之前已经发生了目标事件，从而导致的目标转化）
  * W_MSG_SEND_TOTAl: 总发送数量，用于微信批量发送
  * WECOM_TASK_EXTERNAL_USER_MSG_SEND: 企业微信 - 实际触发
  * METRICS_CONVERT: metrics 产生的转化事件
  * EXIT_FOR_ACCOMPLISH_GOAL: 完成目标事件退出
  * EXIT_FOR_UNMATCHED: 不满足任何分支退出组件
  * EXIT_FOR_QUIET: 勿扰退出
  * EXIT_FOR_OVER_TIME: 超时退出
  * EXIT_FOR_NATURAL: 自然退出
  * SENDER_SEND / ACTOR_PREPARE_SEND: 上游组件已触发发送消息
  * MARK_AUDIENCE_SUCCESS / MARK_AUDIENCE_FAILED: 标记受众成功/失败
  * 还有一些其他的自定义指标，由用户自行补充
* convert_id: 目标id，即流程画布中的canvas_metrics中的 convert_id，表示是哪个目标发生了转化，值为正数才有效；
* current_component: 当前的组件id，即统计了该指标的画布组件id；
* count: 统计的指标数据（发生该指标的数量）；
* day: 用户进入画布的时间；
* current_day: 用户产生该指标的时间。
"""

async def get_canvas_introduce_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="CANVAS_INTRODUCE_PROMPT",
        prompt_name="画布介绍提示词",
        prompt_desc="画布介绍提示词",
        prompt_content=CANVAS_INTRODUCE,
    )
    return template
