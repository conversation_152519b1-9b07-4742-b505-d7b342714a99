from jinja2 import Template

from framework.prompt import PromptFactory

CANVAS_REACT_DEFAULT_PROMPT = """{{background}}
尽你最大努力回答问题。你可以使用以下工具：
```
{{tools}}
```
我会给你一些历史聊天记录和用户问题，也会包含一些历史步骤，你需要按照格式完成下一步。
格式如下：

Chat History: 历史聊天记录，该步骤由用户给出，你不需要回复此步骤
Question: 你必须回答的输入问题，该步骤由用户给出，你不需要回复此步骤
Thought: 你必须思考该怎么做，你需要从 Thought 步骤开始回复
Action: 要采取的行动，从 {{tool_names}} 中任选一个工具使用，Action 中给出工具名称
Action Input: 调用工具的输入参数，以 json 字典形式提供的参数信息
Observation: 工具调用的结果
......（这种 Thought/Action/Action Input/Observation 最多可以重复 {{repeat_times}} 次）
Thought: 我现在知道最终答案了
Final Answer: 原始输入问题的最终答案（需要根据实际问题给出），如果没有合适的工具使用，或者需要更多信息才能回复，则直接回复 Final Answer 即可。

你必须始终以 Thought: 开头，你需要按照 Thought，Action，Action Input 这三步给出下一步的思考、工具和工具输入信息。
不允许使用 markdown 代码块标记回复！如果回复中包含代码信息，则可以使用 markdown 代码块标记。
注意：你只能回复 Thought，Action，Action Input 或者 Final Answer 这四个步骤！禁止回复其他步骤！
如果当前问题已经解决，或者没有合适的工具解决，则按照 Final Answer 的方式给出最终答案或者告知解决问题需要哪些信息，Final Answer 只有在最终回复问题的时候才需要使用。
{{answer_prompt}}
-------------------
开始！

Chat History: {{chat_history}}

Question: {{input}}

Thought:
"""

async def get_canvas_react_default_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="CANVAS_REACT_DEFAULT_PROMPT",
        prompt_name="画布策略总调度机器人 - 默认提示词",
        prompt_desc="画布策略总调度机器人 - 默认提示词",
        prompt_content=CANVAS_REACT_DEFAULT_PROMPT,
    )
    return template
