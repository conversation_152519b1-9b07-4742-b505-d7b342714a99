from pydantic import BaseModel, Field

from framework import config
from common import tools

from typing import Optional, Any
from langchain_core.tools import BaseTool

from framework.config import scenario_reference_model
from business.focus.tenant import ProjectInfo, CdpInfo
from ...canvas_optimizer.prompts.canvas_strategy_optimize_prompts import (
    CANVAS_STRATEGY_OPTIMIZE_PROMPT_BACKGROUND,
    CANVAS_STRATEGY_OPTIMIZE_PROMPT_ANSWER, get_canvas_strategy_optimize_prompt_background,
    get_canvas_strategy_optimize_prompt_answer
)
from framework.agents.react import ReActAgent
from framework.models import BaseLLM, ModelsFactory

from typing import Type
from ...canvas_common.tools.canvas_description_tool import (
    CanvasDescriptionTool,
    CanvasNotFoundError,
    DataFormatError
)

log = tools.get_logger()


class _InputModel(BaseModel):
    canvas_id: int = Field(description="画布id")


class CanvasDescriberTool(BaseTool):
    """画布描述工具（继承自langchain BaseTool）"""

    name: str = "canvas_describer"
    description: str = (
        "用于将画布ID转换为自然语言描述的工具，如果这个画布ID无效，提示无效画布，如果有效，则返回画布详细的策略信息,"
        "需要参数：canvas_id（int类型）,传入的参数一定是int类型，即画布ID"
    )
    args_schema: Type = _InputModel

    project_info: ProjectInfo | None = None
    cdp_info: CdpInfo | None = None
    llm: BaseLLM | None = None

    def _run(self, canvas_id: int, **kwargs) -> str:
        """
        执行工具主逻辑
        """
        try:
            return CanvasDescriptionTool.generate_description(
                canvas_id=canvas_id,
                project_info=self.project_info,
                cdp_info=self.cdp_info,
            )
        except (CanvasNotFoundError, DataFormatError) as e:
            return f"描述画布失败：{e}"

    async def _arun(self, canvas_id: int, **kwargs) -> Any:
        try:
            return await CanvasDescriptionTool.async_generate_description(
                canvas_id=canvas_id,
                project_info=self.project_info,
                cdp_info=self.cdp_info,
            )
        except (CanvasNotFoundError, DataFormatError) as e:
            return f"描述画布失败：{e}"


class CanvasOptimizeAgent(ReActAgent):
    """画布策略优化Agent（集成描述工具版）"""

    def __init__(
            self,
            llm: Optional[BaseLLM] = None,
            project_info: ProjectInfo = None,
            cdp_info: CdpInfo = None,
            **kwargs
    ):
        # 初始化父类
        scenario = 'single_aud_rule_parse'
        super().__init__(
            tools=[
                CanvasDescriberTool(project_info=project_info, cdp_info=cdp_info, llm=llm)
            ],
            description='用于对已有的策略进行分析及优化',
            name='画布策略优化Agent',
            llm=llm or ModelsFactory.get_llm(scenario=scenario),
            system_prompt_background=tools.asyncio_run(get_canvas_strategy_optimize_prompt_background),
            answer_prompt=tools.asyncio_run(get_canvas_strategy_optimize_prompt_answer),
            max_token_limit=config.model_config[scenario_reference_model[scenario]].get('input_token_limit', 8 * 1000),
            **kwargs
        )
