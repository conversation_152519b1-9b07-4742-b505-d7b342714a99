import asyncio
from abc import ABC, abstractmethod
from typing import Optional, List, Any, Callable, Dict, Set, Literal

from pydantic import BaseModel, ValidationError

from common import tools
from ..exception.errorcode import ErrorCodeException
from business.dependency_apis.sensors_openapi.http import (
    Configuration, ApiClient, SaEventMetaApi, ApiException,
    SensorsdataCommonErrorInfo, SensorsdataAnalyticsV1EventWithProperty
)
from business.dependency_apis.sensors_openapi.http.apis.sa_property_meta_api import SaPropertyMetaApi
from business.dependency_apis.sensors_openapi.http.apis.sdh_schema_api import SdhSchemaApi
from business.dependency_apis.sensors_openapi.http.apis.sdh_segment_api import SdhSegmentApi
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_event_property_request import \
    SensorsdataAnalyticsV1EventPropertyRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_property_define import \
    SensorsdataAnalyticsV1PropertyDefine
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_analytics_v1_user_tag_dir_define import \
    SensorsdataAnalyticsV1UserTagDirDefine
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_field import SensorsdataHorizonV1Field
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_logical_schemas_request import \
    SensorsdataHorizonV1ListLogicalSchemasRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_schema_fields_request import \
    SensorsdataHorizonV1ListSchemaFieldsRequest
from business.dependency_apis.sensors_openapi.http.models.sensorsdata_horizon_v1_list_segment_definitions_request import \
    SensorsdataHorizonV1ListSegmentDefinitionsRequest
from ...base_rule import DataType
from ...tenant import CdpInfo, ProjectInfo

log = tools.get_logger()


class PropertyDefinition(BaseModel):
    """属性定义"""
    name: str
    display_name: str
    data_type: DataType


class EventDefinition(BaseModel):
    """事件定义"""
    name: str
    display_name: str
    properties: Optional[List[PropertyDefinition]] = None


class SegmentDefinition(BaseModel):
    """分群定义"""
    name: str
    display_name: str


def _convert_openapi_datatype(openapi_datatype: str) -> DataType | None:
    openapi_datatype = openapi_datatype.upper()
    match openapi_datatype:
        case 'BOOL':
            return DataType.BOOLEAN
        case 'INT' | 'NUMBER' | 'BIGINT' | 'DECIMAL':
            return DataType.NUMBER
        case 'STRING':
            return DataType.STRING
        case 'DATETIME' | 'DATE' | 'TIMESTAMP':
            return DataType.DATETIME
        case 'LIST':
            return DataType.LIST
        case _:
            return None


def _convert_field_to_property(field: SensorsdataHorizonV1Field) -> PropertyDefinition | None:
    data_type = _convert_openapi_datatype(field.data_type.type)
    if data_type is None:
        return None
    return PropertyDefinition(
        name=field.name,
        display_name=field.display_name,
        data_type=data_type
    )


def _convert_inf_property(inf_property: SensorsdataAnalyticsV1PropertyDefine) -> PropertyDefinition | None:
    data_type = _convert_openapi_datatype(inf_property.data_type)
    if data_type:
        return PropertyDefinition(
            name=inf_property.name,
            display_name=inf_property.cname,
            data_type=data_type
        )
    return None


def _check_response(response):
    # 存在错误信息, 抛出 error code 异常
    if response.error_info:
        raise ErrorCodeException(response.error_info)
    if response.code != 'SUCCESS':
        raise ErrorCodeException(SensorsdataCommonErrorInfo(code=response.code, description=response.message))


class MetadataService(ABC):
    def __init__(self, cdp_info: CdpInfo, project_info: ProjectInfo):
        self.cdp_info = cdp_info
        self.project_info = project_info

    @abstractmethod
    async def query_all_properties_and_tags(self, schema_name: str) -> List[PropertyDefinition]:
        """列出实体的所有属性、标签
        :args
            schema_name: str; 用户: users, 其他实体参考 sdh 的实体主表名
        """

    @abstractmethod
    async def query_all_events(self, schema_name: str) -> List[EventDefinition]:
        """
        列出所有事件
        :param schema_name: 用户事件: events, 其他实体参考 sdh 的实体事件主表名
        :return: 事件定义, 不包含事件属性
        """

    @abstractmethod
    async def query_all_segments(self, entity_name: str) -> List[SegmentDefinition]:
        """
        列出所有分群
        :param entity_name: str; 实体名称, 用户分群: user, 其他实体参考 sdh 的实体名
        :return: 实体对应的分群列表
        """

    @abstractmethod
    async def query_event_with_properties_if_present(self, schema_name: str, events: List[str]) -> List[
        EventDefinition]:
        """
        批量查询事件的属性
        :param schema_name: 用户事件: events, 其他实体参考 sdh 的实体事件主表名
        :param events: 事件名称列表
        :return: event with properties
        """

    @abstractmethod
    async def query_event_properties_if_present(self, schema_name: str, event_and_properties: Dict[str, Set[str]]) -> \
            List[EventDefinition]:
        """
        批量查询事件以及事件属性定义
        :param schema_name: 用户事件: events, 其他实体参考 sdh 的实体事件主表名
        :param event_and_properties: dict, key: 事件名, value: 事件属性名集合
        :return: event with properties
        """


class SdhMetadataService(MetadataService):
    def __init__(self, cdp_info: CdpInfo, project_info: ProjectInfo):
        super().__init__(cdp_info, project_info)
        self.configuration = Configuration(
            host=cdp_info.cdp_host,
        )

    async def query_event_with_properties_if_present(self, schema_name: str, events: List[str]) -> List[
        EventDefinition]:
        # 查询事件定义 & 查询事件对应的属性
        list_all_event_task = asyncio.create_task(self.query_all_events(schema_name))
        all_events: List[EventDefinition] = await list_all_event_task
        target_events = {e.name: e for e in all_events if e.name in events}
        if not target_events:
            return []
        query_event_properties_tasks = {}
        for e in target_events:
            task = asyncio.create_task(self._list_event_fields(
                list_schema_fields_request=SensorsdataHorizonV1ListSchemaFieldsRequest(
                    project_id=self.project_info.project_id, schema_name=schema_name + "." + e)))
            query_event_properties_tasks[e] = task
        result = []
        for e, t in query_event_properties_tasks.items():
            event_def = target_events.get(e)
            eps: List[PropertyDefinition] = await t
            result.append(EventDefinition(name=event_def.name, display_name=event_def.display_name, properties=eps))
        return result

    async def query_event_properties_if_present(self, schema_name: str, event_and_properties: Dict[str, Set[str]]) -> \
            List[
                EventDefinition]:
        list_all_event_task = asyncio.create_task(self.query_all_events(schema_name))
        list_properties_task = asyncio.create_task(
            self._list_event_fields(list_schema_fields_request=SensorsdataHorizonV1ListSchemaFieldsRequest(
                project_id=self.project_info.project_id,
                schema_name=schema_name,
                # SDH 支持查询不存在的属性, 所以此处可直接查询
                field_names=list({p for ps in event_and_properties.values() for p in ps})
            )))
        all_events: List[EventDefinition] = await list_all_event_task
        target_events = {e.name: e for e in all_events if e.name in event_and_properties}
        if not target_events:
            return []
        result = []
        properties: List[PropertyDefinition] = await list_properties_task
        property_def_map = {p.name: p for p in properties}
        for e, ps in event_and_properties.items():
            event_def = target_events.get(e)
            if event_def is None:
                # 忽略不存在的事件
                continue
            result.append(EventDefinition(name=event_def.name, display_name=event_def.display_name,
                                          properties=[property_def_map.get(p) for p in ps if p in property_def_map]))
        return result

    async def query_all_properties_and_tags(self, schema_name: str) -> List[PropertyDefinition]:
        async with ApiClient(self.configuration) as api_client:
            schema_api = SdhSchemaApi(api_client)
            # 该接口包含了属性和标签
            response = await schema_api.list_schema_fields(api_key=self.cdp_info.api_key,
                                                           sensorsdata_project=self.project_info.project_name,
                                                           list_schema_fields_request=SensorsdataHorizonV1ListSchemaFieldsRequest(
                                                               project_id=self.project_info.project_id,
                                                               schema_name=schema_name
                                                           ))
        _check_response(response)
        result = []
        for field in response.data.fields:
            if (field.builtin is True
                    or field.visible is False
                    or field.enable is False
                    or field.has_data is False):
                # 内置的(id, first_id等), 或者不可见的、不可用的、无数据的都过滤掉
                continue
            property_define = _convert_field_to_property(field)
            if property_define:
                result.append(property_define)
            else:
                log.warn(
                    f"ignore unsupported property. [org_id={self.project_info.org_id}, project_id={self.project_info.project_id}, property='{field.model_dump_json(exclude_none=True)}']")
                continue
        return result

    async def query_all_events(self, schema_name: str) -> List[EventDefinition]:
        has_next = True
        page = 0
        # 一次最多获取 100 条
        page_size = 100
        result = []
        async with ApiClient(self.configuration) as api_client:
            schema_api = SdhSchemaApi(api_client)
            while has_next:
                response = await schema_api.list_event_schemas(api_key=self.cdp_info.api_key,
                                                               sensorsdata_project=self.project_info.project_name,
                                                               list_logical_schemas_request=SensorsdataHorizonV1ListLogicalSchemasRequest(
                                                                   project_id=self.project_info.project_id,
                                                                   physical_schema_name=schema_name,
                                                                   page=page, page_size=page_size))
                _check_response(response)
                for schema in response.data.schemas:
                    if (schema.builtin is True
                            or schema.visible is False
                            or schema.enable is False
                            or schema.has_data is False):
                        # 内置的、不可见的、不可用的、无数据的都过滤掉
                        continue
                    result.append(
                        EventDefinition(
                            name=schema.original_name,
                            display_name=schema.display_name
                        )
                    )
                has_next = response.data.has_next
                page += 1
        return result

    async def _list_event_fields(self, list_schema_fields_request: SensorsdataHorizonV1ListSchemaFieldsRequest) -> List[
        PropertyDefinition]:
        async with ApiClient(self.configuration) as api_client:
            schema_api = SdhSchemaApi(api_client)
            response = await schema_api.list_event_fields(api_key=self.cdp_info.api_key,
                                                          sensorsdata_project=self.project_info.project_name,
                                                          list_schema_fields_request=list_schema_fields_request)
        _check_response(response)
        result = []
        for field in response.data.fields:
            if (field.builtin is True
                    or field.visible is False
                    or field.enable is False
                    or field.has_data is False):
                # 内置的(day, $time等), 或者不可见的、不可用的、无数据的都过滤掉
                continue
            property_define = _convert_field_to_property(field)
            if property_define:
                result.append(property_define)
            else:
                log.warn(
                    f"ignore unsupported event property. [org_id={self.project_info.org_id}, project_id={self.project_info.project_id}, property='{field.model_dump_json(exclude_none=True)}']")
        return result

    async def query_all_segments(self, entity_name: str) -> List[SegmentDefinition]:
        has_next = True
        page = 0
        # 一次最多获取 100 条
        page_size = 100
        result = []
        async with ApiClient(self.configuration) as api_client:
            segment_api = SdhSegmentApi(api_client)
            while has_next:
                response = await segment_api.list_segment_definitions(api_key=self.cdp_info.api_key,
                                                                      sensorsdata_project=self.project_info.project_name,
                                                                      list_segment_definitions_request=SensorsdataHorizonV1ListSegmentDefinitionsRequest(
                                                                          project_id=self.project_info.project_id,
                                                                          entity_name=entity_name,
                                                                          page=page,
                                                                          page_size=page_size,
                                                                          show_deleted=False,
                                                                          show_invisible=False,
                                                                      ))
                _check_response(response)
                if len(response.data.segment_definitions) < page_size:
                    has_next = False
                for segment_definition in response.data.segment_definitions:
                    result.append(
                        SegmentDefinition(
                            name=segment_definition.name,
                            display_name=segment_definition.display_name
                        )
                    )
        return result


class InfinityMetadataService(MetadataService):
    def __init__(self, cdp_info: CdpInfo, project_info: ProjectInfo):
        super().__init__(cdp_info, project_info)
        self.configuration = Configuration(
            host=cdp_info.cdp_host,
        )

    async def query_event_with_properties_if_present(self, schema_name: str, events: List[str]) -> List[
        EventDefinition]:
        if schema_name != 'events':
            raise ValueError(f"Unsupported event schema name: {schema_name}")
        try:
            event_with_properties = await self._list_event_properties(
                event_property_request=SensorsdataAnalyticsV1EventPropertyRequest(
                    events=events))
        except ErrorCodeException as e:
            if e.error_info.code == 'SA-D-32-1':
                log.warn(
                    f"some events not exist, then query all. [org_id={self.project_info.org_id}, project_id={self.project_info.project_id}, events='{events}', error_code='{str(e)}']")
                all_events = await self.query_all_events(schema_name)
                target_events = [e.name for e in all_events if e.name in events]
                if not target_events:
                    return []
                event_with_properties = await self._list_event_properties(
                    event_property_request=SensorsdataAnalyticsV1EventPropertyRequest(
                        events=target_events))
            else:
                raise e

        if not event_with_properties:
            return []
        result = []
        for event_with_property in event_with_properties:
            event = event_with_property.event_define
            properties = [_convert_inf_property(p) for p in event_with_property.properties]
            result.append(EventDefinition(name=event.name, display_name=event.cname, properties=properties))
        return result

    async def query_event_properties_if_present(self, schema_name: str, event_and_properties: Dict[str, Set[str]]) -> \
            List[
                EventDefinition]:
        if schema_name != 'events':
            raise ValueError(f"Unsupported event schema name: {schema_name}")
        event_with_properties = await self.query_event_with_properties_if_present(schema_name,
                                                                                  list(event_and_properties.keys()))
        if not event_with_properties:
            return []
        result = []
        for event_with_property in event_with_properties:
            event = event_with_property.event_define
            properties = [_convert_inf_property(p) for p in event_with_property.properties if
                          p.name in event_and_properties[event.name]]
            result.append(EventDefinition(name=event.name, display_name=event.cname, properties=properties))
        return result

    async def _list_event_properties(self, event_property_request: SensorsdataAnalyticsV1EventPropertyRequest) -> List[
        SensorsdataAnalyticsV1EventWithProperty]:
        async with ApiClient(self.configuration) as api_client:
            sa_property_api = SaPropertyMetaApi(api_client)
            response = await sa_property_api.list_event_properties(api_key=self.cdp_info.api_key,
                                                                   sensorsdata_project=self.project_info.project_name,
                                                                   event_property_request=event_property_request)
        _check_response(response)
        return response.data.event_properties

    async def query_all_properties_and_tags(self, schema_name: str) -> List[PropertyDefinition]:
        if schema_name != 'users':
            raise ValueError(f"Unsupported schema name: {schema_name}")
        async with ApiClient(self.configuration) as api_client:
            sa_property_api = SaPropertyMetaApi(api_client)
            query_properties_task = asyncio.create_task(
                sa_property_api.list_all_user_properties(api_key=self.cdp_info.api_key,
                                                         sensorsdata_project=self.project_info.project_name))
            query_tags_task = asyncio.create_task(
                sa_property_api.list_user_tags_with_dir(api_key=self.cdp_info.api_key,
                                                        sensorsdata_project=self.project_info.project_name))
            property_response = await query_properties_task
            tag_response = await query_tags_task
        _check_response(property_response)
        _check_response(tag_response)
        result = []
        for user_property in property_response.data.user_properties:
            p = _convert_inf_property(user_property)
            if p:
                result.append(p)
            else:
                log.warn(
                    f"ignore unsupported user property, [org_id={self.project_info.org_id}, project_id={self.project_info.project_id}, property='{user_property.model_dump_json(exclude_none=True)}']")
        tag_node_stack: List[SensorsdataAnalyticsV1UserTagDirDefine] = [*tag_response.data.user_tags]
        while tag_node_stack:
            tag_or_dir = tag_node_stack.pop()
            if tag_or_dir.type != 'TAG':
                if tag_or_dir.sub_nodes:
                    tag_node_stack.extend(tag_or_dir.sub_nodes)
            else:
                datatype = _convert_openapi_datatype(tag_or_dir.data_type)
                if datatype is None:
                    log.warn(
                        f"ignore unsupported tag. [org_id={self.project_info.org_id}, project_id={self.project_info.project_id}, tag='{tag_or_dir.model_dump_json(exclude_none=True)}']")
                else:
                    result.append(
                        PropertyDefinition(name=tag_or_dir.name, display_name=tag_or_dir.cname, data_type=datatype))
        return result

    async def query_all_events(self, schema_name: str) -> List[EventDefinition]:
        if schema_name != 'events':
            raise ValueError(f"Unsupported event schema name: {schema_name}")
        async with ApiClient(self.configuration) as api_client:
            sa_event_api = SaEventMetaApi(api_client)
            response = await sa_event_api.list_events_all(api_key=self.cdp_info.api_key,
                                                          sensorsdata_project=self.project_info.project_name)
        _check_response(response)
        result = []
        for event in response.data.events:
            result.append(EventDefinition(name=event.name, display_name=event.cname))
        return result

    async def query_all_segments(self, entity_name: str) -> List[SegmentDefinition]:
        if entity_name != 'user':
            raise ValueError(f"Unsupported entity name: {entity_name}")
        async with ApiClient(self.configuration) as api_client:
            sa_property_api = SaPropertyMetaApi(api_client)
            response = await sa_property_api.list_user_groups(api_key=self.cdp_info.api_key,
                                                              sensorsdata_project=self.project_info.project_name)
        _check_response(response)
        result = []
        for user_group in response.data.user_groups:
            result.append(SegmentDefinition(name=user_group.name, display_name=user_group.cname))
        return result


class DynamicDelegateMetadataService(MetadataService):
    def __init__(self, cdp_info: CdpInfo, project_info: ProjectInfo):
        super().__init__(cdp_info, project_info)
        # 优先当做 sdh service
        self.metadata_service: MetadataService = SdhMetadataService(cdp_info=self.cdp_info,
                                                                    project_info=self.project_info)
        self.lock = asyncio.Lock()

    async def query_event_with_properties_if_present(self, schema_name: str, events: List[str]) -> List[
        EventDefinition]:
        return await self.inner_run(
            lambda s: s.query_event_with_properties_if_present(schema_name=schema_name, events=events))

    async def query_event_properties_if_present(self, schema_name: str, event_and_properties: Dict[str, Set[str]]) -> \
            List[
                EventDefinition]:
        return await self.inner_run(lambda s: s.query_event_properties_if_present(schema_name=schema_name,
                                                                                  event_and_properties=event_and_properties))

    def _switch_metadata_service(self, metadata_service: MetadataService):
        if isinstance(metadata_service, SdhMetadataService) and isinstance(self.metadata_service,
                                                                           SdhMetadataService):
            log.info(
                "switch to infinity service. [org_id={self.project_info.org_id}, project_id={self.project_info.project_id}]")
            self.metadata_service = InfinityMetadataService(cdp_info=self.cdp_info,
                                                            project_info=self.project_info)
        elif isinstance(metadata_service, InfinityMetadataService) and isinstance(self.metadata_service,
                                                                                  InfinityMetadataService):
            log.info(
                "switch to sdh service. [org_id={self.project_info.org_id}, project_id={self.project_info.project_id}]")
            self.metadata_service = SdhMetadataService(cdp_info=self.cdp_info,
                                                       project_info=self.project_info)

    async def query_all_events(self, schema_name: str) -> List[EventDefinition]:
        return await self.inner_run(lambda s: s.query_all_events(schema_name=schema_name))

    async def query_all_segments(self, entity_name: str) -> List[SegmentDefinition]:
        return await self.inner_run(lambda s: s.query_all_segments(entity_name=entity_name))

    async def query_all_properties_and_tags(self, schema_name: str) -> List[PropertyDefinition]:
        return await self.inner_run(lambda s: s.query_all_properties_and_tags(schema_name=schema_name))

    async def inner_run(self, async_func: Callable[[MetadataService], Any]) -> Any:
        metadata_service = self.metadata_service
        try:
            response = await async_func(metadata_service)
            return response
        except ValidationError as e:
            # CASE: 未返回正确的结构, 可能有多种情况:
            # 1. 由于神策 OpenAPI 与前端共用 ng, 配置的路径不存在时会路由到登录页面, 此时无法解析为目标结构
            # 2. OpenAPI 不兼容, 相当于不支持
            log.warn(
                f"{type(metadata_service)} unavailable, unrecognized response, try another service. [org_id={self.project_info.org_id}, project_id={self.project_info.project_id}]",
                exc_info=e)
        except  ApiException as e:
            if e.status is not None and 400 < e.status <= 499:
                # 非 400 外的异常认为是服务不可用, 此处假定了传入的 api-key 和项目等参数一定是可用的
                # 因为实测非 sdh 环境请求 sdh 路径可能会返回 401...
                log.warn(
                    f"{type(metadata_service)} unavailable, response code error, try another service. [org_id={self.project_info.org_id}, project_id={self.project_info.project_id}]",
                    exc_info=e)
            else:
                raise e
        await self.lock.acquire()
        try:
            self._switch_metadata_service(metadata_service)
        finally:
            self.lock.release()
        return await async_func(self.metadata_service)


class MetadataServiceFactory:
    @staticmethod
    def get_metadata_service(cdp_info: CdpInfo, project_info: ProjectInfo,
                             cdp_type: str = None) -> MetadataService:
        if cdp_type is None:
            return DynamicDelegateMetadataService(cdp_info=cdp_info, project_info=project_info)
        if cdp_type == 'sdh':
            return SdhMetadataService(cdp_info=cdp_info, project_info=project_info)
        elif cdp_type == 'infinity':
            return InfinityMetadataService(cdp_info=cdp_info, project_info=project_info)
        else:
            raise ValueError(f"Unsupported metadata type: {cdp_type}")
