import requests
from requests import HTT<PERSON><PERSON>rror
from tenacity import retry, stop_after_attempt, retry_if_exception_type, wait_fixed

import common.tools

log = common.tools.get_logger(__name__)


def _check_get_result(response: requests.Response):
    """检查 HTTP 响应状态码，并返回 JSON 数据"""
    if response.status_code != 200:
        raise HTTPError(f'Got error code. http_status={response.status_code}\n{response.content}')
    return response.json()


class StrategyInfo:
    def __init__(self, host: str, token: str, organization_id: str, project_name: str, api_key: str, **kwargs):
        self.host = host
        self.token = token
        self.organization_id = organization_id
        self.project_name = project_name
        self.api_key = api_key

    @property
    def http_headers(self):
        """返回请求头信息"""
        return {
            'api-key': self.api_key,
            'X-Organization-Id': self.organization_id,
            'sensorsdata-project': self.project_name,
            'token': self.token,
            'Content-Type': 'application/json'
        }

    @retry(
        stop=stop_after_attempt(5),
        retry=retry_if_exception_type(BaseException),
        wait=wait_fixed(10),
        reraise=True,
    )
    def _make_request(self, method: str, url: str, **kwargs):
        """统一的请求处理函数，支持 GET 和 POST 请求"""
        try:
            response = requests.request(
                method=method,
                url=url,
                headers=self.http_headers,
                **kwargs
            )
            return _check_get_result(response)
        except requests.RequestException as e:
            raise Exception(f"Request failed: {str(e)}")

    def get_info(self, strategy_id: int) -> dict:
        """获取策略信息和指标"""
        canvas_info = self._get_strategy_info(strategy_id)
        try:
            canvas_metrics = self._get_strategy_metrics(strategy_id)
        except BaseException as e:
            log.warning(f"获取指标失败 id={strategy_id}")
            canvas_metrics = f"获取策略指标失败，策略可能已删除，id={strategy_id}"

        return {
            "canvas_info": canvas_info,
            "canvas_metrics": canvas_metrics
        }

    def _get_strategy_info(self, strategy_id):
        """获取策略信息"""
        try:
            url = f'{self.host}/api/v3/focus/v1/web/canvas/get?id={strategy_id}'
            response = self._make_request('GET', url)
            log.info(f'get_strategy_info: {response}')
            return response['data']['canvas']
        except:
            log.warning(f"获取新画布失败，尝试获取计划 id={strategy_id}")

        try:
            url = f'{self.host}/api/v3/focus/v1/web/plan/get?id={strategy_id}'
            response = self._make_request('GET', url)
            log.info(f'get_plan_info: {response}')
            return response['data']['plan']
        except:
            log.warning(f"获取计划失败，尝试获取老画布 id={strategy_id}")

        try:
            url = f'{self.host}/api/v3/focus/v1/web/old-canvas/get?id={strategy_id}'
            response = self._make_request('GET', url)
            log.info(f'get_plan_info: {response}')
            return response['data']['old_canvas']
        except:
            log.warning(f"获取老画布失败，id={strategy_id}")

        return f"获取策略信息失败，策略可能已删除，id={strategy_id}"

    def _get_strategy_metrics(self, strategy_id):
        """获取策略的指标信息"""
        body_data = {
            "filter_metrics_condition": {
                "plan_id": strategy_id,
                "metrics_phases": [
                    "AUDIENCED", "SENDER_SEND", "ENTER", "CONVERTED",
                    "CONVERTED_NATURAL", "TRIGGERED_SUCCESS", "ARRIVED"
                ],
                "from_date": "2023-07-29T18:00:00.000Z",
                "to_date": "2025-08-29T18:00:00.000Z"
            },
            "metrics_sorts": [{"property_name": "plan_id", "sort_order": "ASC"}],
            "account_id": 0
        }
        url = f'{self.host}/api/v3/focus/v1/web/metrics/query'
        return self._make_request('POST', url, json=body_data)['data']['metrics_responses']
