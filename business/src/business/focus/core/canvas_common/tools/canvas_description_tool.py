"""
画布描述生成工具集
提供从画布ID到自然语言描述的完整转换能力
"""

import json
import logging
from common import tools
from typing import Optional

from ..tools.strategy_info import StrategyInfo
from business.focus.tenant import ProjectInfo, CdpInfo
from framework.agents.autogen import ParticipantAgent
from framework.models import BaseLLM, ModelsFactory
from ...prompts.canvas.canvas_id_to_nl_translate_prompts import CANVAS_ID_TO_NL_TRANSLATE_SYSTEM_PROMPT, \
    get_canvas_id_to_nl_translate_prompt

log = tools.get_logger()


class CanvasDescriptionTool:
    """画布描述生成工具类"""

    _DEFAULT_PROMPT = tools.asyncio_run(get_canvas_id_to_nl_translate_prompt)

    @staticmethod
    def generate_description(
            canvas_id: int,
            project_info: ProjectInfo,
            cdp_info: CdpInfo,
            llm: Optional[BaseLLM] = None
    ) -> str:
        """
        生成画布自然语言描述（主入口方法）
        
        参数：
        canvas_id: 画布唯一标识符
        project_info: 项目元数据对象
        cdp_info: CDP平台配置对象
        llm: 可选的语言模型实例
        
        返回：
        自然语言描述文本
        
        异常：
        CanvasNotFoundError: 画布不存在时抛出
        DescriptionGenerationError: 描述生成失败时抛出
        """
        try:
            metadata = CanvasDescriptionTool._get_canvas_metadata(
                canvas_id, project_info, cdp_info
            )
            return CanvasDescriptionTool._generate_nl_description(metadata, llm)
        except Exception as e:
            logging.error(f"描述生成失败 | 画布ID: {canvas_id} | 错误: {str(e)}")
            raise

    @staticmethod
    async def async_generate_description(
            canvas_id: int,
            project_info: ProjectInfo,
            cdp_info: CdpInfo,
            llm: Optional[BaseLLM] = None
    ) -> str:
        try:
            metadata = await CanvasDescriptionTool._async_get_canvas_metadata(
                canvas_id, project_info, cdp_info
            )
            return await CanvasDescriptionTool._async_generate_nl_description(metadata, llm)
        except Exception as e:
            logging.error(f"描述生成失败 | 画布ID: {canvas_id} | 错误: {str(e)}")
            raise

    @staticmethod
    def _get_canvas_metadata(
            canvas_id: int,
            project_info: ProjectInfo,
            cdp_info: CdpInfo
    ) -> dict:
        """
        获取画布元数据（工具方法）

        参数：
        canvas_id: 画布唯一标识符
        project_info: 项目配置信息
        cdp_info: CDP平台连接配置

        返回：
        画布元数据字典

        异常：
        CanvasNotFoundError: 画布不存在时抛出
        """
        client = StrategyInfo(
            host=cdp_info.cdp_host,
            token=cdp_info.api_key,
            organization_id=project_info.org_id,
            project_name=project_info.project_name,
            api_key=cdp_info.api_key,
        )

        result = client.get_info(canvas_id)
        if not result.get('canvas_info'):
            raise CanvasNotFoundError(f"未找到ID为 {canvas_id} 的画布")

        log.info(f"获取画布信息成功，画布id 为{canvas_id}")
        log.info(result)
        #  包含指标信息
        return result

    @staticmethod
    async def _async_get_canvas_metadata(
            canvas_id: int,
            project_info: ProjectInfo,
            cdp_info: CdpInfo
    ) -> dict:
        client = StrategyInfo(
            host=cdp_info.cdp_host,
            token=cdp_info.api_key,
            organization_id=project_info.org_id,
            project_name=project_info.project_name,
            api_key=cdp_info.api_key,
        )

        result = client.get_info(canvas_id)
        if not result.get('canvas_info'):
            raise CanvasNotFoundError(f"未找到ID为 {canvas_id} 的画布")

        log.info(f"获取画布信息成功，画布id 为{canvas_id}")
        log.info(result)
        #  包含指标信息
        return result

    @staticmethod
    def _generate_nl_description(
            canvas_data: dict,
            llm: Optional[BaseLLM] = None
    ) -> str:
        """
        生成自然语言描述（核心工具方法）

        参数：
        canvas_data: 画布元数据字典
        llm: 可选的语言模型实例

        返回：
        结构化自然语言描述

        异常：
        DataFormatError: 数据格式异常时抛出
        """
        if not canvas_data:
            raise ValueError("无效的画布数据")

        try:
            agent = ParticipantAgent(
                name="CanvasDescriber",
                description="画布描述生成器",
                system_prompt=CanvasDescriptionTool._DEFAULT_PROMPT,
                llm=llm or ModelsFactory.get_llm(scenario='single_aud_rule_parse')
            )
            structured_data = json.dumps(canvas_data, ensure_ascii=False)
            return agent.chat_and_save(structured_data)
        except json.JSONDecodeError as e:
            logging.error(f"JSON解析失败 | 数据: {canvas_data[:200]}...")
            raise DataFormatError("画布数据格式错误") from e

    @staticmethod
    async def _async_generate_nl_description(
            canvas_data: dict,
            llm: Optional[BaseLLM] = None
    ) -> str:
        if not canvas_data:
            raise ValueError("无效的画布数据")

        try:
            agent = ParticipantAgent(
                name="CanvasDescriber",
                description="画布描述生成器",
                system_prompt=CanvasDescriptionTool._DEFAULT_PROMPT,
                llm=llm or ModelsFactory.get_llm(scenario='single_aud_rule_parse')
            )
            structured_data = json.dumps(canvas_data, ensure_ascii=False)
            return await agent.achat_and_save(structured_data)
        except json.JSONDecodeError as e:
            logging.error(f"JSON解析失败 | 数据: {canvas_data[:200]}...")
            raise DataFormatError("画布数据格式错误") from e


# 异常体系
class CanvasNotFoundError(Exception): pass


class DescriptionGenerationError(Exception): pass


class DataFormatError(Exception): pass
