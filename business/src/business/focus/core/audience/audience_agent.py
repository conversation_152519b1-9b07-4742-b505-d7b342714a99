from typing import List, Optional

from pydantic import Field

from common import tools
from ..audience.parse.nl_rule_parser import (
    NLAudienceRuleParser,
    NL_RULE_PARSE_TASK,
    NLRuleParseTaskResult,
    NLParseTaskInput,
    NLRuleOpTaskInput,
    NL_RULE_OP_TASK,
    InvalidNLAudienceDesc,
    AudienceRuleParseTaskInput,
    AUDIENCE_RULE_PARSE_TASK
)
from ..audience.retrieve.metadata_retriever import AudienceRelatedMetadata
from ...audience_rule import AudienceRule
from framework.agents.role_playing.role import BaseRoleTaskAgent, Role
from framework.agents.role_playing.task import TaskInput, TaskResult, task

log = tools.get_logger()


class GenAudienceRuleTaskResult(TaskResult):
    """生成受众规则的结果"""
    generated: bool = Field(..., description='是否生成了受众规则')
    invalid_descriptions: Optional[List[InvalidNLAudienceDesc]] = Field(default=None,
                                                                        description='无效的人群圈选描述及其原因, 比如: 无法理解潜客的含义')
    audience_rule: Optional[AudienceRule] = Field(default=None, description='生成的受众规则')
    nl_desc: str = Field(default=None, description='受众规则的自然语言描述')


class GenAudienceRuleFromNLDescTaskInput(TaskInput):
    """gen_rule_from_nl_desc 任务参数"""
    nl_audience_rule: str = Field(default=None, description='自然语言描述的受众规则')
    related_metadata: Optional[List[AudienceRelatedMetadata]] = Field(default=None, description='规则相关的元数据')


# Task 定义
GEN_AUDIENCE_RULE_FROM_NL_DESC_TASK = "gen_rule_from_nl_desc"

# 角色定义
AUDIENCE_ROLE = Role(
    name='AudienceRuleManager',
    job_desc='负责受众规则的解析、生成、规则翻译'
)


class AudienceRuleCreateAgent(BaseRoleTaskAgent):
    """受众创建 agent"""

    def __init__(
            self,
            **kwargs
    ) -> None:
        """
        :param tasks: 该角色负责的任务, 如果为空, 则使用角色的任务, 可以根据场景决定
        :param kwargs: 其他参数
        """
        super().__init__(
            role=AUDIENCE_ROLE,
            **kwargs
        )

    @task(task_def=NL_RULE_PARSE_TASK)
    async def _generate_nl_audience_rule(self, task_input: NLParseTaskInput) -> NLRuleParseTaskResult:
        return await (NLAudienceRuleParser(llm=self.llm, task_callback=self.task_callback)
                      .launch_task(task=NL_RULE_PARSE_TASK, task_arg=task_input))

    @task(task_def=NL_RULE_OP_TASK)
    async def _modify_nl_audience_rule(self, task_input: NLRuleOpTaskInput) -> NLRuleParseTaskResult:
        return await (NLAudienceRuleParser(llm=self.llm, task_callback=self.task_callback)
                      .launch_task(task=NL_RULE_OP_TASK, task_arg=task_input))

    @task(task_def=GEN_AUDIENCE_RULE_FROM_NL_DESC_TASK,
          task_desc="根据用户的描述, 结合系统中实际存在的元数据生成最终的受众规则, 可用于创建画布等")
    async def generate_audience_rule_from_nl_desc(
            self,
            task_input: GenAudienceRuleFromNLDescTaskInput
    ) -> GenAudienceRuleTaskResult:
        nl_rule_parser = NLAudienceRuleParser(llm=self.llm, task_callback=self.task_callback)
        # 兼容大模型未能识别出元数据的情况, 再次解析受众描述使用的元数据并进行生成
        if not task_input.related_metadata:
            nl_parse_result = await nl_rule_parser.launch_task(
                task=NL_RULE_PARSE_TASK,
                task_arg=NLParseTaskInput(audience_rule=task_input.nl_audience_rule)
            )
        else:
            nl_parse_result = NLRuleParseTaskResult(
                origin_nl_audience_rule=task_input.nl_audience_rule,
                generated=True,
                real_nl_audience_rule=task_input.nl_audience_rule,
                related_metadata=task_input.related_metadata
            )
        if nl_parse_result.generated:
            result = await nl_rule_parser.launch_task(
                task=AUDIENCE_RULE_PARSE_TASK,
                task_arg=AudienceRuleParseTaskInput(
                    nl_audience_rule=nl_parse_result.real_nl_audience_rule,
                    related_metadata=nl_parse_result.related_metadata
                )
            )
            return GenAudienceRuleTaskResult(
                audience_rule=result.audience_rule,
                generated=True,
                invalid_descriptions=nl_parse_result.invalid_descriptions,
                nl_desc=nl_parse_result.real_nl_audience_rule
            )
        else:
            return GenAudienceRuleTaskResult(
                generated=False,
                invalid_descriptions=nl_parse_result.invalid_descriptions,
                nl_desc=task_input.nl_audience_rule
            )
