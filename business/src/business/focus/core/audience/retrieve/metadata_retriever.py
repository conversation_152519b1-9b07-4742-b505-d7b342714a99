from collections import defaultdict
from typing import List, Optional, Set, Dict, Literal

from pydantic import BaseModel, Field
import asyncio

from ...cdp.metadata import (
    MetadataServiceFactory,
    MetadataService,
    PropertyDefinition,
    EventDefinition,
    SegmentDefinition
)
from business.focus.audience_rule import AudienceRule, PropertyFilter
from business.focus.base_rule import DataType, EntityField
from business.focus.tenant import ProjectInfo, CdpInfo
from business.context import request_project_context, request_cdp_context
from framework.agents.role_playing.role import BaseRoleTaskAgent, Role
from framework.agents.role_playing.task import TaskInput, TaskResult, Task, task


class PropertyMetadata(BaseModel):
    """属性元数据"""
    name: str = Field(..., description='属性名称')
    display_name: str = Field(..., description='属性显示名称')
    data_type: DataType = Field(..., description='属性数据类型')
    description: Optional[str] = Field(default=None, description='属性描述')


class EventMetadata(BaseModel):
    """事件元数据"""
    name: str = Field(..., description="事件名称")
    display_name: str = Field(..., description="事件显示名称")
    properties: Optional[List[PropertyMetadata]] = Field(default=None, description="事件属性列表")
    description: Optional[str] = Field(default=None, description='事件描述')


class UserGroupMetadata(BaseModel):
    """用户分群元数据"""
    name: str = Field(..., description="用户分群名称")
    display_name: str = Field(..., description="用户分群显示名称")
    description: Optional[str] = Field(default=None, description="用户分群描述")
    nl_group_rule: Optional[str] = Field(default=None, description='用户分群的自然语言描述')

    def to_property_metadata(self):
        return PropertyMetadata(name=self.name,
                                display_name=self.display_name,
                                data_type=DataType.BOOLEAN,
                                description=self.description)


class AudienceRelatedMetadata(BaseModel):
    """受众相关元数据"""
    type: Literal["event", "user_property", "user_group"] = Field(...,
                                                                  description='元数据类型: event(事件), user_property(用户属性), user_group(用户分群)')
    user_property: Optional[PropertyMetadata] = Field(default=None,
                                                      description='用户属性元数据, 仅当 type 为 user_property 时存在')
    event: Optional[EventMetadata] = Field(default=None, description='事件元数据, 仅当 type 为 event 时存在')
    user_group: Optional[UserGroupMetadata] = Field(default=None,
                                                    description='用户分群元数据, 仅当 type 为 user_group 时存在')


class TermRetrieverTaskInput(TaskInput):
    """检索受众相关元数据的任务输入"""
    events: Optional[Set[str]] = Field(default=None, description='事件名称列表')
    user_group_rules: Optional[Set[str]] = Field(default=None, description='用户分群规则描述')
    user_profiles: Optional[Set[str]] = Field(default=None, description='用户属性/用户标签/用户分群名称列表')


class TermRetrieverTaskResult(TaskResult):
    """检索受众相关元数据的任务输出"""
    related_metadata: List[AudienceRelatedMetadata] = Field(..., description='受众相关元数据列表')


class RetrieveEventPropertiesTaskInput(TaskInput):
    """检索事件属性的任务输入"""
    events: List[str] = Field(..., description='实际存在的事件名称列表, 事件名, 非事件显示名')


class RetrieveEventPropertiesTaskResult(TaskResult):
    """检索事件属性的任务输出"""
    events: List[EventMetadata] = Field(..., description='事件以及属性列表')


class RetrieveBaseMetadataTaskResult(TaskResult):
    """检索基础元数据的结果"""
    related_metadata: List[AudienceRelatedMetadata] = Field(..., description='基础元数据信息')


METADATA_RETRIEVER = Role(
    name='audience_metadata_retriever',
    job_desc='检索受众相关的元数据'
)
TERM_RETRIEVE = 'term_retrieve'
RETRIEVE_EVENT_PROPERTIES = 'retrieve_event_properties'
RETRIEVE_BASE_METADATA = 'retrieve_base_metadata'


class AudienceRuleUsedMetadata:
    """与受众规则相关的元数据"""

    def __init__(self,
                 events: Optional[Dict[str, Set[str]]] = None,
                 entity_fields: Optional[Set[EntityField]] = None):
        """
        :param events: 事件以及事件属性名
        :param entity_fields: 除事件外的实体字段, 包括用户属性分群等
        """
        self.events = events
        self.entity_fields = entity_fields

    def merge(self, other: "AudienceRuleUsedMetadata") -> "AudienceRuleUsedMetadata":
        if not other:
            return self
        if not self.events:
            self.events = other.events
        else:
            for event, properties in other.events.items():
                if event in self.events:
                    self.events[event].update(properties)
                else:
                    self.events[event] = properties
        if not self.entity_fields:
            self.entity_fields = other.entity_fields
        else:
            self.entity_fields.update(other.entity_fields)
        return self


class MetadataRetriever(BaseRoleTaskAgent):
    def __init__(self,
                 **kwargs):
        super().__init__(
            role=METADATA_RETRIEVER,
            **kwargs
        )

    @task(task_def=TERM_RETRIEVE, task_desc='根据事件名、用户属性名、分群名、分群规则模糊检索元数据')
    async def term_retriever(self, task_arg: TermRetrieverTaskInput) -> TermRetrieverTaskResult:
        # TODO 后续支持向量数据库检索元数据
        return TermRetrieverTaskResult(related_metadata=(await self.retrieve_base_info()).related_metadata)

    @task(task_def=RETRIEVE_EVENT_PROPERTIES, task_desc='检索事件属性数据')
    async def retrieve_event_properties(self,
                                        task_arg: RetrieveEventPropertiesTaskInput) -> RetrieveEventPropertiesTaskResult:
        """通过事件名称检索事件属性"""
        project: ProjectInfo = request_project_context.get()
        cdp: CdpInfo = request_cdp_context.get()
        metadata_service: MetadataService = MetadataServiceFactory.get_metadata_service(cdp_info=cdp,
                                                                                        project_info=project)
        events = task_arg.events
        response: List[EventDefinition] = await metadata_service.query_event_with_properties_if_present("events",
                                                                                                        events)
        result = []
        for e in response:
            result.append(EventMetadata(
                name=e.name,
                display_name=e.display_name,  # 假设显示名称和事件名称相同
                properties=[PropertyMetadata(
                    name=user_property.name,
                    display_name=user_property.display_name,
                    data_type=user_property.data_type,
                ) for user_property in e.properties]
            ))
        return RetrieveEventPropertiesTaskResult(events=result)

    @task(task_def=RETRIEVE_BASE_METADATA, task_desc='检索基础元数据')
    async def retrieve_base_info(self) -> RetrieveBaseMetadataTaskResult:
        """获取所有基础元数据, 用户属性、标签、分群、事件, 不包含事件属性, 分群规则等详细信息"""
        project: ProjectInfo = request_project_context.get()
        cdp: CdpInfo = request_cdp_context.get()
        metadata_service: MetadataService = MetadataServiceFactory.get_metadata_service(cdp_info=cdp,
                                                                                        project_info=project)
        user_properties_task = asyncio.create_task(metadata_service.query_all_properties_and_tags(schema_name="users"))
        events_task = asyncio.create_task(metadata_service.query_all_events(schema_name="events"))
        user_groups_task = asyncio.create_task(metadata_service.query_all_segments(entity_name="user"))
        user_groups: List[SegmentDefinition] = await user_groups_task
        events: List[EventDefinition] = await events_task
        user_properties: List[PropertyDefinition] = await user_properties_task
        result = []
        for user_property in user_properties:
            result.append(AudienceRelatedMetadata(
                type='user_property',
                user_property=PropertyMetadata(
                    name=user_property.name,
                    display_name=user_property.display_name,
                    data_type=user_property.data_type,
                )))
        for user_group in user_groups:
            result.append(AudienceRelatedMetadata(
                type='user_group',
                user_group=UserGroupMetadata(
                    name=user_group.name,
                    display_name=user_group.display_name,
                )))
        for event in events:
            # 检索场景下不需要"任意事件"
            if event.name == '$Anything':
                continue
            result.append(AudienceRelatedMetadata(
                type='event',
                event=EventMetadata(
                    name=event.name,
                    display_name=event.display_name
                )
            ))
        return RetrieveBaseMetadataTaskResult(related_metadata=result)

    async def retrieve_audience_rule_metadata(self, audience_rule: AudienceRule) -> List[AudienceRelatedMetadata]:
        """根据受众规则检索元数据"""
        project: ProjectInfo = request_project_context.get()
        cdp: CdpInfo = request_cdp_context.get()
        metadata_service: MetadataService = MetadataServiceFactory.get_metadata_service(cdp_info=cdp,
                                                                                        project_info=project)
        metadata = MetadataRetriever._parse_audience_metadata(audience_rule=audience_rule)
        return await self._query_audience_rule_metadata(metadata_service=metadata_service, metadata=metadata)

    @staticmethod
    async def _query_audience_rule_metadata(metadata_service: MetadataService,
                                            metadata: AudienceRuleUsedMetadata) -> List[AudienceRelatedMetadata]:
        fetch_events_task = None
        fetch_profiles_task = None
        if metadata.events:
            fetch_events_task = asyncio.create_task(
                MetadataRetriever._retrieve_event_property_metadata(metadata_service=metadata_service,
                                                                    event_and_properties=metadata.events))
        if metadata.entity_fields:
            up = {f for f in metadata.entity_fields if f.entity_name == "user"}
            if up:
                fetch_profiles_task = asyncio.create_task(
                    MetadataRetriever._retrieve_profile_metadata(metadata_service=metadata_service,
                                                                 profiles={p.field_property for p in up}))
        events: List[EventDefinition] = []
        profiles: List[AudienceRelatedMetadata] = []
        if fetch_events_task:
            events = await fetch_events_task
        if fetch_profiles_task:
            profiles = await fetch_profiles_task
        return [*profiles, *[AudienceRelatedMetadata(type="event", event=e) for e in events]]

    @staticmethod
    def _parse_fields_from_property_filter(property_filter: PropertyFilter, fields: Set[str]) -> None:
        for condition in property_filter.conditions:
            fields.add(condition.field)

    @staticmethod
    def _parse_audience_metadata(audience_rule: AudienceRule) -> AudienceRuleUsedMetadata:
        fields = set()
        event_names = set()
        if audience_rule.profile_rule:
            MetadataRetriever._parse_fields_from_property_filter(property_filter=audience_rule.profile_rule,
                                                                 fields=fields)
        if audience_rule.event_rule:
            for event in audience_rule.event_rule.events:
                event_names.add(event.measure.event)
                if event.measure.aggr_field:
                    fields.add(event.measure.aggr_field)
                if event.measure.filter:
                    MetadataRetriever._parse_fields_from_property_filter(property_filter=event.measure.filter,
                                                                         fields=fields)
        if audience_rule.event_sequence_rule:
            for sequence_rule in audience_rule.event_sequence_rule.multi_sequence_rules:
                for step in sequence_rule.steps:
                    event_names.add(step.event)
                    if step.filter:
                        MetadataRetriever._parse_fields_from_property_filter(property_filter=step.filter,
                                                                             fields=fields)
        events: Dict[str, Set[str]] = defaultdict(set)
        for en in event_names:
            events[en] = set()
        non_event_fields: Set[EntityField] = set()
        for f in fields:
            entity_field = EntityField.from_field(f)
            if entity_field.entity_name == "event":
                events[entity_field.event_name].add(entity_field.field_property)
            else:
                non_event_fields.add(entity_field)
        return AudienceRuleUsedMetadata(events=events, entity_fields=non_event_fields)

    @staticmethod
    async def _retrieve_profile_metadata(metadata_service: MetadataService,
                                         profiles: Set[str]) -> List[AudienceRelatedMetadata]:
        """检索用户属性/标签/分群"""
        user_properties_task = asyncio.create_task(metadata_service.query_all_properties_and_tags(schema_name="users"))
        user_groups_task = asyncio.create_task(metadata_service.query_all_segments(entity_name="user"))
        user_groups: List[SegmentDefinition] = await user_groups_task
        user_properties: List[PropertyDefinition] = await user_properties_task
        result = []
        for user_property in user_properties:
            if user_property.name not in profiles:
                continue
            result.append(AudienceRelatedMetadata(
                type='user_property',
                user_property=PropertyMetadata(
                    name=user_property.name,
                    display_name=user_property.display_name,
                    data_type=user_property.data_type,
                )))
        for user_group in user_groups:
            if user_group.name not in profiles:
                continue
            result.append(AudienceRelatedMetadata(
                type='user_group',
                user_group=UserGroupMetadata(
                    name=user_group.name,
                    display_name=user_group.display_name,
                )))
        return result

    @staticmethod
    async def _retrieve_event_property_metadata(metadata_service: MetadataService,
                                                event_and_properties: Dict[str, Set[str]]) -> List[PropertyMetadata]:
        response = await metadata_service.query_event_properties_if_present(schema_name="events",
                                                                            event_and_properties=event_and_properties)
        result = []
        for e in response:
            result.append(EventMetadata(
                name=e.name,
                display_name=e.display_name,
                properties=[PropertyMetadata(
                    name=user_property.name,
                    display_name=user_property.display_name,
                    data_type=user_property.data_type,
                ) for user_property in e.properties]
            ))
        return result


def gen_metadata_prompt(related_metadata: List[AudienceRelatedMetadata]):
    if not related_metadata:
        return ''
    metadata_desc = ''
    metadata_groups = {}
    for metadata_info in related_metadata:
        cur = metadata_groups.get(metadata_info.type)
        if not cur:
            cur = []
            metadata_groups[metadata_info.type] = cur
        cur.append(metadata_info)
    for metadata_type, metadata_list in metadata_groups.items():
        # 简单起见, 此处直接用 json 字符串, 后续如果有复杂结构, 则需要构建更详细的 prompt
        metadata_desc += f"{metadata_type}:\n"
        for metadata in metadata_list:
            metadata_desc += f"  {metadata.model_dump_json()})\n"
        metadata_desc += "\n"
    return metadata_desc
