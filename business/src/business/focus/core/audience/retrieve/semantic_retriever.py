from pydantic import Field

from framework.agents.role_playing.role import BaseRoleTaskAgent, Role
from framework.agents.role_playing.task import Task, TaskInput, TaskResult, task


class TermRetrieverTaskInput(TaskInput):
    """术语检索任务输入"""
    content: str = Field(..., description='需要检索的内容描述, eg: 检索 流失用户、潜客、淘客的含义')


# TODO 考虑给出参考文档的来源信息
class TermRetrieverTaskResult(TaskResult):
    """术语检索任务输出"""
    content: str = Field(..., description='检索结果')


TERM_RETRIEVER_TASK = "term_semantic_retrieve"

SEMANTIC_RETRIEVER = Role(
    name="semantic_retriever",
    job_desc="术语检索, 负责解释专用名词的含义"
)


class SemanticRetriever(BaseRoleTaskAgent):

    def __init__(self,
                 **kwargs):
        super().__init__(
            role=SEMANTIC_RETRIEVER,
            **kwargs
        )

    @task(task_def=TERM_RETRIEVER_TASK, task_desc="检索特殊名词/术语的含义")
    async def term_semantic_retrieve(self, task_arg: TermRetrieverTaskInput) -> TermRetrieverTaskResult:
        # TODO @bql 对接知识库检索, 暂时返回 mock 结果
        task_input: TermRetrieverTaskInput = task_arg
        return TermRetrieverTaskResult(content="未检索到任何内容")
