from abc import ABC, abstractmethod
from dateutil import parser as date_parser

from typing import Literal, Optional, List, Any, Callable, Dict, Type, Set

from langchain_core.messages import HumanMessage, AIMessage
from llama_index.core.workflow import Workflow, StartEvent, Context, Event, step, StopEvent
from pydantic import BaseModel, Field
from collections import defaultdict

from common import tools
from ..retrieve.metadata_retriever import (
    AudienceRelatedMetadata,
    MetadataRetriever,
    RETRIEVE_EVENT_PROPERTIES,
    RETRIEVE_BASE_METADATA,
    gen_metadata_prompt,
    AudienceRuleUsedMetadata,
    PropertyMetadata
)
from business.focus.audience_rule import (
    AudienceRule,
    AudienceProfileRule,
    AudienceEventRule,
    AudienceEventSequenceRule,
    AudienceEventMeasureCondition,
    SingleAudienceEventSequenceRule,
    PropertyFilterCondition,
    PropertyFilterFunction,
    PropertyFilter,
    EventMeasureAggregator,
    AudienceTimeRange,
    EventMeasureFilterFunction
)
from ..parse.prompts import *
from business.focus.base_rule import DataType, <PERSON><PERSON><PERSON><PERSON><PERSON>, RelativeDateTimeUnit
from framework.agents.react import ReActAgent
from framework.agents.role_playing.role import BaseRoleTaskAgent, DelegateWorkTool, Role
from framework.agents.role_playing.task import TaskInput, TaskResult, Task, task
from framework.llm_exceptions import LLMResultValidationException
from framework.tools.llm_retry import pydantic_chat_with_retry, chat_with_retry
from framework.utils import pydantic_utils
from framework.utils.parse_utils import ParseUtils
from framework.utils.pydantic_utils import gen_pydantic_json_desc
from framework.models import BaseLLM, ModelsFactory
from framework.models.llm import LanguageModelInput

log = tools.get_logger()


class StructuredNLAudienceRuleDesc(BaseModel):
    """自然语言描述的受众规则"""
    type: Literal["rules_relation", "profile_rule", "event_rule", "event_sequence_rule"] = Field(...,
                                                                                                 description='受众规则类型, 用户属性筛选/用户行为筛选/用户行为序列筛选/复合规则(包含多个规则) 分别对应:profile_rule/event_rule/event_sequence_rule/rules_relation')
    relation: Optional[Literal["and", "or"]] = Field(default=None,
                                                     description='and/or, 表示多个规则之间的关系, 仅当 type 为 rules_relation 时有效')
    sub_rules: Optional[List["StructuredNLAudienceRuleDesc"]] = Field(default=None,
                                                                      description='多个规则的列表, 规则同当前结构, 仅当 type 为 rules_relation 时有效')
    content: Optional[str] = Field(default=None,
                                   description='受众规则的描述, 单个受众规则描述, 无法再拆解为多个规则的单一规则, 如果是用户属性筛选则仅包含单个属性, 如果是用户行为筛选则仅包含单个事件, 如果是用户行为序列筛选则仅包含一个行为序列')
    related_metadata: List[AudienceRelatedMetadata] = Field(default=None,
                                                            description='规则相关的元数据, 仅当 type 为 profile_rule/event_rule/event_sequence_rule 时有效')


class InvalidNLAudienceDesc(BaseModel):
    """无效的自然语言描述的受众规则"""
    description: str = Field(..., description='无效的人群圈选描述, 原始的人群圈选的描述片段')
    reason: str = Field(..., description='无效的原因')


class NLRuleOpTaskInput(TaskInput):
    """nl_rule_op 任务参数"""
    nl_audience_rule: str = Field(default=None, description='待修改的自然语言受众规则')
    suggestion: str = Field(..., description='修改建议, 用于指导受众规则的修改, 包括完整的相关上下文')


class NLRuleParseTaskResult(TaskResult):
    """自然语言描述的受众规则"""
    origin_nl_audience_rule: str = Field(..., description='原始自然语言描述的受众规则')
    generated: bool = Field(...,
                            description='是否生成了符合真实数据的自然语言受众规则, 仅当完全无法生成的时候为 false, 否则为true')
    invalid_descriptions: Optional[List[InvalidNLAudienceDesc]] = Field(default=None,
                                                                        description='无效的人群圈选描述及其原因, 比如: 无法理解潜客的含义')
    real_nl_audience_rule: Optional[str] = Field(default=None,
                                                 description='生成的符合真实数据自然语言受众规则, 尽可能的简短描述, 仅当 generated 为 true 时有效')
    related_metadata: Optional[List[AudienceRelatedMetadata]] = Field(default=None, description='规则相关的元数据')


def _grouping_by(objects: List[Any], key_func: Callable[[Any], Any]) -> defaultdict[Any, List[Any]]:
    grouping_by = defaultdict(list)
    for value in objects:
        key = key_func(value)
        grouping_by[key].append(value)
    return grouping_by


class NLParseTaskInput(TaskInput):
    """nl_rule_parse 任务参数"""
    audience_rule: str = Field(..., description='自然语言描述的受众规则')


class AudienceRuleParseTaskInput(TaskInput):
    """audience_rule_parse 任务参数"""
    nl_audience_rule: str = Field(default=..., description='自然语言受众规则')
    related_metadata: Optional[List[AudienceRelatedMetadata]] = Field(default=None, description='规则相关的元数据')


class AudienceRuleParseTaskOutput(TaskResult):
    """audience_rule_parse 任务输出"""
    audience_rule: AudienceRule = Field(..., description='解析后的受众规则')


NL_RULE_PARSE_TASK = Task(
    name="nl_rule_parse",
    description="结合系统中实际存在的元数据解析自然语言描述的受众规则, 生成符合实际数据的自然语言受众规则",
    input_schema=NLParseTaskInput,
    output_schema=NLRuleParseTaskResult
)

NL_RULE_OP_TASK = Task(
    name="nl_rule_op",
    description="根据用户的描述, 对当前的自然语言受众规则进行修改, 生成符合实际数据的自然语言受众规则",
    input_schema=NLRuleOpTaskInput,
    output_schema=NLRuleParseTaskResult
)

AUDIENCE_RULE_PARSE_TASK = Task(
    name="audience_rule_parse",
    description="根据自然语言描述和相关的元数据解析出结构化(AudienceRule)的受众规则",
    input_schema=AudienceRuleParseTaskInput,
    output_schema=AudienceRuleParseTaskOutput
)

NL_AUDIENCE_RULE_PARSER = Role(
    name="nl_rule_parser",
    job_desc="自然语言描述的受众规则解析器, 负责将自然语言描述的受众规则解析为符合实际数据的受众规则"
)


class NLAudienceRuleParser(BaseRoleTaskAgent):
    def __init__(self,
                 **kwargs):
        llm = kwargs.pop('llm', None)
        if not llm:
            llm = ModelsFactory.get_llm()
        super().__init__(
            llm=llm,
            role=NL_AUDIENCE_RULE_PARSER,
            **kwargs,
        )

    @task(task_def=NL_RULE_PARSE_TASK)
    async def _parse_nl_rule(self, task_input: NLParseTaskInput) -> NLRuleParseTaskResult:
        return await self._parse_nl_rule_with_messages(messages=task_input.audience_rule)

    @task(task_def=NL_RULE_OP_TASK)
    async def _modify_nl_audience_rule(self, task_input: NLRuleOpTaskInput) -> NLRuleParseTaskResult:
        origin_answer = None
        if task_input.structured_nl_audience_rule:
            origin_answer = task_input.structured_nl_audience_rule.model_dump_json()
        elif task_input.nl_audience_rule:
            origin_answer = task_input.nl_audience_rule
        else:
            raise LLMResultValidationException("miss nl_audience_rule or structured_nl_audience_rule")
        return await self._parse_nl_rule_with_messages(messages=[HumanMessage(content=task_input.nl_audience_rule),
                                                                 AIMessage(content="Final Answer: " + origin_answer),
                                                                 HumanMessage(content=task_input.suggestion)])

    @task(task_def=AUDIENCE_RULE_PARSE_TASK)
    async def _parse_audience_rule(self, task_input: AudienceRuleParseTaskInput) -> AudienceRuleParseTaskOutput:
        if not task_input.related_metadata:
            # 不存在相关元数据, 说明只能是全部用户, 直接返回
            return AudienceRuleParseTaskOutput(audience_rule=AudienceRule(select_all=True))
        split_result = await self._split_nl_rule(nl_audience_rule=task_input.nl_audience_rule,
                                                 related_metadata=task_input.related_metadata)
        result: AudienceRule = await NLAudienceRuleParseWorkflow(
            llm=ModelsFactory.get_llm(scenario='single_aud_rule_parse'), nl_rule=split_result).run()
        return AudienceRuleParseTaskOutput(audience_rule=result)

    async def _split_nl_rule(self, nl_audience_rule: str,
                             related_metadata: Optional[List[AudienceRelatedMetadata]]) -> StructuredNLAudienceRuleDesc:
        metadata_desc = gen_metadata_prompt(related_metadata)
        sys_prompt = SPLIT_NL_RULE_PROMPT.format(
            metadata_prompt=metadata_desc,
            output_prompt=pydantic_utils.gen_pydantic_json_desc(pydantic_model=StructuredNLAudienceRuleDesc))
        input_message = [sys_prompt, HumanMessage(content=nl_audience_rule)]
        # TODO 可配参数
        retry_times = 3
        try:
            split_result, real_retries = await pydantic_chat_with_retry(llm=self.llm,
                                                                        output_format=StructuredNLAudienceRuleDesc,
                                                                        with_sys_prompt_messages=input_message,
                                                                        retry_times=retry_times)
            log.info(
                f"split nl audience rule. [nl_rule='{nl_audience_rule}', result='{split_result}', retry_times={real_retries}]")
        except LLMResultValidationException as e:
            log.error(
                f"failed to split nl audience rule, response format error. [nl_audience_rule='{nl_audience_rule}', retry_times={retry_times}]",
                exc_info=e)
            raise e
        return split_result

    async def _parse_nl_rule_with_messages(self, messages: LanguageModelInput) -> NLRuleParseTaskResult:
        delegate_work_tool = DelegateWorkTool.from_role_agents(
            agents=[MetadataRetriever(llm=self.llm, task_callback=self.task_callback,
                                      tasks=[RETRIEVE_BASE_METADATA, RETRIEVE_EVENT_PROPERTIES])])
        final_tools = [delegate_work_tool]
        exec_agent = ReActAgent(
            system_prompt_background=PARSE_NL_AUDIENCE_RULE_WITH_METADATA_PROMPT,
            answer_prompt=f"最终结果(Final Answer)的内容以 JSON 字符串格式输出, 结构定义如下:\n{gen_pydantic_json_desc(NLRuleParseTaskResult)}",
            max_iterations=10,
            tools=final_tools,
            llm=self.llm,
            special_tool_parsers={
                delegate_work_tool.name: delegate_work_tool.get_tool_input_parser()
            }
        )
        return await exec_agent.achat_with_pydantic(output_cls=NLRuleParseTaskResult, prompts=messages)


class AllowFailureNLAudienceRule(BaseModel, ABC):
    succeeded: bool = Field(default=False, description='是否成功处理, 仅当所有描述都无法处理时才会返回 false')
    invalid_descriptions: Optional[List[InvalidNLAudienceDesc]] = Field(default=None,
                                                                        description='无效的人群圈选描述及其原因')
    rule: Optional[Any] = Field(default=None,
                                description='解析后的受众规则, 当所有描述都无法处理时无值')

    @abstractmethod
    def check_and_fine_tuning_rule(self, metadata: AudienceRuleUsedMetadata,
                                   property_metadata: Dict[str, Dict[str, PropertyMetadata]]) -> None:
        """检查是否使用了不存在的元数据以及规则合法性等
        :param metadata: 元数据
        :param property_metadata: 属性详情
        :raise ValueError 当存在无效的描述时抛出"""


class IndexedNLAudienceRule(BaseModel):
    index: int
    nl_rule: StructuredNLAudienceRuleDesc


def check_property_condition_param_size(function: Any,
                                        params: Optional[List[str]],
                                        min_size: int,
                                        max_size: Optional[int] = None):
    """校验参数个数是否符合预期
    :param function: 筛选函数, for log
    :param params: 参数列表
    :param min_size: 最小参数个数
    :param max_size: 最大参数个数, 当不传或者 None 时等于 min_size, <=-1 表示不限制"""
    if not params:
        if min_size > 0:
            raise LLMResultValidationException(f"{function} miss params")
        else:
            return
    if len(params) < min_size:
        raise LLMResultValidationException(f"{function} miss some params")
    # 处理 max_size 为 None 的情况
    if max_size is None:
        max_size = min_size
    if max_size == 0:
        raise LLMResultValidationException(f"{function} should not have params")
    if max_size <= -1:
        # CASE: 不限制参数最大数量
        return
    if len(params) > max_size:
        if min_size == max_size:
            raise LLMResultValidationException(f"{function} params size must be {max_size}")
        else:
            raise LLMResultValidationException(f"{function} params size must be between {min_size} and {max_size}")


def check_and_fine_tuning_datetime_param(param: str) -> str:
    """校验日期是否可解析, 并转换为 "%Y-%m-%d %H:%M:%S" 格式"""
    try:
        result = date_parser.parse(param)
        return result.strftime("%Y-%m-%d %H:%M:%S")
    except Exception:
        raise LLMResultValidationException(f"invalid datetime format: {param}")


def check_and_fine_tuning_number_param(param: str) -> str:
    """校验数字是否可解析, 并转换为标准格式的整数或者小数"""
    try:
        num = float(param)
        if num.is_integer():
            return str(int(num))
        return '{:.3f}'.format(round(num, 3))
    except Exception:
        raise LLMResultValidationException(f"invalid number format: {param}")


def check_and_fine_tuning_relative_time_params(params: List[str]):
    """校验相对时间表达式是否符合规范
    :param params: 相对时间表达式列表, 一个或者两个参数"""
    if len(params) == 1:
        params[0] = RelativeTimeExpr.from_string_expr(params[0]).to_string_expr()
    elif len(params) == 2:
        param1 = RelativeTimeExpr.from_string_expr(params[0])
        param2 = RelativeTimeExpr.from_string_expr(params[1])
        # TODO 考虑支持不同单位的转换
        if param1.unit != param2.unit:
            raise LLMResultValidationException(f"relative time unit must be same, invalid params: {params}")
        # 将小的参数放到前面, 大的参数放到后面
        if param1.number > param2.number:
            params[0], params[1] = param2.to_string_expr(), param1.to_string_expr()
        else:
            params[0], params[1] = param1.to_string_expr(), param2.to_string_expr()
    else:
        raise LLMResultValidationException(f"invalid relative params: {params}")


def check_and_fine_tuning_time_range(time_range: AudienceTimeRange):
    if not time_range:
        raise LLMResultValidationException("miss time range")
    function: PropertyFilterFunction = PropertyFilterFunction(time_range.function)
    params: List[str] = time_range.params
    """校验时间范围是否符合规范"""
    match function:
        case PropertyFilterFunction.LESS | PropertyFilterFunction.GREATER:
            check_property_condition_param_size(function, params, 1)
            params[0] = check_and_fine_tuning_datetime_param(params[0])
        case PropertyFilterFunction.BETWEEN:
            check_property_condition_param_size(function, params, 2)
            params[0] = check_and_fine_tuning_datetime_param(params[0])
            params[1] = check_and_fine_tuning_datetime_param(params[1])
        case PropertyFilterFunction.RELATIVE_AFTER | PropertyFilterFunction.RELATIVE_BEFORE:
            check_property_condition_param_size(function, params, 1)
            check_and_fine_tuning_relative_time_params(params)
        case PropertyFilterFunction.RELATIVE_BETWEEN:
            check_property_condition_param_size(function, params, 2)
            check_and_fine_tuning_relative_time_params(params)
        case _:
            raise LLMResultValidationException(f"invalid time range function: {function}")


class RelativeTimeExpr(BaseModel):
    number: int
    unit: RelativeDateTimeUnit

    @classmethod
    def from_string_expr(cls, expr: str):
        """从字符串解析为 RelativeTimeExpr
        :param expr: 字符串表达式, 格式为: {number} {unit}
        兼容单位大小写、兼容数字字符串包含正负号
        """
        param_parts = expr.split()
        if len(param_parts) != 2:
            raise LLMResultValidationException(f"invalid relative time format: {expr}")
        number, unit = param_parts
        try:
            return cls(number=int(number), unit=RelativeDateTimeUnit(unit.lower()))
        except Exception:
            raise LLMResultValidationException(f"invalid relative time format: {expr}")

    def to_string_expr(self):
        return f"{self.number} {self.unit.value}"


def check_and_fine_tuning_property_condition(property_info: PropertyMetadata, condition: PropertyFilterCondition):
    data_type = property_info.data_type
    function = condition.function
    match function:
        case PropertyFilterFunction.IS_TRUE | PropertyFilterFunction.IS_FALSE:
            if data_type != DataType.BOOLEAN:
                raise LLMResultValidationException(f"{data_type} 类型不支持 {function} 函数")
            if condition.params:
                log.debug(f"fine tuning property condition params. [condition='{condition}', new_params=[]]")
                condition.params = []
        case PropertyFilterFunction.IS_SET | PropertyFilterFunction.NOT_SET:
            if condition.params:
                log.debug(f"fine tuning property condition params. [condition='{condition}', new_params=[]]")
                condition.params = []
        case PropertyFilterFunction.EQUAL | PropertyFilterFunction.NOT_EQUAL:
            match data_type:
                case DataType.STRING:
                    check_property_condition_param_size(function, condition.params, 1, -1)
                case DataType.NUMBER:
                    check_property_condition_param_size(function, condition.params, 1)
                    condition.params[0] = check_and_fine_tuning_number_param(condition.params[0])
                case DataType.DATETIME:
                    check_property_condition_param_size(function, condition.params, 1)
                    condition.params[0] = check_and_fine_tuning_datetime_param(condition.params[0])
                case _:
                    raise LLMResultValidationException(f"{data_type} 类型不支持 {function} 函数")
        case PropertyFilterFunction.LESS | PropertyFilterFunction.GREATER:
            match data_type:
                case DataType.NUMBER:
                    check_property_condition_param_size(function, condition.params, 1)
                    condition.params[0] = check_and_fine_tuning_number_param(condition.params[0])
                case DataType.DATETIME:
                    check_property_condition_param_size(function, condition.params, 1)
                    condition.params[0] = check_and_fine_tuning_datetime_param(condition.params[0])
                case _:
                    raise LLMResultValidationException(f"{data_type} 类型不支持 {function} 函数")
        case PropertyFilterFunction.IS_EMPTY | PropertyFilterFunction.IS_NOT_EMPTY:
            if data_type != DataType.STRING:
                raise LLMResultValidationException(f"{data_type} 类型不支持 {function} 函数")
            if condition.params:
                log.debug(f"fine tuning property condition params. [condition='{condition}', new_params=[]]")
                condition.params = []
        case PropertyFilterFunction.RLIKE | PropertyFilterFunction.NOT_RLIKE | PropertyFilterFunction.CONTAIN | \
             PropertyFilterFunction.NOT_CONTAIN:
            if data_type != DataType.STRING:
                raise LLMResultValidationException(f"{data_type} 类型不支持 {function} 函数")
            check_property_condition_param_size(function, condition.params, 1)
        case PropertyFilterFunction.IN | PropertyFilterFunction.NOT_IN:
            if data_type != DataType.LIST:
                raise LLMResultValidationException(f"{data_type} 类型不支持 {function} 函数")
            check_property_condition_param_size(function, condition.params, 1)
        case PropertyFilterFunction.BETWEEN:
            match data_type:
                case DataType.NUMBER:
                    check_property_condition_param_size(function, condition.params, 2)
                    condition.params[0] = check_and_fine_tuning_number_param(condition.params[0])
                    condition.params[1] = check_and_fine_tuning_number_param(condition.params[1])
                case DataType.DATETIME:
                    check_property_condition_param_size(function, condition.params, 2)
                    condition.params[0] = check_and_fine_tuning_datetime_param(condition.params[0])
                    condition.params[1] = check_and_fine_tuning_datetime_param(condition.params[1])
                case _:
                    raise LLMResultValidationException(f"{data_type} 类型不支持 {function} 函数")
        case PropertyFilterFunction.RELATIVE_AFTER | PropertyFilterFunction.RELATIVE_BEFORE:
            if data_type != DataType.DATETIME:
                raise LLMResultValidationException(f"{data_type} 类型不支持 {function} 函数")
            check_property_condition_param_size(function, condition.params, 1)
            check_and_fine_tuning_relative_time_params(condition.params)
        case PropertyFilterFunction.RELATIVE_BETWEEN:
            if data_type != DataType.DATETIME:
                raise LLMResultValidationException(f"{data_type} 类型不支持 {function} 函数")
            check_property_condition_param_size(function, condition.params, 2)
            check_and_fine_tuning_relative_time_params(condition.params)
        case _:
            raise LLMResultValidationException(f"invalid function: {function}")


def check_event_field(entity_field: EntityField,
                      metadata: AudienceRuleUsedMetadata):
    if entity_field.entity_name != 'event':
        raise LLMResultValidationException(f"invalid event field: {entity_field.field}")
    if entity_field.event_name not in metadata.events:
        raise LLMResultValidationException(f"can not found event: {entity_field.event_name}")
    if entity_field.field_property not in metadata.events.get(entity_field.event_name):
        raise LLMResultValidationException(f"{entity_field.event_name} has no property {entity_field.field_property}")


def check_and_fine_tuning_property_filter(property_filter: PropertyFilter,
                                          metadata: AudienceRuleUsedMetadata,
                                          property_metadata: Dict[str, Dict[str, PropertyMetadata]]):
    if not property_filter or not property_filter.conditions:
        return
    for condition in property_filter.conditions:
        entity_field = EntityField.from_field(condition.field)
        if entity_field.entity_name not in property_metadata:
            raise LLMResultValidationException("invalid field:" + entity_field.field)
        property_info: PropertyMetadata = property_metadata.get(entity_field.entity_name).get(
            entity_field.field_property)
        if not property_info:
            raise LLMResultValidationException("can not found property:" + entity_field.field_property)
        if entity_field.entity_name == 'event':
            check_event_field(entity_field, metadata)
        else:
            if entity_field not in metadata.entity_fields:
                raise LLMResultValidationException("invalid field:" + entity_field.field)
        check_and_fine_tuning_property_condition(property_info=property_info, condition=condition)


class AllowFailureAudienceProfileRule(AllowFailureNLAudienceRule):
    """解析后的受众规则"""
    rule: Optional[AudienceProfileRule] = Field(default=None,
                                                description='用户属性筛选规则')

    def check_and_fine_tuning_rule(self, metadata: AudienceRuleUsedMetadata,
                                   property_metadata: Dict[str, Dict[str, PropertyMetadata]]) -> None:
        check_and_fine_tuning_property_filter(self.rule, metadata, property_metadata)


class AllowFailureAudienceEventRule(AllowFailureNLAudienceRule):
    """解析后的受众规则"""

    rule: Optional[AudienceEventMeasureCondition] = Field(default=None,
                                                          description='用户行为筛选规则')

    def check_and_fine_tuning_rule(self, metadata: AudienceRuleUsedMetadata,
                                   property_metadata: Dict[str, Dict[str, PropertyMetadata]]) -> None:
        event_rule = self.rule
        if not event_rule:
            return
        if not event_rule.measure:
            raise LLMResultValidationException("miss measure")
        measure = event_rule.measure
        if measure.event not in metadata.events:
            raise LLMResultValidationException("invalid event:" + measure.event)
        if measure.aggregator == EventMeasureAggregator.GENERAL or measure.aggregator == EventMeasureAggregator.UNIQUE:
            if measure.aggr_field:
                log.debug("fine tuning event measure aggr field. [measure='{measure}', new_aggr_field=None]")
                measure.aggr_field = None
        else:
            if not measure.aggr_field:
                raise LLMResultValidationException("miss aggr_field")
            aggr_field = EntityField.from_field(measure.aggr_field)
            check_event_field(entity_field=aggr_field, metadata=metadata)
            property_info = property_metadata.get(aggr_field.entity_name).get(aggr_field.field_property)
            if property_info.data_type != DataType.NUMBER:
                raise LLMResultValidationException(f"{aggr_field.field} is not number")
        if measure.filter:
            check_and_fine_tuning_property_filter(measure.filter, metadata, property_metadata)
        check_and_fine_tuning_time_range(event_rule.time_range)
        match event_rule.function:
            case EventMeasureFilterFunction.BETWEEN | EventMeasureFilterFunction.RANK_PERCENT_BETWEEN | EventMeasureFilterFunction.RANK_N_BETWEEN:
                check_property_condition_param_size(event_rule.function, event_rule.params, 2)
            case _:
                check_property_condition_param_size(event_rule.function, event_rule.params, 1)
        for param in event_rule.params:
            try:
                int(param)
            except Exception:
                raise LLMResultValidationException(f"event measure param must be number, invalid param: {param}")


class AllowFailureAudienceEventSequenceRule(AllowFailureNLAudienceRule):
    """解析后的受众规则"""

    rule: Optional[SingleAudienceEventSequenceRule] = Field(default=None,
                                                            description='用户行为序列筛选规则')

    def check_and_fine_tuning_rule(self, metadata: AudienceRuleUsedMetadata,
                                   property_metadata: Dict[str, Dict[str, PropertyMetadata]]) -> None:
        event_sequence_rule = self.rule
        check_and_fine_tuning_time_range(event_sequence_rule.time_range)
        if not event_sequence_rule.steps:
            raise LLMResultValidationException("miss steps")
        if len(event_sequence_rule.steps) < 2:
            raise LLMResultValidationException("steps must be more than 2")
        for event_step in event_sequence_rule.steps:
            if event_step.event not in metadata.events:
                raise LLMResultValidationException("can not found event:" + event_step.event)
            check_and_fine_tuning_property_filter(event_step.filter, metadata, property_metadata)


class InvalidAudienceRule(BaseModel):
    invalid_descriptions: List[InvalidNLAudienceDesc] = Field(default=None,
                                                              description='无效的人群圈选描述及其原因')


class MultiNestedAudienceRule(BaseModel):
    relation: Literal["and", "or"]
    sub_rules: List[Any]

    def has_simple_rule(self) -> bool:
        has_simple_rule = False
        for sub_rule in self.sub_rules:
            if not isinstance(sub_rule, MultiNestedAudienceRule):
                has_simple_rule = True
        return has_simple_rule

    def sub_rule_count(self) -> int:
        return len(self.sub_rules)


class NLAudienceRuleParseEvent(Event):
    indexed_nl_rule: IndexedNLAudienceRule


class AudienceRuleConstructEvent(Event):
    index: int
    rule: AudienceProfileRule | AudienceEventMeasureCondition | SingleAudienceEventSequenceRule | MultiNestedAudienceRule | InvalidAudienceRule


class AudienceRuleConvertEvent(Event):
    audience_rule: MultiNestedAudienceRule


class EmptyEvent(Event):
    pass


class CompoundRuleIndexInfo(BaseModel):
    relation: Literal["and", "or"]
    sub_rule_indexes: List[int]


DATA_TYPE_AND_PROMPT_MAPPING = {
    DataType.STRING: STRING_DATA_TYPE_PROMPT,
    DataType.NUMBER: NUMBER_DATA_TYPE_PROMPT,
    DataType.BOOLEAN: BOOL_DATA_TYPE_PROMPT,
    DataType.LIST: LIST_DATA_TYPE_PROMPT,
    DataType.DATETIME: DATE_DATA_TYPE_PROMPT,
}


class NLAudienceRuleParseWorkflow(Workflow):
    def __init__(self,
                 nl_rule: StructuredNLAudienceRuleDesc,
                 llm: BaseLLM,
                 max_concurrent_num: int = 3,
                 **kwargs):
        self.llm = llm
        self._init_rule_hierarchy(nl_rule)
        self._parsed_rules_ctx_key = "parsed_rules"
        concurrent_num = min(max_concurrent_num, len(self._indexed_leaf_rules))
        super().__init__(
            num_concurrent_runs=concurrent_num,
            timeout=60,
            **kwargs
        )

    def _init_rule_hierarchy(self, nl_rule: StructuredNLAudienceRuleDesc):
        # 拆解规则, 将规则的关联关系
        rule_index = 1
        rule_and_index_stack = [IndexedNLAudienceRule(nl_rule=nl_rule, index=rule_index)]
        rule_and_sub_rules_mapping = {}
        rule_and_parent_rule_mapping = {}
        indexed_leaf_rules = []
        while len(rule_and_index_stack) > 0:
            rule: IndexedNLAudienceRule = rule_and_index_stack.pop()
            if rule.nl_rule.sub_rules:
                sub_rule_indexes = []
                for sub_rule in rule.nl_rule.sub_rules:
                    rule_index += 1
                    indexed_sub_rule = IndexedNLAudienceRule(nl_rule=sub_rule, index=rule_index)
                    rule_and_index_stack.append(indexed_sub_rule)
                    sub_rule_indexes.append(rule_index)
                    rule_and_parent_rule_mapping[rule_index] = rule.index
                rule_and_sub_rules_mapping[rule.index] = CompoundRuleIndexInfo(relation=rule.nl_rule.relation,
                                                                               sub_rule_indexes=sub_rule_indexes)
            else:
                indexed_leaf_rules.append(rule)
        self._sub_rule_hierarchy: Dict[int, CompoundRuleIndexInfo] = rule_and_sub_rules_mapping
        self._parent_rule_hierarchy: Dict[int, int] = rule_and_parent_rule_mapping
        self._indexed_leaf_rules: List[IndexedNLAudienceRule] = indexed_leaf_rules

    @step
    async def _setup(self, ctx: Context, event: StartEvent) -> NLAudienceRuleParseEvent | None:
        # 初始化解析结果
        await ctx.set(self._parsed_rules_ctx_key, {})
        for indexed_rule in self._indexed_leaf_rules:
            ctx.send_event(message=NLAudienceRuleParseEvent(indexed_nl_rule=indexed_rule))
        # 返回一个空的 event, 仅仅是为了保证语义完整, 无实际作用
        return None

    @step
    async def _parse_nl_rule(self, ctx: Context, event: NLAudienceRuleParseEvent) -> AudienceRuleConstructEvent:
        indexed_nl_rule: IndexedNLAudienceRule = event.indexed_nl_rule
        nl_rule = indexed_nl_rule.nl_rule
        sys_prompt, result_schema = self._gen_parse_rule_prompt_and_schema(nl_rule=nl_rule)
        result = await self._parse_nl_rule_by_llm(sys_prompt, nl_rule, result_schema)
        return AudienceRuleConstructEvent(index=indexed_nl_rule.index, rule=result)

    @step
    async def _construct_rule(self, ctx: Context,
                              event: AudienceRuleConstructEvent) -> AudienceRuleConstructEvent | AudienceRuleConvertEvent | None:
        parent_rule_index = self._parent_rule_hierarchy.get(event.index)
        if not parent_rule_index:
            # 没有父规则, 说明整个规则已经处理完成, 清空上下文, 进行下一步转换处理
            await ctx.set(self._parsed_rules_ctx_key, {})
            if isinstance(event.rule, MultiNestedAudienceRule):
                return AudienceRuleConvertEvent(audience_rule=event.rule)
            else:
                return AudienceRuleConvertEvent(
                    audience_rule=MultiNestedAudienceRule(relation="and", sub_rules=[event.rule]))
        # 存在父规则, 需要等待同层级所有规则解析完成
        parsed_rules = await ctx.get(key=self._parsed_rules_ctx_key)
        # 对于共享对象的操作需要加锁
        async with ctx.lock:
            parsed_rules[event.index] = event.rule
            compound_rule_index_info: CompoundRuleIndexInfo = self._sub_rule_hierarchy[parent_rule_index]
            for same_hierarchy_rule_index in compound_rule_index_info.sub_rule_indexes:
                if same_hierarchy_rule_index not in parsed_rules:
                    # 有子规则未解析完成, 等待子规则解析完成
                    return None
            # 所有子规则解析完成, 构造复合规则
            parsed_sub_rules = [parsed_rules[same_hierarchy_rule] for same_hierarchy_rule in
                                compound_rule_index_info.sub_rule_indexes]
            return AudienceRuleConstructEvent(index=parent_rule_index,
                                              rule=MultiNestedAudienceRule(relation=compound_rule_index_info.relation,
                                                                           sub_rules=parsed_sub_rules))

    @step
    async def _convert_to_audience_rule(self, ctx: Context, event: AudienceRuleConvertEvent) -> StopEvent:
        multi_nested_audience_rule: MultiNestedAudienceRule = event.audience_rule
        multi_nested_audience_rule = self._simplify_nested_rule(nested_rule=multi_nested_audience_rule,
                                                                judge_nested_rule_func=lambda rule: isinstance(rule,
                                                                                                               MultiNestedAudienceRule),
                                                                list_sub_rule_func=lambda rule: [sr for sr
                                                                                                 in rule.sub_rules if
                                                                                                 not isinstance(sr,
                                                                                                                InvalidAudienceRule)],
                                                                can_merge_func=lambda rule1,
                                                                                      rule2: rule1.relation == rule2.relation,
                                                                merge_func=lambda rule,
                                                                                  sub_rules: self._merge_same_type_sub_rules(
                                                                    multi_nested_rule=rule, sub_rules=sub_rules))

        if not multi_nested_audience_rule.sub_rules:
            # 没有有效规则, 此时返回兜底的规则, 即全部用户
            return AudienceRule(select_all=True)
        audience_rule = self._try_direct_convert_to_audience_rule(multi_nested_audience_rule)
        if not audience_rule:
            audience_rule = self._force_convert_to_audience_rule(multi_nested_audience_rule)
        return StopEvent(result=audience_rule)

    def _try_direct_convert_to_audience_rule(self, multi_nested_audience_rule: MultiNestedAudienceRule) -> Optional[
        AudienceRule]:
        nested_layer_count = self._calc_rule_deep(nested_rule=multi_nested_audience_rule,
                                                  judge_nested_rule_func=lambda rule: isinstance(rule,
                                                                                                 MultiNestedAudienceRule),
                                                  list_sub_nested_rule_func=lambda rule: rule.sub_rules)
        if nested_layer_count == 1:
            _grouping_by(objects=multi_nested_audience_rule.sub_rules, key_func=lambda rule: type(rule))
            single_type_count = defaultdict(int)
            for sub_rule in multi_nested_audience_rule.sub_rules:
                current_count = single_type_count[type(sub_rule)]
                if current_count >= 1:
                    return None
                single_type_count[type(sub_rule)] += 1
        else:
            return None
        audience_rule = AudienceRule(select_all=False, relation=multi_nested_audience_rule.relation)
        for sub_rule in multi_nested_audience_rule.sub_rules:
            if isinstance(sub_rule, AudienceProfileRule):
                audience_rule.profile_rule = sub_rule
            elif isinstance(sub_rule, AudienceEventRule):
                audience_rule.event_rule = sub_rule
            elif isinstance(sub_rule, AudienceEventSequenceRule):
                audience_rule.event_sequence_rule = sub_rule
            else:
                raise LLMResultValidationException(f"invalid rule type: {type(sub_rule)}")
        return audience_rule

    def _merge_same_type_sub_rules(self, multi_nested_rule: MultiNestedAudienceRule,
                                   sub_rules: List[Any]) -> MultiNestedAudienceRule:
        # 将多个 SingleAudienceEventSequenceRule 转换为一个 AudienceEventSequenceRule
        # 将多个 AudienceEventMeasureCondition 转换为一个 AudienceEventRule
        # 尝试合并多个 AudienceEventSequenceRule、AudienceEventRule、AudienceProfileRule
        final_sub_rules = []
        rules_by_type = _grouping_by(sub_rules, lambda r: type(r))
        single_event_sequence_rules = rules_by_type.get(SingleAudienceEventSequenceRule)
        if single_event_sequence_rules:
            rules_by_type[AudienceEventSequenceRule].append(
                AudienceEventSequenceRule(relation=multi_nested_rule.relation,
                                          multi_sequence_rules=single_event_sequence_rules))

        audience_event_conditions = rules_by_type.get(AudienceEventMeasureCondition)
        if audience_event_conditions:
            rules_by_type[AudienceEventRule].append(
                AudienceEventRule(relation=multi_nested_rule.relation,
                                  events=audience_event_conditions))
        event_sequence_rules = rules_by_type.get(AudienceEventSequenceRule)
        if event_sequence_rules:
            same_type_rules_by_relation = _grouping_by(event_sequence_rules, lambda r: r.relation)
            for relation, same_relation_rules in same_type_rules_by_relation.items():
                final_sub_rules.append(AudienceEventSequenceRule(
                    relation=relation,
                    multi_sequence_rules=[event_sequence_rules for sr in same_relation_rules for event_sequence_rules in
                                          sr.multi_sequence_rules]))
        audience_event_rules = rules_by_type.get(AudienceEventRule)
        if audience_event_rules:
            same_type_rules_by_relation = _grouping_by(audience_event_rules, lambda r: r.relation)
            for relation, same_relation_rules in same_type_rules_by_relation.items():
                final_sub_rules.append(AudienceEventRule(
                    relation=relation,
                    events=[event for sr in same_relation_rules for event in sr.events]))
        audience_profile_rules = rules_by_type.get(AudienceProfileRule)
        if audience_profile_rules:
            same_type_rules_by_relation = _grouping_by(audience_profile_rules, lambda r: r.relation)
            for relation, same_relation_rules in same_type_rules_by_relation.items():
                final_sub_rules.append(AudienceProfileRule(
                    relation=relation,
                    conditions=[condition for sr in same_relation_rules for condition in sr.conditions]))
        return MultiNestedAudienceRule(relation=multi_nested_rule.relation, sub_rules=final_sub_rules)

    def _list_all_simple_rules(self, nested_rule: Any,
                               judge_nested_rule_func: Callable[[Any], bool],
                               list_sub_nested_rule_func: Callable[[Any], List[Any]]) -> List[Any]:
        result = []
        for sub_rule in list_sub_nested_rule_func(nested_rule):
            if judge_nested_rule_func(sub_rule):
                result.extend(self._list_all_simple_rules(sub_rule, judge_nested_rule_func, list_sub_nested_rule_func))
            else:
                result.append(sub_rule)
        return result

    def _force_convert_to_audience_rule(self,
                                        multi_nested_audience_rule: MultiNestedAudienceRule) -> AudienceRule:
        # TODO 考虑使用大模型进行转换, 保留最关键部分的规则
        # 若第一层存在非嵌套规则, 则直接舍弃其他嵌套规则
        # 若第一层全是嵌套规则, 则保留最复杂的嵌套规则, 并尝试将该规则转换为 audience rule
        multi_nested_rule_stack = [multi_nested_audience_rule]
        audience_rule = None
        while len(multi_nested_rule_stack) > 0:
            origin_rule = multi_nested_rule_stack.pop()
            if origin_rule.has_simple_rule():
                audience_rule = AudienceRule(select_all=False, relation=origin_rule.relation)
                for sub_rule in origin_rule.sub_rules:
                    if isinstance(sub_rule, AudienceProfileRule):
                        audience_rule.profile_rule = sub_rule
                    elif isinstance(sub_rule, AudienceEventRule):
                        audience_rule.event_rule = sub_rule
                    elif isinstance(sub_rule, AudienceEventSequenceRule):
                        audience_rule.event_sequence_rule = sub_rule
                break
            else:
                # 按照 sub rule count 排序
                sorted_by_rule_count_sub_rules = sorted(origin_rule.sub_rules, key=lambda r: r.sub_rule_count(),
                                                        reverse=True)
                sample_rules = [r for r in sorted_by_rule_count_sub_rules if r.has_simple_rule()]
                if len(sample_rules) > 0:
                    multi_nested_rule_stack.append(sample_rules[0])
                else:
                    multi_nested_rule_stack.append(sorted_by_rule_count_sub_rules[0])
        log.warn(
            f"force convert to audience rule. [multi_nested_audience_rule='{multi_nested_audience_rule}', final_audience_rule='{audience_rule}']")
        return audience_rule

    def _calc_rule_deep(self, nested_rule: Any,
                        judge_nested_rule_func: Callable[[Any], bool],
                        list_sub_nested_rule_func: Callable[[Any], List[Any]]
                        ) -> int:
        """计算嵌套规则的深度"""
        layer_rules = [[nested_rule]]
        current_deep = 0
        while len(layer_rules) > 0:
            current_layer_rules = layer_rules.pop()
            next_layer_rules = [r for sub_rule in current_layer_rules if judge_nested_rule_func(sub_rule)
                                for r in list_sub_nested_rule_func(sub_rule)]
            if len(next_layer_rules) > 0:
                layer_rules.append(next_layer_rules)
                current_deep += 1
        return current_deep

    def _simplify_nested_rule(self, nested_rule: Any,
                              judge_nested_rule_func: Callable[[Any], bool],
                              list_sub_rule_func: Callable[[Any], List[Any]],
                              can_merge_func: Callable[[Any, Any], bool],
                              merge_func: Callable[[Any, List[Any]], Any],
                              allow_merge_when_single_sub_rule: bool = True) -> Any:
        """简化嵌套规则的层级关系"""
        if not judge_nested_rule_func(nested_rule):
            return nested_rule
        final_sub_rules = []
        sub_rules = list_sub_rule_func(nested_rule)
        for sub_rule in sub_rules:
            simplified_sub_rule = self._simplify_nested_rule(nested_rule=sub_rule,
                                                             judge_nested_rule_func=judge_nested_rule_func,
                                                             list_sub_rule_func=list_sub_rule_func,
                                                             can_merge_func=can_merge_func,
                                                             merge_func=merge_func,
                                                             allow_merge_when_single_sub_rule=allow_merge_when_single_sub_rule)
            if judge_nested_rule_func(simplified_sub_rule):
                # CASE: 嵌套的子规则中只有一条规则时, 允许合并到当前规则
                grandchild_rules = list_sub_rule_func(simplified_sub_rule)
                if len(grandchild_rules) == 0:
                    continue
                elif len(grandchild_rules) == 1 and allow_merge_when_single_sub_rule:
                    final_sub_rules.append(grandchild_rules[0])
                    continue
                elif can_merge_func(nested_rule, simplified_sub_rule):
                    final_sub_rules.extend(grandchild_rules)
                    continue
            final_sub_rules.append(simplified_sub_rule)
        return merge_func(nested_rule, final_sub_rules)

    def _gen_parse_rule_prompt_and_schema(self, nl_rule: StructuredNLAudienceRuleDesc) -> (
            str, AllowFailureNLAudienceRule):
        related_metadata: List[AudienceRelatedMetadata] = nl_rule.related_metadata
        metadata_prompt = gen_metadata_prompt(related_metadata)
        property_filter_prompt = self.gen_property_filter_prompt(related_metadata)
        match nl_rule.type:
            case "profile_rule":
                prompt = PROFILE_RULE_PARSE_PROMPT.format(metadata_prompt=metadata_prompt,
                                                          property_filter_prompt=property_filter_prompt,
                                                          output_prompt=gen_pydantic_json_desc(
                                                              AllowFailureAudienceProfileRule))
                result_schema = AllowFailureAudienceProfileRule
            case "event_rule":
                prompt = EVENT_RULE_PARSE_PROMPT.format(metadata_prompt=metadata_prompt,
                                                        property_filter_prompt=property_filter_prompt,
                                                        output_prompt=gen_pydantic_json_desc(
                                                            AllowFailureAudienceEventRule))
                result_schema = AllowFailureAudienceEventRule
            case "event_sequence_rule":
                prompt = EVENT_SEQUENCE_RULE_PARSE_PROMPT.format(metadata_prompt=metadata_prompt,
                                                                 property_filter_prompt=property_filter_prompt,
                                                                 output_prompt=gen_pydantic_json_desc(
                                                                     AllowFailureAudienceEventSequenceRule))
                result_schema = AllowFailureAudienceEventSequenceRule
            case _:
                raise LLMResultValidationException(f"invalid rule type: {nl_rule.type}")
        return prompt, result_schema

    def _parse_nl_rule_llm_response(self, llm_response: str,
                                    output_format: Type[AllowFailureNLAudienceRule],
                                    metadata: AudienceRuleUsedMetadata,
                                    property_metadata: Dict[str, Dict[str, PropertyMetadata]]
                                    ) -> AllowFailureNLAudienceRule:
        allow_failure_result: AllowFailureNLAudienceRule = ParseUtils.extract_model_from_text(text=llm_response,
                                                                                              model=output_format)
        if not allow_failure_result.succeeded:
            if not allow_failure_result.invalid_descriptions:
                raise LLMResultValidationException("invalid rule miss invalid description")
        else:
            if not allow_failure_result.rule:
                raise LLMResultValidationException("miss rule when succeeded")
        allow_failure_result.check_and_fine_tuning_rule(metadata=metadata, property_metadata=property_metadata)
        return allow_failure_result

    async def _parse_nl_rule_by_llm(self, prompt: str, nl_rule: StructuredNLAudienceRuleDesc,
                                    output: Type[
                                        AllowFailureNLAudienceRule]) -> Any | InvalidAudienceRule:
        nl_rule_desc = nl_rule.content
        input_messages = [{'role': 'system', 'content': prompt}, {'role': 'user', 'content': nl_rule_desc}]
        used_metadata = AudienceRuleUsedMetadata(events=defaultdict(set), entity_fields=set())
        property_metadata: Dict[str, Dict[str, PropertyMetadata]] = defaultdict(dict)
        for rm in nl_rule.related_metadata:
            match rm.type:
                case "event":
                    event_name = rm.event.name
                    event_properties = property_metadata["event"]
                    event_property_set = used_metadata.events[event_name]
                    if rm.event.properties:
                        for p in rm.event.properties:
                            event_property_set.add(p.name)
                            event_properties[p.name] = p
                case "user_property":
                    user_properties = property_metadata["user"]
                    user_properties[rm.user_property.name] = rm.user_property
                    used_metadata.entity_fields.add(EntityField.from_entity("user", rm.user_property.name))
                case "user_group":
                    user_properties = property_metadata["user"]
                    up = rm.user_group.to_property_metadata()
                    user_properties[up.name] = up
                    used_metadata.entity_fields.add(EntityField.from_entity("user", up.name))
                case _:
                    raise LLMResultValidationException(f"invalid metadata type: {rm.type}")
        # TODO 可配参数
        retry_times = 3
        try:
            result, real_retries = await chat_with_retry(llm=self.llm,
                                                         output_parser=lambda
                                                             r: self._parse_nl_rule_llm_response(
                                                             llm_response=r, output_format=output,
                                                             metadata=used_metadata,
                                                             property_metadata=property_metadata),
                                                         with_sys_prompt_messages=input_messages,
                                                         retry_times=retry_times)
        except Exception as e:
            log.error(
                f"failed to parse single nl audience rule, response format error. [nl_audience_rule='{nl_rule_desc}', retry_times={retry_times}]",
                exc_info=e)
            raise e
        if not result.succeeded:
            log.warn(
                f"failed to parse nl rule, then ignore. [nl_rule='{nl_rule}', invalid_desc='{result}', retry_times={real_retries}]")
            return InvalidAudienceRule(invalid_descriptions=result.invalid_descriptions)
        elif result.invalid_descriptions:
            log.warn(
                f"rule has some invalid description, then ignore. [nl_rule='{nl_rule}', result='{result}', retry_times={real_retries}]")
        else:
            log.info(f"parse nl rule by llm. [nl_rule='{nl_rule}', result='{result}', retry_times={real_retries}]")
            return result.rule

    @staticmethod
    def gen_property_filter_prompt(related_metadata: List[AudienceRelatedMetadata]):
        if not related_metadata:
            return ''
        related_data_types: Set[DataType] = set()
        for metadata in related_metadata:
            match metadata.type:
                case "event":
                    # 只要包含事件, 则需要添加日期筛选的提示词
                    related_data_types.add(DataType.DATETIME)
                    if metadata.event.properties:
                        for event_property in metadata.event.properties:
                            related_data_types.add(event_property.data_type)
                case "user_property":
                    related_data_types.add(metadata.user_property.data_type)
                case "user_group":
                    related_data_types.add(DataType.BOOLEAN)
                case _:
                    raise LLMResultValidationException(f"invalid metadata type: {metadata.type}")
        property_filter_prompt = '\n'.join(
            [DATA_TYPE_AND_PROMPT_MAPPING.get(data_type, '') for data_type in related_data_types])
        return PROPERTY_FILTER_PROMPT.format(related_function_prompt=property_filter_prompt)
