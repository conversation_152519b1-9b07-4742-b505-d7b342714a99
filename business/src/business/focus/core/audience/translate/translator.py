from pydantic import BaseModel, Field

from business.focus.audience_rule import AudienceRule
from ..parse.nl_rule_parser import NLAudienceRuleParseWorkflow
from ..retrieve.metadata_retriever import MetadataRetriever, gen_metadata_prompt
from .prompts import AUDIENCE_RULE2NL_PROMPT
from framework.agents.role_playing.role import BaseRoleTaskAgent, Role
from framework.agents.role_playing.task import task, TaskInput, Task, TaskResult
from framework.utils import pydantic_utils
from framework.models import BaseLLM, ModelsFactory

AUDIENCE_RULE_TRANSLATOR = Role(
    name="audience_rule_translator",
    job_desc="将结构化的受众规则转换为自然语言描述")


class AudienceRuleTranslateTaskInput(TaskInput):
    audience_rule: AudienceRule = Field(..., description='受众规则')


class AudienceRuleTranslateTaskResult(TaskResult):
    audience_rule: AudienceRule = Field(..., description='受众规则')
    nl_desc: str = Field(..., description='受众规则的自然语言描述')


AUDIENCE_RULE_TO_NL_TASK = Task(name="audience_rule_to_nl", description="将受众规则翻译为自然语言描述",
                                input_schema=AudienceRuleTranslateTaskInput,
                                output_schema=AudienceRuleTranslateTaskResult)


class AudienceRuleTranslator(BaseRoleTaskAgent):
    def __init__(self,
                 **kwargs):
        self.metadata_retriever = MetadataRetriever()
        llm = kwargs.pop('llm', None)
        if llm is None:
            llm = ModelsFactory.get_llm()
        super().__init__(
            llm=llm,
            role=AUDIENCE_RULE_TRANSLATOR,
            **kwargs,
        )

    @task(task_def=AUDIENCE_RULE_TO_NL_TASK)
    async def _translate_audience_rule(self,
                                       task_input: AudienceRuleTranslateTaskInput) -> AudienceRuleTranslateTaskResult:
        audience_rule = task_input.audience_rule
        if audience_rule.select_all:
            return AudienceRuleTranslateTaskResult(audience_rule=audience_rule, nl_desc="所有用户")
        prompt = await self._fill_prompt(audience_rule)
        llm_response = await self.llm.acomplete(input={'role': 'system', 'content': prompt})
        return AudienceRuleTranslateTaskResult(audience_rule=audience_rule, nl_desc=llm_response.content)

    async def _fill_prompt(self, audience_rule: AudienceRule) -> str:
        metadata = await self.metadata_retriever.retrieve_audience_rule_metadata(audience_rule)
        metadata_prompt = gen_metadata_prompt(related_metadata=metadata)
        audience_rule_str = audience_rule.model_dump_json()
        return AUDIENCE_RULE2NL_PROMPT.format(metadata=metadata_prompt,
                                              audience_rule_desc=pydantic_utils.gen_pydantic_json_desc(AudienceRule),
                                              property_filter_prompt=NLAudienceRuleParseWorkflow
                                              .gen_property_filter_prompt(metadata),
                                              audience_rule=audience_rule_str)
