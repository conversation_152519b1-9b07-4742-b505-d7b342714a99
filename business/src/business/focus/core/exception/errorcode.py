from business.dependency_apis.sensors_openapi.http import SensorsdataCommonErrorInfo


class ErrorCodeException(Exception):
    def __init__(self, error_info: SensorsdataCommonErrorInfo):
        self.error_info = error_info
        super().__init__(error_info.code + ": " + str(error_info.description))

    def __str__(self):
        return self.error_info.model_dump_json(exclude_none=True, indent=2)
