from typing import Optional

from common import tools
from ..prompts.canvas_design_requirements_check_prompts import CANVAS_DESIGN_REQUIREMENTS_CHECK_SYSTEM_PROMPT, \
    get_canvas_design_requirements_check_system_prompt
from framework.models import BaseLLM, ModelsFactory
from framework.agents.autogen import (
    ParticipantAgent
)

log = tools.get_logger()


class RequirementCheckAgent(ParticipantAgent):
    """画布设计需求确认Agent"""

    def __init__(
            self,
            llm: Optional[BaseLLM] = None,
            **kwargs,
    ):
        if not llm:
            r_llm = ModelsFactory.get_llm(scenario='single_aud_rule_parse')
        else:
            r_llm = llm

        prompt_template = tools.asyncio_run(get_canvas_design_requirements_check_system_prompt)
        prompt = prompt_template.render()
        kwargs = {
                     "description": ("检查营销画布的需求是否足够创建画布策略，"
                                     "本角色不能补充需求，也不能直接和用户交互，"
                                     "如果需求不明确，则本角色可能会要求补充信息。"),
                     "name": "requirement_check_role",
                     "system_prompt": prompt,
                     "llm": r_llm,
                     "prompt_file_name": "requirement_check_role.json"
                 } | kwargs
        super().__init__(**kwargs)
