import json
from typing import Optional, Generator, Any, AsyncGenerator

from langchain_core.messages import BaseMessage, HumanMessage

from common import tools
from ..prompts.canvas_design_structure_generate_prompts import CANVAS_DESIGN_STRUCTURE_GENERATE_SYSTEM_PROMPT, \
    get_canvas_design_structure_generate_system_prompt
from framework.models import BaseLLM, ModelsFactory
from framework.agents.autogen import (
    ParticipantAgent
)

log = tools.get_logger()


class CanvasStructureGenerateAgent(ParticipantAgent):
    """画布结构生成"""

    def __init__(
            self,
            llm: Optional[BaseLLM] = None,
            **kwargs,
    ):
        if not llm:
            r_llm = ModelsFactory.get_llm(scenario='default')
        else:
            r_llm = llm

        prompt_template = tools.asyncio_run(get_canvas_design_structure_generate_system_prompt)
        prompt = prompt_template.render()
        kwargs = {
                     "description": "根据画布推荐角色所推荐的画布信息，给出画布的结构化的设计，需要完整的画布需求信息才可给出结构设计",
                     "name": "strategy_draft_role",
                     "system_prompt": prompt,
                     "llm": r_llm,
                     "prompt_file_name": "strategy_draft_role.json"
                 } | kwargs
        super().__init__(**kwargs)
        self.latest_design = None

    def run(self) -> BaseMessage:
        """运行 agent 步骤"""
        history = self.get_history()
        history.append(
            HumanMessage(content=f"你是 {self.name}，请按照要求立即给出画布结构", name='system'))
        response = self.chat(prompts=history)
        canvas_structure = self._extract_canvas_structure(response)
        response = f"```json\n{json.dumps(canvas_structure, indent=4, ensure_ascii=False)}\n```"
        return self._convert_message(message={'role': 'assistant', 'content': response, 'name': self.name})[0]

    def stream_run(self) -> Generator[BaseMessage, Any, Any]:
        yield self.run()

    async def arun(self) -> BaseMessage:
        """运行 agent 步骤"""
        history = self.get_history()
        history.append(
            HumanMessage(content=f"你是 {self.name}，请按照要求立即给出画布结构", name='system'))
        response = await self.achat(prompts=history)
        canvas_structure = self._extract_canvas_structure(response)
        response = f"```json\n{json.dumps(canvas_structure, indent=4, ensure_ascii=False)}\n```"
        return self._convert_message(message={'role': 'assistant', 'content': response, 'name': self.name})[0]

    async def astream_run(self) -> AsyncGenerator[Any, Any]:
        response = await self.arun()
        self.latest_design = self._extract_canvas_structure(response.content)
        yield self.latest_design

    def _extract_canvas_structure(self, response: str) -> Any | None:
        try:
            response = tools.extract_code_from_text(response)
            response = json.loads(response)
            if isinstance(response, str):
                response = json.loads(response)
            return self._extract_to_idea(json.dumps(response, ensure_ascii=False))
        except BaseException as e:
            log.error(f"extract_code_from_text error", e)

    def _extract_to_idea(self, response: str) -> Any | None:
        try:
            response = json.loads(response)
            if isinstance(response, str):
                return None

            if isinstance(response, dict) and 'idea' in response:
                return json.dumps(response, ensure_ascii=False)
            if isinstance(response, dict):
                for _, value in response.items():
                    ch_rel = self._extract_to_idea(json.dumps(value, ensure_ascii=False))
                    if ch_rel is not None:
                        return ch_rel
            elif isinstance(response, list):
                for item in response:
                    ch_rel = self._extract_to_idea(json.dumps(item, ensure_ascii=False))
                    if ch_rel is not None:
                        return ch_rel
            else:
                return None
        except BaseException as e:
            log.error(f"extract to idea error", e)
            return None

        return None
