CANVAS_COMMON_KNOWLEDGE_RETRIEVE_PROMPT = """
请根据以下的营销需求描述，尽可能返回相关的信息，为创建一个高质量的营销流程画布，提供信息。
营销需求如下：
{canvas_requirement}
返回的信息请使用markdown格式
"""
CANVAS_SENSORS_KNOWLEDGE_RETRIEVE_PROMPT = """
请结合神策营销系统的运营能力和知识背景， 请根据以下的营销需求描述，尽可能返回相关的信息，为创建一个高质量的营销流程画布，提供信息。
营销需求如下：
{canvas_requirement}
返回的信息请使用markdown格式
"""
CANVAS_INDUSTRY_KNOWLEDGE_RETRIEVE_PROMPT = """
请结合行业知识库的背景知识， 请根据以下的营销需求描述，尽可能返回相关的信息，比如画布模板、营销思路和建议等，为创建一个高质量的营销流程画布，提供信息。
营销需求如下：
{canvas_requirement}
返回的信息请使用markdown格式
"""
CANVAS_CUSTOM_KNOWLEDGE_RETRIEVE_PROMPT_V1 = """
你是一个营销专家，善于检索数据，请结合客户自身的元数据信息、历史画布信息、营销画布需求，返回创建营销画布所需要的事件元数据信息、标签元数据信息、属性元数据信息、分群元数据信息等。
画布需求如下：
{canvas_requirement}

### 事件元数据
- eventBirthdayEntry │ 生日营销入口 │ 每月1日触发生日月会员流程  
- eventCouponClaimed │ 优惠券领取 │ 监测优惠券到账状态（需<24h验证）  
### 标签元数据
- tagVIP_Level3 │ 白金会员 │ 年消费≥5W的核心客户  
- tagBirthdayMonth │ 当月寿星 │ 生日信息已验证会员  
### 属性元数据
- propCouponAmount │ 优惠额度 │ 根据会员等级动态配置（L1:50,L2:100...）  
- propPushTemplate │ 推送模板 │ 含个性化称呼的生日祝福模板  
### 分群元数据
- segmentBirthday_Tiered │ 分级生日客群 │ 按会员等级划分的生日月分群  


"""
CANVAS_CUSTOM_KNOWLEDGE_RETRIEVE_PROMPT_V2 = """
# 你是一个营销专家，善于检索及分析数据，请根据画布需求，检索元数据信息和历史画布信息（历史策略信息），返回两部分数据：
- 第一部分数据是检索到相关的历史策略信息，按照目标完成率、匹配度等因素，按顺序返回相应的画布信息，如果超过3个，则截取前3个；
- 第二部分数据是检索到相关的元数据信息，包含事件元数据、用户属性元数据、标签元数据、分群元数据信息。

# 画布需求如下：
{canvas_requirement}

# 返回历史策略信息说明如下：
检索到并推荐参考的画布信息，用文字详细描述，层次清晰，必须包含以下部分(画布名称/画布ID/画布相似度指数（0-100%)/画布转化效果（0-100%）/画布详细策略)

# 返回的格式如下所示：
# 优先推荐的画布信息如下：
## 画布1：
 - 画布名称：xxx
 - 画布ID： xxx
 - 画布相似度：xxx
 - 画布转化效果：xxx
 - 画布策略拆解：
   1. 定时重复型进入，运行周期1年，每月1号上午9点，圈选生日在本月的会员进入，不允许重入。
   2. 根据会员等级分流
   3. 分别对不同等级的会员发送对应额度的优惠券，不参与触达频次限制和勿扰设置。
   4. 判断优惠券到账
   5. 发送APP 推送，内容为生日祝福和生日福利。不参与触达频次限制和勿扰设置。
   6. 流程结束
   
## 画布2:
 ...

## 画布3:
 ...

#元数据信息：
## 事件元数据信息如下：
事件A的英文名称 事件A的中文名称 事件A的简单描述
事件B的英文名称 事件B的中文名称 事件B的简单描述
事件C的英文名称 事件C的中文名称 事件C的简单描述
事件D的英文名称 事件D的中文名称 事件D的简单描述
...

## 标签元数据信息如下：
标签A的英文名称 标签A的中文名称 标签A的简单描述
标签B的英文名称 标签B的中文名称 标签B的简单描述
标签C的英文名称 标签C的中文名称 标签C的简单描述
标签D的英文名称 标签D的中文名称 标签D的简单描述
...

## 属性元数据信息如下：
属性A的英文名称 属性A的中文名称 属性A的简单描述
属性B的英文名称 属性B的中文名称 属性B的简单描述
属性C的英文名称 属性C的中文名称 属性C的简单描述
属性D的英文名称 属性D的中文名称 属性D的简单描述
...

## 分群元数据信息如下：
分群A的英文名称 分群A的中文名称 分群A的简单描述
分群B的英文名称 分群B的中文名称 分群B的简单描述
分群C的英文名称 分群C的中文名称 分群C的简单描述
分群D的英文名称 分群D的中文名称 分群D的简单描述
...

"""

CANVAS_CUSTOM_KNOWLEDGE_RETRIEVE_PROMPT_V3 = """
# 你是一个营销专家，善于检索及分析数据，请根据画布需求，检索历史画布信息（历史策略信息）:
1.按照目标完成率、匹配度等因素，按顺序返回相应的画布信息，如果超过3个，则截取前3个；
2.检索到并推荐参考的画布信息，用文字详细描述，层次清晰，markdown格式，必须包含以下部分(画布名称/画布ID/画布的营销策略/画布相似度指数/画布转化效果)

# 画布需求如下：
{canvas_requirement}

# 请严格按照下面格式返回信息，使用markdown格式
## 画布1：
 - 画布名称：xxx
 - 画布ID： xxx
 - 画布相似度：xxx
 - 画布转化效果：xxx
 - 画布策略拆解：
   1. 定时重复型进入，运行周期1年，每月1号上午9点，圈选生日在本月的会员进入，不允许重入。
   2. 根据会员等级分流
   3. 分别对不同等级的会员发送对应额度的优惠券，不参与触达频次限制和勿扰设置。
   4. 判断优惠券到账
   5. 发送APP 推送，内容为生日祝福和生日福利。不参与触达频次限制和勿扰设置。
   6. 流程结束
   
## 画布2:
 ...

## 画布3:
 ...


"""
CANVAS_CUSTOM_KNOWLEDGE_RETRIEVE_PROMPT = CANVAS_CUSTOM_KNOWLEDGE_RETRIEVE_PROMPT_V3
