from enum import Enum
from typing import Optional, Self


class RelativeDateTimeUnit(Enum):
    """相对时间单位"""
    DAY = "day"
    WEEK = "week"
    MONTH = "month"
    YEAR = "year"


class DataType(Enum):
    """数据类型"""
    STRING = "string"
    NUMBER = "number"
    BOOLEAN = "bool"
    DATETIME = "datetime"
    LIST = "list"


class EntityField:
    """
    受众字段结构

    格式: {entity_name}.{property_name}、event.{event}.{property_name}
    eg： user.age、event.$appPay.amount
    """

    def __init__(self,
                 field: str = None,
                 entity_name: str = None,
                 event_name: Optional[str] = None,
                 field_property: str = None):
        if field is not None:
            # CASE: init by field
            self.field = field
            field_parts = field.split('.')
            if len(field_parts) == 2:
                self.entity_name = field_parts[0]
                self.field_property = field_parts[1]
            elif len(field_parts) == 3 and field_parts[0] == 'event':
                self.entity_name = field_parts[0]
                self.event_name = field_parts[1]
                self.field_property = field_parts[2]
            else:
                raise ValueError(f'Invalid field: {field}')
        elif event_name is not None and field_property is not None:
            # CASE: init by event
            self.entity_name = 'event'
            self.event_name = event_name
            self.field = f'{entity_name}.{event_name}.{field_property}'
        elif entity_name is not None and field_property is not None:
            # CASE: init by entity_name and field_property
            self.entity_name = entity_name
            self.field_property = field_property
            self.field = f'{entity_name}.{field_property}'
        else:
            raise ValueError('Invalid field')

    @classmethod
    def from_event(cls, event_name: str, field_property: str) -> Self:
        return cls(event_name=event_name, field_property=field_property)

    @classmethod
    def from_field(cls, field: str) -> Self:
        return cls(field=field)

    @classmethod
    def from_entity(cls, entity_name: str, event_property: str) -> Self:
        return cls(entity_name=entity_name, field_property=event_property)

    def __str__(self):
        return self.field

    def __hash__(self):
        return hash(self.field)

    def __eq__(self, other):
        return self.field == other.field
