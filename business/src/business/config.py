import os
from pathlib import Path

import dotenv

dotenv.load_dotenv()

DATA_PATH = Path(os.environ.get('DATA_PATH', str(Path().home() / "data")))
KNOWLEDGE_DATA_PATH = DATA_PATH / "knowledge"
KNOWLEDGE_DATA_PATH.mkdir(parents=True, exist_ok=True)

DEMO_SENSORS_PROJECT_INFO = {
    'organization_id': os.environ.get('ORGANIZATION_ID', 'org-sep-13024'),
    'cdp_host': os.environ.get('CDP_HOST', 'http://**********:8107/'),
    'project_id': int(os.environ.get('PROJECT_ID', 1)),
    'project_name': os.environ.get('PROJECT_NAME', 'default'),
    'api_key': os.environ.get('API_KEY', '#K-9NpCXO7OEz13NHMF5zr6JJgMGOeLbcLN'),
    'industry': os.environ.get('INDUSTRY', 'finance_banking'),
}
