[project]
name = "sensors-focus-ai"
version = "0.1.0"
description = "Focus AI Project"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "apis",
    "core",
    "web",
    "framework",
    "business",
    "backend-server",
    "crawlers",
    "common",
    "prompt-server",
    "colorlog>=6.9.0",
]

[tool.uv.sources]
apis = { workspace = true }
core = { workspace = true }
web = { workspace = true }
framework = { workspace = true }
business = { workspace = true }
crawlers = { workspace = true }
backend-server = { workspace = true }
prompt-server = { workspace = true }
common = { workspace = true }

[tool.uv.workspace]
exclude = ["docker", "helm", "static"]
members = [
    "apis",
    "core",
    "web",
    "framework",
    "business",
    "backend-server",
    "prompt-server",
    "crawlers",
    "common",
]

[[tool.uv.index]]
url = "https://mirrors.aliyun.com/pypi/simple/"
default = true

[dependency-groups]
dev = []
