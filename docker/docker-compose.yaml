x-shared-envs: &shared_envs
  USER: root
  LOCAL_DATA_STORAGE_DIR: ${LOCAL_DATA_STORAGE_DIR:-$HOME/data}
  DATA_PATH: ${DATA_PATH:-/root/data}
  POSTGRES_HOSTS: ${POSTGRES_HOSTS:-postgres}
  POSTGRES_PORT: ${POSTGRES_PORT:-5432}
  POSTGRES_DB: ${POSTGRES_DB:-sensors_ai}
  POSTGRES_USER: ${POSTGRES_USER:-postgres}
  POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-SensorsAI!}
  in_docker: yes

services:
  ai-qdrant:
    image: qdrant/qdrant:v1.12.5
    container_name: ai-qdrant
    environment:
      <<: *shared_envs
    volumes:
      - ${LOCAL_DATA_STORAGE_DIR:-$HOME/data/sensors-ai}/qdrant:/qdrant/storage
    ports:
      - 6333:6333
      - 6334:6334
      - 6335:6335
    networks:
      - sensors-ai
    restart: on-failure

  nebula-metad0:
    image: vesoft/nebula-metad:v3.8.0
    container_name: nebula-metad0
    environment:
      <<: *shared_envs
    command:
      - --meta_server_addrs=nebula-metad0:9559
      - --local_ip=nebula-metad0
      - --ws_ip=nebula-metad0
      - --port=9559
      - --ws_http_port=19559
      - --data_path=/data/meta
      - --log_dir=/logs
      - --v=0
      - --minloglevel=0
    healthcheck:
      test: [ "CMD", "curl", "-sf", "http://nebula-metad0:19559/status" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    ports:
      - 9559
      - 19559
      - 19560
    volumes:
      - ${LOCAL_DATA_STORAGE_DIR:-$HOME/data/sensors-ai}/nebula-graph/nebula-metad0:/data/meta
    networks:
      - sensors-ai
    restart: on-failure
    cap_add:
      - SYS_PTRACE

  nebula-storaged0:
    image: vesoft/nebula-storaged:v3.8.0
    container_name: nebula-storaged0
    environment:
      <<: *shared_envs
    command:
      - --meta_server_addrs=nebula-metad0:9559
      - --local_ip=nebula-storaged0
      - --ws_ip=nebula-storaged0
      - --port=9779
      - --ws_http_port=19779
      - --data_path=/data/storage
      - --log_dir=/logs
      - --v=0
      - --minloglevel=0
    depends_on:
      - nebula-metad0
    healthcheck:
      test: [ "CMD", "curl", "-sf", "http://nebula-storaged0:19779/status" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    ports:
      - 9779
      - 19779
      - 19780
    volumes:
      - ${LOCAL_DATA_STORAGE_DIR:-$HOME/data/sensors-ai}/nebula-graph/nebula-storaged0:/data/storage
    networks:
      - sensors-ai
    restart: on-failure
    cap_add:
      - SYS_PTRACE

  nebula-graphd0:
    image: vesoft/nebula-graphd:v3.8.0
    container_name: nebula-graphd0
    environment:
      <<: *shared_envs
    command:
      - --meta_server_addrs=nebula-metad0:9559
      - --port=9669
      - --local_ip=nebula-graphd0
      - --ws_ip=nebula-graphd0
      - --ws_http_port=19669
      - --log_dir=/logs
      - --v=0
      - --minloglevel=0
    depends_on:
      - nebula-storaged0
    healthcheck:
      test: [ "CMD", "curl", "-sf", "http://nebula-graphd0:19669/status" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    ports:
      - "8003:9669"
      - "19669"
      - "19670"
    networks:
      - sensors-ai
    restart: on-failure
    cap_add:
      - SYS_PTRACE

  nebula-console:
    image: docker.io/vesoft/nebula-console:v3.6.0
    container_name: nebula-console
    entrypoint: ""
    command:
      - sh
      - -c
      - |
        for i in `seq 1 60`;do
          var=`nebula-console -addr nebula-graphd0 -port 9669 -u root -p nebula -e 'ADD HOSTS "nebula-storaged0":9779'`;
          if [[ $$? == 0 ]];then
            break;
          fi;
          sleep 1;
          echo "retry to add hosts.";
        done && tail -f /dev/null;
    depends_on:
      - nebula-graphd0
    networks:
      - sensors-ai

  nebula-studio:
    image: vesoft/nebula-graph-studio:v3.10.0
    container_name: nebula-studio
    environment:
      <<: *shared_envs
    ports:
      - 7001:7001
    depends_on:
      - nebula-graphd0
      - nebula-storaged0
      - nebula-metad0
    networks:
      - sensors-ai

  postgres:
    image: postgres:17-bookworm
    container_name: postgres
    shm_size: 128mb
    environment:
      <<: *shared_envs
    volumes:
      - ${LOCAL_DATA_STORAGE_DIR:-$HOME/data/sensors-ai}/db/postgres:/var/lib/postgresql/data
    ports:
      - ${POSTGRES_PORT:-5432}:5432
    networks:
      - sensors-ai

  ai-web:
    image: ai-web:0.0.1
    container_name: ai-web
    environment:
      <<: *shared_envs
    build:
      context: ../
      dockerfile: docker/web/Dockerfile
    volumes:
      - ${LOCAL_DATA_STORAGE_DIR:-$HOME/data/sensors-ai}/web:/root/data
      - ${LOCAL_DATA_STORAGE_DIR:-$HOME/data/sensors-ai}/logs:/root/logs
    ports:
      - 8005:8005
      - 8007:8007
    depends_on:
      - ai-qdrant
      - nebula-studio
    networks:
      - sensors-ai

  ai-backend-server:
    image: ai-backend-server:0.0.1
    container_name: ai-backend-server
    environment:
      <<: *shared_envs
    build:
      context: ../
      dockerfile: docker/backend_server/Dockerfile
    volumes:
      - ${LOCAL_DATA_STORAGE_DIR:-$HOME/data/sensors-ai}/web:/root/data
      - ${LOCAL_DATA_STORAGE_DIR:-$HOME/data/sensors-ai}/logs:/root/logs
    ports:
      - 8000:8000
    depends_on:
      - ai-qdrant
      - nebula-studio
    networks:
      - sensors-ai

  ai-prompt-server:
    image: ai-prompt-server:0.0.1
    container_name: ai-prompt-server
    environment:
      <<: *shared_envs
    build:
      context: ../
      dockerfile: docker/prompt_server/Dockerfile
    volumes:
      - ${LOCAL_DATA_STORAGE_DIR:-$HOME/data/sensors-ai}/web:/root/data
      - ${LOCAL_DATA_STORAGE_DIR:-$HOME/data/sensors-ai}/logs:/root/logs
    ports:
      - 8001:8001
    networks:
      - sensors-ai

networks:
  sensors-ai: