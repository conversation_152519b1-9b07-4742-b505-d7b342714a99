#!/bin/bash

# 将一些初始化的工作放在 shell 里面，让镜像包更轻量
source ~/.bashrc
PACKAGE_DIR=/root/packages
LOG_DIR=/root/logs
cd $PACKAGE_DIR

# init environments
firefox_file=/usr/bin/firefox-esr
if test -f "firefox_file"; then
  echo 'firefox-esr existed, skip.'
else
  echo 'install firefox-esr.'
  chmod 777 /tmp
  apt update && apt upgrade && apt install -y firefox-esr
fi
source ~/.bashrc

rustc_file=~/.cargo/bin/rustc
if test -f "$rustc_file"; then
  echo 'rustc existed, skip.'
else
  echo 'install rustc.'
  echo 'export RUSTUP_UPDATE_ROOT=https://mirrors.aliyun.com/rustup/rustup' >> ~/.bashrc
  echo 'export RUSTUP_DIST_SERVER=https://mirrors.aliyun.com/rustup' >> ~/.bashrc

  source ~/.bashrc
  wget https://mirrors.aliyun.com/repo/rust/rustup-init.sh
  bash rustup-init.sh --quiet -y
  rm -rf rustup-init.sh

  echo '[source.crates-io]' >> ~/.cargo/config
  echo 'replace-with = "ustc"' >> ~/.cargo/config
  echo '[source.ustc]' >> ~/.cargo/config
  echo 'registry = "https://mirrors.ustc.edu.cn/crates.io-index"' >> ~/.cargo/config
fi
source ~/.bashrc

# install the requirements
echo 'install requirements.'
pip config set global.index-url 'https://mirrors.ustc.edu.cn/pypi/simple'
pip install uv
uv pip install --system *.whl --index https://mirrors.ustc.edu.cn/pypi/simple
source ~/.bashrc

echo 'start open api server.'
log_file=$LOG_DIR/http_api.log
nohup http_server > $log_file 2>&1 &

echo 'Start OpenAPI server success.' >> $log_file
echo '_______________________________________________' >> $log_file
tail -f $log_file