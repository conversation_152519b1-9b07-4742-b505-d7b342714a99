#!/bin/bash

# 将一些初始化的工作放在 shell 里面，让镜像包更轻量
source ~/.bashrc
PACKAGE_DIR=/root/packages
LOG_DIR=/root/logs
cd $PACKAGE_DIR

# install the requirements
echo 'install requirements.'
pip config set global.index-url 'https://mirrors.ustc.edu.cn/pypi/simple'
pip install uv
uv pip install --system *.whl --index https://mirrors.ustc.edu.cn/pypi/simple
source ~/.bashrc

echo 'start prompt streamlit server.'
log_file=$LOG_DIR/st_log.log
nohup python -m streamlit run /usr/local/lib/python3.11/site-packages/prompt_server/server.py --server.port 8001 > $log_file 2>&1 &

echo 'Start Prompt server success.' >> $log_file
echo '_______________________________________________' >> $log_file
tail -f $log_file