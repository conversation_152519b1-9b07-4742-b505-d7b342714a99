from python:3.11.11-bookworm
MAINTAINER SensorsData<<EMAIL>>

SHELL ["/bin/bash", "-c"]

EXPOSE 8000

ENV MYPATH /root
ENV DATA_PATH /root/data
WORKDIR $MYPATH

COPY ./dist /root/packages
COPY ./docker/nltk_data.zip /root/nltk_data.zip
COPY ./docker/backend_server/start.sh $MYPATH
COPY ./docker/debian.sources /etc/apt/sources.list.d/debian.sources

RUN unzip -d /root/ /root/nltk_data.zip

# startup script
ENTRYPOINT ["/bin/bash", "start.sh"]