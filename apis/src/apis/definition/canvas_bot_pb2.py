# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: canvas_bot.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import stream_event_pb2 as stream__event__pb2
import chat_session_pb2 as chat__session__pb2
import canvas_draft_pb2 as canvas__draft__pb2
import knowledge_base_pb2 as knowledge__base__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x10\x63\x61nvas_bot.proto\x12\x14sensorsdata.focus.v1\x1a\x12stream_event.proto\x1a\x12\x63hat_session.proto\x1a\x12\x63\x61nvas_draft.proto\x1a\x14knowledge_base.proto"\xef\x02\n\x15\x43\x61nvasDesignStructure\x12M\n\x0e\x63omponent_list\x18\x01 \x03(\x0b\x32\x35.sensorsdata.focus.v1.CanvasDesignStructure.Component\x12M\n\x13\x63omponent_edge_list\x18\x02 \x03(\x0b\x32\x30.sensorsdata.focus.v1.CanvasDesignStructure.Edge\x1a[\n\tComponent\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05\x63name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x0c\n\x04type\x18\x04 \x01(\t\x12\x10\n\x08sub_type\x18\x05 \x01(\t\x1a[\n\x04\x45\x64ge\x12\x19\n\x11\x65ntry_source_node\x18\x01 \x01(\t\x12\x1b\n\x13source_component_id\x18\x02 \x01(\t\x12\x1b\n\x13target_component_id\x18\x03 \x01(\t"\x9f\x01\n\x0c\x43\x61nvasDesign\x12\x0c\n\x04idea\x18\x01 \x01(\t\x12\x10\n\x08\x61udience\x18\x02 \x01(\t\x12\x0e\n\x06timing\x18\x03 \x01(\t\x12\x0f\n\x07\x63hannel\x18\x04 \x01(\t\x12\x0e\n\x06target\x18\x05 \x01(\t\x12>\n\tstructure\x18\x06 \x01(\x0b\x32+.sensorsdata.focus.v1.CanvasDesignStructure"\x80\x02\n\x16\x43\x61nvasDesignSuggestion\x12\x32\n\x06\x64\x65sign\x18\x01 \x01(\x0b\x32".sensorsdata.focus.v1.CanvasDesign\x12\x14\n\x0c\x63omponent_id\x18\x02 \x03(\x05\x12\x12\n\nsuggestion\x18\x03 \x01(\t\x12I\n\x04mode\x18\x04 \x01(\x0e\x32;.sensorsdata.focus.v1.CanvasDesignSuggestion.SuggestionMode"=\n\x0eSuggestionMode\x12\x13\n\x0f\x46ULLY_GENERATED\x10\x00\x12\x16\n\x12PRESERVE_STRUCTURE\x10\x01"\xf9\x03\n\x18\x43hatSessionCanvasMessage\x12R\n\tgenerator\x18\x01 \x01(\x0e\x32?.sensorsdata.focus.v1.ChatSessionCanvasMessage.MessageGenerator\x12H\n\x04type\x18\x02 \x01(\x0e\x32:.sensorsdata.focus.v1.ChatSessionCanvasMessage.MessageType\x12\x0c\n\x04text\x18\x03 \x01(\t\x12\x32\n\x06\x64\x65sign\x18\x04 \x03(\x0b\x32".sensorsdata.focus.v1.CanvasDesign\x12=\n\x07suggest\x18\x05 \x01(\x0b\x32,.sensorsdata.focus.v1.CanvasDesignSuggestion\x12\x31\n\x06\x63\x61nvas\x18\x06 \x01(\x0b\x32!.sensorsdata.focus.v1.CanvasDraft"1\n\x10MessageGenerator\x12\n\n\x06SYSTEM\x10\x00\x12\x08\n\x04USER\x10\x01\x12\x07\n\x03\x42OT\x10\x02"X\n\x0bMessageType\x12\x08\n\x04TEXT\x10\x00\x12\n\n\x06\x44\x45SIGN\x10\x01\x12\x0b\n\x07SUGGEST\x10\x02\x12\n\n\x06\x43\x41NVAS\x10\x03\x12\x0c\n\x08THINKING\x10\x04\x12\x0c\n\x08MARKDOWN\x10\x05"\xe1\x01\n\x14GeneralCanvasRequest\x12@\n\x11\x63hat_session_info\x18\x01 \x01(\x0b\x32%.sensorsdata.focus.v1.ChatSessionInfo\x12\x45\n\x14knowledge_base_infos\x18\x02 \x03(\x0b\x32\'.sensorsdata.focus.v1.KnowledgeBaseInfo\x12@\n\x08messages\x18\x03 \x03(\x0b\x32..sensorsdata.focus.v1.ChatSessionCanvasMessage2\xae\x06\n\x10\x43\x61nvasBotService\x12]\n\x08\x44ispatch\x12*.sensorsdata.focus.v1.GeneralCanvasRequest\x1a!.sensorsdata.focus.v1.StreamEvent"\x00\x30\x01\x12\x61\n\x0c\x44\x65signCanvas\x12*.sensorsdata.focus.v1.GeneralCanvasRequest\x1a!.sensorsdata.focus.v1.StreamEvent"\x00\x30\x01\x12\x61\n\x0c\x41\x64justCanvas\x12*.sensorsdata.focus.v1.GeneralCanvasRequest\x1a!.sensorsdata.focus.v1.StreamEvent"\x00\x30\x01\x12\x63\n\x0eGenerateCanvas\x12*.sensorsdata.focus.v1.GeneralCanvasRequest\x1a!.sensorsdata.focus.v1.StreamEvent"\x00\x30\x01\x12\x63\n\x0eOptimizeCanvas\x12*.sensorsdata.focus.v1.GeneralCanvasRequest\x1a!.sensorsdata.focus.v1.StreamEvent"\x00\x30\x01\x12\x62\n\rInsightCanvas\x12*.sensorsdata.focus.v1.GeneralCanvasRequest\x1a!.sensorsdata.focus.v1.StreamEvent"\x00\x30\x01\x12\x63\n\x0eRetrieveCanvas\x12*.sensorsdata.focus.v1.GeneralCanvasRequest\x1a!.sensorsdata.focus.v1.StreamEvent"\x00\x30\x01\x12\x62\n\rPredictCanvas\x12*.sensorsdata.focus.v1.GeneralCanvasRequest\x1a!.sensorsdata.focus.v1.StreamEvent"\x00\x30\x01\x42\x1f\n\x1b\x63om.sensorsdata.focus.v1.aiP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "canvas_bot_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    _globals["DESCRIPTOR"]._options = None
    _globals["DESCRIPTOR"]._serialized_options = (
        b"\n\033com.sensorsdata.focus.v1.aiP\001"
    )
    _globals["_CANVASDESIGNSTRUCTURE"]._serialized_start = 125
    _globals["_CANVASDESIGNSTRUCTURE"]._serialized_end = 492
    _globals["_CANVASDESIGNSTRUCTURE_COMPONENT"]._serialized_start = 308
    _globals["_CANVASDESIGNSTRUCTURE_COMPONENT"]._serialized_end = 399
    _globals["_CANVASDESIGNSTRUCTURE_EDGE"]._serialized_start = 401
    _globals["_CANVASDESIGNSTRUCTURE_EDGE"]._serialized_end = 492
    _globals["_CANVASDESIGN"]._serialized_start = 495
    _globals["_CANVASDESIGN"]._serialized_end = 654
    _globals["_CANVASDESIGNSUGGESTION"]._serialized_start = 657
    _globals["_CANVASDESIGNSUGGESTION"]._serialized_end = 913
    _globals["_CANVASDESIGNSUGGESTION_SUGGESTIONMODE"]._serialized_start = 852
    _globals["_CANVASDESIGNSUGGESTION_SUGGESTIONMODE"]._serialized_end = 913
    _globals["_CHATSESSIONCANVASMESSAGE"]._serialized_start = 916
    _globals["_CHATSESSIONCANVASMESSAGE"]._serialized_end = 1421
    _globals["_CHATSESSIONCANVASMESSAGE_MESSAGEGENERATOR"]._serialized_start = 1282
    _globals["_CHATSESSIONCANVASMESSAGE_MESSAGEGENERATOR"]._serialized_end = 1331
    _globals["_CHATSESSIONCANVASMESSAGE_MESSAGETYPE"]._serialized_start = 1333
    _globals["_CHATSESSIONCANVASMESSAGE_MESSAGETYPE"]._serialized_end = 1421
    _globals["_GENERALCANVASREQUEST"]._serialized_start = 1424
    _globals["_GENERALCANVASREQUEST"]._serialized_end = 1649
    _globals["_CANVASBOTSERVICE"]._serialized_start = 1652
    _globals["_CANVASBOTSERVICE"]._serialized_end = 2466
# @@protoc_insertion_point(module_scope)
