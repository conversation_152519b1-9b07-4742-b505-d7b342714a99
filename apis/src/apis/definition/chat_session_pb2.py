# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: chat_session.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x12\x63hat_session.proto\x12\x14sensorsdata.focus.v1"\xa0\x01\n\x0f\x43hatSessionInfo\x12\x17\n\x0forganization_id\x18\x01 \x01(\t\x12\x16\n\x0esfn_project_id\x18\x02 \x01(\x05\x12\x12\n\nproject_id\x18\x03 \x01(\x05\x12\x14\n\x0cproject_name\x18\x04 \x01(\t\x12\x17\n\x0f\x63hat_session_id\x18\x05 \x01(\t\x12\x19\n\x11\x63hat_session_name\x18\x06 \x01(\t*]\n\x0cIntentDomain\x12\x0b\n\x07UNKNOWN\x10\x00\x12\n\n\x06\x44\x45SIGN\x10\x01\x12\x0b\n\x07INSIGHT\x10\x02\x12\x0c\n\x08OPTIMIZE\x10\x03\x12\x0c\n\x08RETRIEVE\x10\x04\x12\x0b\n\x07PREDICT\x10\x05\x42\x1f\n\x1b\x63om.sensorsdata.focus.v1.aiP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "chat_session_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    _globals["DESCRIPTOR"]._options = None
    _globals["DESCRIPTOR"]._serialized_options = (
        b"\n\033com.sensorsdata.focus.v1.aiP\001"
    )
    _globals["_INTENTDOMAIN"]._serialized_start = 207
    _globals["_INTENTDOMAIN"]._serialized_end = 300
    _globals["_CHATSESSIONINFO"]._serialized_start = 45
    _globals["_CHATSESSIONINFO"]._serialized_end = 205
# @@protoc_insertion_point(module_scope)
