# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: backend/knowledgebase_service.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n#backend/knowledgebase_service.proto\x12\x11sensorsdata.ai.v12\x1d\n\x1bKnowledgeBaseBackendServiceB\x19\n\x15\x63om.sensorsdata.ai.v1P\x01\x62\x06proto3"
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "backend.knowledgebase_service_pb2", _globals
)
if _descriptor._USE_C_DESCRIPTORS == False:
    _globals["DESCRIPTOR"]._options = None
    _globals["DESCRIPTOR"]._serialized_options = b"\n\025com.sensorsdata.ai.v1P\001"
    _globals["_KNOWLEDGEBASEBACKENDSERVICE"]._serialized_start = 58
    _globals["_KNOWLEDGEBASEBACKENDSERVICE"]._serialized_end = 87
# @@protoc_insertion_point(module_scope)
