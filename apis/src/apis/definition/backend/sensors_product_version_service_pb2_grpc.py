# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc


class SensorsProductVersionBackendServiceStub(object):
    """{_description_} 神策产品版本后台管理服务"""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """


class SensorsProductVersionBackendServiceServicer(object):
    """{_description_} 神策产品版本后台管理服务"""


def add_SensorsProductVersionBackendServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {}
    generic_handler = grpc.method_handlers_generic_handler(
        "sensorsdata.ai.v1.SensorsProductVersionBackendService", rpc_method_handlers
    )
    server.add_generic_rpc_handlers((generic_handler,))


# This class is part of an EXPERIMENTAL API.
class SensorsProductVersionBackendService(object):
    """{_description_} 神策产品版本后台管理服务"""
