# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc


class IndustryBackendServiceStub(object):
    """{_description_} 行业后台管理服务"""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """


class IndustryBackendServiceServicer(object):
    """{_description_} 行业后台管理服务"""


def add_IndustryBackendServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {}
    generic_handler = grpc.method_handlers_generic_handler(
        "sensorsdata.ai.v1.IndustryBackendService", rpc_method_handlers
    )
    server.add_generic_rpc_handlers((generic_handler,))


# This class is part of an EXPERIMENTAL API.
class IndustryBackendService(object):
    """{_description_} 行业后台管理服务"""
