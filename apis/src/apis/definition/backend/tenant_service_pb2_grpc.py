# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc


class TenantBackendServiceStub(object):
    """{_description_} 租户后台管理服务"""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """


class TenantBackendServiceServicer(object):
    """{_description_} 租户后台管理服务"""


def add_TenantBackendServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {}
    generic_handler = grpc.method_handlers_generic_handler(
        "sensorsdata.ai.v1.TenantBackendService", rpc_method_handlers
    )
    server.add_generic_rpc_handlers((generic_handler,))


# This class is part of an EXPERIMENTAL API.
class TenantBackendService(object):
    """{_description_} 租户后台管理服务"""
