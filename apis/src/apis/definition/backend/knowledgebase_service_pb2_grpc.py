# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc


class KnowledgeBaseBackendServiceStub(object):
    """{_description_} 知识库后台管理服务"""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """


class KnowledgeBaseBackendServiceServicer(object):
    """{_description_} 知识库后台管理服务"""


def add_KnowledgeBaseBackendServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {}
    generic_handler = grpc.method_handlers_generic_handler(
        "sensorsdata.ai.v1.KnowledgeBaseBackendService", rpc_method_handlers
    )
    server.add_generic_rpc_handlers((generic_handler,))


# This class is part of an EXPERIMENTAL API.
class KnowledgeBaseBackendService(object):
    """{_description_} 知识库后台管理服务"""
