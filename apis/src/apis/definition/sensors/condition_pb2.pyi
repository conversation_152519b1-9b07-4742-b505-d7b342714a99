from sensors import common_pb2 as _common_pb2
from sensors import time_range_pb2 as _time_range_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class PredicateFunctionType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    PREDICATE_FUNCTION_TYPE_UNSPECIFIED: _ClassVar[PredicateFunctionType]
    IS_SET: _ClassVar[PredicateFunctionType]
    is_set: _ClassVar[PredicateFunctionType]
    isSet: _ClassVar[PredicateFunctionType]
    IS_NOT_SET: _ClassVar[PredicateFunctionType]
    EQUAL: _ClassVar[PredicateFunctionType]
    NOT_EQUAL: _ClassVar[PredicateFunctionType]
    IN: _ClassVar[PredicateFunctionType]
    NOT_IN: _ClassVar[PredicateFunctionType]
    LESS: _ClassVar[PredicateFunctionType]
    LESS_EQUAL: _ClassVar[PredicateFunctionType]
    GREATER: _ClassVar[PredicateFunctionType]
    GREATER_EQUAL: _ClassVar[PredicateFunctionType]
    BETWEEN: _ClassVar[PredicateFunctionType]
    RIGHT_OPEN_BETWEEN: _ClassVar[PredicateFunctionType]
    HASH_BETWEEN: _ClassVar[PredicateFunctionType]
    LEFT_OPEN_BETWEEN: _ClassVar[PredicateFunctionType]
    OPEN_BETWEEN: _ClassVar[PredicateFunctionType]
    IS_TRUE: _ClassVar[PredicateFunctionType]
    IS_FALSE: _ClassVar[PredicateFunctionType]
    CONTAIN: _ClassVar[PredicateFunctionType]
    NOT_CONTAIN: _ClassVar[PredicateFunctionType]
    IS_EMPTY: _ClassVar[PredicateFunctionType]
    IS_NOT_EMPTY: _ClassVar[PredicateFunctionType]
    RLIKE: _ClassVar[PredicateFunctionType]
    NOT_RLIKE: _ClassVar[PredicateFunctionType]
    INCLUDE: _ClassVar[PredicateFunctionType]
    NOT_INCLUDE: _ClassVar[PredicateFunctionType]
    ABSOLUTE_BETWEEN: _ClassVar[PredicateFunctionType]
    RELATIVE_WITHIN: _ClassVar[PredicateFunctionType]
    RELATIVE_BETWEEN: _ClassVar[PredicateFunctionType]
    RELATIVE_EVENT_TIME: _ClassVar[PredicateFunctionType]
    RELATIVE_BEFORE: _ClassVar[PredicateFunctionType]
    INTERSECTS: _ClassVar[PredicateFunctionType]
    NOT_INTERSECTS: _ClassVar[PredicateFunctionType]
    SUBSET_OF: _ClassVar[PredicateFunctionType]
    NOT_SUBSET_OF: _ClassVar[PredicateFunctionType]
    SUPERSET_OF: _ClassVar[PredicateFunctionType]
    NOT_SUPERSET_OF: _ClassVar[PredicateFunctionType]

class PredicateFunctionParamType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    FUNCTION_PARAM_TYPE_UNSPECIFIED: _ClassVar[PredicateFunctionParamType]
    FIELD: _ClassVar[PredicateFunctionParamType]
    VALUE: _ClassVar[PredicateFunctionParamType]
    VARIABLE: _ClassVar[PredicateFunctionParamType]
    TIME_POINT: _ClassVar[PredicateFunctionParamType]
    EXPRESSION: _ClassVar[PredicateFunctionParamType]

class LogicalOperator(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    LOGICAL_OPERATOR_UNSPECIFIED: _ClassVar[LogicalOperator]
    AND: _ClassVar[LogicalOperator]
    OR: _ClassVar[LogicalOperator]
    and: _ClassVar[LogicalOperator]
    or: _ClassVar[LogicalOperator]
PREDICATE_FUNCTION_TYPE_UNSPECIFIED: PredicateFunctionType
IS_SET: PredicateFunctionType
is_set: PredicateFunctionType
isSet: PredicateFunctionType
IS_NOT_SET: PredicateFunctionType
EQUAL: PredicateFunctionType
NOT_EQUAL: PredicateFunctionType
IN: PredicateFunctionType
NOT_IN: PredicateFunctionType
LESS: PredicateFunctionType
LESS_EQUAL: PredicateFunctionType
GREATER: PredicateFunctionType
GREATER_EQUAL: PredicateFunctionType
BETWEEN: PredicateFunctionType
RIGHT_OPEN_BETWEEN: PredicateFunctionType
HASH_BETWEEN: PredicateFunctionType
LEFT_OPEN_BETWEEN: PredicateFunctionType
OPEN_BETWEEN: PredicateFunctionType
IS_TRUE: PredicateFunctionType
IS_FALSE: PredicateFunctionType
CONTAIN: PredicateFunctionType
NOT_CONTAIN: PredicateFunctionType
IS_EMPTY: PredicateFunctionType
IS_NOT_EMPTY: PredicateFunctionType
RLIKE: PredicateFunctionType
NOT_RLIKE: PredicateFunctionType
INCLUDE: PredicateFunctionType
NOT_INCLUDE: PredicateFunctionType
ABSOLUTE_BETWEEN: PredicateFunctionType
RELATIVE_WITHIN: PredicateFunctionType
RELATIVE_BETWEEN: PredicateFunctionType
RELATIVE_EVENT_TIME: PredicateFunctionType
RELATIVE_BEFORE: PredicateFunctionType
INTERSECTS: PredicateFunctionType
NOT_INTERSECTS: PredicateFunctionType
SUBSET_OF: PredicateFunctionType
NOT_SUBSET_OF: PredicateFunctionType
SUPERSET_OF: PredicateFunctionType
NOT_SUPERSET_OF: PredicateFunctionType
FUNCTION_PARAM_TYPE_UNSPECIFIED: PredicateFunctionParamType
FIELD: PredicateFunctionParamType
VALUE: PredicateFunctionParamType
VARIABLE: PredicateFunctionParamType
TIME_POINT: PredicateFunctionParamType
EXPRESSION: PredicateFunctionParamType
LOGICAL_OPERATOR_UNSPECIFIED: LogicalOperator
AND: LogicalOperator
OR: LogicalOperator
and: LogicalOperator
or: LogicalOperator

class PredicateFunctionParam(_message.Message):
    __slots__ = ("param_type", "field", "value", "variable", "time_point", "expression")
    PARAM_TYPE_FIELD_NUMBER: _ClassVar[int]
    FIELD_FIELD_NUMBER: _ClassVar[int]
    VALUE_FIELD_NUMBER: _ClassVar[int]
    VARIABLE_FIELD_NUMBER: _ClassVar[int]
    TIME_POINT_FIELD_NUMBER: _ClassVar[int]
    EXPRESSION_FIELD_NUMBER: _ClassVar[int]
    param_type: PredicateFunctionParamType
    field: str
    value: _common_pb2.Literal
    variable: str
    time_point: _time_range_pb2.TimePoint
    expression: str
    def __init__(self, param_type: _Optional[_Union[PredicateFunctionParamType, str]] = ..., field: _Optional[str] = ..., value: _Optional[_Union[_common_pb2.Literal, _Mapping]] = ..., variable: _Optional[str] = ..., time_point: _Optional[_Union[_time_range_pb2.TimePoint, _Mapping]] = ..., expression: _Optional[str] = ...) -> None: ...

class FilterCondition(_message.Message):
    __slots__ = ("function", "params", "index")
    FUNCTION_FIELD_NUMBER: _ClassVar[int]
    PARAMS_FIELD_NUMBER: _ClassVar[int]
    INDEX_FIELD_NUMBER: _ClassVar[int]
    function: PredicateFunctionType
    params: _containers.RepeatedCompositeFieldContainer[PredicateFunctionParam]
    index: int
    def __init__(self, function: _Optional[_Union[PredicateFunctionType, str]] = ..., params: _Optional[_Iterable[_Union[PredicateFunctionParam, _Mapping]]] = ..., index: _Optional[int] = ...) -> None: ...

class CompoundFilterCondition(_message.Message):
    __slots__ = ("operator", "conditions", "compound_conditions", "index")
    OPERATOR_FIELD_NUMBER: _ClassVar[int]
    CONDITIONS_FIELD_NUMBER: _ClassVar[int]
    COMPOUND_CONDITIONS_FIELD_NUMBER: _ClassVar[int]
    INDEX_FIELD_NUMBER: _ClassVar[int]
    operator: LogicalOperator
    conditions: _containers.RepeatedCompositeFieldContainer[FilterCondition]
    compound_conditions: _containers.RepeatedCompositeFieldContainer[CompoundFilterCondition]
    index: int
    def __init__(self, operator: _Optional[_Union[LogicalOperator, str]] = ..., conditions: _Optional[_Iterable[_Union[FilterCondition, _Mapping]]] = ..., compound_conditions: _Optional[_Iterable[_Union[CompoundFilterCondition, _Mapping]]] = ..., index: _Optional[int] = ...) -> None: ...
