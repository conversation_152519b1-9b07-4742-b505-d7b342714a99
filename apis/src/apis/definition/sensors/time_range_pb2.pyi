from google.protobuf import timestamp_pb2 as _timestamp_pb2
from sensors import common_pb2 as _common_pb2
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class TimeInterval(_message.Message):
    __slots__ = ("size", "unit")
    SIZE_FIELD_NUMBER: _ClassVar[int]
    UNIT_FIELD_NUMBER: _ClassVar[int]
    size: int
    unit: _common_pb2.DateTimeUnit
    def __init__(self, size: _Optional[int] = ..., unit: _Optional[_Union[_common_pb2.DateTimeUnit, str]] = ...) -> None: ...

class TimePoint(_message.Message):
    __slots__ = ("type", "trunc_unit", "static_time", "relative_time", "expression")
    class TimePointType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        TIME_POINT_TYPE_UNSPECIFIED: _ClassVar[TimePoint.TimePointType]
        STATIC: _ClassVar[TimePoint.TimePointType]
        RELATIVE: _ClassVar[TimePoint.TimePointType]
        EXPRESSION: _ClassVar[TimePoint.TimePointType]
    TIME_POINT_TYPE_UNSPECIFIED: TimePoint.TimePointType
    STATIC: TimePoint.TimePointType
    RELATIVE: TimePoint.TimePointType
    EXPRESSION: TimePoint.TimePointType
    class RelativeTimePoint(_message.Message):
        __slots__ = ("base_time", "time_interval")
        BASE_TIME_FIELD_NUMBER: _ClassVar[int]
        TIME_INTERVAL_FIELD_NUMBER: _ClassVar[int]
        base_time: str
        time_interval: TimeInterval
        def __init__(self, base_time: _Optional[str] = ..., time_interval: _Optional[_Union[TimeInterval, _Mapping]] = ...) -> None: ...
    TYPE_FIELD_NUMBER: _ClassVar[int]
    TRUNC_UNIT_FIELD_NUMBER: _ClassVar[int]
    STATIC_TIME_FIELD_NUMBER: _ClassVar[int]
    RELATIVE_TIME_FIELD_NUMBER: _ClassVar[int]
    EXPRESSION_FIELD_NUMBER: _ClassVar[int]
    type: TimePoint.TimePointType
    trunc_unit: _common_pb2.DateTimeTruncUnit
    static_time: _timestamp_pb2.Timestamp
    relative_time: TimePoint.RelativeTimePoint
    expression: str
    def __init__(self, type: _Optional[_Union[TimePoint.TimePointType, str]] = ..., trunc_unit: _Optional[_Union[_common_pb2.DateTimeTruncUnit, str]] = ..., static_time: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., relative_time: _Optional[_Union[TimePoint.RelativeTimePoint, _Mapping]] = ..., expression: _Optional[str] = ...) -> None: ...

class TimeRange(_message.Message):
    __slots__ = ("start_time", "end_time")
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    start_time: TimePoint
    end_time: TimePoint
    def __init__(self, start_time: _Optional[_Union[TimePoint, _Mapping]] = ..., end_time: _Optional[_Union[TimePoint, _Mapping]] = ...) -> None: ...

class TimeWindow(_message.Message):
    __slots__ = ("value", "unit")
    VALUE_FIELD_NUMBER: _ClassVar[int]
    UNIT_FIELD_NUMBER: _ClassVar[int]
    value: int
    unit: str
    def __init__(self, value: _Optional[int] = ..., unit: _Optional[str] = ...) -> None: ...
