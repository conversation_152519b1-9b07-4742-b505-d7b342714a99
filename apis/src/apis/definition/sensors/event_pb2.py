# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: sensors/event.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from sensors import condition_pb2 as sensors_dot_condition__pb2
from sensors import time_range_pb2 as sensors_dot_time__range__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x13sensors/event.proto\x12\x14sensorsdata.focus.v1\x1a\x17sensors/condition.proto\x1a\x18sensors/time_range.proto"\x98\x01\n\x0b\x45ventSeries\x12\x12\n\nevent_name\x18\x01 \x01(\t\x12\x33\n\ntime_range\x18\x02 \x01(\x0b\x32\x1f.sensorsdata.focus.v1.TimeRange\x12@\n\tcondition\x18\x03 \x01(\x0b\x32-.sensorsdata.focus.v1.CompoundFilterConditionB\x1f\n\x1b\x63om.sensorsdata.focus.v1.aiP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "sensors.event_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    _globals["DESCRIPTOR"]._options = None
    _globals["DESCRIPTOR"]._serialized_options = (
        b"\n\033com.sensorsdata.focus.v1.aiP\001"
    )
    _globals["_EVENTSERIES"]._serialized_start = 97
    _globals["_EVENTSERIES"]._serialized_end = 249
# @@protoc_insertion_point(module_scope)
