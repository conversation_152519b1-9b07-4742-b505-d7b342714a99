from google.protobuf import timestamp_pb2 as _timestamp_pb2
from sensors import common_pb2 as _common_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class StaticTrigger(_message.Message):
    __slots__ = ("time",)
    TIME_FIELD_NUMBER: _ClassVar[int]
    time: _timestamp_pb2.Timestamp
    def __init__(self, time: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...

class PeriodicTrigger(_message.Message):
    __slots__ = ("start_time", "end_time", "interval_size", "interval_unit", "trunc_unit")
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    INTERVAL_SIZE_FIELD_NUMBER: _ClassVar[int]
    INTERVAL_UNIT_FIELD_NUMBER: _ClassVar[int]
    TRUNC_UNIT_FIELD_NUMBER: _ClassVar[int]
    start_time: _timestamp_pb2.Timestamp
    end_time: _timestamp_pb2.Timestamp
    interval_size: int
    interval_unit: _common_pb2.DateTimeUnit
    trunc_unit: _common_pb2.DateTimeTruncUnit
    def __init__(self, start_time: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., end_time: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., interval_size: _Optional[int] = ..., interval_unit: _Optional[_Union[_common_pb2.DateTimeUnit, str]] = ..., trunc_unit: _Optional[_Union[_common_pb2.DateTimeTruncUnit, str]] = ...) -> None: ...

class CronTrigger(_message.Message):
    __slots__ = ("crontab_exp", "start_time", "end_time")
    CRONTAB_EXP_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    crontab_exp: str
    start_time: _timestamp_pb2.Timestamp
    end_time: _timestamp_pb2.Timestamp
    def __init__(self, crontab_exp: _Optional[str] = ..., start_time: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., end_time: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...

class SyncJobTrigger(_message.Message):
    __slots__ = ("seconds_of_day", "execute_unit", "execute_days", "execute_months")
    SECONDS_OF_DAY_FIELD_NUMBER: _ClassVar[int]
    EXECUTE_UNIT_FIELD_NUMBER: _ClassVar[int]
    EXECUTE_DAYS_FIELD_NUMBER: _ClassVar[int]
    EXECUTE_MONTHS_FIELD_NUMBER: _ClassVar[int]
    seconds_of_day: int
    execute_unit: _common_pb2.DateTimeUnit
    execute_days: _containers.RepeatedScalarFieldContainer[int]
    execute_months: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, seconds_of_day: _Optional[int] = ..., execute_unit: _Optional[_Union[_common_pb2.DateTimeUnit, str]] = ..., execute_days: _Optional[_Iterable[int]] = ..., execute_months: _Optional[_Iterable[int]] = ...) -> None: ...

class Trigger(_message.Message):
    __slots__ = ("trigger_type", "periodic_trigger", "cron_trigger", "static_trigger", "sync_job_trigger")
    class TriggerType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        TRIGGER_TYPE_UNSPECIFIED: _ClassVar[Trigger.TriggerType]
        MANUAL: _ClassVar[Trigger.TriggerType]
        PERIODIC: _ClassVar[Trigger.TriggerType]
        CRON: _ClassVar[Trigger.TriggerType]
        STATIC: _ClassVar[Trigger.TriggerType]
        DYNAMIC: _ClassVar[Trigger.TriggerType]
        SYNC_JOB: _ClassVar[Trigger.TriggerType]
    TRIGGER_TYPE_UNSPECIFIED: Trigger.TriggerType
    MANUAL: Trigger.TriggerType
    PERIODIC: Trigger.TriggerType
    CRON: Trigger.TriggerType
    STATIC: Trigger.TriggerType
    DYNAMIC: Trigger.TriggerType
    SYNC_JOB: Trigger.TriggerType
    TRIGGER_TYPE_FIELD_NUMBER: _ClassVar[int]
    PERIODIC_TRIGGER_FIELD_NUMBER: _ClassVar[int]
    CRON_TRIGGER_FIELD_NUMBER: _ClassVar[int]
    STATIC_TRIGGER_FIELD_NUMBER: _ClassVar[int]
    SYNC_JOB_TRIGGER_FIELD_NUMBER: _ClassVar[int]
    trigger_type: Trigger.TriggerType
    periodic_trigger: PeriodicTrigger
    cron_trigger: CronTrigger
    static_trigger: StaticTrigger
    sync_job_trigger: SyncJobTrigger
    def __init__(self, trigger_type: _Optional[_Union[Trigger.TriggerType, str]] = ..., periodic_trigger: _Optional[_Union[PeriodicTrigger, _Mapping]] = ..., cron_trigger: _Optional[_Union[CronTrigger, _Mapping]] = ..., static_trigger: _Optional[_Union[StaticTrigger, _Mapping]] = ..., sync_job_trigger: _Optional[_Union[SyncJobTrigger, _Mapping]] = ...) -> None: ...

class TriggerExternalInfo(_message.Message):
    __slots__ = ("account_id", "schedule_id", "triggered_reason")
    ACCOUNT_ID_FIELD_NUMBER: _ClassVar[int]
    SCHEDULE_ID_FIELD_NUMBER: _ClassVar[int]
    TRIGGERED_REASON_FIELD_NUMBER: _ClassVar[int]
    account_id: int
    schedule_id: int
    triggered_reason: str
    def __init__(self, account_id: _Optional[int] = ..., schedule_id: _Optional[int] = ..., triggered_reason: _Optional[str] = ...) -> None: ...
