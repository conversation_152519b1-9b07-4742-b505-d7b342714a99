# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: sensors/condition.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from sensors import common_pb2 as sensors_dot_common__pb2
from sensors import time_range_pb2 as sensors_dot_time__range__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x17sensors/condition.proto\x12\x14sensorsdata.focus.v1\x1a\x14sensors/common.proto\x1a\x18sensors/time_range.proto"\xf6\x01\n\x16PredicateFunctionParam\x12\x44\n\nparam_type\x18\x01 \x01(\x0e\x32\x30.sensorsdata.focus.v1.PredicateFunctionParamType\x12\r\n\x05\x66ield\x18\x02 \x01(\t\x12,\n\x05value\x18\x03 \x01(\x0b\x32\x1d.sensorsdata.focus.v1.Literal\x12\x10\n\x08variable\x18\x04 \x01(\t\x12\x33\n\ntime_point\x18\x05 \x01(\x0b\x32\x1f.sensorsdata.focus.v1.TimePoint\x12\x12\n\nexpression\x18\x06 \x01(\t"\x9d\x01\n\x0f\x46ilterCondition\x12=\n\x08\x66unction\x18\x01 \x01(\x0e\x32+.sensorsdata.focus.v1.PredicateFunctionType\x12<\n\x06params\x18\x02 \x03(\x0b\x32,.sensorsdata.focus.v1.PredicateFunctionParam\x12\r\n\x05index\x18\x03 \x01(\x05"\xe8\x01\n\x17\x43ompoundFilterCondition\x12\x37\n\x08operator\x18\x01 \x01(\x0e\x32%.sensorsdata.focus.v1.LogicalOperator\x12\x39\n\nconditions\x18\x02 \x03(\x0b\x32%.sensorsdata.focus.v1.FilterCondition\x12J\n\x13\x63ompound_conditions\x18\x03 \x03(\x0b\x32-.sensorsdata.focus.v1.CompoundFilterCondition\x12\r\n\x05index\x18\x04 \x01(\x05*\xb3\x05\n\x15PredicateFunctionType\x12\'\n#PREDICATE_FUNCTION_TYPE_UNSPECIFIED\x10\x00\x12\n\n\x06IS_SET\x10\x01\x12\x0e\n\x06is_set\x10\x01\x1a\x02\x08\x01\x12\r\n\x05isSet\x10\x01\x1a\x02\x08\x01\x12\x0e\n\nIS_NOT_SET\x10\x02\x12\t\n\x05\x45QUAL\x10\x0b\x12\r\n\tNOT_EQUAL\x10\x0c\x12\x06\n\x02IN\x10%\x12\n\n\x06NOT_IN\x10&\x12\x08\n\x04LESS\x10\r\x12\x0e\n\nLESS_EQUAL\x10\x0e\x12\x0b\n\x07GREATER\x10\x0f\x12\x11\n\rGREATER_EQUAL\x10\x10\x12\x0b\n\x07\x42\x45TWEEN\x10\x11\x12\x16\n\x12RIGHT_OPEN_BETWEEN\x10\x12\x12\x10\n\x0cHASH_BETWEEN\x10\x13\x12\x15\n\x11LEFT_OPEN_BETWEEN\x10/\x12\x10\n\x0cOPEN_BETWEEN\x10\x30\x12\x0b\n\x07IS_TRUE\x10\x15\x12\x0c\n\x08IS_FALSE\x10\x16\x12\x0b\n\x07\x43ONTAIN\x10\x1f\x12\x0f\n\x0bNOT_CONTAIN\x10 \x12\x0c\n\x08IS_EMPTY\x10!\x12\x10\n\x0cIS_NOT_EMPTY\x10"\x12\t\n\x05RLIKE\x10#\x12\r\n\tNOT_RLIKE\x10$\x12\x0b\n\x07INCLUDE\x10)\x12\x0f\n\x0bNOT_INCLUDE\x10*\x12\x14\n\x10\x41\x42SOLUTE_BETWEEN\x10+\x12\x13\n\x0fRELATIVE_WITHIN\x10,\x12\x14\n\x10RELATIVE_BETWEEN\x10-\x12\x17\n\x13RELATIVE_EVENT_TIME\x10.\x12\x13\n\x0fRELATIVE_BEFORE\x10\x38\x12\x0e\n\nINTERSECTS\x10\x32\x12\x12\n\x0eNOT_INTERSECTS\x10\x33\x12\r\n\tSUBSET_OF\x10\x34\x12\x11\n\rNOT_SUBSET_OF\x10\x35\x12\x0f\n\x0bSUPERSET_OF\x10\x36\x12\x13\n\x0fNOT_SUPERSET_OF\x10\x37\x1a\x02\x10\x01*\x85\x01\n\x1aPredicateFunctionParamType\x12#\n\x1f\x46UNCTION_PARAM_TYPE_UNSPECIFIED\x10\x00\x12\t\n\x05\x46IELD\x10\x01\x12\t\n\x05VALUE\x10\x02\x12\x0c\n\x08VARIABLE\x10\x03\x12\x0e\n\nTIME_POINT\x10\x04\x12\x0e\n\nEXPRESSION\x10\x05*a\n\x0fLogicalOperator\x12 \n\x1cLOGICAL_OPERATOR_UNSPECIFIED\x10\x00\x12\x07\n\x03\x41ND\x10\x01\x12\x06\n\x02OR\x10\x02\x12\x0b\n\x03\x61nd\x10\x01\x1a\x02\x08\x01\x12\n\n\x02or\x10\x02\x1a\x02\x08\x01\x1a\x02\x10\x01\x42\x1f\n\x1b\x63om.sensorsdata.focus.v1.aiP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "sensors.condition_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    _globals["DESCRIPTOR"]._options = None
    _globals["DESCRIPTOR"]._serialized_options = (
        b"\n\033com.sensorsdata.focus.v1.aiP\001"
    )
    _globals["_PREDICATEFUNCTIONTYPE"]._options = None
    _globals["_PREDICATEFUNCTIONTYPE"]._serialized_options = b"\020\001"
    _globals["_PREDICATEFUNCTIONTYPE"].values_by_name["is_set"]._options = None
    _globals["_PREDICATEFUNCTIONTYPE"].values_by_name[
        "is_set"
    ]._serialized_options = b"\010\001"
    _globals["_PREDICATEFUNCTIONTYPE"].values_by_name["isSet"]._options = None
    _globals["_PREDICATEFUNCTIONTYPE"].values_by_name[
        "isSet"
    ]._serialized_options = b"\010\001"
    _globals["_LOGICALOPERATOR"]._options = None
    _globals["_LOGICALOPERATOR"]._serialized_options = b"\020\001"
    _globals["_LOGICALOPERATOR"].values_by_name["and"]._options = None
    _globals["_LOGICALOPERATOR"].values_by_name["and"]._serialized_options = b"\010\001"
    _globals["_LOGICALOPERATOR"].values_by_name["or"]._options = None
    _globals["_LOGICALOPERATOR"].values_by_name["or"]._serialized_options = b"\010\001"
    _globals["_PREDICATEFUNCTIONTYPE"]._serialized_start = 742
    _globals["_PREDICATEFUNCTIONTYPE"]._serialized_end = 1433
    _globals["_PREDICATEFUNCTIONPARAMTYPE"]._serialized_start = 1436
    _globals["_PREDICATEFUNCTIONPARAMTYPE"]._serialized_end = 1569
    _globals["_LOGICALOPERATOR"]._serialized_start = 1571
    _globals["_LOGICALOPERATOR"]._serialized_end = 1668
    _globals["_PREDICATEFUNCTIONPARAM"]._serialized_start = 98
    _globals["_PREDICATEFUNCTIONPARAM"]._serialized_end = 344
    _globals["_FILTERCONDITION"]._serialized_start = 347
    _globals["_FILTERCONDITION"]._serialized_end = 504
    _globals["_COMPOUNDFILTERCONDITION"]._serialized_start = 507
    _globals["_COMPOUNDFILTERCONDITION"]._serialized_end = 739
# @@protoc_insertion_point(module_scope)
