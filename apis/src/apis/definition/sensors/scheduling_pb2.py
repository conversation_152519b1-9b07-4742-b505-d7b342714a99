# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: sensors/scheduling.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from sensors import common_pb2 as sensors_dot_common__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x18sensors/scheduling.proto\x12\x14sensorsdata.focus.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x14sensors/common.proto"9\n\rStaticTrigger\x12(\n\x04time\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp"\xfe\x01\n\x0fPeriodicTrigger\x12.\n\nstart_time\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x15\n\rinterval_size\x18\x05 \x01(\x05\x12\x39\n\rinterval_unit\x18\x03 \x01(\x0e\x32".sensorsdata.focus.v1.DateTimeUnit\x12;\n\ntrunc_unit\x18\x04 \x01(\x0e\x32\'.sensorsdata.focus.v1.DateTimeTruncUnit"\x80\x01\n\x0b\x43ronTrigger\x12\x13\n\x0b\x63rontab_exp\x18\x01 \x01(\t\x12.\n\nstart_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_time\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp"\x90\x01\n\x0eSyncJobTrigger\x12\x16\n\x0eseconds_of_day\x18\x01 \x01(\x05\x12\x38\n\x0c\x65xecute_unit\x18\x02 \x01(\x0e\x32".sensorsdata.focus.v1.DateTimeUnit\x12\x14\n\x0c\x65xecute_days\x18\x04 \x03(\x05\x12\x16\n\x0e\x65xecute_months\x18\x05 \x03(\x05"\xb9\x03\n\x07Trigger\x12?\n\x0ctrigger_type\x18\x01 \x01(\x0e\x32).sensorsdata.focus.v1.Trigger.TriggerType\x12?\n\x10periodic_trigger\x18\x02 \x01(\x0b\x32%.sensorsdata.focus.v1.PeriodicTrigger\x12\x37\n\x0c\x63ron_trigger\x18\x03 \x01(\x0b\x32!.sensorsdata.focus.v1.CronTrigger\x12;\n\x0estatic_trigger\x18\x04 \x01(\x0b\x32#.sensorsdata.focus.v1.StaticTrigger\x12>\n\x10sync_job_trigger\x18\x05 \x01(\x0b\x32$.sensorsdata.focus.v1.SyncJobTrigger"v\n\x0bTriggerType\x12\x1c\n\x18TRIGGER_TYPE_UNSPECIFIED\x10\x00\x12\n\n\x06MANUAL\x10\x01\x12\x0c\n\x08PERIODIC\x10\x02\x12\x08\n\x04\x43RON\x10\x03\x12\n\n\x06STATIC\x10\x04\x12\x0b\n\x07\x44YNAMIC\x10\x05\x12\x0c\n\x08SYNC_JOB\x10\x06"r\n\x13TriggerExternalInfo\x12\x14\n\naccount_id\x18\x01 \x01(\x05H\x00\x12\x15\n\x0bschedule_id\x18\x02 \x01(\x05H\x00\x12\x18\n\x10triggered_reason\x18\x03 \x01(\tB\x14\n\x12trigger_subject_idB\x1f\n\x1b\x63om.sensorsdata.focus.v1.aiP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "sensors.scheduling_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    _globals["DESCRIPTOR"]._options = None
    _globals["DESCRIPTOR"]._serialized_options = (
        b"\n\033com.sensorsdata.focus.v1.aiP\001"
    )
    _globals["_STATICTRIGGER"]._serialized_start = 105
    _globals["_STATICTRIGGER"]._serialized_end = 162
    _globals["_PERIODICTRIGGER"]._serialized_start = 165
    _globals["_PERIODICTRIGGER"]._serialized_end = 419
    _globals["_CRONTRIGGER"]._serialized_start = 422
    _globals["_CRONTRIGGER"]._serialized_end = 550
    _globals["_SYNCJOBTRIGGER"]._serialized_start = 553
    _globals["_SYNCJOBTRIGGER"]._serialized_end = 697
    _globals["_TRIGGER"]._serialized_start = 700
    _globals["_TRIGGER"]._serialized_end = 1141
    _globals["_TRIGGER_TRIGGERTYPE"]._serialized_start = 1023
    _globals["_TRIGGER_TRIGGERTYPE"]._serialized_end = 1141
    _globals["_TRIGGEREXTERNALINFO"]._serialized_start = 1143
    _globals["_TRIGGEREXTERNALINFO"]._serialized_end = 1257
# @@protoc_insertion_point(module_scope)
