# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: sensors/common.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x14sensors/common.proto\x12\x14sensorsdata.focus.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1egoogle/protobuf/wrappers.proto"\x84\x04\n\x07Literal\x12\x31\n\tdata_type\x18\x01 \x01(\x0e\x32\x1e.sensorsdata.focus.v1.DataType\x12.\n\nbool_value\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.BoolValue\x12.\n\tint_value\x18\x0c \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12\x32\n\x0cnumber_value\x18\x0e \x01(\x0b\x32\x1c.google.protobuf.DoubleValue\x12\x32\n\x0cstring_value\x18\x0f \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x30\n\nlist_value\x18\x10 \x03(\x0b\x32\x1c.google.protobuf.StringValue\x12\x32\n\x0e\x64\x61tetime_value\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\ndate_value\x18\x12 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x31\n\x0c\x62igint_value\x18\x13 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12\x33\n\rdecimal_value\x18\x14 \x01(\x0b\x32\x1c.google.protobuf.StringValue*\xb0\x03\n\x14ProductComponentCode\x12&\n"PRODUCT_COMPONENT_CODE_UNSPECIFIED\x10\x00\x12\x14\n\x10SENSORS_DATAFLOW\x10\x02\x12\x17\n\x13SENSORS_CLOUD_ATLAS\x10\x03\x12\x1d\n\x19SENSORS_BUSINESS_PLATFORM\x10\x04\x12\x15\n\x11SENSORS_ANALYTICS\x10\x01\x12\x14\n\x10SENSORS_PERSONAS\x10\x05\x12\x11\n\rSENSORS_FOCUS\x10\x06\x12\x17\n\x13SENSORS_RECOMMENDER\x10\x07\x12\x13\n\x0fSENSORS_JOURNEY\x10\x08\x12\x19\n\x15SENSORS_DATA_GOVERNOR\x10\t\x12\x14\n\x10SENSORS_INFINITY\x10\n\x12\x12\n\x0eSENSORS_HUBBLE\x10\x0b\x12\x13\n\x0fSENSORS_DIVINER\x10\x0c\x12\x11\n\rSENSORS_CYBER\x10\r\x12\x18\n\x14SENSORS_DATA_HORIZON\x10\x0e\x12\x10\n\x0cSENSORS_EDGE\x10\x0f\x12\x1b\n\x17SENSORS_DATA_INTEGRATOR\x10\x10*\x8b\x01\n\x08\x44\x61taType\x12\x19\n\x15\x44\x41TA_TYPE_UNSPECIFIED\x10\x00\x12\x08\n\x04\x42OOL\x10\x01\x12\x07\n\x03INT\x10\x02\x12\n\n\x06NUMBER\x10\x03\x12\n\n\x06STRING\x10\x04\x12\x08\n\x04LIST\x10\x05\x12\x0c\n\x08\x44\x41TETIME\x10\x06\x12\x08\n\x04\x44\x41TE\x10\x07\x12\n\n\x06\x42IGINT\x10\x08\x12\x0b\n\x07\x44\x45\x43IMAL\x10\t*\x89\x01\n\x0c\x44\x61teTimeUnit\x12\x1e\n\x1a\x44\x41TE_TIME_UNIT_UNSPECIFIED\x10\x00\x12\x0f\n\x0bMILLISECOND\x10\x01\x12\n\n\x06SECOND\x10\x02\x12\n\n\x06MINUTE\x10\x03\x12\x08\n\x04HOUR\x10\x04\x12\x07\n\x03\x44\x41Y\x10\x05\x12\x08\n\x04WEEK\x10\x06\x12\t\n\x05MONTH\x10\x07\x12\x08\n\x04YEAR\x10\x08*\x92\x01\n\x11\x44\x61teTimeTruncUnit\x12\x1a\n\x16TRUNC_UNIT_UNSPECIFIED\x10\x00\x12\x0e\n\nTRUNC_HOUR\x10\x01\x12\r\n\tTRUNC_DAY\x10\x02\x12\x0e\n\nTRUNC_WEEK\x10\x03\x12\x0f\n\x0bTRUNC_MONTH\x10\x04\x12\x0e\n\nTRUNC_YEAR\x10\x05\x12\x11\n\rTRUNC_BIGBANG\x10\nB\x1f\n\x1b\x63om.sensorsdata.focus.v1.aiP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "sensors.common_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    _globals["DESCRIPTOR"]._options = None
    _globals["DESCRIPTOR"]._serialized_options = (
        b"\n\033com.sensorsdata.focus.v1.aiP\001"
    )
    _globals["_PRODUCTCOMPONENTCODE"]._serialized_start = 631
    _globals["_PRODUCTCOMPONENTCODE"]._serialized_end = 1063
    _globals["_DATATYPE"]._serialized_start = 1066
    _globals["_DATATYPE"]._serialized_end = 1205
    _globals["_DATETIMEUNIT"]._serialized_start = 1208
    _globals["_DATETIMEUNIT"]._serialized_end = 1345
    _globals["_DATETIMETRUNCUNIT"]._serialized_start = 1348
    _globals["_DATETIMETRUNCUNIT"]._serialized_end = 1494
    _globals["_LITERAL"]._serialized_start = 112
    _globals["_LITERAL"]._serialized_end = 628
# @@protoc_insertion_point(module_scope)
