from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from typing import ClassVar as _ClassVar

DESCRIPTOR: _descriptor.FileDescriptor

class AggregatorType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    AGGREGATOR_TYPE_UNSPECIFIED: _ClassVar[AggregatorType]
    COUNT: _ClassVar[AggregatorType]
    AVG: _ClassVar[AggregatorType]
    MAX: _ClassVar[AggregatorType]
    MIN: _ClassVar[AggregatorType]
    SUM: _ClassVar[AggregatorType]
    UNIQUE_COUNT: _ClassVar[AggregatorType]
    UNIQUE_AVG: _ClassVar[AggregatorType]
    BOUNCE_RATE: _ClassVar[AggregatorType]
    EXIT_RATE: _ClassVar[AggregatorType]
    UNIQUE_COUNT_APPROX: _ClassVar[AggregatorType]
    COUNT_PERCENT: _ClassVar[AggregatorType]
    RANK_ASC: _ClassVar[AggregatorType]
    RANK_DESC: _ClassVar[AggregatorType]
    PERCENT_RANK_ASC: _ClassVar[AggregatorType]
    PERCENT_RANK_DESC: _ClassVar[AggregatorType]
    SESSION_COUNT: _ClassVar[AggregatorType]
    FIRST_TIME: _ClassVar[AggregatorType]
    LAST_TIME: _ClassVar[AggregatorType]
    FIRST_TIME_INTERVAL: _ClassVar[AggregatorType]
    LAST_TIME_INTERVAL: _ClassVar[AggregatorType]
    GROUP_CONCAT: _ClassVar[AggregatorType]
    UNIQUE_COUNT_BITMAP: _ClassVar[AggregatorType]
    UNIQUE_COUNT_APPROX_ORDINARY: _ClassVar[AggregatorType]
    COUNT_DISTINCT: _ClassVar[AggregatorType]
    LTV: _ClassVar[AggregatorType]
    LTV_AVG: _ClassVar[AggregatorType]
    COUNT_RANK: _ClassVar[AggregatorType]
    AVG_RANK: _ClassVar[AggregatorType]
    MAX_RANK: _ClassVar[AggregatorType]
    MIN_RANK: _ClassVar[AggregatorType]
    SUM_RANK: _ClassVar[AggregatorType]
    COUNT_RANK_ASC: _ClassVar[AggregatorType]
    COUNT_RANK_DESC: _ClassVar[AggregatorType]
    COUNT_PERCENT_RANK_ASC: _ClassVar[AggregatorType]
    COUNT_PERCENT_RANK_DESC: _ClassVar[AggregatorType]
    AVG_RANK_ASC: _ClassVar[AggregatorType]
    AVG_RANK_DESC: _ClassVar[AggregatorType]
    AVG_PERCENT_RANK_ASC: _ClassVar[AggregatorType]
    AVG_PERCENT_RANK_DESC: _ClassVar[AggregatorType]
    MAX_RANK_ASC: _ClassVar[AggregatorType]
    MAX_RANK_DESC: _ClassVar[AggregatorType]
    MAX_PERCENT_RANK_ASC: _ClassVar[AggregatorType]
    MAX_PERCENT_RANK_DESC: _ClassVar[AggregatorType]
    MIN_RANK_ASC: _ClassVar[AggregatorType]
    MIN_RANK_DESC: _ClassVar[AggregatorType]
    MIN_PERCENT_RANK_ASC: _ClassVar[AggregatorType]
    MIN_PERCENT_RANK_DESC: _ClassVar[AggregatorType]
    SUM_RANK_ASC: _ClassVar[AggregatorType]
    SUM_RANK_DESC: _ClassVar[AggregatorType]
    SUM_PERCENT_RANK_ASC: _ClassVar[AggregatorType]
    SUM_PERCENT_RANK_DESC: _ClassVar[AggregatorType]
AGGREGATOR_TYPE_UNSPECIFIED: AggregatorType
COUNT: AggregatorType
AVG: AggregatorType
MAX: AggregatorType
MIN: AggregatorType
SUM: AggregatorType
UNIQUE_COUNT: AggregatorType
UNIQUE_AVG: AggregatorType
BOUNCE_RATE: AggregatorType
EXIT_RATE: AggregatorType
UNIQUE_COUNT_APPROX: AggregatorType
COUNT_PERCENT: AggregatorType
RANK_ASC: AggregatorType
RANK_DESC: AggregatorType
PERCENT_RANK_ASC: AggregatorType
PERCENT_RANK_DESC: AggregatorType
SESSION_COUNT: AggregatorType
FIRST_TIME: AggregatorType
LAST_TIME: AggregatorType
FIRST_TIME_INTERVAL: AggregatorType
LAST_TIME_INTERVAL: AggregatorType
GROUP_CONCAT: AggregatorType
UNIQUE_COUNT_BITMAP: AggregatorType
UNIQUE_COUNT_APPROX_ORDINARY: AggregatorType
COUNT_DISTINCT: AggregatorType
LTV: AggregatorType
LTV_AVG: AggregatorType
COUNT_RANK: AggregatorType
AVG_RANK: AggregatorType
MAX_RANK: AggregatorType
MIN_RANK: AggregatorType
SUM_RANK: AggregatorType
COUNT_RANK_ASC: AggregatorType
COUNT_RANK_DESC: AggregatorType
COUNT_PERCENT_RANK_ASC: AggregatorType
COUNT_PERCENT_RANK_DESC: AggregatorType
AVG_RANK_ASC: AggregatorType
AVG_RANK_DESC: AggregatorType
AVG_PERCENT_RANK_ASC: AggregatorType
AVG_PERCENT_RANK_DESC: AggregatorType
MAX_RANK_ASC: AggregatorType
MAX_RANK_DESC: AggregatorType
MAX_PERCENT_RANK_ASC: AggregatorType
MAX_PERCENT_RANK_DESC: AggregatorType
MIN_RANK_ASC: AggregatorType
MIN_RANK_DESC: AggregatorType
MIN_PERCENT_RANK_ASC: AggregatorType
MIN_PERCENT_RANK_DESC: AggregatorType
SUM_RANK_ASC: AggregatorType
SUM_RANK_DESC: AggregatorType
SUM_PERCENT_RANK_ASC: AggregatorType
SUM_PERCENT_RANK_DESC: AggregatorType
