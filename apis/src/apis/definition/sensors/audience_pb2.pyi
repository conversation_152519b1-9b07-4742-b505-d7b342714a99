from sensors import condition_pb2 as _condition_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class PropertyFilterCondition(_message.Message):
    __slots__ = ("field", "function", "params")
    class PropertyFilterFunction(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        PROPERTY_FILTER_FUNCTION_UNSPECIFIED: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        IS_SET: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        is_set: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        isSet: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        NOT_SET: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        not_set: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        notSet: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        IS_TRUE: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        is_true: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        isTrue: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        IS_FALSE: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        is_false: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        isFalse: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        LESS: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        less: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        EQUAL: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        equal: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        GREATER: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        greater: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        NOT_EQUAL: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        not_equal: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        notEqual: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        BETWEEN: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        between: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        CONTAIN: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        contain: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        NOT_CONTAIN: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        not_contain: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        notContain: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        IS_EMPTY: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        is_empty: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        isEmpty: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        IS_NOT_EMPTY: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        is_not_empty: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        notEmpty: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        isNotEmpty: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        RLIKE: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        rlike: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        rLike: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        NOT_RLIKE: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        not_rlike: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        notRLike: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        IN: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        in: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        NOT_IN: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        not_in: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        notIn: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        RELATIVE_BETWEEN: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        relative_between: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        relativeBetween: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        RELATIVE_AFTER: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        relative_after: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        relativeAfter: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        RELATIVE_BEFORE: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        relative_before: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
        relativeBefore: _ClassVar[PropertyFilterCondition.PropertyFilterFunction]
    PROPERTY_FILTER_FUNCTION_UNSPECIFIED: PropertyFilterCondition.PropertyFilterFunction
    IS_SET: PropertyFilterCondition.PropertyFilterFunction
    is_set: PropertyFilterCondition.PropertyFilterFunction
    isSet: PropertyFilterCondition.PropertyFilterFunction
    NOT_SET: PropertyFilterCondition.PropertyFilterFunction
    not_set: PropertyFilterCondition.PropertyFilterFunction
    notSet: PropertyFilterCondition.PropertyFilterFunction
    IS_TRUE: PropertyFilterCondition.PropertyFilterFunction
    is_true: PropertyFilterCondition.PropertyFilterFunction
    isTrue: PropertyFilterCondition.PropertyFilterFunction
    IS_FALSE: PropertyFilterCondition.PropertyFilterFunction
    is_false: PropertyFilterCondition.PropertyFilterFunction
    isFalse: PropertyFilterCondition.PropertyFilterFunction
    LESS: PropertyFilterCondition.PropertyFilterFunction
    less: PropertyFilterCondition.PropertyFilterFunction
    EQUAL: PropertyFilterCondition.PropertyFilterFunction
    equal: PropertyFilterCondition.PropertyFilterFunction
    GREATER: PropertyFilterCondition.PropertyFilterFunction
    greater: PropertyFilterCondition.PropertyFilterFunction
    NOT_EQUAL: PropertyFilterCondition.PropertyFilterFunction
    not_equal: PropertyFilterCondition.PropertyFilterFunction
    notEqual: PropertyFilterCondition.PropertyFilterFunction
    BETWEEN: PropertyFilterCondition.PropertyFilterFunction
    between: PropertyFilterCondition.PropertyFilterFunction
    CONTAIN: PropertyFilterCondition.PropertyFilterFunction
    contain: PropertyFilterCondition.PropertyFilterFunction
    NOT_CONTAIN: PropertyFilterCondition.PropertyFilterFunction
    not_contain: PropertyFilterCondition.PropertyFilterFunction
    notContain: PropertyFilterCondition.PropertyFilterFunction
    IS_EMPTY: PropertyFilterCondition.PropertyFilterFunction
    is_empty: PropertyFilterCondition.PropertyFilterFunction
    isEmpty: PropertyFilterCondition.PropertyFilterFunction
    IS_NOT_EMPTY: PropertyFilterCondition.PropertyFilterFunction
    is_not_empty: PropertyFilterCondition.PropertyFilterFunction
    notEmpty: PropertyFilterCondition.PropertyFilterFunction
    isNotEmpty: PropertyFilterCondition.PropertyFilterFunction
    RLIKE: PropertyFilterCondition.PropertyFilterFunction
    rlike: PropertyFilterCondition.PropertyFilterFunction
    rLike: PropertyFilterCondition.PropertyFilterFunction
    NOT_RLIKE: PropertyFilterCondition.PropertyFilterFunction
    not_rlike: PropertyFilterCondition.PropertyFilterFunction
    notRLike: PropertyFilterCondition.PropertyFilterFunction
    IN: PropertyFilterCondition.PropertyFilterFunction
    in: PropertyFilterCondition.PropertyFilterFunction
    NOT_IN: PropertyFilterCondition.PropertyFilterFunction
    not_in: PropertyFilterCondition.PropertyFilterFunction
    notIn: PropertyFilterCondition.PropertyFilterFunction
    RELATIVE_BETWEEN: PropertyFilterCondition.PropertyFilterFunction
    relative_between: PropertyFilterCondition.PropertyFilterFunction
    relativeBetween: PropertyFilterCondition.PropertyFilterFunction
    RELATIVE_AFTER: PropertyFilterCondition.PropertyFilterFunction
    relative_after: PropertyFilterCondition.PropertyFilterFunction
    relativeAfter: PropertyFilterCondition.PropertyFilterFunction
    RELATIVE_BEFORE: PropertyFilterCondition.PropertyFilterFunction
    relative_before: PropertyFilterCondition.PropertyFilterFunction
    relativeBefore: PropertyFilterCondition.PropertyFilterFunction
    FIELD_FIELD_NUMBER: _ClassVar[int]
    FUNCTION_FIELD_NUMBER: _ClassVar[int]
    PARAMS_FIELD_NUMBER: _ClassVar[int]
    field: str
    function: PropertyFilterCondition.PropertyFilterFunction
    params: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, field: _Optional[str] = ..., function: _Optional[_Union[PropertyFilterCondition.PropertyFilterFunction, str]] = ..., params: _Optional[_Iterable[str]] = ...) -> None: ...

class PropertyFilter(_message.Message):
    __slots__ = ("relation", "conditions")
    RELATION_FIELD_NUMBER: _ClassVar[int]
    CONDITIONS_FIELD_NUMBER: _ClassVar[int]
    relation: _condition_pb2.LogicalOperator
    conditions: _containers.RepeatedCompositeFieldContainer[PropertyFilterCondition]
    def __init__(self, relation: _Optional[_Union[_condition_pb2.LogicalOperator, str]] = ..., conditions: _Optional[_Iterable[_Union[PropertyFilterCondition, _Mapping]]] = ...) -> None: ...

class AudienceProfileRule(_message.Message):
    __slots__ = ("property_filter",)
    PROPERTY_FILTER_FIELD_NUMBER: _ClassVar[int]
    property_filter: PropertyFilter
    def __init__(self, property_filter: _Optional[_Union[PropertyFilter, _Mapping]] = ...) -> None: ...

class AudienceEventMeasure(_message.Message):
    __slots__ = ("event", "aggregator", "aggr_field", "filter")
    class EventMeasureAggregator(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        EVENT_MEASURE_AGGREGATOR_UNSPECIFIED: _ClassVar[AudienceEventMeasure.EventMeasureAggregator]
        GENERAL: _ClassVar[AudienceEventMeasure.EventMeasureAggregator]
        general: _ClassVar[AudienceEventMeasure.EventMeasureAggregator]
        UNIQUE: _ClassVar[AudienceEventMeasure.EventMeasureAggregator]
        unique: _ClassVar[AudienceEventMeasure.EventMeasureAggregator]
        SUM: _ClassVar[AudienceEventMeasure.EventMeasureAggregator]
        sum: _ClassVar[AudienceEventMeasure.EventMeasureAggregator]
        AVG: _ClassVar[AudienceEventMeasure.EventMeasureAggregator]
        avg: _ClassVar[AudienceEventMeasure.EventMeasureAggregator]
        MAX: _ClassVar[AudienceEventMeasure.EventMeasureAggregator]
        max: _ClassVar[AudienceEventMeasure.EventMeasureAggregator]
        MIN: _ClassVar[AudienceEventMeasure.EventMeasureAggregator]
        min: _ClassVar[AudienceEventMeasure.EventMeasureAggregator]
    EVENT_MEASURE_AGGREGATOR_UNSPECIFIED: AudienceEventMeasure.EventMeasureAggregator
    GENERAL: AudienceEventMeasure.EventMeasureAggregator
    general: AudienceEventMeasure.EventMeasureAggregator
    UNIQUE: AudienceEventMeasure.EventMeasureAggregator
    unique: AudienceEventMeasure.EventMeasureAggregator
    SUM: AudienceEventMeasure.EventMeasureAggregator
    sum: AudienceEventMeasure.EventMeasureAggregator
    AVG: AudienceEventMeasure.EventMeasureAggregator
    avg: AudienceEventMeasure.EventMeasureAggregator
    MAX: AudienceEventMeasure.EventMeasureAggregator
    max: AudienceEventMeasure.EventMeasureAggregator
    MIN: AudienceEventMeasure.EventMeasureAggregator
    min: AudienceEventMeasure.EventMeasureAggregator
    EVENT_FIELD_NUMBER: _ClassVar[int]
    AGGREGATOR_FIELD_NUMBER: _ClassVar[int]
    AGGR_FIELD_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    event: str
    aggregator: AudienceEventMeasure.EventMeasureAggregator
    aggr_field: str
    filter: PropertyFilter
    def __init__(self, event: _Optional[str] = ..., aggregator: _Optional[_Union[AudienceEventMeasure.EventMeasureAggregator, str]] = ..., aggr_field: _Optional[str] = ..., filter: _Optional[_Union[PropertyFilter, _Mapping]] = ...) -> None: ...

class AudienceTimeRange(_message.Message):
    __slots__ = ("function", "params")
    FUNCTION_FIELD_NUMBER: _ClassVar[int]
    PARAMS_FIELD_NUMBER: _ClassVar[int]
    function: str
    params: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, function: _Optional[str] = ..., params: _Optional[_Iterable[str]] = ...) -> None: ...

class AudienceEventMeasureCondition(_message.Message):
    __slots__ = ("measure", "time_range", "function", "params")
    class EventMeasureFilterFunction(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        EVENT_MEASURE_FILTER_FUNCTION_UNSPECIFIED: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        LESS: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        less: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        EQUAL: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        equal: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        GREATER: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        greater: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        NOT_EQUAL: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        not_equal: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        notEqual: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        GREATER_EQUAL: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        greater_equal: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        greaterEqual: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        LESS_EQUAL: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        less_equal: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        lessEqual: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        BETWEEN: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        between: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        TOP_PERCENT: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        top_percent: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        topPercent: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        BOTTOM_PERCENT: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        bottom_percent: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        bottomPercent: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        RANK_PERCENT_BETWEEN: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        rank_percent_between: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        rankPercentBetween: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        TOP_N: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        top_n: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        topN: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        BOTTOM_N: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        bottom_n: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        bottomN: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        RANK_N_BETWEEN: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        rank_n_between: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
        rankNBetween: _ClassVar[AudienceEventMeasureCondition.EventMeasureFilterFunction]
    EVENT_MEASURE_FILTER_FUNCTION_UNSPECIFIED: AudienceEventMeasureCondition.EventMeasureFilterFunction
    LESS: AudienceEventMeasureCondition.EventMeasureFilterFunction
    less: AudienceEventMeasureCondition.EventMeasureFilterFunction
    EQUAL: AudienceEventMeasureCondition.EventMeasureFilterFunction
    equal: AudienceEventMeasureCondition.EventMeasureFilterFunction
    GREATER: AudienceEventMeasureCondition.EventMeasureFilterFunction
    greater: AudienceEventMeasureCondition.EventMeasureFilterFunction
    NOT_EQUAL: AudienceEventMeasureCondition.EventMeasureFilterFunction
    not_equal: AudienceEventMeasureCondition.EventMeasureFilterFunction
    notEqual: AudienceEventMeasureCondition.EventMeasureFilterFunction
    GREATER_EQUAL: AudienceEventMeasureCondition.EventMeasureFilterFunction
    greater_equal: AudienceEventMeasureCondition.EventMeasureFilterFunction
    greaterEqual: AudienceEventMeasureCondition.EventMeasureFilterFunction
    LESS_EQUAL: AudienceEventMeasureCondition.EventMeasureFilterFunction
    less_equal: AudienceEventMeasureCondition.EventMeasureFilterFunction
    lessEqual: AudienceEventMeasureCondition.EventMeasureFilterFunction
    BETWEEN: AudienceEventMeasureCondition.EventMeasureFilterFunction
    between: AudienceEventMeasureCondition.EventMeasureFilterFunction
    TOP_PERCENT: AudienceEventMeasureCondition.EventMeasureFilterFunction
    top_percent: AudienceEventMeasureCondition.EventMeasureFilterFunction
    topPercent: AudienceEventMeasureCondition.EventMeasureFilterFunction
    BOTTOM_PERCENT: AudienceEventMeasureCondition.EventMeasureFilterFunction
    bottom_percent: AudienceEventMeasureCondition.EventMeasureFilterFunction
    bottomPercent: AudienceEventMeasureCondition.EventMeasureFilterFunction
    RANK_PERCENT_BETWEEN: AudienceEventMeasureCondition.EventMeasureFilterFunction
    rank_percent_between: AudienceEventMeasureCondition.EventMeasureFilterFunction
    rankPercentBetween: AudienceEventMeasureCondition.EventMeasureFilterFunction
    TOP_N: AudienceEventMeasureCondition.EventMeasureFilterFunction
    top_n: AudienceEventMeasureCondition.EventMeasureFilterFunction
    topN: AudienceEventMeasureCondition.EventMeasureFilterFunction
    BOTTOM_N: AudienceEventMeasureCondition.EventMeasureFilterFunction
    bottom_n: AudienceEventMeasureCondition.EventMeasureFilterFunction
    bottomN: AudienceEventMeasureCondition.EventMeasureFilterFunction
    RANK_N_BETWEEN: AudienceEventMeasureCondition.EventMeasureFilterFunction
    rank_n_between: AudienceEventMeasureCondition.EventMeasureFilterFunction
    rankNBetween: AudienceEventMeasureCondition.EventMeasureFilterFunction
    MEASURE_FIELD_NUMBER: _ClassVar[int]
    TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
    FUNCTION_FIELD_NUMBER: _ClassVar[int]
    PARAMS_FIELD_NUMBER: _ClassVar[int]
    measure: AudienceEventMeasure
    time_range: AudienceTimeRange
    function: AudienceEventMeasureCondition.EventMeasureFilterFunction
    params: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, measure: _Optional[_Union[AudienceEventMeasure, _Mapping]] = ..., time_range: _Optional[_Union[AudienceTimeRange, _Mapping]] = ..., function: _Optional[_Union[AudienceEventMeasureCondition.EventMeasureFilterFunction, str]] = ..., params: _Optional[_Iterable[str]] = ...) -> None: ...

class AudienceEventCondition(_message.Message):
    __slots__ = ("event", "filter")
    EVENT_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    event: str
    filter: PropertyFilter
    def __init__(self, event: _Optional[str] = ..., filter: _Optional[_Union[PropertyFilter, _Mapping]] = ...) -> None: ...

class AudienceEventRule(_message.Message):
    __slots__ = ("relation", "events")
    RELATION_FIELD_NUMBER: _ClassVar[int]
    EVENTS_FIELD_NUMBER: _ClassVar[int]
    relation: _condition_pb2.LogicalOperator
    events: _containers.RepeatedCompositeFieldContainer[AudienceEventMeasureCondition]
    def __init__(self, relation: _Optional[_Union[_condition_pb2.LogicalOperator, str]] = ..., events: _Optional[_Iterable[_Union[AudienceEventMeasureCondition, _Mapping]]] = ...) -> None: ...

class SingleAudienceEventSequenceRule(_message.Message):
    __slots__ = ("time_range", "steps")
    TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
    STEPS_FIELD_NUMBER: _ClassVar[int]
    time_range: AudienceTimeRange
    steps: _containers.RepeatedCompositeFieldContainer[AudienceEventCondition]
    def __init__(self, time_range: _Optional[_Union[AudienceTimeRange, _Mapping]] = ..., steps: _Optional[_Iterable[_Union[AudienceEventCondition, _Mapping]]] = ...) -> None: ...

class AudienceEventSequenceRule(_message.Message):
    __slots__ = ("relation", "multi_sequence_rules")
    RELATION_FIELD_NUMBER: _ClassVar[int]
    MULTI_SEQUENCE_RULES_FIELD_NUMBER: _ClassVar[int]
    relation: _condition_pb2.LogicalOperator
    multi_sequence_rules: _containers.RepeatedCompositeFieldContainer[SingleAudienceEventSequenceRule]
    def __init__(self, relation: _Optional[_Union[_condition_pb2.LogicalOperator, str]] = ..., multi_sequence_rules: _Optional[_Iterable[_Union[SingleAudienceEventSequenceRule, _Mapping]]] = ...) -> None: ...

class AudienceRule(_message.Message):
    __slots__ = ("select_all", "relation", "profile_rule", "event_rule", "event_sequence_rule")
    SELECT_ALL_FIELD_NUMBER: _ClassVar[int]
    RELATION_FIELD_NUMBER: _ClassVar[int]
    PROFILE_RULE_FIELD_NUMBER: _ClassVar[int]
    EVENT_RULE_FIELD_NUMBER: _ClassVar[int]
    EVENT_SEQUENCE_RULE_FIELD_NUMBER: _ClassVar[int]
    select_all: bool
    relation: _condition_pb2.LogicalOperator
    profile_rule: AudienceProfileRule
    event_rule: AudienceEventRule
    event_sequence_rule: AudienceEventSequenceRule
    def __init__(self, select_all: bool = ..., relation: _Optional[_Union[_condition_pb2.LogicalOperator, str]] = ..., profile_rule: _Optional[_Union[AudienceProfileRule, _Mapping]] = ..., event_rule: _Optional[_Union[AudienceEventRule, _Mapping]] = ..., event_sequence_rule: _Optional[_Union[AudienceEventSequenceRule, _Mapping]] = ...) -> None: ...
