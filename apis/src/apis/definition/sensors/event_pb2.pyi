from sensors import condition_pb2 as _condition_pb2
from sensors import time_range_pb2 as _time_range_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class EventSeries(_message.Message):
    __slots__ = ("event_name", "time_range", "condition")
    EVENT_NAME_FIELD_NUMBER: _ClassVar[int]
    TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
    CONDITION_FIELD_NUMBER: _ClassVar[int]
    event_name: str
    time_range: _time_range_pb2.TimeRange
    condition: _condition_pb2.CompoundFilterCondition
    def __init__(self, event_name: _Optional[str] = ..., time_range: _Optional[_Union[_time_range_pb2.TimeRange, _Mapping]] = ..., condition: _Optional[_Union[_condition_pb2.CompoundFilterCondition, _Mapping]] = ...) -> None: ...
