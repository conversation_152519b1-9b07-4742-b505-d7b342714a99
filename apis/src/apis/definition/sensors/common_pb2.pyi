from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.protobuf import wrappers_pb2 as _wrappers_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class ProductComponentCode(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    PRODUCT_COMPONENT_CODE_UNSPECIFIED: _ClassVar[ProductComponentCode]
    SENSORS_DATAFLOW: _ClassVar[ProductComponentCode]
    SENSORS_CLOUD_ATLAS: _ClassVar[ProductComponentCode]
    SENSORS_BUSINESS_PLATFORM: _ClassVar[ProductComponentCode]
    SENSORS_ANALYTICS: _ClassVar[ProductComponentCode]
    SENSORS_PERSONAS: _ClassVar[ProductComponentCode]
    SENSORS_FOCUS: _ClassVar[ProductComponentCode]
    SENSORS_RECOMMENDER: _ClassVar[ProductComponentCode]
    SENSORS_JOURNEY: _ClassVar[ProductComponentCode]
    SENSORS_DATA_GOVERNOR: _ClassVar[ProductComponentCode]
    SENSORS_INFINITY: _ClassVar[ProductComponentCode]
    SENSORS_HUBBLE: _ClassVar[ProductComponentCode]
    SENSORS_DIVINER: _ClassVar[ProductComponentCode]
    SENSORS_CYBER: _ClassVar[ProductComponentCode]
    SENSORS_DATA_HORIZON: _ClassVar[ProductComponentCode]
    SENSORS_EDGE: _ClassVar[ProductComponentCode]
    SENSORS_DATA_INTEGRATOR: _ClassVar[ProductComponentCode]

class DataType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    DATA_TYPE_UNSPECIFIED: _ClassVar[DataType]
    BOOL: _ClassVar[DataType]
    INT: _ClassVar[DataType]
    NUMBER: _ClassVar[DataType]
    STRING: _ClassVar[DataType]
    LIST: _ClassVar[DataType]
    DATETIME: _ClassVar[DataType]
    DATE: _ClassVar[DataType]
    BIGINT: _ClassVar[DataType]
    DECIMAL: _ClassVar[DataType]

class DateTimeUnit(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    DATE_TIME_UNIT_UNSPECIFIED: _ClassVar[DateTimeUnit]
    MILLISECOND: _ClassVar[DateTimeUnit]
    SECOND: _ClassVar[DateTimeUnit]
    MINUTE: _ClassVar[DateTimeUnit]
    HOUR: _ClassVar[DateTimeUnit]
    DAY: _ClassVar[DateTimeUnit]
    WEEK: _ClassVar[DateTimeUnit]
    MONTH: _ClassVar[DateTimeUnit]
    YEAR: _ClassVar[DateTimeUnit]

class DateTimeTruncUnit(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    TRUNC_UNIT_UNSPECIFIED: _ClassVar[DateTimeTruncUnit]
    TRUNC_HOUR: _ClassVar[DateTimeTruncUnit]
    TRUNC_DAY: _ClassVar[DateTimeTruncUnit]
    TRUNC_WEEK: _ClassVar[DateTimeTruncUnit]
    TRUNC_MONTH: _ClassVar[DateTimeTruncUnit]
    TRUNC_YEAR: _ClassVar[DateTimeTruncUnit]
    TRUNC_BIGBANG: _ClassVar[DateTimeTruncUnit]
PRODUCT_COMPONENT_CODE_UNSPECIFIED: ProductComponentCode
SENSORS_DATAFLOW: ProductComponentCode
SENSORS_CLOUD_ATLAS: ProductComponentCode
SENSORS_BUSINESS_PLATFORM: ProductComponentCode
SENSORS_ANALYTICS: ProductComponentCode
SENSORS_PERSONAS: ProductComponentCode
SENSORS_FOCUS: ProductComponentCode
SENSORS_RECOMMENDER: ProductComponentCode
SENSORS_JOURNEY: ProductComponentCode
SENSORS_DATA_GOVERNOR: ProductComponentCode
SENSORS_INFINITY: ProductComponentCode
SENSORS_HUBBLE: ProductComponentCode
SENSORS_DIVINER: ProductComponentCode
SENSORS_CYBER: ProductComponentCode
SENSORS_DATA_HORIZON: ProductComponentCode
SENSORS_EDGE: ProductComponentCode
SENSORS_DATA_INTEGRATOR: ProductComponentCode
DATA_TYPE_UNSPECIFIED: DataType
BOOL: DataType
INT: DataType
NUMBER: DataType
STRING: DataType
LIST: DataType
DATETIME: DataType
DATE: DataType
BIGINT: DataType
DECIMAL: DataType
DATE_TIME_UNIT_UNSPECIFIED: DateTimeUnit
MILLISECOND: DateTimeUnit
SECOND: DateTimeUnit
MINUTE: DateTimeUnit
HOUR: DateTimeUnit
DAY: DateTimeUnit
WEEK: DateTimeUnit
MONTH: DateTimeUnit
YEAR: DateTimeUnit
TRUNC_UNIT_UNSPECIFIED: DateTimeTruncUnit
TRUNC_HOUR: DateTimeTruncUnit
TRUNC_DAY: DateTimeTruncUnit
TRUNC_WEEK: DateTimeTruncUnit
TRUNC_MONTH: DateTimeTruncUnit
TRUNC_YEAR: DateTimeTruncUnit
TRUNC_BIGBANG: DateTimeTruncUnit

class Literal(_message.Message):
    __slots__ = ("data_type", "bool_value", "int_value", "number_value", "string_value", "list_value", "datetime_value", "date_value", "bigint_value", "decimal_value")
    DATA_TYPE_FIELD_NUMBER: _ClassVar[int]
    BOOL_VALUE_FIELD_NUMBER: _ClassVar[int]
    INT_VALUE_FIELD_NUMBER: _ClassVar[int]
    NUMBER_VALUE_FIELD_NUMBER: _ClassVar[int]
    STRING_VALUE_FIELD_NUMBER: _ClassVar[int]
    LIST_VALUE_FIELD_NUMBER: _ClassVar[int]
    DATETIME_VALUE_FIELD_NUMBER: _ClassVar[int]
    DATE_VALUE_FIELD_NUMBER: _ClassVar[int]
    BIGINT_VALUE_FIELD_NUMBER: _ClassVar[int]
    DECIMAL_VALUE_FIELD_NUMBER: _ClassVar[int]
    data_type: DataType
    bool_value: _wrappers_pb2.BoolValue
    int_value: _wrappers_pb2.Int64Value
    number_value: _wrappers_pb2.DoubleValue
    string_value: _wrappers_pb2.StringValue
    list_value: _containers.RepeatedCompositeFieldContainer[_wrappers_pb2.StringValue]
    datetime_value: _timestamp_pb2.Timestamp
    date_value: _wrappers_pb2.StringValue
    bigint_value: _wrappers_pb2.Int64Value
    decimal_value: _wrappers_pb2.StringValue
    def __init__(self, data_type: _Optional[_Union[DataType, str]] = ..., bool_value: _Optional[_Union[_wrappers_pb2.BoolValue, _Mapping]] = ..., int_value: _Optional[_Union[_wrappers_pb2.Int64Value, _Mapping]] = ..., number_value: _Optional[_Union[_wrappers_pb2.DoubleValue, _Mapping]] = ..., string_value: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ..., list_value: _Optional[_Iterable[_Union[_wrappers_pb2.StringValue, _Mapping]]] = ..., datetime_value: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., date_value: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ..., bigint_value: _Optional[_Union[_wrappers_pb2.Int64Value, _Mapping]] = ..., decimal_value: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ...) -> None: ...
