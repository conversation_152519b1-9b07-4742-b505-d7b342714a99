# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: sensors/audience.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from sensors import condition_pb2 as sensors_dot_condition__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x16sensors/audience.proto\x1a\x17sensors/condition.proto"\xe3\x08\n\x17PropertyFilterCondition\x12\r\n\x05\x66ield\x18\x01 \x01(\t\x12\x41\n\x08\x66unction\x18\x02 \x01(\x0e\x32/.PropertyFilterCondition.PropertyFilterFunction\x12\x0e\n\x06params\x18\x03 \x03(\t"\xe5\x07\n\x16PropertyFilterFunction\x12(\n$PROPERTY_FILTER_FUNCTION_UNSPECIFIED\x10\x00\x12\n\n\x06IS_SET\x10\x01\x12\x0e\n\x06is_set\x10\x01\x1a\x02\x08\x01\x12\r\n\x05isSet\x10\x01\x1a\x02\x08\x01\x12\x0b\n\x07NOT_SET\x10\x02\x12\x0f\n\x07not_set\x10\x02\x1a\x02\x08\x01\x12\x0e\n\x06notSet\x10\x02\x1a\x02\x08\x01\x12\x0b\n\x07IS_TRUE\x10\x03\x12\x0f\n\x07is_true\x10\x03\x1a\x02\x08\x01\x12\x0e\n\x06isTrue\x10\x03\x1a\x02\x08\x01\x12\x0c\n\x08IS_FALSE\x10\x04\x12\x10\n\x08is_false\x10\x04\x1a\x02\x08\x01\x12\x0f\n\x07isFalse\x10\x04\x1a\x02\x08\x01\x12\x08\n\x04LESS\x10\x05\x12\x0c\n\x04less\x10\x05\x1a\x02\x08\x01\x12\t\n\x05\x45QUAL\x10\x06\x12\r\n\x05\x65qual\x10\x06\x1a\x02\x08\x01\x12\x0b\n\x07GREATER\x10\x07\x12\x0f\n\x07greater\x10\x07\x1a\x02\x08\x01\x12\r\n\tNOT_EQUAL\x10\x08\x12\x11\n\tnot_equal\x10\x08\x1a\x02\x08\x01\x12\x10\n\x08notEqual\x10\x08\x1a\x02\x08\x01\x12\x0b\n\x07\x42\x45TWEEN\x10\t\x12\x0f\n\x07\x62\x65tween\x10\t\x1a\x02\x08\x01\x12\x0b\n\x07\x43ONTAIN\x10\n\x12\x0f\n\x07\x63ontain\x10\n\x1a\x02\x08\x01\x12\x0f\n\x0bNOT_CONTAIN\x10\x0b\x12\x13\n\x0bnot_contain\x10\x0b\x1a\x02\x08\x01\x12\x12\n\nnotContain\x10\x0b\x1a\x02\x08\x01\x12\x0c\n\x08IS_EMPTY\x10\x0c\x12\x10\n\x08is_empty\x10\x0c\x1a\x02\x08\x01\x12\x0f\n\x07isEmpty\x10\x0c\x1a\x02\x08\x01\x12\x10\n\x0cIS_NOT_EMPTY\x10\r\x12\x14\n\x0cis_not_empty\x10\r\x1a\x02\x08\x01\x12\x10\n\x08notEmpty\x10\r\x1a\x02\x08\x01\x12\x12\n\nisNotEmpty\x10\r\x1a\x02\x08\x01\x12\t\n\x05RLIKE\x10\x0e\x12\r\n\x05rlike\x10\x0e\x1a\x02\x08\x01\x12\r\n\x05rLike\x10\x0e\x1a\x02\x08\x01\x12\r\n\tNOT_RLIKE\x10\x0f\x12\x11\n\tnot_rlike\x10\x0f\x1a\x02\x08\x01\x12\x10\n\x08notRLike\x10\x0f\x1a\x02\x08\x01\x12\x06\n\x02IN\x10\x10\x12\n\n\x02in\x10\x10\x1a\x02\x08\x01\x12\n\n\x06NOT_IN\x10\x11\x12\x0e\n\x06not_in\x10\x11\x1a\x02\x08\x01\x12\r\n\x05notIn\x10\x11\x1a\x02\x08\x01\x12\x14\n\x10RELATIVE_BETWEEN\x10\x12\x12\x18\n\x10relative_between\x10\x12\x1a\x02\x08\x01\x12\x17\n\x0frelativeBetween\x10\x12\x1a\x02\x08\x01\x12\x12\n\x0eRELATIVE_AFTER\x10\x13\x12\x16\n\x0erelative_after\x10\x13\x1a\x02\x08\x01\x12\x15\n\rrelativeAfter\x10\x13\x1a\x02\x08\x01\x12\x13\n\x0fRELATIVE_BEFORE\x10\x14\x12\x17\n\x0frelative_before\x10\x14\x1a\x02\x08\x01\x12\x16\n\x0erelativeBefore\x10\x14\x1a\x02\x08\x01\x1a\x02\x10\x01"w\n\x0ePropertyFilter\x12\x37\n\x08relation\x18\x01 \x01(\x0e\x32%.sensorsdata.focus.v1.LogicalOperator\x12,\n\nconditions\x18\x02 \x03(\x0b\x32\x18.PropertyFilterCondition"?\n\x13\x41udienceProfileRule\x12(\n\x0fproperty_filter\x18\x01 \x01(\x0b\x32\x0f.PropertyFilter"\xf7\x02\n\x14\x41udienceEventMeasure\x12\r\n\x05\x65vent\x18\x01 \x01(\t\x12@\n\naggregator\x18\x02 \x01(\x0e\x32,.AudienceEventMeasure.EventMeasureAggregator\x12\x12\n\naggr_field\x18\x03 \x01(\t\x12\x1f\n\x06\x66ilter\x18\x04 \x01(\x0b\x32\x0f.PropertyFilter"\xd8\x01\n\x16\x45ventMeasureAggregator\x12(\n$EVENT_MEASURE_AGGREGATOR_UNSPECIFIED\x10\x00\x12\x0b\n\x07GENERAL\x10\x01\x12\x0f\n\x07general\x10\x01\x1a\x02\x08\x01\x12\n\n\x06UNIQUE\x10\x02\x12\x0e\n\x06unique\x10\x02\x1a\x02\x08\x01\x12\x07\n\x03SUM\x10\x03\x12\x0b\n\x03sum\x10\x03\x1a\x02\x08\x01\x12\x07\n\x03\x41VG\x10\x04\x12\x0b\n\x03\x61vg\x10\x04\x1a\x02\x08\x01\x12\x07\n\x03MAX\x10\x05\x12\x0b\n\x03max\x10\x05\x1a\x02\x08\x01\x12\x07\n\x03MIN\x10\x06\x12\x0b\n\x03min\x10\x06\x1a\x02\x08\x01\x1a\x02\x10\x01"5\n\x11\x41udienceTimeRange\x12\x10\n\x08\x66unction\x18\x01 \x01(\t\x12\x0e\n\x06params\x18\x02 \x03(\t"\xa3\x07\n\x1d\x41udienceEventMeasureCondition\x12&\n\x07measure\x18\x01 \x01(\x0b\x32\x15.AudienceEventMeasure\x12&\n\ntime_range\x18\x02 \x01(\x0b\x32\x12.AudienceTimeRange\x12K\n\x08\x66unction\x18\x03 \x01(\x0e\x32\x39.AudienceEventMeasureCondition.EventMeasureFilterFunction\x12\x0e\n\x06params\x18\x04 \x03(\t"\xd4\x05\n\x1a\x45ventMeasureFilterFunction\x12-\n)EVENT_MEASURE_FILTER_FUNCTION_UNSPECIFIED\x10\x00\x12\x08\n\x04LESS\x10\x01\x12\x0c\n\x04less\x10\x01\x1a\x02\x08\x01\x12\t\n\x05\x45QUAL\x10\x02\x12\r\n\x05\x65qual\x10\x02\x1a\x02\x08\x01\x12\x0b\n\x07GREATER\x10\x03\x12\x0f\n\x07greater\x10\x03\x1a\x02\x08\x01\x12\r\n\tNOT_EQUAL\x10\x04\x12\x11\n\tnot_equal\x10\x04\x1a\x02\x08\x01\x12\x10\n\x08notEqual\x10\x04\x1a\x02\x08\x01\x12\x11\n\rGREATER_EQUAL\x10\x05\x12\x15\n\rgreater_equal\x10\x05\x1a\x02\x08\x01\x12\x14\n\x0cgreaterEqual\x10\x05\x1a\x02\x08\x01\x12\x0e\n\nLESS_EQUAL\x10\x06\x12\x12\n\nless_equal\x10\x06\x1a\x02\x08\x01\x12\x11\n\tlessEqual\x10\x06\x1a\x02\x08\x01\x12\x0b\n\x07\x42\x45TWEEN\x10\x07\x12\x0f\n\x07\x62\x65tween\x10\x07\x1a\x02\x08\x01\x12\x0f\n\x0bTOP_PERCENT\x10\x08\x12\x13\n\x0btop_percent\x10\x08\x1a\x02\x08\x01\x12\x12\n\ntopPercent\x10\x08\x1a\x02\x08\x01\x12\x12\n\x0e\x42OTTOM_PERCENT\x10\t\x12\x16\n\x0e\x62ottom_percent\x10\t\x1a\x02\x08\x01\x12\x15\n\rbottomPercent\x10\t\x1a\x02\x08\x01\x12\x18\n\x14RANK_PERCENT_BETWEEN\x10\n\x12\x1c\n\x14rank_percent_between\x10\n\x1a\x02\x08\x01\x12\x1a\n\x12rankPercentBetween\x10\n\x1a\x02\x08\x01\x12\t\n\x05TOP_N\x10\x0b\x12\r\n\x05top_n\x10\x0b\x1a\x02\x08\x01\x12\x0c\n\x04topN\x10\x0b\x1a\x02\x08\x01\x12\x0c\n\x08\x42OTTOM_N\x10\x0c\x12\x10\n\x08\x62ottom_n\x10\x0c\x1a\x02\x08\x01\x12\x0f\n\x07\x62ottomN\x10\x0c\x1a\x02\x08\x01\x12\x12\n\x0eRANK_N_BETWEEN\x10\r\x12\x16\n\x0erank_n_between\x10\r\x1a\x02\x08\x01\x12\x14\n\x0crankNBetween\x10\r\x1a\x02\x08\x01\x1a\x02\x10\x01"H\n\x16\x41udienceEventCondition\x12\r\n\x05\x65vent\x18\x01 \x01(\t\x12\x1f\n\x06\x66ilter\x18\x02 \x01(\x0b\x32\x0f.PropertyFilter"|\n\x11\x41udienceEventRule\x12\x37\n\x08relation\x18\x01 \x01(\x0e\x32%.sensorsdata.focus.v1.LogicalOperator\x12.\n\x06\x65vents\x18\x02 \x03(\x0b\x32\x1e.AudienceEventMeasureCondition"q\n\x1fSingleAudienceEventSequenceRule\x12&\n\ntime_range\x18\x01 \x01(\x0b\x32\x12.AudienceTimeRange\x12&\n\x05steps\x18\x02 \x03(\x0b\x32\x17.AudienceEventCondition"\x94\x01\n\x19\x41udienceEventSequenceRule\x12\x37\n\x08relation\x18\x01 \x01(\x0e\x32%.sensorsdata.focus.v1.LogicalOperator\x12>\n\x14multi_sequence_rules\x18\x02 \x03(\x0b\x32 .SingleAudienceEventSequenceRule"\xe8\x01\n\x0c\x41udienceRule\x12\x12\n\nselect_all\x18\x01 \x01(\x08\x12\x37\n\x08relation\x18\x02 \x01(\x0e\x32%.sensorsdata.focus.v1.LogicalOperator\x12*\n\x0cprofile_rule\x18\x03 \x01(\x0b\x32\x14.AudienceProfileRule\x12&\n\nevent_rule\x18\x04 \x01(\x0b\x32\x12.AudienceEventRule\x12\x37\n\x13\x65vent_sequence_rule\x18\x05 \x01(\x0b\x32\x1a.AudienceEventSequenceRuleb\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "sensors.audience_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"]._serialized_options = (
        b"\020\001"
    )
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "is_set"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "is_set"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "isSet"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "isSet"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "not_set"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "not_set"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "notSet"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "notSet"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "is_true"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "is_true"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "isTrue"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "isTrue"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "is_false"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "is_false"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "isFalse"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "isFalse"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "less"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "less"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "equal"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "equal"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "greater"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "greater"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "not_equal"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "not_equal"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "notEqual"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "notEqual"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "between"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "between"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "contain"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "contain"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "not_contain"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "not_contain"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "notContain"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "notContain"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "is_empty"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "is_empty"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "isEmpty"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "isEmpty"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "is_not_empty"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "is_not_empty"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "notEmpty"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "notEmpty"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "isNotEmpty"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "isNotEmpty"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "rlike"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "rlike"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "rLike"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "rLike"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "not_rlike"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "not_rlike"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "notRLike"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "notRLike"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "in"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "in"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "not_in"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "not_in"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "notIn"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "notIn"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "relative_between"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "relative_between"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "relativeBetween"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "relativeBetween"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "relative_after"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "relative_after"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "relativeAfter"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "relativeAfter"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "relative_before"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "relative_before"
    ]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "relativeBefore"
    ]._options = None
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"].values_by_name[
        "relativeBefore"
    ]._serialized_options = b"\010\001"
    _globals["_AUDIENCEEVENTMEASURE_EVENTMEASUREAGGREGATOR"]._options = None
    _globals["_AUDIENCEEVENTMEASURE_EVENTMEASUREAGGREGATOR"]._serialized_options = (
        b"\020\001"
    )
    _globals["_AUDIENCEEVENTMEASURE_EVENTMEASUREAGGREGATOR"].values_by_name[
        "general"
    ]._options = None
    _globals["_AUDIENCEEVENTMEASURE_EVENTMEASUREAGGREGATOR"].values_by_name[
        "general"
    ]._serialized_options = b"\010\001"
    _globals["_AUDIENCEEVENTMEASURE_EVENTMEASUREAGGREGATOR"].values_by_name[
        "unique"
    ]._options = None
    _globals["_AUDIENCEEVENTMEASURE_EVENTMEASUREAGGREGATOR"].values_by_name[
        "unique"
    ]._serialized_options = b"\010\001"
    _globals["_AUDIENCEEVENTMEASURE_EVENTMEASUREAGGREGATOR"].values_by_name[
        "sum"
    ]._options = None
    _globals["_AUDIENCEEVENTMEASURE_EVENTMEASUREAGGREGATOR"].values_by_name[
        "sum"
    ]._serialized_options = b"\010\001"
    _globals["_AUDIENCEEVENTMEASURE_EVENTMEASUREAGGREGATOR"].values_by_name[
        "avg"
    ]._options = None
    _globals["_AUDIENCEEVENTMEASURE_EVENTMEASUREAGGREGATOR"].values_by_name[
        "avg"
    ]._serialized_options = b"\010\001"
    _globals["_AUDIENCEEVENTMEASURE_EVENTMEASUREAGGREGATOR"].values_by_name[
        "max"
    ]._options = None
    _globals["_AUDIENCEEVENTMEASURE_EVENTMEASUREAGGREGATOR"].values_by_name[
        "max"
    ]._serialized_options = b"\010\001"
    _globals["_AUDIENCEEVENTMEASURE_EVENTMEASUREAGGREGATOR"].values_by_name[
        "min"
    ]._options = None
    _globals["_AUDIENCEEVENTMEASURE_EVENTMEASUREAGGREGATOR"].values_by_name[
        "min"
    ]._serialized_options = b"\010\001"
    _globals["_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"]._options = (
        None
    )
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ]._serialized_options = b"\020\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["less"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["less"]._serialized_options = b"\010\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["equal"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["equal"]._serialized_options = b"\010\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["greater"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["greater"]._serialized_options = b"\010\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["not_equal"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["not_equal"]._serialized_options = b"\010\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["notEqual"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["notEqual"]._serialized_options = b"\010\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["greater_equal"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["greater_equal"]._serialized_options = b"\010\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["greaterEqual"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["greaterEqual"]._serialized_options = b"\010\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["less_equal"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["less_equal"]._serialized_options = b"\010\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["lessEqual"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["lessEqual"]._serialized_options = b"\010\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["between"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["between"]._serialized_options = b"\010\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["top_percent"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["top_percent"]._serialized_options = b"\010\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["topPercent"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["topPercent"]._serialized_options = b"\010\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["bottom_percent"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["bottom_percent"]._serialized_options = b"\010\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["bottomPercent"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["bottomPercent"]._serialized_options = b"\010\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["rank_percent_between"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["rank_percent_between"]._serialized_options = b"\010\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["rankPercentBetween"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["rankPercentBetween"]._serialized_options = b"\010\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["top_n"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["top_n"]._serialized_options = b"\010\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["topN"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["topN"]._serialized_options = b"\010\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["bottom_n"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["bottom_n"]._serialized_options = b"\010\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["bottomN"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["bottomN"]._serialized_options = b"\010\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["rank_n_between"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["rank_n_between"]._serialized_options = b"\010\001"
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["rankNBetween"]._options = None
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ].values_by_name["rankNBetween"]._serialized_options = b"\010\001"
    _globals["_PROPERTYFILTERCONDITION"]._serialized_start = 52
    _globals["_PROPERTYFILTERCONDITION"]._serialized_end = 1175
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"]._serialized_start = 178
    _globals["_PROPERTYFILTERCONDITION_PROPERTYFILTERFUNCTION"]._serialized_end = 1175
    _globals["_PROPERTYFILTER"]._serialized_start = 1177
    _globals["_PROPERTYFILTER"]._serialized_end = 1296
    _globals["_AUDIENCEPROFILERULE"]._serialized_start = 1298
    _globals["_AUDIENCEPROFILERULE"]._serialized_end = 1361
    _globals["_AUDIENCEEVENTMEASURE"]._serialized_start = 1364
    _globals["_AUDIENCEEVENTMEASURE"]._serialized_end = 1739
    _globals["_AUDIENCEEVENTMEASURE_EVENTMEASUREAGGREGATOR"]._serialized_start = 1523
    _globals["_AUDIENCEEVENTMEASURE_EVENTMEASUREAGGREGATOR"]._serialized_end = 1739
    _globals["_AUDIENCETIMERANGE"]._serialized_start = 1741
    _globals["_AUDIENCETIMERANGE"]._serialized_end = 1794
    _globals["_AUDIENCEEVENTMEASURECONDITION"]._serialized_start = 1797
    _globals["_AUDIENCEEVENTMEASURECONDITION"]._serialized_end = 2728
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ]._serialized_start = 2004
    _globals[
        "_AUDIENCEEVENTMEASURECONDITION_EVENTMEASUREFILTERFUNCTION"
    ]._serialized_end = 2728
    _globals["_AUDIENCEEVENTCONDITION"]._serialized_start = 2730
    _globals["_AUDIENCEEVENTCONDITION"]._serialized_end = 2802
    _globals["_AUDIENCEEVENTRULE"]._serialized_start = 2804
    _globals["_AUDIENCEEVENTRULE"]._serialized_end = 2928
    _globals["_SINGLEAUDIENCEEVENTSEQUENCERULE"]._serialized_start = 2930
    _globals["_SINGLEAUDIENCEEVENTSEQUENCERULE"]._serialized_end = 3043
    _globals["_AUDIENCEEVENTSEQUENCERULE"]._serialized_start = 3046
    _globals["_AUDIENCEEVENTSEQUENCERULE"]._serialized_end = 3194
    _globals["_AUDIENCERULE"]._serialized_start = 3197
    _globals["_AUDIENCERULE"]._serialized_end = 3429
# @@protoc_insertion_point(module_scope)
