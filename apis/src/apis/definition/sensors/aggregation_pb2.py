# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: sensors/aggregation.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x19sensors/aggregation.proto\x12\x14sensorsdata.focus.v1*\x84\x08\n\x0e\x41ggregatorType\x12\x1f\n\x1b\x41GGREGATOR_TYPE_UNSPECIFIED\x10\x00\x12\t\n\x05\x43OUNT\x10\x01\x12\x07\n\x03\x41VG\x10\x02\x12\x07\n\x03MAX\x10\x03\x12\x07\n\x03MIN\x10\x04\x12\x07\n\x03SUM\x10\x05\x12\x10\n\x0cUNIQUE_COUNT\x10\x06\x12\x0e\n\nUNIQUE_AVG\x10\x07\x12\x0f\n\x0b\x42OUNCE_RATE\x10\x08\x12\r\n\tEXIT_RATE\x10\t\x12\x17\n\x13UNIQUE_COUNT_APPROX\x10\n\x12\x11\n\rCOUNT_PERCENT\x10o\x12\x0c\n\x08RANK_ASC\x10\x0c\x12\r\n\tRANK_DESC\x10\r\x12\x14\n\x10PERCENT_RANK_ASC\x10\x0e\x12\x15\n\x11PERCENT_RANK_DESC\x10\x0f\x12\x11\n\rSESSION_COUNT\x10\x10\x12\x0e\n\nFIRST_TIME\x10\x11\x12\r\n\tLAST_TIME\x10\x12\x12\x17\n\x13\x46IRST_TIME_INTERVAL\x10\x13\x12\x16\n\x12LAST_TIME_INTERVAL\x10\x14\x12\x10\n\x0cGROUP_CONCAT\x10\x15\x12\x17\n\x13UNIQUE_COUNT_BITMAP\x10\x16\x12 \n\x1cUNIQUE_COUNT_APPROX_ORDINARY\x10\x17\x12\x12\n\x0e\x43OUNT_DISTINCT\x10\x18\x12\x07\n\x03LTV\x10\x19\x12\x0b\n\x07LTV_AVG\x10\x1a\x12\x0e\n\nCOUNT_RANK\x10\x1e\x12\x0c\n\x08\x41VG_RANK\x10\x1f\x12\x0c\n\x08MAX_RANK\x10 \x12\x0c\n\x08MIN_RANK\x10!\x12\x0c\n\x08SUM_RANK\x10"\x12\x12\n\x0e\x43OUNT_RANK_ASC\x10(\x12\x13\n\x0f\x43OUNT_RANK_DESC\x10)\x12\x1a\n\x16\x43OUNT_PERCENT_RANK_ASC\x10*\x12\x1b\n\x17\x43OUNT_PERCENT_RANK_DESC\x10+\x12\x10\n\x0c\x41VG_RANK_ASC\x10,\x12\x11\n\rAVG_RANK_DESC\x10-\x12\x18\n\x14\x41VG_PERCENT_RANK_ASC\x10.\x12\x19\n\x15\x41VG_PERCENT_RANK_DESC\x10/\x12\x10\n\x0cMAX_RANK_ASC\x10\x30\x12\x11\n\rMAX_RANK_DESC\x10\x31\x12\x18\n\x14MAX_PERCENT_RANK_ASC\x10\x32\x12\x19\n\x15MAX_PERCENT_RANK_DESC\x10\x33\x12\x10\n\x0cMIN_RANK_ASC\x10\x34\x12\x11\n\rMIN_RANK_DESC\x10\x35\x12\x18\n\x14MIN_PERCENT_RANK_ASC\x10\x36\x12\x19\n\x15MIN_PERCENT_RANK_DESC\x10\x37\x12\x10\n\x0cSUM_RANK_ASC\x10\x38\x12\x11\n\rSUM_RANK_DESC\x10\x39\x12\x18\n\x14SUM_PERCENT_RANK_ASC\x10:\x12\x19\n\x15SUM_PERCENT_RANK_DESC\x10;B\x1f\n\x1b\x63om.sensorsdata.focus.v1.aiP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "sensors.aggregation_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    _globals["DESCRIPTOR"]._options = None
    _globals["DESCRIPTOR"]._serialized_options = (
        b"\n\033com.sensorsdata.focus.v1.aiP\001"
    )
    _globals["_AGGREGATORTYPE"]._serialized_start = 52
    _globals["_AGGREGATORTYPE"]._serialized_end = 1080
# @@protoc_insertion_point(module_scope)
