# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: sensors/time_range.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from sensors import common_pb2 as sensors_dot_common__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x18sensors/time_range.proto\x12\x14sensorsdata.focus.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x14sensors/common.proto"N\n\x0cTimeInterval\x12\x0c\n\x04size\x18\x01 \x01(\x05\x12\x30\n\x04unit\x18\x02 \x01(\x0e\x32".sensorsdata.focus.v1.DateTimeUnit"\xd3\x03\n\tTimePoint\x12;\n\x04type\x18\x01 \x01(\x0e\x32-.sensorsdata.focus.v1.TimePoint.TimePointType\x12;\n\ntrunc_unit\x18\x02 \x01(\x0e\x32\'.sensorsdata.focus.v1.DateTimeTruncUnit\x12/\n\x0bstatic_time\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12H\n\rrelative_time\x18\x05 \x01(\x0b\x32\x31.sensorsdata.focus.v1.TimePoint.RelativeTimePoint\x12\x12\n\nexpression\x18\x06 \x01(\t\x1a\x61\n\x11RelativeTimePoint\x12\x11\n\tbase_time\x18\x01 \x01(\t\x12\x39\n\rtime_interval\x18\x02 \x01(\x0b\x32".sensorsdata.focus.v1.TimeInterval"Z\n\rTimePointType\x12\x1f\n\x1bTIME_POINT_TYPE_UNSPECIFIED\x10\x00\x12\n\n\x06STATIC\x10\x01\x12\x0c\n\x08RELATIVE\x10\x02\x12\x0e\n\nEXPRESSION\x10\x03"s\n\tTimeRange\x12\x33\n\nstart_time\x18\x01 \x01(\x0b\x32\x1f.sensorsdata.focus.v1.TimePoint\x12\x31\n\x08\x65nd_time\x18\x02 \x01(\x0b\x32\x1f.sensorsdata.focus.v1.TimePoint")\n\nTimeWindow\x12\r\n\x05value\x18\x01 \x01(\x05\x12\x0c\n\x04unit\x18\x02 \x01(\tB\x1f\n\x1b\x63om.sensorsdata.focus.v1.aiP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "sensors.time_range_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    _globals["DESCRIPTOR"]._options = None
    _globals["DESCRIPTOR"]._serialized_options = (
        b"\n\033com.sensorsdata.focus.v1.aiP\001"
    )
    _globals["_TIMEINTERVAL"]._serialized_start = 105
    _globals["_TIMEINTERVAL"]._serialized_end = 183
    _globals["_TIMEPOINT"]._serialized_start = 186
    _globals["_TIMEPOINT"]._serialized_end = 653
    _globals["_TIMEPOINT_RELATIVETIMEPOINT"]._serialized_start = 464
    _globals["_TIMEPOINT_RELATIVETIMEPOINT"]._serialized_end = 561
    _globals["_TIMEPOINT_TIMEPOINTTYPE"]._serialized_start = 563
    _globals["_TIMEPOINT_TIMEPOINTTYPE"]._serialized_end = 653
    _globals["_TIMERANGE"]._serialized_start = 655
    _globals["_TIMERANGE"]._serialized_end = 770
    _globals["_TIMEWINDOW"]._serialized_start = 772
    _globals["_TIMEWINDOW"]._serialized_end = 813
# @@protoc_insertion_point(module_scope)
