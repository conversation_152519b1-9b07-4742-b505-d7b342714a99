# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: stream_event.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x12stream_event.proto\x12\x14sensorsdata.focus.v1"\xfe\x03\n\x15\x42lockBasedStreamEvent\x12\x43\n\x04type\x18\x01 \x01(\x0e\x32\x35.sensorsdata.focus.v1.BlockBasedStreamEvent.EventType\x12\x0c\n\x04\x64\x61ta\x18\x02 \x01(\t\x12\x46\n\x06status\x18\x03 \x01(\x0b\x32\x36.sensorsdata.focus.v1.BlockBasedStreamEvent.StatusData\x1a\xd2\x01\n\nStatusData\x12Q\n\x06status\x18\x01 \x01(\x0e\x32\x41.sensorsdata.focus.v1.BlockBasedStreamEvent.StatusData.StatusType\x12\x0c\n\x04text\x18\x02 \x01(\t\x12\x0f\n\x07keeping\x18\x03 \x01(\x08"R\n\nStatusType\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\x0e\n\nPROCESSING\x10\x01\x12\t\n\x05\x45RROR\x10\x02\x12\x0b\n\x07WARNING\x10\x03\x12\x0b\n\x07SUCCESS\x10\x04"u\n\tEventType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x11\n\rMESSAGE_START\x10\x01\x12\x0f\n\x0b\x42LOCK_START\x10\x02\x12\x0b\n\x07MESSAGE\x10\x03\x12\n\n\x06STATUS\x10\x04\x12\r\n\tBLOCK_END\x10\x05\x12\x0f\n\x0bMESSAGE_END\x10\x06"\x85\x07\n\x0bStreamEvent\x12\x39\n\x04type\x18\x01 \x01(\x0e\x32+.sensorsdata.focus.v1.StreamEvent.EventType\x12I\n\rmessage_start\x18\x02 \x01(\x0b\x32\x32.sensorsdata.focus.v1.StreamEvent.MessageStartData\x12>\n\x07message\x18\x03 \x01(\x0b\x32-.sensorsdata.focus.v1.StreamEvent.MessageData\x12<\n\x06status\x18\x04 \x01(\x0b\x32,.sensorsdata.focus.v1.StreamEvent.StatusData\x1a\x46\n\x10MessageStartData\x12\x17\n\x0f\x63hat_session_id\x18\x01 \x01(\t\x12\x19\n\x11\x63hat_session_name\x18\x02 \x01(\t\x1a\x87\x02\n\x0bMessageData\x12G\n\x04type\x18\x01 \x01(\x0e\x32\x39.sensorsdata.focus.v1.StreamEvent.MessageData.MessageType\x12\x0f\n\x07\x63ontent\x18\x02 \x01(\t"\x9d\x01\n\x0bMessageType\x12\x0c\n\x08MARKDOWN\x10\x00\x12\x0c\n\x08THINKING\x10\x01\x12\t\n\x05TABLE\x10\x02\x12\t\n\x05\x43HART\x10\x03\x12\x13\n\x0f\x45NTITY_RELATION\x10\x04\x12\x11\n\rCANVAS_DESIGN\x10\x05\x12\n\n\x06\x43\x41NVAS\x10\x06\x12\x10\n\x0c\x41NALYSE_IDEA\x10\x07\x12\x16\n\x12RECOMMEND_QUESTION\x10\x08\x1a\xc8\x01\n\nStatusData\x12G\n\x06status\x18\x01 \x01(\x0e\x32\x37.sensorsdata.focus.v1.StreamEvent.StatusData.StatusType\x12\x0c\n\x04text\x18\x02 \x01(\t\x12\x0f\n\x07keeping\x18\x03 \x01(\x08"R\n\nStatusType\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\x0e\n\nPROCESSING\x10\x01\x12\t\n\x05\x45RROR\x10\x02\x12\x0b\n\x07WARNING\x10\x03\x12\x0b\n\x07SUCCESS\x10\x04"U\n\tEventType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x11\n\rMESSAGE_START\x10\x01\x12\x0b\n\x07MESSAGE\x10\x02\x12\n\n\x06STATUS\x10\x03\x12\x0f\n\x0bMESSAGE_END\x10\x04\x42\x1f\n\x1b\x63om.sensorsdata.focus.v1.aiP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "stream_event_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    _globals["DESCRIPTOR"]._options = None
    _globals["DESCRIPTOR"]._serialized_options = (
        b"\n\033com.sensorsdata.focus.v1.aiP\001"
    )
    _globals["_BLOCKBASEDSTREAMEVENT"]._serialized_start = 45
    _globals["_BLOCKBASEDSTREAMEVENT"]._serialized_end = 555
    _globals["_BLOCKBASEDSTREAMEVENT_STATUSDATA"]._serialized_start = 226
    _globals["_BLOCKBASEDSTREAMEVENT_STATUSDATA"]._serialized_end = 436
    _globals["_BLOCKBASEDSTREAMEVENT_STATUSDATA_STATUSTYPE"]._serialized_start = 354
    _globals["_BLOCKBASEDSTREAMEVENT_STATUSDATA_STATUSTYPE"]._serialized_end = 436
    _globals["_BLOCKBASEDSTREAMEVENT_EVENTTYPE"]._serialized_start = 438
    _globals["_BLOCKBASEDSTREAMEVENT_EVENTTYPE"]._serialized_end = 555
    _globals["_STREAMEVENT"]._serialized_start = 558
    _globals["_STREAMEVENT"]._serialized_end = 1459
    _globals["_STREAMEVENT_MESSAGESTARTDATA"]._serialized_start = 833
    _globals["_STREAMEVENT_MESSAGESTARTDATA"]._serialized_end = 903
    _globals["_STREAMEVENT_MESSAGEDATA"]._serialized_start = 906
    _globals["_STREAMEVENT_MESSAGEDATA"]._serialized_end = 1169
    _globals["_STREAMEVENT_MESSAGEDATA_MESSAGETYPE"]._serialized_start = 1012
    _globals["_STREAMEVENT_MESSAGEDATA_MESSAGETYPE"]._serialized_end = 1169
    _globals["_STREAMEVENT_STATUSDATA"]._serialized_start = 1172
    _globals["_STREAMEVENT_STATUSDATA"]._serialized_end = 1372
    _globals["_STREAMEVENT_STATUSDATA_STATUSTYPE"]._serialized_start = 354
    _globals["_STREAMEVENT_STATUSDATA_STATUSTYPE"]._serialized_end = 436
    _globals["_STREAMEVENT_EVENTTYPE"]._serialized_start = 1374
    _globals["_STREAMEVENT_EVENTTYPE"]._serialized_end = 1459
# @@protoc_insertion_point(module_scope)
