# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: crawler.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\rcrawler.proto\x12\x14sensorsdata.focus.v1"!\n\x11\x41nalyzeUrlRequest\x12\x0c\n\x04urls\x18\x02 \x03(\t"\x8f\x01\n\x12\x41nalyzeUrlResponse\x12\x41\n\x04urls\x18\x01 \x03(\x0b\x32\x33.sensorsdata.focus.v1.AnalyzeUrlResponse.AnalyzeUrl\x1a\x36\n\nAnalyzeUrl\x12\x0b\n\x03url\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\r\n\x05valid\x18\x03 \x01(\x08"\xfa\x01\n\x08\x43rawlUrl\x12\x0b\n\x03url\x18\x01 \x01(\t\x12<\n\ncrawl_type\x18\x02 \x01(\x0e\x32(.sensorsdata.focus.v1.CrawlUrl.CrawlType\x12@\n\x0c\x63rawl_status\x18\x03 \x01(\x0e\x32*.sensorsdata.focus.v1.CrawlUrl.CrawlStatus"+\n\tCrawlType\x12\x0f\n\x0bSINGLE_PAGE\x10\x00\x12\r\n\tRECURSIVE\x10\x01"4\n\x0b\x43rawlStatus\x12\x0e\n\nPROCESSING\x10\x00\x12\n\n\x06\x46\x41ILED\x10\x02\x12\t\n\x05READY\x10\x03"?\n\x0f\x43rawlUrlRequest\x12,\n\x04urls\x18\x01 \x03(\x0b\x32\x1e.sensorsdata.focus.v1.CrawlUrl"@\n\x10\x43rawlUrlResponse\x12,\n\x04urls\x18\x01 \x03(\x0b\x32\x1e.sensorsdata.focus.v1.CrawlUrl2\xce\x01\n\x0e\x43rawlerService\x12`\n\x0b\x61nalyzeUrls\x12\'.sensorsdata.focus.v1.AnalyzeUrlRequest\x1a(.sensorsdata.focus.v1.AnalyzeUrlResponse\x12Z\n\tcrawlUrls\x12%.sensorsdata.focus.v1.CrawlUrlRequest\x1a&.sensorsdata.focus.v1.CrawlUrlResponseB\x1f\n\x1b\x63om.sensorsdata.focus.v1.aiP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "crawler_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    _globals["DESCRIPTOR"]._options = None
    _globals["DESCRIPTOR"]._serialized_options = (
        b"\n\033com.sensorsdata.focus.v1.aiP\001"
    )
    _globals["_ANALYZEURLREQUEST"]._serialized_start = 39
    _globals["_ANALYZEURLREQUEST"]._serialized_end = 72
    _globals["_ANALYZEURLRESPONSE"]._serialized_start = 75
    _globals["_ANALYZEURLRESPONSE"]._serialized_end = 218
    _globals["_ANALYZEURLRESPONSE_ANALYZEURL"]._serialized_start = 164
    _globals["_ANALYZEURLRESPONSE_ANALYZEURL"]._serialized_end = 218
    _globals["_CRAWLURL"]._serialized_start = 221
    _globals["_CRAWLURL"]._serialized_end = 471
    _globals["_CRAWLURL_CRAWLTYPE"]._serialized_start = 374
    _globals["_CRAWLURL_CRAWLTYPE"]._serialized_end = 417
    _globals["_CRAWLURL_CRAWLSTATUS"]._serialized_start = 419
    _globals["_CRAWLURL_CRAWLSTATUS"]._serialized_end = 471
    _globals["_CRAWLURLREQUEST"]._serialized_start = 473
    _globals["_CRAWLURLREQUEST"]._serialized_end = 536
    _globals["_CRAWLURLRESPONSE"]._serialized_start = 538
    _globals["_CRAWLURLRESPONSE"]._serialized_end = 602
    _globals["_CRAWLERSERVICE"]._serialized_start = 605
    _globals["_CRAWLERSERVICE"]._serialized_end = 811
# @@protoc_insertion_point(module_scope)
