from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class ContentOverLengthStrategy(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    FULL: _ClassVar[ContentOverLengthStrategy]
    TRUNCATE: _ClassVar[ContentOverLengthStrategy]
    DONOTHING: _ClassVar[ContentOverLengthStrategy]
FULL: ContentOverLengthStrategy
TRUNCATE: ContentOverLengthStrategy
DONOTHING: ContentOverLengthStrategy

class CanvasMsgContent(_message.Message):
    __slots__ = ("type", "push", "webhook", "text_msg", "wechat_active_push", "wechat_service_template_msg", "wechat_miniprogram_template_msg", "edm", "line", "reward_grant", "common")
    class CanvasMsgContentType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        PUSH: _ClassVar[CanvasMsgContent.CanvasMsgContentType]
        WEBHOOK: _ClassVar[CanvasMsgContent.CanvasMsgContentType]
        TEXT_MSG: _ClassVar[CanvasMsgContent.CanvasMsgContentType]
        WECHAT_ACTIVE_PUSH: _ClassVar[CanvasMsgContent.CanvasMsgContentType]
        WECHAT_SERVICE_TEMPLATE_MSG: _ClassVar[CanvasMsgContent.CanvasMsgContentType]
        WECHAT_MINIPROGRAM_TEMPLATE_MSG: _ClassVar[CanvasMsgContent.CanvasMsgContentType]
        EDM: _ClassVar[CanvasMsgContent.CanvasMsgContentType]
        LINE: _ClassVar[CanvasMsgContent.CanvasMsgContentType]
        REWARD_GRANT: _ClassVar[CanvasMsgContent.CanvasMsgContentType]
        COMMON: _ClassVar[CanvasMsgContent.CanvasMsgContentType]
    PUSH: CanvasMsgContent.CanvasMsgContentType
    WEBHOOK: CanvasMsgContent.CanvasMsgContentType
    TEXT_MSG: CanvasMsgContent.CanvasMsgContentType
    WECHAT_ACTIVE_PUSH: CanvasMsgContent.CanvasMsgContentType
    WECHAT_SERVICE_TEMPLATE_MSG: CanvasMsgContent.CanvasMsgContentType
    WECHAT_MINIPROGRAM_TEMPLATE_MSG: CanvasMsgContent.CanvasMsgContentType
    EDM: CanvasMsgContent.CanvasMsgContentType
    LINE: CanvasMsgContent.CanvasMsgContentType
    REWARD_GRANT: CanvasMsgContent.CanvasMsgContentType
    COMMON: CanvasMsgContent.CanvasMsgContentType
    TYPE_FIELD_NUMBER: _ClassVar[int]
    PUSH_FIELD_NUMBER: _ClassVar[int]
    WEBHOOK_FIELD_NUMBER: _ClassVar[int]
    TEXT_MSG_FIELD_NUMBER: _ClassVar[int]
    WECHAT_ACTIVE_PUSH_FIELD_NUMBER: _ClassVar[int]
    WECHAT_SERVICE_TEMPLATE_MSG_FIELD_NUMBER: _ClassVar[int]
    WECHAT_MINIPROGRAM_TEMPLATE_MSG_FIELD_NUMBER: _ClassVar[int]
    EDM_FIELD_NUMBER: _ClassVar[int]
    LINE_FIELD_NUMBER: _ClassVar[int]
    REWARD_GRANT_FIELD_NUMBER: _ClassVar[int]
    COMMON_FIELD_NUMBER: _ClassVar[int]
    type: CanvasMsgContent.CanvasMsgContentType
    push: PushContent
    webhook: WebHookContent
    text_msg: TextMsgContent
    wechat_active_push: _containers.RepeatedCompositeFieldContainer[WxReplyContent]
    wechat_service_template_msg: WechatServiceTemplateMsgContent
    wechat_miniprogram_template_msg: WechatMiniProgramTemplateMsgContent
    edm: EdmContent
    line: LineContent
    reward_grant: RewardContent
    common: str
    def __init__(self, type: _Optional[_Union[CanvasMsgContent.CanvasMsgContentType, str]] = ..., push: _Optional[_Union[PushContent, _Mapping]] = ..., webhook: _Optional[_Union[WebHookContent, _Mapping]] = ..., text_msg: _Optional[_Union[TextMsgContent, _Mapping]] = ..., wechat_active_push: _Optional[_Iterable[_Union[WxReplyContent, _Mapping]]] = ..., wechat_service_template_msg: _Optional[_Union[WechatServiceTemplateMsgContent, _Mapping]] = ..., wechat_miniprogram_template_msg: _Optional[_Union[WechatMiniProgramTemplateMsgContent, _Mapping]] = ..., edm: _Optional[_Union[EdmContent, _Mapping]] = ..., line: _Optional[_Union[LineContent, _Mapping]] = ..., reward_grant: _Optional[_Union[RewardContent, _Mapping]] = ..., common: _Optional[str] = ...) -> None: ...

class NameValueParam(_message.Message):
    __slots__ = ("name", "value")
    NAME_FIELD_NUMBER: _ClassVar[int]
    VALUE_FIELD_NUMBER: _ClassVar[int]
    name: str
    value: str
    def __init__(self, name: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...

class PushContent(_message.Message):
    __slots__ = ("freemarker_syntax_version", "title", "content", "landing_type", "customized_fields", "notification_icon", "advanced_setting", "over_length_strategy", "enable_in_app_message")
    class LandingType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        OPEN_APP: _ClassVar[PushContent.LandingType]
        LINK: _ClassVar[PushContent.LandingType]
        CUSTOMIZED: _ClassVar[PushContent.LandingType]
    OPEN_APP: PushContent.LandingType
    LINK: PushContent.LandingType
    CUSTOMIZED: PushContent.LandingType
    FREEMARKER_SYNTAX_VERSION_FIELD_NUMBER: _ClassVar[int]
    TITLE_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    LANDING_TYPE_FIELD_NUMBER: _ClassVar[int]
    CUSTOMIZED_FIELDS_FIELD_NUMBER: _ClassVar[int]
    NOTIFICATION_ICON_FIELD_NUMBER: _ClassVar[int]
    ADVANCED_SETTING_FIELD_NUMBER: _ClassVar[int]
    OVER_LENGTH_STRATEGY_FIELD_NUMBER: _ClassVar[int]
    ENABLE_IN_APP_MESSAGE_FIELD_NUMBER: _ClassVar[int]
    freemarker_syntax_version: int
    title: str
    content: str
    landing_type: PushContent.LandingType
    customized_fields: _containers.RepeatedCompositeFieldContainer[NameValueParam]
    notification_icon: str
    advanced_setting: PushAdvancedSetting
    over_length_strategy: ContentOverLengthStrategy
    enable_in_app_message: bool
    def __init__(self, freemarker_syntax_version: _Optional[int] = ..., title: _Optional[str] = ..., content: _Optional[str] = ..., landing_type: _Optional[_Union[PushContent.LandingType, str]] = ..., customized_fields: _Optional[_Iterable[_Union[NameValueParam, _Mapping]]] = ..., notification_icon: _Optional[str] = ..., advanced_setting: _Optional[_Union[PushAdvancedSetting, _Mapping]] = ..., over_length_strategy: _Optional[_Union[ContentOverLengthStrategy, str]] = ..., enable_in_app_message: bool = ...) -> None: ...

class PushAdvancedSetting(_message.Message):
    __slots__ = ("channel_id", "enable", "notification_style", "notification_tip", "manufacturer", "message_ttl", "ios")
    CHANNEL_ID_FIELD_NUMBER: _ClassVar[int]
    ENABLE_FIELD_NUMBER: _ClassVar[int]
    NOTIFICATION_STYLE_FIELD_NUMBER: _ClassVar[int]
    NOTIFICATION_TIP_FIELD_NUMBER: _ClassVar[int]
    MANUFACTURER_FIELD_NUMBER: _ClassVar[int]
    MESSAGE_TTL_FIELD_NUMBER: _ClassVar[int]
    IOS_FIELD_NUMBER: _ClassVar[int]
    channel_id: str
    enable: bool
    notification_style: PushNotificationStyle
    notification_tip: PushNotificationTip
    manufacturer: PushManufacturer
    message_ttl: PushMessageTtl
    ios: PushAdvancedSettingIOS
    def __init__(self, channel_id: _Optional[str] = ..., enable: bool = ..., notification_style: _Optional[_Union[PushNotificationStyle, _Mapping]] = ..., notification_tip: _Optional[_Union[PushNotificationTip, _Mapping]] = ..., manufacturer: _Optional[_Union[PushManufacturer, _Mapping]] = ..., message_ttl: _Optional[_Union[PushMessageTtl, _Mapping]] = ..., ios: _Optional[_Union[PushAdvancedSettingIOS, _Mapping]] = ...) -> None: ...

class PushNotificationStyle(_message.Message):
    __slots__ = ("style", "big_text", "big_picture")
    class NotificationStyleType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        NONE: _ClassVar[PushNotificationStyle.NotificationStyleType]
        DEFAULT: _ClassVar[PushNotificationStyle.NotificationStyleType]
        BIG_TEXT: _ClassVar[PushNotificationStyle.NotificationStyleType]
        BIG_PICTURE: _ClassVar[PushNotificationStyle.NotificationStyleType]
    NONE: PushNotificationStyle.NotificationStyleType
    DEFAULT: PushNotificationStyle.NotificationStyleType
    BIG_TEXT: PushNotificationStyle.NotificationStyleType
    BIG_PICTURE: PushNotificationStyle.NotificationStyleType
    STYLE_FIELD_NUMBER: _ClassVar[int]
    BIG_TEXT_FIELD_NUMBER: _ClassVar[int]
    BIG_PICTURE_FIELD_NUMBER: _ClassVar[int]
    style: PushNotificationStyle.NotificationStyleType
    big_text: str
    big_picture: str
    def __init__(self, style: _Optional[_Union[PushNotificationStyle.NotificationStyleType, str]] = ..., big_text: _Optional[str] = ..., big_picture: _Optional[str] = ...) -> None: ...

class PushNotificationTip(_message.Message):
    __slots__ = ("sound_type", "custom_sound", "vibrate", "light")
    class SoundType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        NONE: _ClassVar[PushNotificationTip.SoundType]
        DEFAULT: _ClassVar[PushNotificationTip.SoundType]
        CUSTOM: _ClassVar[PushNotificationTip.SoundType]
        CLOSE: _ClassVar[PushNotificationTip.SoundType]
    NONE: PushNotificationTip.SoundType
    DEFAULT: PushNotificationTip.SoundType
    CUSTOM: PushNotificationTip.SoundType
    CLOSE: PushNotificationTip.SoundType
    SOUND_TYPE_FIELD_NUMBER: _ClassVar[int]
    CUSTOM_SOUND_FIELD_NUMBER: _ClassVar[int]
    VIBRATE_FIELD_NUMBER: _ClassVar[int]
    LIGHT_FIELD_NUMBER: _ClassVar[int]
    sound_type: PushNotificationTip.SoundType
    custom_sound: str
    vibrate: int
    light: int
    def __init__(self, sound_type: _Optional[_Union[PushNotificationTip.SoundType, str]] = ..., custom_sound: _Optional[str] = ..., vibrate: _Optional[int] = ..., light: _Optional[int] = ...) -> None: ...

class PushManufacturer(_message.Message):
    __slots__ = ("enable", "skip_quota", "enabled_manufacturer", "jiguang_distribution", "getui_android", "third_party_channel")
    ENABLE_FIELD_NUMBER: _ClassVar[int]
    SKIP_QUOTA_FIELD_NUMBER: _ClassVar[int]
    ENABLED_MANUFACTURER_FIELD_NUMBER: _ClassVar[int]
    JIGUANG_DISTRIBUTION_FIELD_NUMBER: _ClassVar[int]
    GETUI_ANDROID_FIELD_NUMBER: _ClassVar[int]
    THIRD_PARTY_CHANNEL_FIELD_NUMBER: _ClassVar[int]
    enable: bool
    skip_quota: bool
    enabled_manufacturer: _containers.RepeatedScalarFieldContainer[str]
    jiguang_distribution: str
    getui_android: int
    third_party_channel: ThirdPartyChannel
    def __init__(self, enable: bool = ..., skip_quota: bool = ..., enabled_manufacturer: _Optional[_Iterable[str]] = ..., jiguang_distribution: _Optional[str] = ..., getui_android: _Optional[int] = ..., third_party_channel: _Optional[_Union[ThirdPartyChannel, _Mapping]] = ...) -> None: ...

class ThirdPartyChannel(_message.Message):
    __slots__ = ("honor", "huawei", "oppo", "vivo", "xiaomi")
    class Honor(_message.Message):
        __slots__ = ("importance",)
        IMPORTANCE_FIELD_NUMBER: _ClassVar[int]
        importance: str
        def __init__(self, importance: _Optional[str] = ...) -> None: ...
    class Huawei(_message.Message):
        __slots__ = ("importance", "channel_id", "category")
        IMPORTANCE_FIELD_NUMBER: _ClassVar[int]
        CHANNEL_ID_FIELD_NUMBER: _ClassVar[int]
        CATEGORY_FIELD_NUMBER: _ClassVar[int]
        importance: str
        channel_id: str
        category: str
        def __init__(self, importance: _Optional[str] = ..., channel_id: _Optional[str] = ..., category: _Optional[str] = ...) -> None: ...
    class Oppo(_message.Message):
        __slots__ = ("channel_id",)
        CHANNEL_ID_FIELD_NUMBER: _ClassVar[int]
        channel_id: str
        def __init__(self, channel_id: _Optional[str] = ...) -> None: ...
    class Vivo(_message.Message):
        __slots__ = ("category",)
        CATEGORY_FIELD_NUMBER: _ClassVar[int]
        category: str
        def __init__(self, category: _Optional[str] = ...) -> None: ...
    class Xiaomi(_message.Message):
        __slots__ = ("channel_id",)
        CHANNEL_ID_FIELD_NUMBER: _ClassVar[int]
        channel_id: str
        def __init__(self, channel_id: _Optional[str] = ...) -> None: ...
    HONOR_FIELD_NUMBER: _ClassVar[int]
    HUAWEI_FIELD_NUMBER: _ClassVar[int]
    OPPO_FIELD_NUMBER: _ClassVar[int]
    VIVO_FIELD_NUMBER: _ClassVar[int]
    XIAOMI_FIELD_NUMBER: _ClassVar[int]
    honor: ThirdPartyChannel.Honor
    huawei: ThirdPartyChannel.Huawei
    oppo: ThirdPartyChannel.Oppo
    vivo: ThirdPartyChannel.Vivo
    xiaomi: ThirdPartyChannel.Xiaomi
    def __init__(self, honor: _Optional[_Union[ThirdPartyChannel.Honor, _Mapping]] = ..., huawei: _Optional[_Union[ThirdPartyChannel.Huawei, _Mapping]] = ..., oppo: _Optional[_Union[ThirdPartyChannel.Oppo, _Mapping]] = ..., vivo: _Optional[_Union[ThirdPartyChannel.Vivo, _Mapping]] = ..., xiaomi: _Optional[_Union[ThirdPartyChannel.Xiaomi, _Mapping]] = ...) -> None: ...

class PushMessageTtl(_message.Message):
    __slots__ = ("enable", "ttl_second", "unit")
    ENABLE_FIELD_NUMBER: _ClassVar[int]
    TTL_SECOND_FIELD_NUMBER: _ClassVar[int]
    UNIT_FIELD_NUMBER: _ClassVar[int]
    enable: bool
    ttl_second: int
    unit: str
    def __init__(self, enable: bool = ..., ttl_second: _Optional[int] = ..., unit: _Optional[str] = ...) -> None: ...

class PushAdvancedSettingIOS(_message.Message):
    __slots__ = ("sub_title", "badge_number", "thread_id", "notification_tip")
    SUB_TITLE_FIELD_NUMBER: _ClassVar[int]
    BADGE_NUMBER_FIELD_NUMBER: _ClassVar[int]
    THREAD_ID_FIELD_NUMBER: _ClassVar[int]
    NOTIFICATION_TIP_FIELD_NUMBER: _ClassVar[int]
    sub_title: str
    badge_number: str
    thread_id: str
    notification_tip: PushNotificationTip
    def __init__(self, sub_title: _Optional[str] = ..., badge_number: _Optional[str] = ..., thread_id: _Optional[str] = ..., notification_tip: _Optional[_Union[PushNotificationTip, _Mapping]] = ...) -> None: ...

class WebHookContent(_message.Message):
    __slots__ = ("freemarker_syntax_version", "plan_params", "over_length_strategy")
    class ContentParam(_message.Message):
        __slots__ = ("name", "cname", "value", "required", "extra")
        NAME_FIELD_NUMBER: _ClassVar[int]
        CNAME_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        REQUIRED_FIELD_NUMBER: _ClassVar[int]
        EXTRA_FIELD_NUMBER: _ClassVar[int]
        name: str
        cname: str
        value: str
        required: bool
        extra: str
        def __init__(self, name: _Optional[str] = ..., cname: _Optional[str] = ..., value: _Optional[str] = ..., required: bool = ..., extra: _Optional[str] = ...) -> None: ...
    FREEMARKER_SYNTAX_VERSION_FIELD_NUMBER: _ClassVar[int]
    PLAN_PARAMS_FIELD_NUMBER: _ClassVar[int]
    OVER_LENGTH_STRATEGY_FIELD_NUMBER: _ClassVar[int]
    freemarker_syntax_version: int
    plan_params: _containers.RepeatedCompositeFieldContainer[WebHookContent.ContentParam]
    over_length_strategy: ContentOverLengthStrategy
    def __init__(self, freemarker_syntax_version: _Optional[int] = ..., plan_params: _Optional[_Iterable[_Union[WebHookContent.ContentParam, _Mapping]]] = ..., over_length_strategy: _Optional[_Union[ContentOverLengthStrategy, str]] = ...) -> None: ...

class TextMsgContent(_message.Message):
    __slots__ = ("freemarker_syntax_version", "template_id", "template_param_list", "template_content", "over_length_strategy")
    FREEMARKER_SYNTAX_VERSION_FIELD_NUMBER: _ClassVar[int]
    TEMPLATE_ID_FIELD_NUMBER: _ClassVar[int]
    TEMPLATE_PARAM_LIST_FIELD_NUMBER: _ClassVar[int]
    TEMPLATE_CONTENT_FIELD_NUMBER: _ClassVar[int]
    OVER_LENGTH_STRATEGY_FIELD_NUMBER: _ClassVar[int]
    freemarker_syntax_version: int
    template_id: str
    template_param_list: _containers.RepeatedCompositeFieldContainer[NameValueParam]
    template_content: str
    over_length_strategy: ContentOverLengthStrategy
    def __init__(self, freemarker_syntax_version: _Optional[int] = ..., template_id: _Optional[str] = ..., template_param_list: _Optional[_Iterable[_Union[NameValueParam, _Mapping]]] = ..., template_content: _Optional[str] = ..., over_length_strategy: _Optional[_Union[ContentOverLengthStrategy, str]] = ...) -> None: ...

class WechatServiceTemplateMsgContent(_message.Message):
    __slots__ = ("template_id", "template_title", "template_content", "content", "primary_industry", "deputy_industry", "landing_type", "url", "miniprogram", "over_length_strategy")
    class LandingType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        H5: _ClassVar[WechatServiceTemplateMsgContent.LandingType]
        MINIPROGRAM: _ClassVar[WechatServiceTemplateMsgContent.LandingType]
        NONE: _ClassVar[WechatServiceTemplateMsgContent.LandingType]
    H5: WechatServiceTemplateMsgContent.LandingType
    MINIPROGRAM: WechatServiceTemplateMsgContent.LandingType
    NONE: WechatServiceTemplateMsgContent.LandingType
    class TemplateContentParam(_message.Message):
        __slots__ = ("key", "value", "color")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        COLOR_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        color: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ..., color: _Optional[str] = ...) -> None: ...
    class MiniProgramParam(_message.Message):
        __slots__ = ("app_id", "pagepath")
        APP_ID_FIELD_NUMBER: _ClassVar[int]
        PAGEPATH_FIELD_NUMBER: _ClassVar[int]
        app_id: str
        pagepath: str
        def __init__(self, app_id: _Optional[str] = ..., pagepath: _Optional[str] = ...) -> None: ...
    TEMPLATE_ID_FIELD_NUMBER: _ClassVar[int]
    TEMPLATE_TITLE_FIELD_NUMBER: _ClassVar[int]
    TEMPLATE_CONTENT_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    PRIMARY_INDUSTRY_FIELD_NUMBER: _ClassVar[int]
    DEPUTY_INDUSTRY_FIELD_NUMBER: _ClassVar[int]
    LANDING_TYPE_FIELD_NUMBER: _ClassVar[int]
    URL_FIELD_NUMBER: _ClassVar[int]
    MINIPROGRAM_FIELD_NUMBER: _ClassVar[int]
    OVER_LENGTH_STRATEGY_FIELD_NUMBER: _ClassVar[int]
    template_id: str
    template_title: str
    template_content: str
    content: _containers.RepeatedCompositeFieldContainer[WechatServiceTemplateMsgContent.TemplateContentParam]
    primary_industry: str
    deputy_industry: str
    landing_type: WechatServiceTemplateMsgContent.LandingType
    url: str
    miniprogram: WechatServiceTemplateMsgContent.MiniProgramParam
    over_length_strategy: ContentOverLengthStrategy
    def __init__(self, template_id: _Optional[str] = ..., template_title: _Optional[str] = ..., template_content: _Optional[str] = ..., content: _Optional[_Iterable[_Union[WechatServiceTemplateMsgContent.TemplateContentParam, _Mapping]]] = ..., primary_industry: _Optional[str] = ..., deputy_industry: _Optional[str] = ..., landing_type: _Optional[_Union[WechatServiceTemplateMsgContent.LandingType, str]] = ..., url: _Optional[str] = ..., miniprogram: _Optional[_Union[WechatServiceTemplateMsgContent.MiniProgramParam, _Mapping]] = ..., over_length_strategy: _Optional[_Union[ContentOverLengthStrategy, str]] = ...) -> None: ...

class WechatMiniProgramTemplateMsgContent(_message.Message):
    __slots__ = ("template_id", "template_title", "page", "lang", "state", "template_content", "data", "over_length_strategy")
    class TemplateContentParam(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    TEMPLATE_ID_FIELD_NUMBER: _ClassVar[int]
    TEMPLATE_TITLE_FIELD_NUMBER: _ClassVar[int]
    PAGE_FIELD_NUMBER: _ClassVar[int]
    LANG_FIELD_NUMBER: _ClassVar[int]
    STATE_FIELD_NUMBER: _ClassVar[int]
    TEMPLATE_CONTENT_FIELD_NUMBER: _ClassVar[int]
    DATA_FIELD_NUMBER: _ClassVar[int]
    OVER_LENGTH_STRATEGY_FIELD_NUMBER: _ClassVar[int]
    template_id: str
    template_title: str
    page: str
    lang: str
    state: str
    template_content: str
    data: _containers.RepeatedCompositeFieldContainer[WechatMiniProgramTemplateMsgContent.TemplateContentParam]
    over_length_strategy: ContentOverLengthStrategy
    def __init__(self, template_id: _Optional[str] = ..., template_title: _Optional[str] = ..., page: _Optional[str] = ..., lang: _Optional[str] = ..., state: _Optional[str] = ..., template_content: _Optional[str] = ..., data: _Optional[_Iterable[_Union[WechatMiniProgramTemplateMsgContent.TemplateContentParam, _Mapping]]] = ..., over_length_strategy: _Optional[_Union[ContentOverLengthStrategy, str]] = ...) -> None: ...

class WxReplyContent(_message.Message):
    __slots__ = ("msg_type", "text", "image", "msg_menu", "mp_news", "news", "wx_card", "mini_program_page")
    class WxReplyContentMsgType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        TEXT: _ClassVar[WxReplyContent.WxReplyContentMsgType]
        IMAGE: _ClassVar[WxReplyContent.WxReplyContentMsgType]
        MSG_MENU: _ClassVar[WxReplyContent.WxReplyContentMsgType]
        MP_NEWS: _ClassVar[WxReplyContent.WxReplyContentMsgType]
        NEWS: _ClassVar[WxReplyContent.WxReplyContentMsgType]
        WX_CARD: _ClassVar[WxReplyContent.WxReplyContentMsgType]
        MINI_PROGRAM_PAGE: _ClassVar[WxReplyContent.WxReplyContentMsgType]
    TEXT: WxReplyContent.WxReplyContentMsgType
    IMAGE: WxReplyContent.WxReplyContentMsgType
    MSG_MENU: WxReplyContent.WxReplyContentMsgType
    MP_NEWS: WxReplyContent.WxReplyContentMsgType
    NEWS: WxReplyContent.WxReplyContentMsgType
    WX_CARD: WxReplyContent.WxReplyContentMsgType
    MINI_PROGRAM_PAGE: WxReplyContent.WxReplyContentMsgType
    MSG_TYPE_FIELD_NUMBER: _ClassVar[int]
    TEXT_FIELD_NUMBER: _ClassVar[int]
    IMAGE_FIELD_NUMBER: _ClassVar[int]
    MSG_MENU_FIELD_NUMBER: _ClassVar[int]
    MP_NEWS_FIELD_NUMBER: _ClassVar[int]
    NEWS_FIELD_NUMBER: _ClassVar[int]
    WX_CARD_FIELD_NUMBER: _ClassVar[int]
    MINI_PROGRAM_PAGE_FIELD_NUMBER: _ClassVar[int]
    msg_type: WxReplyContent.WxReplyContentMsgType
    text: WxTextMsg
    image: WxImageMsg
    msg_menu: MenuTree
    mp_news: WxMpNewsMsg
    news: WxNewsMsg
    wx_card: WxCardMsg
    mini_program_page: WxMiniProgramMsg
    def __init__(self, msg_type: _Optional[_Union[WxReplyContent.WxReplyContentMsgType, str]] = ..., text: _Optional[_Union[WxTextMsg, _Mapping]] = ..., image: _Optional[_Union[WxImageMsg, _Mapping]] = ..., msg_menu: _Optional[_Union[MenuTree, _Mapping]] = ..., mp_news: _Optional[_Union[WxMpNewsMsg, _Mapping]] = ..., news: _Optional[_Union[WxNewsMsg, _Mapping]] = ..., wx_card: _Optional[_Union[WxCardMsg, _Mapping]] = ..., mini_program_page: _Optional[_Union[WxMiniProgramMsg, _Mapping]] = ...) -> None: ...

class WxTextMsg(_message.Message):
    __slots__ = ("content",)
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    content: str
    def __init__(self, content: _Optional[str] = ...) -> None: ...

class WxImageMsg(_message.Message):
    __slots__ = ("id", "media_id", "temp", "media_type", "url")
    ID_FIELD_NUMBER: _ClassVar[int]
    MEDIA_ID_FIELD_NUMBER: _ClassVar[int]
    TEMP_FIELD_NUMBER: _ClassVar[int]
    MEDIA_TYPE_FIELD_NUMBER: _ClassVar[int]
    URL_FIELD_NUMBER: _ClassVar[int]
    id: str
    media_id: str
    temp: int
    media_type: str
    url: str
    def __init__(self, id: _Optional[str] = ..., media_id: _Optional[str] = ..., temp: _Optional[int] = ..., media_type: _Optional[str] = ..., url: _Optional[str] = ...) -> None: ...

class MenuTree(_message.Message):
    __slots__ = ("id", "cname", "create_user_cname", "create_user_name", "created_by_user_id", "created_by_roles", "create_time", "update_user_cname", "update_user_name", "update_time", "menu")
    ID_FIELD_NUMBER: _ClassVar[int]
    CNAME_FIELD_NUMBER: _ClassVar[int]
    CREATE_USER_CNAME_FIELD_NUMBER: _ClassVar[int]
    CREATE_USER_NAME_FIELD_NUMBER: _ClassVar[int]
    CREATED_BY_USER_ID_FIELD_NUMBER: _ClassVar[int]
    CREATED_BY_ROLES_FIELD_NUMBER: _ClassVar[int]
    CREATE_TIME_FIELD_NUMBER: _ClassVar[int]
    UPDATE_USER_CNAME_FIELD_NUMBER: _ClassVar[int]
    UPDATE_USER_NAME_FIELD_NUMBER: _ClassVar[int]
    UPDATE_TIME_FIELD_NUMBER: _ClassVar[int]
    MENU_FIELD_NUMBER: _ClassVar[int]
    id: int
    cname: str
    create_user_cname: str
    create_user_name: str
    created_by_user_id: int
    created_by_roles: _containers.RepeatedScalarFieldContainer[int]
    create_time: str
    update_user_cname: str
    update_user_name: str
    update_time: str
    menu: WxMenu
    def __init__(self, id: _Optional[int] = ..., cname: _Optional[str] = ..., create_user_cname: _Optional[str] = ..., create_user_name: _Optional[str] = ..., created_by_user_id: _Optional[int] = ..., created_by_roles: _Optional[_Iterable[int]] = ..., create_time: _Optional[str] = ..., update_user_cname: _Optional[str] = ..., update_user_name: _Optional[str] = ..., update_time: _Optional[str] = ..., menu: _Optional[_Union[WxMenu, _Mapping]] = ...) -> None: ...

class WxMenu(_message.Message):
    __slots__ = ("id", "option_id", "type", "title", "data", "children")
    class MenuType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        MSG_MENU: _ClassVar[WxMenu.MenuType]
        TEXT: _ClassVar[WxMenu.MenuType]
        IMAGE: _ClassVar[WxMenu.MenuType]
        NEWS: _ClassVar[WxMenu.MenuType]
        MP_NEWS: _ClassVar[WxMenu.MenuType]
        MINI_PROGRAM_PAGE: _ClassVar[WxMenu.MenuType]
        WX_CARD: _ClassVar[WxMenu.MenuType]
    MSG_MENU: WxMenu.MenuType
    TEXT: WxMenu.MenuType
    IMAGE: WxMenu.MenuType
    NEWS: WxMenu.MenuType
    MP_NEWS: WxMenu.MenuType
    MINI_PROGRAM_PAGE: WxMenu.MenuType
    WX_CARD: WxMenu.MenuType
    ID_FIELD_NUMBER: _ClassVar[int]
    OPTION_ID_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    TITLE_FIELD_NUMBER: _ClassVar[int]
    DATA_FIELD_NUMBER: _ClassVar[int]
    CHILDREN_FIELD_NUMBER: _ClassVar[int]
    id: str
    option_id: str
    type: WxMenu.MenuType
    title: str
    data: _containers.RepeatedScalarFieldContainer[str]
    children: _containers.RepeatedCompositeFieldContainer[WxMenu]
    def __init__(self, id: _Optional[str] = ..., option_id: _Optional[str] = ..., type: _Optional[_Union[WxMenu.MenuType, str]] = ..., title: _Optional[str] = ..., data: _Optional[_Iterable[str]] = ..., children: _Optional[_Iterable[_Union[WxMenu, _Mapping]]] = ...) -> None: ...

class WxMpNewsMsg(_message.Message):
    __slots__ = ("media_id", "url", "title", "author", "thumb_media_id", "thumb_media_url", "digest", "show_cover_pic", "content", "content_source_url", "is_multi", "news_item")
    class WxNewsItemDetail(_message.Message):
        __slots__ = ("id", "title", "thumb_media_url", "show_cover_pic", "author", "digest", "content", "url", "content_source_url")
        ID_FIELD_NUMBER: _ClassVar[int]
        TITLE_FIELD_NUMBER: _ClassVar[int]
        THUMB_MEDIA_URL_FIELD_NUMBER: _ClassVar[int]
        SHOW_COVER_PIC_FIELD_NUMBER: _ClassVar[int]
        AUTHOR_FIELD_NUMBER: _ClassVar[int]
        DIGEST_FIELD_NUMBER: _ClassVar[int]
        CONTENT_FIELD_NUMBER: _ClassVar[int]
        URL_FIELD_NUMBER: _ClassVar[int]
        CONTENT_SOURCE_URL_FIELD_NUMBER: _ClassVar[int]
        id: int
        title: str
        thumb_media_url: str
        show_cover_pic: int
        author: str
        digest: str
        content: str
        url: str
        content_source_url: str
        def __init__(self, id: _Optional[int] = ..., title: _Optional[str] = ..., thumb_media_url: _Optional[str] = ..., show_cover_pic: _Optional[int] = ..., author: _Optional[str] = ..., digest: _Optional[str] = ..., content: _Optional[str] = ..., url: _Optional[str] = ..., content_source_url: _Optional[str] = ...) -> None: ...
    MEDIA_ID_FIELD_NUMBER: _ClassVar[int]
    URL_FIELD_NUMBER: _ClassVar[int]
    TITLE_FIELD_NUMBER: _ClassVar[int]
    AUTHOR_FIELD_NUMBER: _ClassVar[int]
    THUMB_MEDIA_ID_FIELD_NUMBER: _ClassVar[int]
    THUMB_MEDIA_URL_FIELD_NUMBER: _ClassVar[int]
    DIGEST_FIELD_NUMBER: _ClassVar[int]
    SHOW_COVER_PIC_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    CONTENT_SOURCE_URL_FIELD_NUMBER: _ClassVar[int]
    IS_MULTI_FIELD_NUMBER: _ClassVar[int]
    NEWS_ITEM_FIELD_NUMBER: _ClassVar[int]
    media_id: str
    url: str
    title: str
    author: str
    thumb_media_id: str
    thumb_media_url: str
    digest: str
    show_cover_pic: int
    content: str
    content_source_url: str
    is_multi: bool
    news_item: _containers.RepeatedCompositeFieldContainer[WxMpNewsMsg.WxNewsItemDetail]
    def __init__(self, media_id: _Optional[str] = ..., url: _Optional[str] = ..., title: _Optional[str] = ..., author: _Optional[str] = ..., thumb_media_id: _Optional[str] = ..., thumb_media_url: _Optional[str] = ..., digest: _Optional[str] = ..., show_cover_pic: _Optional[int] = ..., content: _Optional[str] = ..., content_source_url: _Optional[str] = ..., is_multi: bool = ..., news_item: _Optional[_Iterable[_Union[WxMpNewsMsg.WxNewsItemDetail, _Mapping]]] = ...) -> None: ...

class WxNewsMsg(_message.Message):
    __slots__ = ("title", "description", "url", "picurl")
    TITLE_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    URL_FIELD_NUMBER: _ClassVar[int]
    PICURL_FIELD_NUMBER: _ClassVar[int]
    title: str
    description: str
    url: str
    picurl: str
    def __init__(self, title: _Optional[str] = ..., description: _Optional[str] = ..., url: _Optional[str] = ..., picurl: _Optional[str] = ...) -> None: ...

class WxCardMsg(_message.Message):
    __slots__ = ("card_id", "card_title", "brand_name", "logo_url", "card_type")
    CARD_ID_FIELD_NUMBER: _ClassVar[int]
    CARD_TITLE_FIELD_NUMBER: _ClassVar[int]
    BRAND_NAME_FIELD_NUMBER: _ClassVar[int]
    LOGO_URL_FIELD_NUMBER: _ClassVar[int]
    CARD_TYPE_FIELD_NUMBER: _ClassVar[int]
    card_id: str
    card_title: str
    brand_name: str
    logo_url: str
    card_type: str
    def __init__(self, card_id: _Optional[str] = ..., card_title: _Optional[str] = ..., brand_name: _Optional[str] = ..., logo_url: _Optional[str] = ..., card_type: _Optional[str] = ...) -> None: ...

class WxMiniProgramMsg(_message.Message):
    __slots__ = ("title", "appid", "pagepath", "thumb_media_id", "thumb_media_type", "thumb_temp", "thumb_pic_url")
    TITLE_FIELD_NUMBER: _ClassVar[int]
    APPID_FIELD_NUMBER: _ClassVar[int]
    PAGEPATH_FIELD_NUMBER: _ClassVar[int]
    THUMB_MEDIA_ID_FIELD_NUMBER: _ClassVar[int]
    THUMB_MEDIA_TYPE_FIELD_NUMBER: _ClassVar[int]
    THUMB_TEMP_FIELD_NUMBER: _ClassVar[int]
    THUMB_PIC_URL_FIELD_NUMBER: _ClassVar[int]
    title: str
    appid: str
    pagepath: str
    thumb_media_id: str
    thumb_media_type: str
    thumb_temp: int
    thumb_pic_url: str
    def __init__(self, title: _Optional[str] = ..., appid: _Optional[str] = ..., pagepath: _Optional[str] = ..., thumb_media_id: _Optional[str] = ..., thumb_media_type: _Optional[str] = ..., thumb_temp: _Optional[int] = ..., thumb_pic_url: _Optional[str] = ...) -> None: ...

class EdmContent(_message.Message):
    __slots__ = ("from_name", "subject", "type", "content", "template_id", "template_name", "template_subject", "freemarker_syntax_version", "over_length_strategy")
    class ContentType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        TEXT: _ClassVar[EdmContent.ContentType]
        RICH_TEXT: _ClassVar[EdmContent.ContentType]
        HTML: _ClassVar[EdmContent.ContentType]
        TEMPLATE: _ClassVar[EdmContent.ContentType]
    TEXT: EdmContent.ContentType
    RICH_TEXT: EdmContent.ContentType
    HTML: EdmContent.ContentType
    TEMPLATE: EdmContent.ContentType
    FROM_NAME_FIELD_NUMBER: _ClassVar[int]
    SUBJECT_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    TEMPLATE_ID_FIELD_NUMBER: _ClassVar[int]
    TEMPLATE_NAME_FIELD_NUMBER: _ClassVar[int]
    TEMPLATE_SUBJECT_FIELD_NUMBER: _ClassVar[int]
    FREEMARKER_SYNTAX_VERSION_FIELD_NUMBER: _ClassVar[int]
    OVER_LENGTH_STRATEGY_FIELD_NUMBER: _ClassVar[int]
    from_name: str
    subject: str
    type: EdmContent.ContentType
    content: str
    template_id: str
    template_name: str
    template_subject: str
    freemarker_syntax_version: int
    over_length_strategy: ContentOverLengthStrategy
    def __init__(self, from_name: _Optional[str] = ..., subject: _Optional[str] = ..., type: _Optional[_Union[EdmContent.ContentType, str]] = ..., content: _Optional[str] = ..., template_id: _Optional[str] = ..., template_name: _Optional[str] = ..., template_subject: _Optional[str] = ..., freemarker_syntax_version: _Optional[int] = ..., over_length_strategy: _Optional[_Union[ContentOverLengthStrategy, str]] = ...) -> None: ...

class LineContent(_message.Message):
    __slots__ = ("content",)
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    content: str
    def __init__(self, content: _Optional[str] = ...) -> None: ...

class RewardContent(_message.Message):
    __slots__ = ("content",)
    class ContentEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    content: _containers.ScalarMap[str, str]
    def __init__(self, content: _Optional[_Mapping[str, str]] = ...) -> None: ...
