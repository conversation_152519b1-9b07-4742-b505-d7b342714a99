from sensors import audience_pb2 as _audience_pb2
from sensors import time_range_pb2 as _time_range_pb2
from sensors import condition_pb2 as _condition_pb2
from sensors import aggregation_pb2 as _aggregation_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class CanvasDraft(_message.Message):
    __slots__ = ("cname", "desc", "active_time_range", "finished_time", "re_enter", "enable_global_control", "component_list", "component_edge_list", "do_not_disturb", "convert_target")
    CNAME_FIELD_NUMBER: _ClassVar[int]
    DESC_FIELD_NUMBER: _ClassVar[int]
    ACTIVE_TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
    FINISHED_TIME_FIELD_NUMBER: _ClassVar[int]
    RE_ENTER_FIELD_NUMBER: _ClassVar[int]
    ENABLE_GLOBAL_CONTROL_FIELD_NUMBER: _ClassVar[int]
    COMPONENT_LIST_FIELD_NUMBER: _ClassVar[int]
    COMPONENT_EDGE_LIST_FIELD_NUMBER: _ClassVar[int]
    DO_NOT_DISTURB_FIELD_NUMBER: _ClassVar[int]
    CONVERT_TARGET_FIELD_NUMBER: _ClassVar[int]
    cname: str
    desc: str
    active_time_range: _time_range_pb2.TimeRange
    finished_time: _time_range_pb2.TimePoint
    re_enter: ReEnterConfig
    enable_global_control: bool
    component_list: _containers.RepeatedCompositeFieldContainer[Component]
    component_edge_list: _containers.RepeatedCompositeFieldContainer[ComponentEdge]
    do_not_disturb: DoNotDisturbConfig
    convert_target: ConvertTargetConfig
    def __init__(self, cname: _Optional[str] = ..., desc: _Optional[str] = ..., active_time_range: _Optional[_Union[_time_range_pb2.TimeRange, _Mapping]] = ..., finished_time: _Optional[_Union[_time_range_pb2.TimePoint, _Mapping]] = ..., re_enter: _Optional[_Union[ReEnterConfig, _Mapping]] = ..., enable_global_control: bool = ..., component_list: _Optional[_Iterable[_Union[Component, _Mapping]]] = ..., component_edge_list: _Optional[_Iterable[_Union[ComponentEdge, _Mapping]]] = ..., do_not_disturb: _Optional[_Union[DoNotDisturbConfig, _Mapping]] = ..., convert_target: _Optional[_Union[ConvertTargetConfig, _Mapping]] = ...) -> None: ...

class ReEnterConfig(_message.Message):
    __slots__ = ("unlimited", "reentry_strategy", "reentry_limit")
    class ReEntryStrategy(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        ENTRY_STRATEGY_UNSPECIFIED: _ClassVar[ReEnterConfig.ReEntryStrategy]
        REENTRY_NO: _ClassVar[ReEnterConfig.ReEntryStrategy]
        REENTRY_ANY_TIME: _ClassVar[ReEnterConfig.ReEntryStrategy]
        REENTRY_AFTER_EXITING: _ClassVar[ReEnterConfig.ReEntryStrategy]
    ENTRY_STRATEGY_UNSPECIFIED: ReEnterConfig.ReEntryStrategy
    REENTRY_NO: ReEnterConfig.ReEntryStrategy
    REENTRY_ANY_TIME: ReEnterConfig.ReEntryStrategy
    REENTRY_AFTER_EXITING: ReEnterConfig.ReEntryStrategy
    UNLIMITED_FIELD_NUMBER: _ClassVar[int]
    REENTRY_STRATEGY_FIELD_NUMBER: _ClassVar[int]
    REENTRY_LIMIT_FIELD_NUMBER: _ClassVar[int]
    unlimited: bool
    reentry_strategy: ReEnterConfig.ReEntryStrategy
    reentry_limit: SlidingWindowWithLimit
    def __init__(self, unlimited: bool = ..., reentry_strategy: _Optional[_Union[ReEnterConfig.ReEntryStrategy, str]] = ..., reentry_limit: _Optional[_Union[SlidingWindowWithLimit, _Mapping]] = ...) -> None: ...

class SlidingWindowWithLimit(_message.Message):
    __slots__ = ("window", "limit")
    WINDOW_FIELD_NUMBER: _ClassVar[int]
    LIMIT_FIELD_NUMBER: _ClassVar[int]
    window: _time_range_pb2.TimeInterval
    limit: int
    def __init__(self, window: _Optional[_Union[_time_range_pb2.TimeInterval, _Mapping]] = ..., limit: _Optional[int] = ...) -> None: ...

class DoNotDisturbConfig(_message.Message):
    __slots__ = ("enable_do_not_disturb", "repeat_cycle", "unit_list", "action_strategy")
    class ActionStrategy(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        ACTION_STRATEGY_UNSPECIFIED: _ClassVar[DoNotDisturbConfig.ActionStrategy]
        DROP: _ClassVar[DoNotDisturbConfig.ActionStrategy]
        DELAY: _ClassVar[DoNotDisturbConfig.ActionStrategy]
        DROP_AND_NOT_FINISH: _ClassVar[DoNotDisturbConfig.ActionStrategy]
    ACTION_STRATEGY_UNSPECIFIED: DoNotDisturbConfig.ActionStrategy
    DROP: DoNotDisturbConfig.ActionStrategy
    DELAY: DoNotDisturbConfig.ActionStrategy
    DROP_AND_NOT_FINISH: DoNotDisturbConfig.ActionStrategy
    class RepeatCycle(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        REPEAT_CYCLE_UNSPECIFIED: _ClassVar[DoNotDisturbConfig.RepeatCycle]
        DAY: _ClassVar[DoNotDisturbConfig.RepeatCycle]
        WEEK: _ClassVar[DoNotDisturbConfig.RepeatCycle]
        MONTH: _ClassVar[DoNotDisturbConfig.RepeatCycle]
        CUSTOM: _ClassVar[DoNotDisturbConfig.RepeatCycle]
    REPEAT_CYCLE_UNSPECIFIED: DoNotDisturbConfig.RepeatCycle
    DAY: DoNotDisturbConfig.RepeatCycle
    WEEK: DoNotDisturbConfig.RepeatCycle
    MONTH: DoNotDisturbConfig.RepeatCycle
    CUSTOM: DoNotDisturbConfig.RepeatCycle
    class DoNotDisturbUnit(_message.Message):
        __slots__ = ("day", "time_range")
        class DoNotDisturbTimeRange(_message.Message):
            __slots__ = ("start_time", "end_time")
            START_TIME_FIELD_NUMBER: _ClassVar[int]
            END_TIME_FIELD_NUMBER: _ClassVar[int]
            start_time: str
            end_time: str
            def __init__(self, start_time: _Optional[str] = ..., end_time: _Optional[str] = ...) -> None: ...
        DAY_FIELD_NUMBER: _ClassVar[int]
        TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
        day: int
        time_range: DoNotDisturbConfig.DoNotDisturbUnit.DoNotDisturbTimeRange
        def __init__(self, day: _Optional[int] = ..., time_range: _Optional[_Union[DoNotDisturbConfig.DoNotDisturbUnit.DoNotDisturbTimeRange, _Mapping]] = ...) -> None: ...
    ENABLE_DO_NOT_DISTURB_FIELD_NUMBER: _ClassVar[int]
    REPEAT_CYCLE_FIELD_NUMBER: _ClassVar[int]
    UNIT_LIST_FIELD_NUMBER: _ClassVar[int]
    ACTION_STRATEGY_FIELD_NUMBER: _ClassVar[int]
    enable_do_not_disturb: bool
    repeat_cycle: DoNotDisturbConfig.RepeatCycle
    unit_list: _containers.RepeatedCompositeFieldContainer[DoNotDisturbConfig.DoNotDisturbUnit]
    action_strategy: DoNotDisturbConfig.ActionStrategy
    def __init__(self, enable_do_not_disturb: bool = ..., repeat_cycle: _Optional[_Union[DoNotDisturbConfig.RepeatCycle, str]] = ..., unit_list: _Optional[_Iterable[_Union[DoNotDisturbConfig.DoNotDisturbUnit, _Mapping]]] = ..., action_strategy: _Optional[_Union[DoNotDisturbConfig.ActionStrategy, str]] = ...) -> None: ...

class ConvertTargetConfig(_message.Message):
    __slots__ = ("convert_rule_list",)
    class ConvertRule(_message.Message):
        __slots__ = ("convert_id", "convert_name", "end_after_convert", "convert_window", "match_rule_list")
        CONVERT_ID_FIELD_NUMBER: _ClassVar[int]
        CONVERT_NAME_FIELD_NUMBER: _ClassVar[int]
        END_AFTER_CONVERT_FIELD_NUMBER: _ClassVar[int]
        CONVERT_WINDOW_FIELD_NUMBER: _ClassVar[int]
        MATCH_RULE_LIST_FIELD_NUMBER: _ClassVar[int]
        convert_id: int
        convert_name: str
        end_after_convert: bool
        convert_window: _time_range_pb2.TimeWindow
        match_rule_list: _audience_pb2.AudienceEventRule
        def __init__(self, convert_id: _Optional[int] = ..., convert_name: _Optional[str] = ..., end_after_convert: bool = ..., convert_window: _Optional[_Union[_time_range_pb2.TimeWindow, _Mapping]] = ..., match_rule_list: _Optional[_Union[_audience_pb2.AudienceEventRule, _Mapping]] = ...) -> None: ...
    CONVERT_RULE_LIST_FIELD_NUMBER: _ClassVar[int]
    convert_rule_list: _containers.RepeatedCompositeFieldContainer[ConvertTargetConfig.ConvertRule]
    def __init__(self, convert_rule_list: _Optional[_Iterable[_Union[ConvertTargetConfig.ConvertRule, _Mapping]]] = ...) -> None: ...

class ComponentEdge(_message.Message):
    __slots__ = ("style", "entry_source_node", "source_component_id", "target_component_id")
    class EdgeStyle(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        EDGE_STYLE_UNSPECIFIED: _ClassVar[ComponentEdge.EdgeStyle]
        DIRECTION: _ClassVar[ComponentEdge.EdgeStyle]
        RELATION: _ClassVar[ComponentEdge.EdgeStyle]
    EDGE_STYLE_UNSPECIFIED: ComponentEdge.EdgeStyle
    DIRECTION: ComponentEdge.EdgeStyle
    RELATION: ComponentEdge.EdgeStyle
    class EdgePassWay(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        ALL: _ClassVar[ComponentEdge.EdgePassWay]
        YES: _ClassVar[ComponentEdge.EdgePassWay]
        NO: _ClassVar[ComponentEdge.EdgePassWay]
    ALL: ComponentEdge.EdgePassWay
    YES: ComponentEdge.EdgePassWay
    NO: ComponentEdge.EdgePassWay
    STYLE_FIELD_NUMBER: _ClassVar[int]
    ENTRY_SOURCE_NODE_FIELD_NUMBER: _ClassVar[int]
    SOURCE_COMPONENT_ID_FIELD_NUMBER: _ClassVar[int]
    TARGET_COMPONENT_ID_FIELD_NUMBER: _ClassVar[int]
    style: ComponentEdge.EdgeStyle
    entry_source_node: ComponentEdge.EdgePassWay
    source_component_id: int
    target_component_id: int
    def __init__(self, style: _Optional[_Union[ComponentEdge.EdgeStyle, str]] = ..., entry_source_node: _Optional[_Union[ComponentEdge.EdgePassWay, str]] = ..., source_component_id: _Optional[int] = ..., target_component_id: _Optional[int] = ...) -> None: ...

class Component(_message.Message):
    __slots__ = ("component_id", "cname", "component_type", "entry_component", "condition_component", "action_component", "control_component")
    class ComponentType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        COMPONENT_TYPE_UNSPECIFIED: _ClassVar[Component.ComponentType]
        ENTRY: _ClassVar[Component.ComponentType]
        CONDITION: _ClassVar[Component.ComponentType]
        ACTION: _ClassVar[Component.ComponentType]
        CONTROL: _ClassVar[Component.ComponentType]
    COMPONENT_TYPE_UNSPECIFIED: Component.ComponentType
    ENTRY: Component.ComponentType
    CONDITION: Component.ComponentType
    ACTION: Component.ComponentType
    CONTROL: Component.ComponentType
    COMPONENT_ID_FIELD_NUMBER: _ClassVar[int]
    CNAME_FIELD_NUMBER: _ClassVar[int]
    COMPONENT_TYPE_FIELD_NUMBER: _ClassVar[int]
    ENTRY_COMPONENT_FIELD_NUMBER: _ClassVar[int]
    CONDITION_COMPONENT_FIELD_NUMBER: _ClassVar[int]
    ACTION_COMPONENT_FIELD_NUMBER: _ClassVar[int]
    CONTROL_COMPONENT_FIELD_NUMBER: _ClassVar[int]
    component_id: int
    cname: str
    component_type: Component.ComponentType
    entry_component: EntryComponent
    condition_component: ConditionComponent
    action_component: ActionComponent
    control_component: ControlComponent
    def __init__(self, component_id: _Optional[int] = ..., cname: _Optional[str] = ..., component_type: _Optional[_Union[Component.ComponentType, str]] = ..., entry_component: _Optional[_Union[EntryComponent, _Mapping]] = ..., condition_component: _Optional[_Union[ConditionComponent, _Mapping]] = ..., action_component: _Optional[_Union[ActionComponent, _Mapping]] = ..., control_component: _Optional[_Union[ControlComponent, _Mapping]] = ...) -> None: ...

class EntryComponent(_message.Message):
    __slots__ = ("entry_type", "audience_rule", "do_entry_rule", "not_do_entry_rule")
    class EntryComponentType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        ENTRY_TYPE_UNSPECIFIED: _ClassVar[EntryComponent.EntryComponentType]
        FIXED_TIME: _ClassVar[EntryComponent.EntryComponentType]
        ROUTINE: _ClassVar[EntryComponent.EntryComponentType]
        TRIGGER_ONLY_A: _ClassVar[EntryComponent.EntryComponentType]
        TRIGGER_A_NOT_B: _ClassVar[EntryComponent.EntryComponentType]
    ENTRY_TYPE_UNSPECIFIED: EntryComponent.EntryComponentType
    FIXED_TIME: EntryComponent.EntryComponentType
    ROUTINE: EntryComponent.EntryComponentType
    TRIGGER_ONLY_A: EntryComponent.EntryComponentType
    TRIGGER_A_NOT_B: EntryComponent.EntryComponentType
    ENTRY_TYPE_FIELD_NUMBER: _ClassVar[int]
    AUDIENCE_RULE_FIELD_NUMBER: _ClassVar[int]
    DO_ENTRY_RULE_FIELD_NUMBER: _ClassVar[int]
    NOT_DO_ENTRY_RULE_FIELD_NUMBER: _ClassVar[int]
    entry_type: EntryComponent.EntryComponentType
    audience_rule: AudienceComponentRule
    do_entry_rule: EventComponentRule
    not_do_entry_rule: EventComponentRule
    def __init__(self, entry_type: _Optional[_Union[EntryComponent.EntryComponentType, str]] = ..., audience_rule: _Optional[_Union[AudienceComponentRule, _Mapping]] = ..., do_entry_rule: _Optional[_Union[EventComponentRule, _Mapping]] = ..., not_do_entry_rule: _Optional[_Union[EventComponentRule, _Mapping]] = ...) -> None: ...

class AudienceComponentRule(_message.Message):
    __slots__ = ("audience_type", "fixed_time", "cron", "audience_rule")
    class AudienceType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        AUDIENCE_TYPE_UNSPECIFIED: _ClassVar[AudienceComponentRule.AudienceType]
        AUDIENCE_ONETIME: _ClassVar[AudienceComponentRule.AudienceType]
        AUDIENCE_ROUTINE: _ClassVar[AudienceComponentRule.AudienceType]
    AUDIENCE_TYPE_UNSPECIFIED: AudienceComponentRule.AudienceType
    AUDIENCE_ONETIME: AudienceComponentRule.AudienceType
    AUDIENCE_ROUTINE: AudienceComponentRule.AudienceType
    AUDIENCE_TYPE_FIELD_NUMBER: _ClassVar[int]
    FIXED_TIME_FIELD_NUMBER: _ClassVar[int]
    CRON_FIELD_NUMBER: _ClassVar[int]
    AUDIENCE_RULE_FIELD_NUMBER: _ClassVar[int]
    audience_type: AudienceComponentRule.AudienceType
    fixed_time: _time_range_pb2.TimePoint
    cron: str
    audience_rule: _audience_pb2.AudienceRule
    def __init__(self, audience_type: _Optional[_Union[AudienceComponentRule.AudienceType, str]] = ..., fixed_time: _Optional[_Union[_time_range_pb2.TimePoint, _Mapping]] = ..., cron: _Optional[str] = ..., audience_rule: _Optional[_Union[_audience_pb2.AudienceRule, _Mapping]] = ...) -> None: ...

class EventComponentRule(_message.Message):
    __slots__ = ("match_window", "event_rule")
    MATCH_WINDOW_FIELD_NUMBER: _ClassVar[int]
    EVENT_RULE_FIELD_NUMBER: _ClassVar[int]
    match_window: _time_range_pb2.TimeWindow
    event_rule: _audience_pb2.AudienceEventRule
    def __init__(self, match_window: _Optional[_Union[_time_range_pb2.TimeWindow, _Mapping]] = ..., event_rule: _Optional[_Union[_audience_pb2.AudienceEventRule, _Mapping]] = ...) -> None: ...

class EventPropertyRule(_message.Message):
    __slots__ = ("event_property_params",)
    class EventPropertyParam(_message.Message):
        __slots__ = ("id", "event_name", "event_property_field")
        ID_FIELD_NUMBER: _ClassVar[int]
        EVENT_NAME_FIELD_NUMBER: _ClassVar[int]
        EVENT_PROPERTY_FIELD_FIELD_NUMBER: _ClassVar[int]
        id: int
        event_name: str
        event_property_field: str
        def __init__(self, id: _Optional[int] = ..., event_name: _Optional[str] = ..., event_property_field: _Optional[str] = ...) -> None: ...
    EVENT_PROPERTY_PARAMS_FIELD_NUMBER: _ClassVar[int]
    event_property_params: _containers.RepeatedCompositeFieldContainer[EventPropertyRule.EventPropertyParam]
    def __init__(self, event_property_params: _Optional[_Iterable[_Union[EventPropertyRule.EventPropertyParam, _Mapping]]] = ...) -> None: ...

class ConditionComponent(_message.Message):
    __slots__ = ("condition_type", "audience_rule", "event_rule", "event_property_rule", "enable_send_event")
    class ConditionType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        CONDITION_TYPE_UNSPECIFIED: _ClassVar[ConditionComponent.ConditionType]
        CONDITION_EVENT: _ClassVar[ConditionComponent.ConditionType]
        CONDITION_EVENT_PROPERTIES: _ClassVar[ConditionComponent.ConditionType]
        CONDITION_AUDIENCE_BATCH: _ClassVar[ConditionComponent.ConditionType]
        CONDITION_AUDIENCE_STREAM: _ClassVar[ConditionComponent.ConditionType]
    CONDITION_TYPE_UNSPECIFIED: ConditionComponent.ConditionType
    CONDITION_EVENT: ConditionComponent.ConditionType
    CONDITION_EVENT_PROPERTIES: ConditionComponent.ConditionType
    CONDITION_AUDIENCE_BATCH: ConditionComponent.ConditionType
    CONDITION_AUDIENCE_STREAM: ConditionComponent.ConditionType
    CONDITION_TYPE_FIELD_NUMBER: _ClassVar[int]
    AUDIENCE_RULE_FIELD_NUMBER: _ClassVar[int]
    EVENT_RULE_FIELD_NUMBER: _ClassVar[int]
    EVENT_PROPERTY_RULE_FIELD_NUMBER: _ClassVar[int]
    ENABLE_SEND_EVENT_FIELD_NUMBER: _ClassVar[int]
    condition_type: ConditionComponent.ConditionType
    audience_rule: AudienceComponentRule
    event_rule: EventComponentRule
    event_property_rule: EventPropertyRule
    enable_send_event: bool
    def __init__(self, condition_type: _Optional[_Union[ConditionComponent.ConditionType, str]] = ..., audience_rule: _Optional[_Union[AudienceComponentRule, _Mapping]] = ..., event_rule: _Optional[_Union[EventComponentRule, _Mapping]] = ..., event_property_rule: _Optional[_Union[EventPropertyRule, _Mapping]] = ..., enable_send_event: bool = ...) -> None: ...

class ActionComponent(_message.Message):
    __slots__ = ("action_desc",)
    ACTION_DESC_FIELD_NUMBER: _ClassVar[int]
    action_desc: str
    def __init__(self, action_desc: _Optional[str] = ...) -> None: ...

class ControlComponent(_message.Message):
    __slots__ = ("control_type", "splitter", "percent_branch", "time_branch", "delay", "enable_send_event")
    class ControlType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        CONTROL_TYPE_UNSPECIFIED: _ClassVar[ControlComponent.ControlType]
        AUDIENCE_SPLITTER: _ClassVar[ControlComponent.ControlType]
        EVENT_SPLITTER: _ClassVar[ControlComponent.ControlType]
        EVENT_PROPERTIES_SPLITTER: _ClassVar[ControlComponent.ControlType]
        PERCENT_SPLITTER: _ClassVar[ControlComponent.ControlType]
        PERCENT_SPLITTER_BRANCH: _ClassVar[ControlComponent.ControlType]
        TIME_SPLITTER: _ClassVar[ControlComponent.ControlType]
        TIME_SPLITTER_BRANCH: _ClassVar[ControlComponent.ControlType]
        ELSE_BRANCH: _ClassVar[ControlComponent.ControlType]
        DELAY: _ClassVar[ControlComponent.ControlType]
        END: _ClassVar[ControlComponent.ControlType]
    CONTROL_TYPE_UNSPECIFIED: ControlComponent.ControlType
    AUDIENCE_SPLITTER: ControlComponent.ControlType
    EVENT_SPLITTER: ControlComponent.ControlType
    EVENT_PROPERTIES_SPLITTER: ControlComponent.ControlType
    PERCENT_SPLITTER: ControlComponent.ControlType
    PERCENT_SPLITTER_BRANCH: ControlComponent.ControlType
    TIME_SPLITTER: ControlComponent.ControlType
    TIME_SPLITTER_BRANCH: ControlComponent.ControlType
    ELSE_BRANCH: ControlComponent.ControlType
    DELAY: ControlComponent.ControlType
    END: ControlComponent.ControlType
    CONTROL_TYPE_FIELD_NUMBER: _ClassVar[int]
    SPLITTER_FIELD_NUMBER: _ClassVar[int]
    PERCENT_BRANCH_FIELD_NUMBER: _ClassVar[int]
    TIME_BRANCH_FIELD_NUMBER: _ClassVar[int]
    DELAY_FIELD_NUMBER: _ClassVar[int]
    ENABLE_SEND_EVENT_FIELD_NUMBER: _ClassVar[int]
    control_type: ControlComponent.ControlType
    splitter: SplitterControlRule
    percent_branch: PercentSplitterBranchRule
    time_branch: TimeSplitterBranchRule
    delay: DelayControlRule
    enable_send_event: bool
    def __init__(self, control_type: _Optional[_Union[ControlComponent.ControlType, str]] = ..., splitter: _Optional[_Union[SplitterControlRule, _Mapping]] = ..., percent_branch: _Optional[_Union[PercentSplitterBranchRule, _Mapping]] = ..., time_branch: _Optional[_Union[TimeSplitterBranchRule, _Mapping]] = ..., delay: _Optional[_Union[DelayControlRule, _Mapping]] = ..., enable_send_event: bool = ...) -> None: ...

class SplitterControlRule(_message.Message):
    __slots__ = ("ab_enable",)
    AB_ENABLE_FIELD_NUMBER: _ClassVar[int]
    ab_enable: bool
    def __init__(self, ab_enable: bool = ...) -> None: ...

class PercentSplitterBranchRule(_message.Message):
    __slots__ = ("ab_strategy_type", "percent")
    class AbStrategyType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        UNSPECIFIED: _ClassVar[PercentSplitterBranchRule.AbStrategyType]
        CONTROL: _ClassVar[PercentSplitterBranchRule.AbStrategyType]
        EXPERIMENT: _ClassVar[PercentSplitterBranchRule.AbStrategyType]
    UNSPECIFIED: PercentSplitterBranchRule.AbStrategyType
    CONTROL: PercentSplitterBranchRule.AbStrategyType
    EXPERIMENT: PercentSplitterBranchRule.AbStrategyType
    AB_STRATEGY_TYPE_FIELD_NUMBER: _ClassVar[int]
    PERCENT_FIELD_NUMBER: _ClassVar[int]
    ab_strategy_type: PercentSplitterBranchRule.AbStrategyType
    percent: int
    def __init__(self, ab_strategy_type: _Optional[_Union[PercentSplitterBranchRule.AbStrategyType, str]] = ..., percent: _Optional[int] = ...) -> None: ...

class TimeSplitterBranchRule(_message.Message):
    __slots__ = ("time_splitter_type", "start_time", "end_time", "days")
    class TimeSplitterType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        STATIC: _ClassVar[TimeSplitterBranchRule.TimeSplitterType]
        PERIOD_DAY: _ClassVar[TimeSplitterBranchRule.TimeSplitterType]
        PERIOD_WEEK: _ClassVar[TimeSplitterBranchRule.TimeSplitterType]
        PERIOD_MONTH: _ClassVar[TimeSplitterBranchRule.TimeSplitterType]
    STATIC: TimeSplitterBranchRule.TimeSplitterType
    PERIOD_DAY: TimeSplitterBranchRule.TimeSplitterType
    PERIOD_WEEK: TimeSplitterBranchRule.TimeSplitterType
    PERIOD_MONTH: TimeSplitterBranchRule.TimeSplitterType
    TIME_SPLITTER_TYPE_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    DAYS_FIELD_NUMBER: _ClassVar[int]
    time_splitter_type: TimeSplitterBranchRule.TimeSplitterType
    start_time: str
    end_time: str
    days: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, time_splitter_type: _Optional[_Union[TimeSplitterBranchRule.TimeSplitterType, str]] = ..., start_time: _Optional[str] = ..., end_time: _Optional[str] = ..., days: _Optional[_Iterable[int]] = ...) -> None: ...

class DelayControlRule(_message.Message):
    __slots__ = ("delay_config", "delay_end_action")
    class DelayEndAction(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        DELAY_END_ACTION_UNSPECIFIED: _ClassVar[DelayControlRule.DelayEndAction]
        WAIT_UNTIL_NEXT_CYCLE: _ClassVar[DelayControlRule.DelayEndAction]
        EXIT_JOURNEY: _ClassVar[DelayControlRule.DelayEndAction]
        DIRECT_TO_NEXT_NODE: _ClassVar[DelayControlRule.DelayEndAction]
    DELAY_END_ACTION_UNSPECIFIED: DelayControlRule.DelayEndAction
    WAIT_UNTIL_NEXT_CYCLE: DelayControlRule.DelayEndAction
    EXIT_JOURNEY: DelayControlRule.DelayEndAction
    DIRECT_TO_NEXT_NODE: DelayControlRule.DelayEndAction
    DELAY_CONFIG_FIELD_NUMBER: _ClassVar[int]
    DELAY_END_ACTION_FIELD_NUMBER: _ClassVar[int]
    delay_config: _time_range_pb2.TimePoint
    delay_end_action: DelayControlRule.DelayEndAction
    def __init__(self, delay_config: _Optional[_Union[_time_range_pb2.TimePoint, _Mapping]] = ..., delay_end_action: _Optional[_Union[DelayControlRule.DelayEndAction, str]] = ...) -> None: ...
