import stream_event_pb2 as _stream_event_pb2
import chat_session_pb2 as _chat_session_pb2
import canvas_draft_pb2 as _canvas_draft_pb2
import knowledge_base_pb2 as _knowledge_base_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class CanvasDesignStructure(_message.Message):
    __slots__ = ("component_list", "component_edge_list")
    class Component(_message.Message):
        __slots__ = ("id", "cname", "description", "type", "sub_type")
        ID_FIELD_NUMBER: _ClassVar[int]
        CNAME_FIELD_NUMBER: _ClassVar[int]
        DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
        TYPE_FIELD_NUMBER: _ClassVar[int]
        SUB_TYPE_FIELD_NUMBER: _ClassVar[int]
        id: str
        cname: str
        description: str
        type: str
        sub_type: str
        def __init__(self, id: _Optional[str] = ..., cname: _Optional[str] = ..., description: _Optional[str] = ..., type: _Optional[str] = ..., sub_type: _Optional[str] = ...) -> None: ...
    class Edge(_message.Message):
        __slots__ = ("entry_source_node", "source_component_id", "target_component_id")
        ENTRY_SOURCE_NODE_FIELD_NUMBER: _ClassVar[int]
        SOURCE_COMPONENT_ID_FIELD_NUMBER: _ClassVar[int]
        TARGET_COMPONENT_ID_FIELD_NUMBER: _ClassVar[int]
        entry_source_node: str
        source_component_id: str
        target_component_id: str
        def __init__(self, entry_source_node: _Optional[str] = ..., source_component_id: _Optional[str] = ..., target_component_id: _Optional[str] = ...) -> None: ...
    COMPONENT_LIST_FIELD_NUMBER: _ClassVar[int]
    COMPONENT_EDGE_LIST_FIELD_NUMBER: _ClassVar[int]
    component_list: _containers.RepeatedCompositeFieldContainer[CanvasDesignStructure.Component]
    component_edge_list: _containers.RepeatedCompositeFieldContainer[CanvasDesignStructure.Edge]
    def __init__(self, component_list: _Optional[_Iterable[_Union[CanvasDesignStructure.Component, _Mapping]]] = ..., component_edge_list: _Optional[_Iterable[_Union[CanvasDesignStructure.Edge, _Mapping]]] = ...) -> None: ...

class CanvasDesign(_message.Message):
    __slots__ = ("idea", "audience", "timing", "channel", "target", "structure")
    IDEA_FIELD_NUMBER: _ClassVar[int]
    AUDIENCE_FIELD_NUMBER: _ClassVar[int]
    TIMING_FIELD_NUMBER: _ClassVar[int]
    CHANNEL_FIELD_NUMBER: _ClassVar[int]
    TARGET_FIELD_NUMBER: _ClassVar[int]
    STRUCTURE_FIELD_NUMBER: _ClassVar[int]
    idea: str
    audience: str
    timing: str
    channel: str
    target: str
    structure: CanvasDesignStructure
    def __init__(self, idea: _Optional[str] = ..., audience: _Optional[str] = ..., timing: _Optional[str] = ..., channel: _Optional[str] = ..., target: _Optional[str] = ..., structure: _Optional[_Union[CanvasDesignStructure, _Mapping]] = ...) -> None: ...

class CanvasDesignSuggestion(_message.Message):
    __slots__ = ("design", "component_id", "suggestion", "mode")
    class SuggestionMode(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        FULLY_GENERATED: _ClassVar[CanvasDesignSuggestion.SuggestionMode]
        PRESERVE_STRUCTURE: _ClassVar[CanvasDesignSuggestion.SuggestionMode]
    FULLY_GENERATED: CanvasDesignSuggestion.SuggestionMode
    PRESERVE_STRUCTURE: CanvasDesignSuggestion.SuggestionMode
    DESIGN_FIELD_NUMBER: _ClassVar[int]
    COMPONENT_ID_FIELD_NUMBER: _ClassVar[int]
    SUGGESTION_FIELD_NUMBER: _ClassVar[int]
    MODE_FIELD_NUMBER: _ClassVar[int]
    design: CanvasDesign
    component_id: _containers.RepeatedScalarFieldContainer[int]
    suggestion: str
    mode: CanvasDesignSuggestion.SuggestionMode
    def __init__(self, design: _Optional[_Union[CanvasDesign, _Mapping]] = ..., component_id: _Optional[_Iterable[int]] = ..., suggestion: _Optional[str] = ..., mode: _Optional[_Union[CanvasDesignSuggestion.SuggestionMode, str]] = ...) -> None: ...

class ChatSessionCanvasMessage(_message.Message):
    __slots__ = ("generator", "type", "text", "design", "suggest", "canvas")
    class MessageGenerator(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        SYSTEM: _ClassVar[ChatSessionCanvasMessage.MessageGenerator]
        USER: _ClassVar[ChatSessionCanvasMessage.MessageGenerator]
        BOT: _ClassVar[ChatSessionCanvasMessage.MessageGenerator]
    SYSTEM: ChatSessionCanvasMessage.MessageGenerator
    USER: ChatSessionCanvasMessage.MessageGenerator
    BOT: ChatSessionCanvasMessage.MessageGenerator
    class MessageType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        TEXT: _ClassVar[ChatSessionCanvasMessage.MessageType]
        DESIGN: _ClassVar[ChatSessionCanvasMessage.MessageType]
        SUGGEST: _ClassVar[ChatSessionCanvasMessage.MessageType]
        CANVAS: _ClassVar[ChatSessionCanvasMessage.MessageType]
        THINKING: _ClassVar[ChatSessionCanvasMessage.MessageType]
        MARKDOWN: _ClassVar[ChatSessionCanvasMessage.MessageType]
    TEXT: ChatSessionCanvasMessage.MessageType
    DESIGN: ChatSessionCanvasMessage.MessageType
    SUGGEST: ChatSessionCanvasMessage.MessageType
    CANVAS: ChatSessionCanvasMessage.MessageType
    THINKING: ChatSessionCanvasMessage.MessageType
    MARKDOWN: ChatSessionCanvasMessage.MessageType
    GENERATOR_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    TEXT_FIELD_NUMBER: _ClassVar[int]
    DESIGN_FIELD_NUMBER: _ClassVar[int]
    SUGGEST_FIELD_NUMBER: _ClassVar[int]
    CANVAS_FIELD_NUMBER: _ClassVar[int]
    generator: ChatSessionCanvasMessage.MessageGenerator
    type: ChatSessionCanvasMessage.MessageType
    text: str
    design: _containers.RepeatedCompositeFieldContainer[CanvasDesign]
    suggest: CanvasDesignSuggestion
    canvas: _canvas_draft_pb2.CanvasDraft
    def __init__(self, generator: _Optional[_Union[ChatSessionCanvasMessage.MessageGenerator, str]] = ..., type: _Optional[_Union[ChatSessionCanvasMessage.MessageType, str]] = ..., text: _Optional[str] = ..., design: _Optional[_Iterable[_Union[CanvasDesign, _Mapping]]] = ..., suggest: _Optional[_Union[CanvasDesignSuggestion, _Mapping]] = ..., canvas: _Optional[_Union[_canvas_draft_pb2.CanvasDraft, _Mapping]] = ...) -> None: ...

class GeneralCanvasRequest(_message.Message):
    __slots__ = ("chat_session_info", "knowledge_base_infos", "messages")
    CHAT_SESSION_INFO_FIELD_NUMBER: _ClassVar[int]
    KNOWLEDGE_BASE_INFOS_FIELD_NUMBER: _ClassVar[int]
    MESSAGES_FIELD_NUMBER: _ClassVar[int]
    chat_session_info: _chat_session_pb2.ChatSessionInfo
    knowledge_base_infos: _containers.RepeatedCompositeFieldContainer[_knowledge_base_pb2.KnowledgeBaseInfo]
    messages: _containers.RepeatedCompositeFieldContainer[ChatSessionCanvasMessage]
    def __init__(self, chat_session_info: _Optional[_Union[_chat_session_pb2.ChatSessionInfo, _Mapping]] = ..., knowledge_base_infos: _Optional[_Iterable[_Union[_knowledge_base_pb2.KnowledgeBaseInfo, _Mapping]]] = ..., messages: _Optional[_Iterable[_Union[ChatSessionCanvasMessage, _Mapping]]] = ...) -> None: ...
