from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class AnalyzeUrlRequest(_message.Message):
    __slots__ = ("urls",)
    URLS_FIELD_NUMBER: _ClassVar[int]
    urls: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, urls: _Optional[_Iterable[str]] = ...) -> None: ...

class AnalyzeUrlResponse(_message.Message):
    __slots__ = ("urls",)
    class AnalyzeUrl(_message.Message):
        __slots__ = ("url", "name", "valid")
        URL_FIELD_NUMBER: _ClassVar[int]
        NAME_FIELD_NUMBER: _ClassVar[int]
        VALID_FIELD_NUMBER: _ClassVar[int]
        url: str
        name: str
        valid: bool
        def __init__(self, url: _Optional[str] = ..., name: _Optional[str] = ..., valid: bool = ...) -> None: ...
    URLS_FIELD_NUMBER: _ClassVar[int]
    urls: _containers.RepeatedCompositeFieldContainer[AnalyzeUrlResponse.AnalyzeUrl]
    def __init__(self, urls: _Optional[_Iterable[_Union[AnalyzeUrlResponse.AnalyzeUrl, _Mapping]]] = ...) -> None: ...

class CrawlUrl(_message.Message):
    __slots__ = ("url", "crawl_type", "crawl_status")
    class CrawlType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        SINGLE_PAGE: _ClassVar[CrawlUrl.CrawlType]
        RECURSIVE: _ClassVar[CrawlUrl.CrawlType]
    SINGLE_PAGE: CrawlUrl.CrawlType
    RECURSIVE: CrawlUrl.CrawlType
    class CrawlStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        PROCESSING: _ClassVar[CrawlUrl.CrawlStatus]
        FAILED: _ClassVar[CrawlUrl.CrawlStatus]
        READY: _ClassVar[CrawlUrl.CrawlStatus]
    PROCESSING: CrawlUrl.CrawlStatus
    FAILED: CrawlUrl.CrawlStatus
    READY: CrawlUrl.CrawlStatus
    URL_FIELD_NUMBER: _ClassVar[int]
    CRAWL_TYPE_FIELD_NUMBER: _ClassVar[int]
    CRAWL_STATUS_FIELD_NUMBER: _ClassVar[int]
    url: str
    crawl_type: CrawlUrl.CrawlType
    crawl_status: CrawlUrl.CrawlStatus
    def __init__(self, url: _Optional[str] = ..., crawl_type: _Optional[_Union[CrawlUrl.CrawlType, str]] = ..., crawl_status: _Optional[_Union[CrawlUrl.CrawlStatus, str]] = ...) -> None: ...

class CrawlUrlRequest(_message.Message):
    __slots__ = ("urls",)
    URLS_FIELD_NUMBER: _ClassVar[int]
    urls: _containers.RepeatedCompositeFieldContainer[CrawlUrl]
    def __init__(self, urls: _Optional[_Iterable[_Union[CrawlUrl, _Mapping]]] = ...) -> None: ...

class CrawlUrlResponse(_message.Message):
    __slots__ = ("urls",)
    URLS_FIELD_NUMBER: _ClassVar[int]
    urls: _containers.RepeatedCompositeFieldContainer[CrawlUrl]
    def __init__(self, urls: _Optional[_Iterable[_Union[CrawlUrl, _Mapping]]] = ...) -> None: ...
