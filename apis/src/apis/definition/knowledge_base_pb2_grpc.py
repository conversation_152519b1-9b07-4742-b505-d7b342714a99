# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import knowledge_base_pb2 as knowledge__base__pb2


class KnowledgeBaseServiceStub(object):
    """{_description_} 知识库服务"""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.BuildKnowledgeBase = channel.unary_unary(
            "/sensorsdata.focus.v1.KnowledgeBaseService/BuildKnowledgeBase",
            request_serializer=knowledge__base__pb2.BuildKnowledgeBaseRequest.SerializeToString,
            response_deserializer=knowledge__base__pb2.KnowledgeBaseResponse.FromString,
        )
        self.UpdateKnowledgeBase = channel.unary_unary(
            "/sensorsdata.focus.v1.KnowledgeBaseService/UpdateKnowledgeBase",
            request_serializer=knowledge__base__pb2.BuildKnowledgeBaseRequest.SerializeToString,
            response_deserializer=knowledge__base__pb2.KnowledgeBaseResponse.FromString,
        )
        self.UploadKnowledgeBaseFile = channel.stream_unary(
            "/sensorsdata.focus.v1.KnowledgeBaseService/UploadKnowledgeBaseFile",
            request_serializer=knowledge__base__pb2.UploadKnowledgeBaseFileRequest.SerializeToString,
            response_deserializer=knowledge__base__pb2.KnowledgeBaseResponse.FromString,
        )
        self.AddKnowledgeBaseResource = channel.unary_unary(
            "/sensorsdata.focus.v1.KnowledgeBaseService/AddKnowledgeBaseResource",
            request_serializer=knowledge__base__pb2.OperateKnowledgeBaseResourceRequest.SerializeToString,
            response_deserializer=knowledge__base__pb2.KnowledgeBaseResponse.FromString,
        )
        self.DeleteKnowledgeBaseResource = channel.unary_unary(
            "/sensorsdata.focus.v1.KnowledgeBaseService/DeleteKnowledgeBaseResource",
            request_serializer=knowledge__base__pb2.OperateKnowledgeBaseResourceRequest.SerializeToString,
            response_deserializer=knowledge__base__pb2.KnowledgeBaseResponse.FromString,
        )
        self.DeleteKnowledgeBase = channel.unary_unary(
            "/sensorsdata.focus.v1.KnowledgeBaseService/DeleteKnowledgeBase",
            request_serializer=knowledge__base__pb2.DeleteKnowledgeBaseRequest.SerializeToString,
            response_deserializer=knowledge__base__pb2.KnowledgeBaseResponse.FromString,
        )
        self.QueryKnowledgeBaseStatus = channel.unary_unary(
            "/sensorsdata.focus.v1.KnowledgeBaseService/QueryKnowledgeBaseStatus",
            request_serializer=knowledge__base__pb2.QueryKnowledgeBaseRequest.SerializeToString,
            response_deserializer=knowledge__base__pb2.KnowledgeBaseResponse.FromString,
        )


class KnowledgeBaseServiceServicer(object):
    """{_description_} 知识库服务"""

    def BuildKnowledgeBase(self, request, context):
        """{_summary_} 构建知识库"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def UpdateKnowledgeBase(self, request, context):
        """{_summary_} 更新知识库"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def UploadKnowledgeBaseFile(self, request_iterator, context):
        """{_summary_} 上传知识库文件"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def AddKnowledgeBaseResource(self, request, context):
        """{_summary_} 增加知识库资源"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def DeleteKnowledgeBaseResource(self, request, context):
        """{_summary_} 删除知识库资源"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def DeleteKnowledgeBase(self, request, context):
        """{_summary_} 删除知识库 TODO 暂不实现"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def QueryKnowledgeBaseStatus(self, request, context):
        """{_summary_} 查询知识库状态"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")


def add_KnowledgeBaseServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
        "BuildKnowledgeBase": grpc.unary_unary_rpc_method_handler(
            servicer.BuildKnowledgeBase,
            request_deserializer=knowledge__base__pb2.BuildKnowledgeBaseRequest.FromString,
            response_serializer=knowledge__base__pb2.KnowledgeBaseResponse.SerializeToString,
        ),
        "UpdateKnowledgeBase": grpc.unary_unary_rpc_method_handler(
            servicer.UpdateKnowledgeBase,
            request_deserializer=knowledge__base__pb2.BuildKnowledgeBaseRequest.FromString,
            response_serializer=knowledge__base__pb2.KnowledgeBaseResponse.SerializeToString,
        ),
        "UploadKnowledgeBaseFile": grpc.stream_unary_rpc_method_handler(
            servicer.UploadKnowledgeBaseFile,
            request_deserializer=knowledge__base__pb2.UploadKnowledgeBaseFileRequest.FromString,
            response_serializer=knowledge__base__pb2.KnowledgeBaseResponse.SerializeToString,
        ),
        "AddKnowledgeBaseResource": grpc.unary_unary_rpc_method_handler(
            servicer.AddKnowledgeBaseResource,
            request_deserializer=knowledge__base__pb2.OperateKnowledgeBaseResourceRequest.FromString,
            response_serializer=knowledge__base__pb2.KnowledgeBaseResponse.SerializeToString,
        ),
        "DeleteKnowledgeBaseResource": grpc.unary_unary_rpc_method_handler(
            servicer.DeleteKnowledgeBaseResource,
            request_deserializer=knowledge__base__pb2.OperateKnowledgeBaseResourceRequest.FromString,
            response_serializer=knowledge__base__pb2.KnowledgeBaseResponse.SerializeToString,
        ),
        "DeleteKnowledgeBase": grpc.unary_unary_rpc_method_handler(
            servicer.DeleteKnowledgeBase,
            request_deserializer=knowledge__base__pb2.DeleteKnowledgeBaseRequest.FromString,
            response_serializer=knowledge__base__pb2.KnowledgeBaseResponse.SerializeToString,
        ),
        "QueryKnowledgeBaseStatus": grpc.unary_unary_rpc_method_handler(
            servicer.QueryKnowledgeBaseStatus,
            request_deserializer=knowledge__base__pb2.QueryKnowledgeBaseRequest.FromString,
            response_serializer=knowledge__base__pb2.KnowledgeBaseResponse.SerializeToString,
        ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
        "sensorsdata.focus.v1.KnowledgeBaseService", rpc_method_handlers
    )
    server.add_generic_rpc_handlers((generic_handler,))


# This class is part of an EXPERIMENTAL API.
class KnowledgeBaseService(object):
    """{_description_} 知识库服务"""

    @staticmethod
    def BuildKnowledgeBase(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/sensorsdata.focus.v1.KnowledgeBaseService/BuildKnowledgeBase",
            knowledge__base__pb2.BuildKnowledgeBaseRequest.SerializeToString,
            knowledge__base__pb2.KnowledgeBaseResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def UpdateKnowledgeBase(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/sensorsdata.focus.v1.KnowledgeBaseService/UpdateKnowledgeBase",
            knowledge__base__pb2.BuildKnowledgeBaseRequest.SerializeToString,
            knowledge__base__pb2.KnowledgeBaseResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def UploadKnowledgeBaseFile(
        request_iterator,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.stream_unary(
            request_iterator,
            target,
            "/sensorsdata.focus.v1.KnowledgeBaseService/UploadKnowledgeBaseFile",
            knowledge__base__pb2.UploadKnowledgeBaseFileRequest.SerializeToString,
            knowledge__base__pb2.KnowledgeBaseResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def AddKnowledgeBaseResource(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/sensorsdata.focus.v1.KnowledgeBaseService/AddKnowledgeBaseResource",
            knowledge__base__pb2.OperateKnowledgeBaseResourceRequest.SerializeToString,
            knowledge__base__pb2.KnowledgeBaseResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def DeleteKnowledgeBaseResource(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/sensorsdata.focus.v1.KnowledgeBaseService/DeleteKnowledgeBaseResource",
            knowledge__base__pb2.OperateKnowledgeBaseResourceRequest.SerializeToString,
            knowledge__base__pb2.KnowledgeBaseResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def DeleteKnowledgeBase(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/sensorsdata.focus.v1.KnowledgeBaseService/DeleteKnowledgeBase",
            knowledge__base__pb2.DeleteKnowledgeBaseRequest.SerializeToString,
            knowledge__base__pb2.KnowledgeBaseResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def QueryKnowledgeBaseStatus(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/sensorsdata.focus.v1.KnowledgeBaseService/QueryKnowledgeBaseStatus",
            knowledge__base__pb2.QueryKnowledgeBaseRequest.SerializeToString,
            knowledge__base__pb2.KnowledgeBaseResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )
