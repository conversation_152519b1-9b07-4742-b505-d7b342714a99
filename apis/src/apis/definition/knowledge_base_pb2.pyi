from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class KnowledgeBaseStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    WAITING: _ClassVar[KnowledgeBaseStatus]
    BUILDING: _ClassVar[KnowledgeBaseStatus]
    FAILED: _ClassVar[KnowledgeBaseStatus]
    READY: _ClassVar[KnowledgeBaseStatus]
    EMPTY: _ClassVar[KnowledgeBaseStatus]
WAITING: KnowledgeBaseStatus
BUILDING: KnowledgeBaseStatus
FAILED: KnowledgeBaseStatus
READY: KnowledgeBaseStatus
EMPTY: KnowledgeBaseStatus

class TenantProjectInfo(_message.Message):
    __slots__ = ("organization_id", "sfn_project_id", "project_id", "project_name")
    ORGANIZATION_ID_FIELD_NUMBER: _ClassVar[int]
    SFN_PROJECT_ID_FIELD_NUMBER: _ClassVar[int]
    PROJECT_ID_FIELD_NUMBER: _ClassVar[int]
    PROJECT_NAME_FIELD_NUMBER: _ClassVar[int]
    organization_id: str
    sfn_project_id: int
    project_id: int
    project_name: str
    def __init__(self, organization_id: _Optional[str] = ..., sfn_project_id: _Optional[int] = ..., project_id: _Optional[int] = ..., project_name: _Optional[str] = ...) -> None: ...

class KnowledgeBaseInfo(_message.Message):
    __slots__ = ("type", "knowledge_base_id", "knowledge_base_name", "remark", "scenario", "status", "content_status")
    class KnowledgeBaseType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        CUSTOM: _ClassVar[KnowledgeBaseInfo.KnowledgeBaseType]
        STRATEGIES: _ClassVar[KnowledgeBaseInfo.KnowledgeBaseType]
        METADATA: _ClassVar[KnowledgeBaseInfo.KnowledgeBaseType]
    CUSTOM: KnowledgeBaseInfo.KnowledgeBaseType
    STRATEGIES: KnowledgeBaseInfo.KnowledgeBaseType
    METADATA: KnowledgeBaseInfo.KnowledgeBaseType
    class KnowledgeBaseScenario(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        SUPPLY_INFO: _ClassVar[KnowledgeBaseInfo.KnowledgeBaseScenario]
        STRATEGY_DESIGN_SUGGESTION: _ClassVar[KnowledgeBaseInfo.KnowledgeBaseScenario]
        STRATEGY_INSIGHT: _ClassVar[KnowledgeBaseInfo.KnowledgeBaseScenario]
    SUPPLY_INFO: KnowledgeBaseInfo.KnowledgeBaseScenario
    STRATEGY_DESIGN_SUGGESTION: KnowledgeBaseInfo.KnowledgeBaseScenario
    STRATEGY_INSIGHT: KnowledgeBaseInfo.KnowledgeBaseScenario
    TYPE_FIELD_NUMBER: _ClassVar[int]
    KNOWLEDGE_BASE_ID_FIELD_NUMBER: _ClassVar[int]
    KNOWLEDGE_BASE_NAME_FIELD_NUMBER: _ClassVar[int]
    REMARK_FIELD_NUMBER: _ClassVar[int]
    SCENARIO_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    CONTENT_STATUS_FIELD_NUMBER: _ClassVar[int]
    type: KnowledgeBaseInfo.KnowledgeBaseType
    knowledge_base_id: str
    knowledge_base_name: str
    remark: str
    scenario: _containers.RepeatedScalarFieldContainer[KnowledgeBaseInfo.KnowledgeBaseScenario]
    status: KnowledgeBaseStatus
    content_status: _containers.RepeatedCompositeFieldContainer[KnowledgeBaseContentStatus]
    def __init__(self, type: _Optional[_Union[KnowledgeBaseInfo.KnowledgeBaseType, str]] = ..., knowledge_base_id: _Optional[str] = ..., knowledge_base_name: _Optional[str] = ..., remark: _Optional[str] = ..., scenario: _Optional[_Iterable[_Union[KnowledgeBaseInfo.KnowledgeBaseScenario, str]]] = ..., status: _Optional[_Union[KnowledgeBaseStatus, str]] = ..., content_status: _Optional[_Iterable[_Union[KnowledgeBaseContentStatus, _Mapping]]] = ...) -> None: ...

class KnowledgeBaseContentStatus(_message.Message):
    __slots__ = ("type", "content_id", "status")
    class ContentStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        WAITING: _ClassVar[KnowledgeBaseContentStatus.ContentStatus]
        PROCESSING: _ClassVar[KnowledgeBaseContentStatus.ContentStatus]
        FAILED: _ClassVar[KnowledgeBaseContentStatus.ContentStatus]
        READY: _ClassVar[KnowledgeBaseContentStatus.ContentStatus]
        DELETED: _ClassVar[KnowledgeBaseContentStatus.ContentStatus]
    WAITING: KnowledgeBaseContentStatus.ContentStatus
    PROCESSING: KnowledgeBaseContentStatus.ContentStatus
    FAILED: KnowledgeBaseContentStatus.ContentStatus
    READY: KnowledgeBaseContentStatus.ContentStatus
    DELETED: KnowledgeBaseContentStatus.ContentStatus
    class ContentType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        DOCUMENT: _ClassVar[KnowledgeBaseContentStatus.ContentType]
        WEBPAGE: _ClassVar[KnowledgeBaseContentStatus.ContentType]
        CUSTOM: _ClassVar[KnowledgeBaseContentStatus.ContentType]
        STRATEGY: _ClassVar[KnowledgeBaseContentStatus.ContentType]
    DOCUMENT: KnowledgeBaseContentStatus.ContentType
    WEBPAGE: KnowledgeBaseContentStatus.ContentType
    CUSTOM: KnowledgeBaseContentStatus.ContentType
    STRATEGY: KnowledgeBaseContentStatus.ContentType
    TYPE_FIELD_NUMBER: _ClassVar[int]
    CONTENT_ID_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    type: KnowledgeBaseContentStatus.ContentType
    content_id: str
    status: KnowledgeBaseContentStatus.ContentStatus
    def __init__(self, type: _Optional[_Union[KnowledgeBaseContentStatus.ContentType, str]] = ..., content_id: _Optional[str] = ..., status: _Optional[_Union[KnowledgeBaseContentStatus.ContentStatus, str]] = ...) -> None: ...

class KnowledgeBaseResources(_message.Message):
    __slots__ = ("type", "filenames", "urls", "text")
    class ResourceType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        DOCUMENT: _ClassVar[KnowledgeBaseResources.ResourceType]
        WEBPAGE: _ClassVar[KnowledgeBaseResources.ResourceType]
        CUSTOM: _ClassVar[KnowledgeBaseResources.ResourceType]
    DOCUMENT: KnowledgeBaseResources.ResourceType
    WEBPAGE: KnowledgeBaseResources.ResourceType
    CUSTOM: KnowledgeBaseResources.ResourceType
    class WebPageUrl(_message.Message):
        __slots__ = ("url", "crawl_type", "update_frequency")
        class CrawlType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
            __slots__ = ()
            SINGLE_PAGE: _ClassVar[KnowledgeBaseResources.WebPageUrl.CrawlType]
            RECURSIVE: _ClassVar[KnowledgeBaseResources.WebPageUrl.CrawlType]
        SINGLE_PAGE: KnowledgeBaseResources.WebPageUrl.CrawlType
        RECURSIVE: KnowledgeBaseResources.WebPageUrl.CrawlType
        class UpdateFrequency(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
            __slots__ = ()
            NEVER: _ClassVar[KnowledgeBaseResources.WebPageUrl.UpdateFrequency]
            DAILY: _ClassVar[KnowledgeBaseResources.WebPageUrl.UpdateFrequency]
            WEEKLY: _ClassVar[KnowledgeBaseResources.WebPageUrl.UpdateFrequency]
            MONTHLY: _ClassVar[KnowledgeBaseResources.WebPageUrl.UpdateFrequency]
        NEVER: KnowledgeBaseResources.WebPageUrl.UpdateFrequency
        DAILY: KnowledgeBaseResources.WebPageUrl.UpdateFrequency
        WEEKLY: KnowledgeBaseResources.WebPageUrl.UpdateFrequency
        MONTHLY: KnowledgeBaseResources.WebPageUrl.UpdateFrequency
        URL_FIELD_NUMBER: _ClassVar[int]
        CRAWL_TYPE_FIELD_NUMBER: _ClassVar[int]
        UPDATE_FREQUENCY_FIELD_NUMBER: _ClassVar[int]
        url: str
        crawl_type: KnowledgeBaseResources.WebPageUrl.CrawlType
        update_frequency: KnowledgeBaseResources.WebPageUrl.UpdateFrequency
        def __init__(self, url: _Optional[str] = ..., crawl_type: _Optional[_Union[KnowledgeBaseResources.WebPageUrl.CrawlType, str]] = ..., update_frequency: _Optional[_Union[KnowledgeBaseResources.WebPageUrl.UpdateFrequency, str]] = ...) -> None: ...
    class TextEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    TYPE_FIELD_NUMBER: _ClassVar[int]
    FILENAMES_FIELD_NUMBER: _ClassVar[int]
    URLS_FIELD_NUMBER: _ClassVar[int]
    TEXT_FIELD_NUMBER: _ClassVar[int]
    type: KnowledgeBaseResources.ResourceType
    filenames: _containers.RepeatedScalarFieldContainer[str]
    urls: _containers.RepeatedCompositeFieldContainer[KnowledgeBaseResources.WebPageUrl]
    text: _containers.ScalarMap[str, str]
    def __init__(self, type: _Optional[_Union[KnowledgeBaseResources.ResourceType, str]] = ..., filenames: _Optional[_Iterable[str]] = ..., urls: _Optional[_Iterable[_Union[KnowledgeBaseResources.WebPageUrl, _Mapping]]] = ..., text: _Optional[_Mapping[str, str]] = ...) -> None: ...

class KnowledgeBaseStrategies(_message.Message):
    __slots__ = ("type", "ids")
    class StrategyType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        PLAN: _ClassVar[KnowledgeBaseStrategies.StrategyType]
        CANVAS: _ClassVar[KnowledgeBaseStrategies.StrategyType]
        SECTION: _ClassVar[KnowledgeBaseStrategies.StrategyType]
    PLAN: KnowledgeBaseStrategies.StrategyType
    CANVAS: KnowledgeBaseStrategies.StrategyType
    SECTION: KnowledgeBaseStrategies.StrategyType
    TYPE_FIELD_NUMBER: _ClassVar[int]
    IDS_FIELD_NUMBER: _ClassVar[int]
    type: KnowledgeBaseStrategies.StrategyType
    ids: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, type: _Optional[_Union[KnowledgeBaseStrategies.StrategyType, str]] = ..., ids: _Optional[_Iterable[str]] = ...) -> None: ...

class BuildKnowledgeBaseRequest(_message.Message):
    __slots__ = ("project_info", "base_infos")
    PROJECT_INFO_FIELD_NUMBER: _ClassVar[int]
    BASE_INFOS_FIELD_NUMBER: _ClassVar[int]
    project_info: TenantProjectInfo
    base_infos: _containers.RepeatedCompositeFieldContainer[KnowledgeBaseInfo]
    def __init__(self, project_info: _Optional[_Union[TenantProjectInfo, _Mapping]] = ..., base_infos: _Optional[_Iterable[_Union[KnowledgeBaseInfo, _Mapping]]] = ...) -> None: ...

class OperateKnowledgeBaseResourceRequest(_message.Message):
    __slots__ = ("project_info", "base_info", "resources", "strategies")
    PROJECT_INFO_FIELD_NUMBER: _ClassVar[int]
    BASE_INFO_FIELD_NUMBER: _ClassVar[int]
    RESOURCES_FIELD_NUMBER: _ClassVar[int]
    STRATEGIES_FIELD_NUMBER: _ClassVar[int]
    project_info: TenantProjectInfo
    base_info: KnowledgeBaseInfo
    resources: _containers.RepeatedCompositeFieldContainer[KnowledgeBaseResources]
    strategies: _containers.RepeatedCompositeFieldContainer[KnowledgeBaseStrategies]
    def __init__(self, project_info: _Optional[_Union[TenantProjectInfo, _Mapping]] = ..., base_info: _Optional[_Union[KnowledgeBaseInfo, _Mapping]] = ..., resources: _Optional[_Iterable[_Union[KnowledgeBaseResources, _Mapping]]] = ..., strategies: _Optional[_Iterable[_Union[KnowledgeBaseStrategies, _Mapping]]] = ...) -> None: ...

class UploadKnowledgeBaseFileRequest(_message.Message):
    __slots__ = ("project_info", "base_info", "filename", "content")
    PROJECT_INFO_FIELD_NUMBER: _ClassVar[int]
    BASE_INFO_FIELD_NUMBER: _ClassVar[int]
    FILENAME_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    project_info: TenantProjectInfo
    base_info: KnowledgeBaseInfo
    filename: str
    content: bytes
    def __init__(self, project_info: _Optional[_Union[TenantProjectInfo, _Mapping]] = ..., base_info: _Optional[_Union[KnowledgeBaseInfo, _Mapping]] = ..., filename: _Optional[str] = ..., content: _Optional[bytes] = ...) -> None: ...

class DeleteKnowledgeBaseRequest(_message.Message):
    __slots__ = ("project_info", "base_info")
    PROJECT_INFO_FIELD_NUMBER: _ClassVar[int]
    BASE_INFO_FIELD_NUMBER: _ClassVar[int]
    project_info: TenantProjectInfo
    base_info: KnowledgeBaseInfo
    def __init__(self, project_info: _Optional[_Union[TenantProjectInfo, _Mapping]] = ..., base_info: _Optional[_Union[KnowledgeBaseInfo, _Mapping]] = ...) -> None: ...

class QueryKnowledgeBaseRequest(_message.Message):
    __slots__ = ("project_info", "base_infos", "query_content_status")
    PROJECT_INFO_FIELD_NUMBER: _ClassVar[int]
    BASE_INFOS_FIELD_NUMBER: _ClassVar[int]
    QUERY_CONTENT_STATUS_FIELD_NUMBER: _ClassVar[int]
    project_info: TenantProjectInfo
    base_infos: _containers.RepeatedCompositeFieldContainer[KnowledgeBaseInfo]
    query_content_status: bool
    def __init__(self, project_info: _Optional[_Union[TenantProjectInfo, _Mapping]] = ..., base_infos: _Optional[_Iterable[_Union[KnowledgeBaseInfo, _Mapping]]] = ..., query_content_status: bool = ...) -> None: ...

class KnowledgeBaseResponse(_message.Message):
    __slots__ = ("base_infos",)
    BASE_INFOS_FIELD_NUMBER: _ClassVar[int]
    base_infos: _containers.RepeatedCompositeFieldContainer[KnowledgeBaseInfo]
    def __init__(self, base_infos: _Optional[_Iterable[_Union[KnowledgeBaseInfo, _Mapping]]] = ...) -> None: ...
