from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class BlockBasedStreamEvent(_message.Message):
    __slots__ = ("type", "data", "status")
    class EventType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        UNKNOWN: _ClassVar[BlockBasedStreamEvent.EventType]
        MESSAGE_START: _ClassVar[BlockBasedStreamEvent.EventType]
        BLOCK_START: _ClassVar[BlockBasedStreamEvent.EventType]
        MESSAGE: _ClassVar[BlockBasedStreamEvent.EventType]
        STATUS: _ClassVar[BlockBasedStreamEvent.EventType]
        BLOCK_END: _ClassVar[BlockBasedStreamEvent.EventType]
        MESSAGE_END: _ClassVar[BlockBasedStreamEvent.EventType]
    UNKNOWN: BlockBasedStreamEvent.EventType
    MESSAGE_START: BlockBasedStreamEvent.EventType
    BLOCK_START: BlockBasedStreamEvent.EventType
    MESSAGE: BlockBasedStreamEvent.EventType
    STATUS: BlockBasedStreamEvent.EventType
    BLOCK_END: BlockBasedStreamEvent.EventType
    MESSAGE_END: BlockBasedStreamEvent.EventType
    class StatusData(_message.Message):
        __slots__ = ("status", "text", "keeping")
        class StatusType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
            __slots__ = ()
            UNSPECIFIED: _ClassVar[BlockBasedStreamEvent.StatusData.StatusType]
            PROCESSING: _ClassVar[BlockBasedStreamEvent.StatusData.StatusType]
            ERROR: _ClassVar[BlockBasedStreamEvent.StatusData.StatusType]
            WARNING: _ClassVar[BlockBasedStreamEvent.StatusData.StatusType]
            SUCCESS: _ClassVar[BlockBasedStreamEvent.StatusData.StatusType]
        UNSPECIFIED: BlockBasedStreamEvent.StatusData.StatusType
        PROCESSING: BlockBasedStreamEvent.StatusData.StatusType
        ERROR: BlockBasedStreamEvent.StatusData.StatusType
        WARNING: BlockBasedStreamEvent.StatusData.StatusType
        SUCCESS: BlockBasedStreamEvent.StatusData.StatusType
        STATUS_FIELD_NUMBER: _ClassVar[int]
        TEXT_FIELD_NUMBER: _ClassVar[int]
        KEEPING_FIELD_NUMBER: _ClassVar[int]
        status: BlockBasedStreamEvent.StatusData.StatusType
        text: str
        keeping: bool
        def __init__(self, status: _Optional[_Union[BlockBasedStreamEvent.StatusData.StatusType, str]] = ..., text: _Optional[str] = ..., keeping: bool = ...) -> None: ...
    TYPE_FIELD_NUMBER: _ClassVar[int]
    DATA_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    type: BlockBasedStreamEvent.EventType
    data: str
    status: BlockBasedStreamEvent.StatusData
    def __init__(self, type: _Optional[_Union[BlockBasedStreamEvent.EventType, str]] = ..., data: _Optional[str] = ..., status: _Optional[_Union[BlockBasedStreamEvent.StatusData, _Mapping]] = ...) -> None: ...

class StreamEvent(_message.Message):
    __slots__ = ("type", "message_start", "message", "status")
    class EventType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        UNKNOWN: _ClassVar[StreamEvent.EventType]
        MESSAGE_START: _ClassVar[StreamEvent.EventType]
        MESSAGE: _ClassVar[StreamEvent.EventType]
        STATUS: _ClassVar[StreamEvent.EventType]
        MESSAGE_END: _ClassVar[StreamEvent.EventType]
    UNKNOWN: StreamEvent.EventType
    MESSAGE_START: StreamEvent.EventType
    MESSAGE: StreamEvent.EventType
    STATUS: StreamEvent.EventType
    MESSAGE_END: StreamEvent.EventType
    class MessageStartData(_message.Message):
        __slots__ = ("chat_session_id", "chat_session_name")
        CHAT_SESSION_ID_FIELD_NUMBER: _ClassVar[int]
        CHAT_SESSION_NAME_FIELD_NUMBER: _ClassVar[int]
        chat_session_id: str
        chat_session_name: str
        def __init__(self, chat_session_id: _Optional[str] = ..., chat_session_name: _Optional[str] = ...) -> None: ...
    class MessageData(_message.Message):
        __slots__ = ("type", "content")
        class MessageType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
            __slots__ = ()
            MARKDOWN: _ClassVar[StreamEvent.MessageData.MessageType]
            THINKING: _ClassVar[StreamEvent.MessageData.MessageType]
            TABLE: _ClassVar[StreamEvent.MessageData.MessageType]
            CHART: _ClassVar[StreamEvent.MessageData.MessageType]
            ENTITY_RELATION: _ClassVar[StreamEvent.MessageData.MessageType]
            CANVAS_DESIGN: _ClassVar[StreamEvent.MessageData.MessageType]
            CANVAS: _ClassVar[StreamEvent.MessageData.MessageType]
            ANALYSE_IDEA: _ClassVar[StreamEvent.MessageData.MessageType]
            RECOMMEND_QUESTION: _ClassVar[StreamEvent.MessageData.MessageType]
        MARKDOWN: StreamEvent.MessageData.MessageType
        THINKING: StreamEvent.MessageData.MessageType
        TABLE: StreamEvent.MessageData.MessageType
        CHART: StreamEvent.MessageData.MessageType
        ENTITY_RELATION: StreamEvent.MessageData.MessageType
        CANVAS_DESIGN: StreamEvent.MessageData.MessageType
        CANVAS: StreamEvent.MessageData.MessageType
        ANALYSE_IDEA: StreamEvent.MessageData.MessageType
        RECOMMEND_QUESTION: StreamEvent.MessageData.MessageType
        TYPE_FIELD_NUMBER: _ClassVar[int]
        CONTENT_FIELD_NUMBER: _ClassVar[int]
        type: StreamEvent.MessageData.MessageType
        content: str
        def __init__(self, type: _Optional[_Union[StreamEvent.MessageData.MessageType, str]] = ..., content: _Optional[str] = ...) -> None: ...
    class StatusData(_message.Message):
        __slots__ = ("status", "text", "keeping")
        class StatusType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
            __slots__ = ()
            UNSPECIFIED: _ClassVar[StreamEvent.StatusData.StatusType]
            PROCESSING: _ClassVar[StreamEvent.StatusData.StatusType]
            ERROR: _ClassVar[StreamEvent.StatusData.StatusType]
            WARNING: _ClassVar[StreamEvent.StatusData.StatusType]
            SUCCESS: _ClassVar[StreamEvent.StatusData.StatusType]
        UNSPECIFIED: StreamEvent.StatusData.StatusType
        PROCESSING: StreamEvent.StatusData.StatusType
        ERROR: StreamEvent.StatusData.StatusType
        WARNING: StreamEvent.StatusData.StatusType
        SUCCESS: StreamEvent.StatusData.StatusType
        STATUS_FIELD_NUMBER: _ClassVar[int]
        TEXT_FIELD_NUMBER: _ClassVar[int]
        KEEPING_FIELD_NUMBER: _ClassVar[int]
        status: StreamEvent.StatusData.StatusType
        text: str
        keeping: bool
        def __init__(self, status: _Optional[_Union[StreamEvent.StatusData.StatusType, str]] = ..., text: _Optional[str] = ..., keeping: bool = ...) -> None: ...
    TYPE_FIELD_NUMBER: _ClassVar[int]
    MESSAGE_START_FIELD_NUMBER: _ClassVar[int]
    MESSAGE_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    type: StreamEvent.EventType
    message_start: StreamEvent.MessageStartData
    message: StreamEvent.MessageData
    status: StreamEvent.StatusData
    def __init__(self, type: _Optional[_Union[StreamEvent.EventType, str]] = ..., message_start: _Optional[_Union[StreamEvent.MessageStartData, _Mapping]] = ..., message: _Optional[_Union[StreamEvent.MessageData, _Mapping]] = ..., status: _Optional[_Union[StreamEvent.StatusData, _Mapping]] = ...) -> None: ...
