# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from portal import portal_bot_pb2 as portal_dot_portal__bot__pb2
import stream_event_pb2 as stream__event__pb2


class PortalBotServiceStub(object):
    """{_description_} portal 智能体 API 服务"""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.RetrievalIdeas = channel.unary_unary(
            "/sensorsdata.focus.v1.PortalBotService/RetrievalIdeas",
            request_serializer=portal_dot_portal__bot__pb2.RetrievalIdeasRequest.SerializeToString,
            response_deserializer=portal_dot_portal__bot__pb2.RetrievalIdeasResponse.FromString,
        )
        self.Chat = channel.unary_stream(
            "/sensorsdata.focus.v1.PortalBotService/Chat",
            request_serializer=portal_dot_portal__bot__pb2.GeneralPortalRequest.SerializeToString,
            response_deserializer=stream__event__pb2.StreamEvent.FromString,
        )


class PortalBotServiceServicer(object):
    """{_description_} portal 智能体 API 服务"""

    def RetrievalIdeas(self, request, context):
        """{_summary_} 思路检索接口"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def Chat(self, request, context):
        """{_summary_} 对话接口"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")


def add_PortalBotServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
        "RetrievalIdeas": grpc.unary_unary_rpc_method_handler(
            servicer.RetrievalIdeas,
            request_deserializer=portal_dot_portal__bot__pb2.RetrievalIdeasRequest.FromString,
            response_serializer=portal_dot_portal__bot__pb2.RetrievalIdeasResponse.SerializeToString,
        ),
        "Chat": grpc.unary_stream_rpc_method_handler(
            servicer.Chat,
            request_deserializer=portal_dot_portal__bot__pb2.GeneralPortalRequest.FromString,
            response_serializer=stream__event__pb2.StreamEvent.SerializeToString,
        ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
        "sensorsdata.focus.v1.PortalBotService", rpc_method_handlers
    )
    server.add_generic_rpc_handlers((generic_handler,))


# This class is part of an EXPERIMENTAL API.
class PortalBotService(object):
    """{_description_} portal 智能体 API 服务"""

    @staticmethod
    def RetrievalIdeas(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/sensorsdata.focus.v1.PortalBotService/RetrievalIdeas",
            portal_dot_portal__bot__pb2.RetrievalIdeasRequest.SerializeToString,
            portal_dot_portal__bot__pb2.RetrievalIdeasResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def Chat(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_stream(
            request,
            target,
            "/sensorsdata.focus.v1.PortalBotService/Chat",
            portal_dot_portal__bot__pb2.GeneralPortalRequest.SerializeToString,
            stream__event__pb2.StreamEvent.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )
