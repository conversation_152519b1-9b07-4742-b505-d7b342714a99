# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: portal/portal_bot.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import stream_event_pb2 as stream__event__pb2
import chat_session_pb2 as chat__session__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x17portal/portal_bot.proto\x12\x14sensorsdata.focus.v1\x1a\x12stream_event.proto\x1a\x12\x63hat_session.proto"\x96\x02\n\x15RetrievalIdeasRequest\x12@\n\x11\x63hat_session_info\x18\x01 \x01(\x0b\x32%.sensorsdata.focus.v1.ChatSessionInfo\x12\r\n\x05query\x18\x02 \x01(\t\x12\r\n\x05limit\x18\x03 \x01(\x05\x12\x46\n\x08scenario\x18\x04 \x01(\x0e\x32\x34.sensorsdata.focus.v1.RetrievalIdeasRequest.Scenario"U\n\x08Scenario\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\x14\n\x10INSIGHT_STRATEGY\x10\x01\x12\x10\n\x0cINSIGHT_GOAL\x10\x02\x12\x10\n\x0c\x44\x41TA_ANALYSE\x10\x03"\xe6\x02\n\rRetrievalIdea\x12;\n\x05units\x18\x01 \x03(\x0b\x32,.sensorsdata.focus.v1.RetrievalIdea.IdeaUnit\x12\x39\n\x06refers\x18\x03 \x03(\x0b\x32).sensorsdata.focus.v1.RetrievalIdea.Refer\x1a/\n\x05Refer\x12\n\n\x02id\x18\x01 \x01(\x05\x12\r\n\x05\x63name\x18\x02 \x01(\t\x12\x0b\n\x03url\x18\x03 \x01(\t\x1a\xab\x01\n\x08IdeaUnit\x12\r\n\x05title\x18\x01 \x01(\t\x12\x0f\n\x07\x63ontent\x18\x02 \x01(\t\x12\x39\n\x06refers\x18\x03 \x03(\x0b\x32).sensorsdata.focus.v1.RetrievalIdea.Refer\x12\x44\n\x0e\x63hildren_units\x18\x04 \x03(\x0b\x32,.sensorsdata.focus.v1.RetrievalIdea.IdeaUnit"L\n\x16RetrievalIdeasResponse\x12\x32\n\x05ideas\x18\x01 \x03(\x0b\x32#.sensorsdata.focus.v1.RetrievalIdea"\x86\x03\n\rInsightParams\x12\x12\n\nstart_time\x18\x01 \x01(\t\x12\x10\n\x08\x65nd_time\x18\x02 \x01(\t\x12?\n\x05level\x18\x03 \x01(\x0e\x32\x30.sensorsdata.focus.v1.InsightParams.InsightLevel\x12\x10\n\x08scene_id\x18\x04 \x01(\x05\x12\x14\n\x0csub_scene_id\x18\x05 \x01(\x05\x12\x18\n\x10\x62usiness_goal_id\x18\x06 \x01(\x05\x12\x1b\n\x13\x62usiness_goal_value\x18\x07 \x01(\t\x12\x18\n\x10\x63\x61mpaign_case_id\x18\x08 \x01(\x05\x12\x34\n\nstrategies\x18\t \x03(\x0b\x32 .sensorsdata.focus.v1.Strategies"_\n\x0cInsightLevel\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\t\n\x05SCENE\x10\x01\x12\r\n\tSUB_SCENE\x10\x02\x12\x11\n\rBUSINESS_GOAL\x10\x03\x12\x11\n\rCAMPAIGN_CASE\x10\x04"\x89\x01\n\nStrategies\x12;\n\x04type\x18\x01 \x01(\x0e\x32-.sensorsdata.focus.v1.Strategies.StrategyType\x12\x0b\n\x03ids\x18\x02 \x03(\t"1\n\x0cStrategyType\x12\x08\n\x04PLAN\x10\x00\x12\n\n\x06\x43\x41NVAS\x10\x01\x12\x0b\n\x07SECTION\x10\x02"\xf7\x04\n\rPortalMessage\x12G\n\tgenerator\x18\x01 \x01(\x0e\x32\x34.sensorsdata.focus.v1.PortalMessage.MessageGenerator\x12=\n\x04type\x18\x02 \x01(\x0e\x32/.sensorsdata.focus.v1.PortalMessage.MessageType\x12\x0c\n\x04text\x18\x03 \x01(\t\x12=\n\x10insight_strategy\x18\x04 \x01(\x0b\x32#.sensorsdata.focus.v1.InsightParams\x12\x39\n\x0cinsight_goal\x18\x05 \x01(\x0b\x32#.sensorsdata.focus.v1.InsightParams\x12\x14\n\x0c\x61nalyse_idea\x18\x06 \x01(\t\x12\x42\n\ncapability\x18\x07 \x01(\x0b\x32..sensorsdata.focus.v1.PortalMessage.Capability\x1a$\n\nCapability\x12\x16\n\x0eretrieval_idea\x18\x01 \x01(\x08"1\n\x10MessageGenerator\x12\n\n\x06SYSTEM\x10\x00\x12\x08\n\x04USER\x10\x01\x12\x07\n\x03\x42OT\x10\x02"\xa2\x01\n\x0bMessageType\x12\x08\n\x04TEXT\x10\x00\x12\x0c\n\x08MARKDOWN\x10\x01\x12\t\n\x05TABLE\x10\x02\x12\t\n\x05\x43HART\x10\x03\x12\x13\n\x0f\x45NTITY_RELATION\x10\x04\x12\x14\n\x10INSIGHT_STRATEGY\x10\x05\x12\x10\n\x0cINSIGHT_GOAL\x10\x06\x12\x10\n\x0c\x41NALYSE_IDEA\x10\x07\x12\x16\n\x12RECOMMEND_QUESTION\x10\x08"\x8f\x01\n\x14GeneralPortalRequest\x12@\n\x11\x63hat_session_info\x18\x01 \x01(\x0b\x32%.sensorsdata.focus.v1.ChatSessionInfo\x12\x35\n\x08messages\x18\x02 \x03(\x0b\x32#.sensorsdata.focus.v1.PortalMessage2\xdc\x01\n\x10PortalBotService\x12m\n\x0eRetrievalIdeas\x12+.sensorsdata.focus.v1.RetrievalIdeasRequest\x1a,.sensorsdata.focus.v1.RetrievalIdeasResponse"\x00\x12Y\n\x04\x43hat\x12*.sensorsdata.focus.v1.GeneralPortalRequest\x1a!.sensorsdata.focus.v1.StreamEvent"\x00\x30\x01\x42\x1f\n\x1b\x63om.sensorsdata.focus.v1.aiP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "portal.portal_bot_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    _globals["DESCRIPTOR"]._options = None
    _globals["DESCRIPTOR"]._serialized_options = (
        b"\n\033com.sensorsdata.focus.v1.aiP\001"
    )
    _globals["_RETRIEVALIDEASREQUEST"]._serialized_start = 90
    _globals["_RETRIEVALIDEASREQUEST"]._serialized_end = 368
    _globals["_RETRIEVALIDEASREQUEST_SCENARIO"]._serialized_start = 283
    _globals["_RETRIEVALIDEASREQUEST_SCENARIO"]._serialized_end = 368
    _globals["_RETRIEVALIDEA"]._serialized_start = 371
    _globals["_RETRIEVALIDEA"]._serialized_end = 729
    _globals["_RETRIEVALIDEA_REFER"]._serialized_start = 508
    _globals["_RETRIEVALIDEA_REFER"]._serialized_end = 555
    _globals["_RETRIEVALIDEA_IDEAUNIT"]._serialized_start = 558
    _globals["_RETRIEVALIDEA_IDEAUNIT"]._serialized_end = 729
    _globals["_RETRIEVALIDEASRESPONSE"]._serialized_start = 731
    _globals["_RETRIEVALIDEASRESPONSE"]._serialized_end = 807
    _globals["_INSIGHTPARAMS"]._serialized_start = 810
    _globals["_INSIGHTPARAMS"]._serialized_end = 1200
    _globals["_INSIGHTPARAMS_INSIGHTLEVEL"]._serialized_start = 1105
    _globals["_INSIGHTPARAMS_INSIGHTLEVEL"]._serialized_end = 1200
    _globals["_STRATEGIES"]._serialized_start = 1203
    _globals["_STRATEGIES"]._serialized_end = 1340
    _globals["_STRATEGIES_STRATEGYTYPE"]._serialized_start = 1291
    _globals["_STRATEGIES_STRATEGYTYPE"]._serialized_end = 1340
    _globals["_PORTALMESSAGE"]._serialized_start = 1343
    _globals["_PORTALMESSAGE"]._serialized_end = 1974
    _globals["_PORTALMESSAGE_CAPABILITY"]._serialized_start = 1722
    _globals["_PORTALMESSAGE_CAPABILITY"]._serialized_end = 1758
    _globals["_PORTALMESSAGE_MESSAGEGENERATOR"]._serialized_start = 1760
    _globals["_PORTALMESSAGE_MESSAGEGENERATOR"]._serialized_end = 1809
    _globals["_PORTALMESSAGE_MESSAGETYPE"]._serialized_start = 1812
    _globals["_PORTALMESSAGE_MESSAGETYPE"]._serialized_end = 1974
    _globals["_GENERALPORTALREQUEST"]._serialized_start = 1977
    _globals["_GENERALPORTALREQUEST"]._serialized_end = 2120
    _globals["_PORTALBOTSERVICE"]._serialized_start = 2123
    _globals["_PORTALBOTSERVICE"]._serialized_end = 2343
# @@protoc_insertion_point(module_scope)
