import stream_event_pb2 as _stream_event_pb2
import chat_session_pb2 as _chat_session_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class RetrievalIdeasRequest(_message.Message):
    __slots__ = ("chat_session_info", "query", "limit", "scenario")
    class Scenario(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        UNSPECIFIED: _ClassVar[RetrievalIdeasRequest.Scenario]
        INSIGHT_STRATEGY: _ClassVar[RetrievalIdeasRequest.Scenario]
        INSIGHT_GOAL: _ClassVar[RetrievalIdeasRequest.Scenario]
        DATA_ANALYSE: _ClassVar[RetrievalIdeasRequest.Scenario]
    UNSPECIFIED: RetrievalIdeasRequest.Scenario
    INSIGHT_STRATEGY: RetrievalIdeasRequest.Scenario
    INSIGHT_GOAL: RetrievalIdeasRequest.Scenario
    DATA_ANALYSE: RetrievalIdeasRequest.Scenario
    CHAT_SESSION_INFO_FIELD_NUMBER: _ClassVar[int]
    QUERY_FIELD_NUMBER: _ClassVar[int]
    LIMIT_FIELD_NUMBER: _ClassVar[int]
    SCENARIO_FIELD_NUMBER: _ClassVar[int]
    chat_session_info: _chat_session_pb2.ChatSessionInfo
    query: str
    limit: int
    scenario: RetrievalIdeasRequest.Scenario
    def __init__(self, chat_session_info: _Optional[_Union[_chat_session_pb2.ChatSessionInfo, _Mapping]] = ..., query: _Optional[str] = ..., limit: _Optional[int] = ..., scenario: _Optional[_Union[RetrievalIdeasRequest.Scenario, str]] = ...) -> None: ...

class RetrievalIdea(_message.Message):
    __slots__ = ("units", "refers")
    class Refer(_message.Message):
        __slots__ = ("id", "cname", "url")
        ID_FIELD_NUMBER: _ClassVar[int]
        CNAME_FIELD_NUMBER: _ClassVar[int]
        URL_FIELD_NUMBER: _ClassVar[int]
        id: int
        cname: str
        url: str
        def __init__(self, id: _Optional[int] = ..., cname: _Optional[str] = ..., url: _Optional[str] = ...) -> None: ...
    class IdeaUnit(_message.Message):
        __slots__ = ("title", "content", "refers", "children_units")
        TITLE_FIELD_NUMBER: _ClassVar[int]
        CONTENT_FIELD_NUMBER: _ClassVar[int]
        REFERS_FIELD_NUMBER: _ClassVar[int]
        CHILDREN_UNITS_FIELD_NUMBER: _ClassVar[int]
        title: str
        content: str
        refers: _containers.RepeatedCompositeFieldContainer[RetrievalIdea.Refer]
        children_units: _containers.RepeatedCompositeFieldContainer[RetrievalIdea.IdeaUnit]
        def __init__(self, title: _Optional[str] = ..., content: _Optional[str] = ..., refers: _Optional[_Iterable[_Union[RetrievalIdea.Refer, _Mapping]]] = ..., children_units: _Optional[_Iterable[_Union[RetrievalIdea.IdeaUnit, _Mapping]]] = ...) -> None: ...
    UNITS_FIELD_NUMBER: _ClassVar[int]
    REFERS_FIELD_NUMBER: _ClassVar[int]
    units: _containers.RepeatedCompositeFieldContainer[RetrievalIdea.IdeaUnit]
    refers: _containers.RepeatedCompositeFieldContainer[RetrievalIdea.Refer]
    def __init__(self, units: _Optional[_Iterable[_Union[RetrievalIdea.IdeaUnit, _Mapping]]] = ..., refers: _Optional[_Iterable[_Union[RetrievalIdea.Refer, _Mapping]]] = ...) -> None: ...

class RetrievalIdeasResponse(_message.Message):
    __slots__ = ("ideas",)
    IDEAS_FIELD_NUMBER: _ClassVar[int]
    ideas: _containers.RepeatedCompositeFieldContainer[RetrievalIdea]
    def __init__(self, ideas: _Optional[_Iterable[_Union[RetrievalIdea, _Mapping]]] = ...) -> None: ...

class InsightParams(_message.Message):
    __slots__ = ("start_time", "end_time", "level", "scene_id", "sub_scene_id", "business_goal_id", "business_goal_value", "campaign_case_id", "strategies")
    class InsightLevel(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        UNSPECIFIED: _ClassVar[InsightParams.InsightLevel]
        SCENE: _ClassVar[InsightParams.InsightLevel]
        SUB_SCENE: _ClassVar[InsightParams.InsightLevel]
        BUSINESS_GOAL: _ClassVar[InsightParams.InsightLevel]
        CAMPAIGN_CASE: _ClassVar[InsightParams.InsightLevel]
    UNSPECIFIED: InsightParams.InsightLevel
    SCENE: InsightParams.InsightLevel
    SUB_SCENE: InsightParams.InsightLevel
    BUSINESS_GOAL: InsightParams.InsightLevel
    CAMPAIGN_CASE: InsightParams.InsightLevel
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    LEVEL_FIELD_NUMBER: _ClassVar[int]
    SCENE_ID_FIELD_NUMBER: _ClassVar[int]
    SUB_SCENE_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_GOAL_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_GOAL_VALUE_FIELD_NUMBER: _ClassVar[int]
    CAMPAIGN_CASE_ID_FIELD_NUMBER: _ClassVar[int]
    STRATEGIES_FIELD_NUMBER: _ClassVar[int]
    start_time: str
    end_time: str
    level: InsightParams.InsightLevel
    scene_id: int
    sub_scene_id: int
    business_goal_id: int
    business_goal_value: str
    campaign_case_id: int
    strategies: _containers.RepeatedCompositeFieldContainer[Strategies]
    def __init__(self, start_time: _Optional[str] = ..., end_time: _Optional[str] = ..., level: _Optional[_Union[InsightParams.InsightLevel, str]] = ..., scene_id: _Optional[int] = ..., sub_scene_id: _Optional[int] = ..., business_goal_id: _Optional[int] = ..., business_goal_value: _Optional[str] = ..., campaign_case_id: _Optional[int] = ..., strategies: _Optional[_Iterable[_Union[Strategies, _Mapping]]] = ...) -> None: ...

class Strategies(_message.Message):
    __slots__ = ("type", "ids")
    class StrategyType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        PLAN: _ClassVar[Strategies.StrategyType]
        CANVAS: _ClassVar[Strategies.StrategyType]
        SECTION: _ClassVar[Strategies.StrategyType]
    PLAN: Strategies.StrategyType
    CANVAS: Strategies.StrategyType
    SECTION: Strategies.StrategyType
    TYPE_FIELD_NUMBER: _ClassVar[int]
    IDS_FIELD_NUMBER: _ClassVar[int]
    type: Strategies.StrategyType
    ids: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, type: _Optional[_Union[Strategies.StrategyType, str]] = ..., ids: _Optional[_Iterable[str]] = ...) -> None: ...

class PortalMessage(_message.Message):
    __slots__ = ("generator", "type", "text", "insight_strategy", "insight_goal", "analyse_idea", "capability")
    class MessageGenerator(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        SYSTEM: _ClassVar[PortalMessage.MessageGenerator]
        USER: _ClassVar[PortalMessage.MessageGenerator]
        BOT: _ClassVar[PortalMessage.MessageGenerator]
    SYSTEM: PortalMessage.MessageGenerator
    USER: PortalMessage.MessageGenerator
    BOT: PortalMessage.MessageGenerator
    class MessageType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        TEXT: _ClassVar[PortalMessage.MessageType]
        MARKDOWN: _ClassVar[PortalMessage.MessageType]
        TABLE: _ClassVar[PortalMessage.MessageType]
        CHART: _ClassVar[PortalMessage.MessageType]
        ENTITY_RELATION: _ClassVar[PortalMessage.MessageType]
        INSIGHT_STRATEGY: _ClassVar[PortalMessage.MessageType]
        INSIGHT_GOAL: _ClassVar[PortalMessage.MessageType]
        ANALYSE_IDEA: _ClassVar[PortalMessage.MessageType]
        RECOMMEND_QUESTION: _ClassVar[PortalMessage.MessageType]
    TEXT: PortalMessage.MessageType
    MARKDOWN: PortalMessage.MessageType
    TABLE: PortalMessage.MessageType
    CHART: PortalMessage.MessageType
    ENTITY_RELATION: PortalMessage.MessageType
    INSIGHT_STRATEGY: PortalMessage.MessageType
    INSIGHT_GOAL: PortalMessage.MessageType
    ANALYSE_IDEA: PortalMessage.MessageType
    RECOMMEND_QUESTION: PortalMessage.MessageType
    class Capability(_message.Message):
        __slots__ = ("retrieval_idea",)
        RETRIEVAL_IDEA_FIELD_NUMBER: _ClassVar[int]
        retrieval_idea: bool
        def __init__(self, retrieval_idea: bool = ...) -> None: ...
    GENERATOR_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    TEXT_FIELD_NUMBER: _ClassVar[int]
    INSIGHT_STRATEGY_FIELD_NUMBER: _ClassVar[int]
    INSIGHT_GOAL_FIELD_NUMBER: _ClassVar[int]
    ANALYSE_IDEA_FIELD_NUMBER: _ClassVar[int]
    CAPABILITY_FIELD_NUMBER: _ClassVar[int]
    generator: PortalMessage.MessageGenerator
    type: PortalMessage.MessageType
    text: str
    insight_strategy: InsightParams
    insight_goal: InsightParams
    analyse_idea: str
    capability: PortalMessage.Capability
    def __init__(self, generator: _Optional[_Union[PortalMessage.MessageGenerator, str]] = ..., type: _Optional[_Union[PortalMessage.MessageType, str]] = ..., text: _Optional[str] = ..., insight_strategy: _Optional[_Union[InsightParams, _Mapping]] = ..., insight_goal: _Optional[_Union[InsightParams, _Mapping]] = ..., analyse_idea: _Optional[str] = ..., capability: _Optional[_Union[PortalMessage.Capability, _Mapping]] = ...) -> None: ...

class GeneralPortalRequest(_message.Message):
    __slots__ = ("chat_session_info", "messages")
    CHAT_SESSION_INFO_FIELD_NUMBER: _ClassVar[int]
    MESSAGES_FIELD_NUMBER: _ClassVar[int]
    chat_session_info: _chat_session_pb2.ChatSessionInfo
    messages: _containers.RepeatedCompositeFieldContainer[PortalMessage]
    def __init__(self, chat_session_info: _Optional[_Union[_chat_session_pb2.ChatSessionInfo, _Mapping]] = ..., messages: _Optional[_Iterable[_Union[PortalMessage, _Mapping]]] = ...) -> None: ...
