# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: knowledge_base.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x14knowledge_base.proto\x12\x14sensorsdata.focus.v1"n\n\x11TenantProjectInfo\x12\x17\n\x0forganization_id\x18\x01 \x01(\t\x12\x16\n\x0esfn_project_id\x18\x02 \x01(\x05\x12\x12\n\nproject_id\x18\x03 \x01(\x05\x12\x14\n\x0cproject_name\x18\x04 \x01(\t"\x99\x04\n\x11KnowledgeBaseInfo\x12G\n\x04type\x18\x01 \x01(\x0e\x32\x39.sensorsdata.focus.v1.KnowledgeBaseInfo.KnowledgeBaseType\x12\x19\n\x11knowledge_base_id\x18\x02 \x01(\t\x12\x1b\n\x13knowledge_base_name\x18\x03 \x01(\t\x12\x0e\n\x06remark\x18\x04 \x01(\t\x12O\n\x08scenario\x18\x05 \x03(\x0e\x32=.sensorsdata.focus.v1.KnowledgeBaseInfo.KnowledgeBaseScenario\x12\x39\n\x06status\x18\x06 \x01(\x0e\x32).sensorsdata.focus.v1.KnowledgeBaseStatus\x12H\n\x0e\x63ontent_status\x18\x07 \x03(\x0b\x32\x30.sensorsdata.focus.v1.KnowledgeBaseContentStatus"=\n\x11KnowledgeBaseType\x12\n\n\x06\x43USTOM\x10\x00\x12\x0e\n\nSTRATEGIES\x10\x01\x12\x0c\n\x08METADATA\x10\x02"^\n\x15KnowledgeBaseScenario\x12\x0f\n\x0bSUPPLY_INFO\x10\x00\x12\x1e\n\x1aSTRATEGY_DESIGN_SUGGESTION\x10\x01\x12\x14\n\x10STRATEGY_INSIGHT\x10\x02"\xe2\x02\n\x1aKnowledgeBaseContentStatus\x12J\n\x04type\x18\x01 \x01(\x0e\x32<.sensorsdata.focus.v1.KnowledgeBaseContentStatus.ContentType\x12\x12\n\ncontent_id\x18\x02 \x01(\t\x12N\n\x06status\x18\x03 \x01(\x0e\x32>.sensorsdata.focus.v1.KnowledgeBaseContentStatus.ContentStatus"P\n\rContentStatus\x12\x0b\n\x07WAITING\x10\x00\x12\x0e\n\nPROCESSING\x10\x01\x12\n\n\x06\x46\x41ILED\x10\x02\x12\t\n\x05READY\x10\x03\x12\x0b\n\x07\x44\x45LETED\x10\x04"B\n\x0b\x43ontentType\x12\x0c\n\x08\x44OCUMENT\x10\x00\x12\x0b\n\x07WEBPAGE\x10\x01\x12\n\n\x06\x43USTOM\x10\x02\x12\x0c\n\x08STRATEGY\x10\x03"\xaa\x05\n\x16KnowledgeBaseResources\x12G\n\x04type\x18\x01 \x01(\x0e\x32\x39.sensorsdata.focus.v1.KnowledgeBaseResources.ResourceType\x12\x11\n\tfilenames\x18\x02 \x03(\t\x12\x45\n\x04urls\x18\x03 \x03(\x0b\x32\x37.sensorsdata.focus.v1.KnowledgeBaseResources.WebPageUrl\x12\x44\n\x04text\x18\x04 \x03(\x0b\x32\x36.sensorsdata.focus.v1.KnowledgeBaseResources.TextEntry\x1a\xc2\x02\n\nWebPageUrl\x12\x0b\n\x03url\x18\x01 \x01(\t\x12U\n\ncrawl_type\x18\x02 \x01(\x0e\x32\x41.sensorsdata.focus.v1.KnowledgeBaseResources.WebPageUrl.CrawlType\x12\x61\n\x10update_frequency\x18\x03 \x01(\x0e\x32G.sensorsdata.focus.v1.KnowledgeBaseResources.WebPageUrl.UpdateFrequency"+\n\tCrawlType\x12\x0f\n\x0bSINGLE_PAGE\x10\x00\x12\r\n\tRECURSIVE\x10\x01"@\n\x0fUpdateFrequency\x12\t\n\x05NEVER\x10\x00\x12\t\n\x05\x44\x41ILY\x10\x01\x12\n\n\x06WEEKLY\x10\x02\x12\x0b\n\x07MONTHLY\x10\x03\x1a+\n\tTextEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01"5\n\x0cResourceType\x12\x0c\n\x08\x44OCUMENT\x10\x00\x12\x0b\n\x07WEBPAGE\x10\x01\x12\n\n\x06\x43USTOM\x10\x02"\xa3\x01\n\x17KnowledgeBaseStrategies\x12H\n\x04type\x18\x01 \x01(\x0e\x32:.sensorsdata.focus.v1.KnowledgeBaseStrategies.StrategyType\x12\x0b\n\x03ids\x18\x02 \x03(\t"1\n\x0cStrategyType\x12\x08\n\x04PLAN\x10\x00\x12\n\n\x06\x43\x41NVAS\x10\x01\x12\x0b\n\x07SECTION\x10\x02"\x97\x01\n\x19\x42uildKnowledgeBaseRequest\x12=\n\x0cproject_info\x18\x01 \x01(\x0b\x32\'.sensorsdata.focus.v1.TenantProjectInfo\x12;\n\nbase_infos\x18\x02 \x03(\x0b\x32\'.sensorsdata.focus.v1.KnowledgeBaseInfo"\xa4\x02\n#OperateKnowledgeBaseResourceRequest\x12=\n\x0cproject_info\x18\x01 \x01(\x0b\x32\'.sensorsdata.focus.v1.TenantProjectInfo\x12:\n\tbase_info\x18\x02 \x01(\x0b\x32\'.sensorsdata.focus.v1.KnowledgeBaseInfo\x12?\n\tresources\x18\x03 \x03(\x0b\x32,.sensorsdata.focus.v1.KnowledgeBaseResources\x12\x41\n\nstrategies\x18\x04 \x03(\x0b\x32-.sensorsdata.focus.v1.KnowledgeBaseStrategies"\xbe\x01\n\x1eUploadKnowledgeBaseFileRequest\x12=\n\x0cproject_info\x18\x01 \x01(\x0b\x32\'.sensorsdata.focus.v1.TenantProjectInfo\x12:\n\tbase_info\x18\x02 \x01(\x0b\x32\'.sensorsdata.focus.v1.KnowledgeBaseInfo\x12\x10\n\x08\x66ilename\x18\x08 \x01(\t\x12\x0f\n\x07\x63ontent\x18\t \x01(\x0c"\x97\x01\n\x1a\x44\x65leteKnowledgeBaseRequest\x12=\n\x0cproject_info\x18\x01 \x01(\x0b\x32\'.sensorsdata.focus.v1.TenantProjectInfo\x12:\n\tbase_info\x18\x02 \x01(\x0b\x32\'.sensorsdata.focus.v1.KnowledgeBaseInfo"\xb5\x01\n\x19QueryKnowledgeBaseRequest\x12=\n\x0cproject_info\x18\x01 \x01(\x0b\x32\'.sensorsdata.focus.v1.TenantProjectInfo\x12;\n\nbase_infos\x18\x02 \x03(\x0b\x32\'.sensorsdata.focus.v1.KnowledgeBaseInfo\x12\x1c\n\x14query_content_status\x18\x03 \x01(\x08"T\n\x15KnowledgeBaseResponse\x12;\n\nbase_infos\x18\x01 \x03(\x0b\x32\'.sensorsdata.focus.v1.KnowledgeBaseInfo*R\n\x13KnowledgeBaseStatus\x12\x0b\n\x07WAITING\x10\x00\x12\x0c\n\x08\x42UILDING\x10\x01\x12\n\n\x06\x46\x41ILED\x10\x03\x12\t\n\x05READY\x10\x02\x12\t\n\x05\x45MPTY\x10\x05\x32\xfc\x06\n\x14KnowledgeBaseService\x12r\n\x12\x42uildKnowledgeBase\x12/.sensorsdata.focus.v1.BuildKnowledgeBaseRequest\x1a+.sensorsdata.focus.v1.KnowledgeBaseResponse\x12s\n\x13UpdateKnowledgeBase\x12/.sensorsdata.focus.v1.BuildKnowledgeBaseRequest\x1a+.sensorsdata.focus.v1.KnowledgeBaseResponse\x12~\n\x17UploadKnowledgeBaseFile\x12\x34.sensorsdata.focus.v1.UploadKnowledgeBaseFileRequest\x1a+.sensorsdata.focus.v1.KnowledgeBaseResponse(\x01\x12\x82\x01\n\x18\x41\x64\x64KnowledgeBaseResource\x12\x39.sensorsdata.focus.v1.OperateKnowledgeBaseResourceRequest\x1a+.sensorsdata.focus.v1.KnowledgeBaseResponse\x12\x85\x01\n\x1b\x44\x65leteKnowledgeBaseResource\x12\x39.sensorsdata.focus.v1.OperateKnowledgeBaseResourceRequest\x1a+.sensorsdata.focus.v1.KnowledgeBaseResponse\x12t\n\x13\x44\x65leteKnowledgeBase\x12\x30.sensorsdata.focus.v1.DeleteKnowledgeBaseRequest\x1a+.sensorsdata.focus.v1.KnowledgeBaseResponse\x12x\n\x18QueryKnowledgeBaseStatus\x12/.sensorsdata.focus.v1.QueryKnowledgeBaseRequest\x1a+.sensorsdata.focus.v1.KnowledgeBaseResponseB\x1f\n\x1b\x63om.sensorsdata.focus.v1.aiP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "knowledge_base_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    _globals["DESCRIPTOR"]._options = None
    _globals["DESCRIPTOR"]._serialized_options = (
        b"\n\033com.sensorsdata.focus.v1.aiP\001"
    )
    _globals["_KNOWLEDGEBASERESOURCES_TEXTENTRY"]._options = None
    _globals["_KNOWLEDGEBASERESOURCES_TEXTENTRY"]._serialized_options = b"8\001"
    _globals["_KNOWLEDGEBASESTATUS"]._serialized_start = 2972
    _globals["_KNOWLEDGEBASESTATUS"]._serialized_end = 3054
    _globals["_TENANTPROJECTINFO"]._serialized_start = 46
    _globals["_TENANTPROJECTINFO"]._serialized_end = 156
    _globals["_KNOWLEDGEBASEINFO"]._serialized_start = 159
    _globals["_KNOWLEDGEBASEINFO"]._serialized_end = 696
    _globals["_KNOWLEDGEBASEINFO_KNOWLEDGEBASETYPE"]._serialized_start = 539
    _globals["_KNOWLEDGEBASEINFO_KNOWLEDGEBASETYPE"]._serialized_end = 600
    _globals["_KNOWLEDGEBASEINFO_KNOWLEDGEBASESCENARIO"]._serialized_start = 602
    _globals["_KNOWLEDGEBASEINFO_KNOWLEDGEBASESCENARIO"]._serialized_end = 696
    _globals["_KNOWLEDGEBASECONTENTSTATUS"]._serialized_start = 699
    _globals["_KNOWLEDGEBASECONTENTSTATUS"]._serialized_end = 1053
    _globals["_KNOWLEDGEBASECONTENTSTATUS_CONTENTSTATUS"]._serialized_start = 905
    _globals["_KNOWLEDGEBASECONTENTSTATUS_CONTENTSTATUS"]._serialized_end = 985
    _globals["_KNOWLEDGEBASECONTENTSTATUS_CONTENTTYPE"]._serialized_start = 987
    _globals["_KNOWLEDGEBASECONTENTSTATUS_CONTENTTYPE"]._serialized_end = 1053
    _globals["_KNOWLEDGEBASERESOURCES"]._serialized_start = 1056
    _globals["_KNOWLEDGEBASERESOURCES"]._serialized_end = 1738
    _globals["_KNOWLEDGEBASERESOURCES_WEBPAGEURL"]._serialized_start = 1316
    _globals["_KNOWLEDGEBASERESOURCES_WEBPAGEURL"]._serialized_end = 1638
    _globals["_KNOWLEDGEBASERESOURCES_WEBPAGEURL_CRAWLTYPE"]._serialized_start = 1529
    _globals["_KNOWLEDGEBASERESOURCES_WEBPAGEURL_CRAWLTYPE"]._serialized_end = 1572
    _globals["_KNOWLEDGEBASERESOURCES_WEBPAGEURL_UPDATEFREQUENCY"]._serialized_start = (
        1574
    )
    _globals["_KNOWLEDGEBASERESOURCES_WEBPAGEURL_UPDATEFREQUENCY"]._serialized_end = (
        1638
    )
    _globals["_KNOWLEDGEBASERESOURCES_TEXTENTRY"]._serialized_start = 1640
    _globals["_KNOWLEDGEBASERESOURCES_TEXTENTRY"]._serialized_end = 1683
    _globals["_KNOWLEDGEBASERESOURCES_RESOURCETYPE"]._serialized_start = 1685
    _globals["_KNOWLEDGEBASERESOURCES_RESOURCETYPE"]._serialized_end = 1738
    _globals["_KNOWLEDGEBASESTRATEGIES"]._serialized_start = 1741
    _globals["_KNOWLEDGEBASESTRATEGIES"]._serialized_end = 1904
    _globals["_KNOWLEDGEBASESTRATEGIES_STRATEGYTYPE"]._serialized_start = 1855
    _globals["_KNOWLEDGEBASESTRATEGIES_STRATEGYTYPE"]._serialized_end = 1904
    _globals["_BUILDKNOWLEDGEBASEREQUEST"]._serialized_start = 1907
    _globals["_BUILDKNOWLEDGEBASEREQUEST"]._serialized_end = 2058
    _globals["_OPERATEKNOWLEDGEBASERESOURCEREQUEST"]._serialized_start = 2061
    _globals["_OPERATEKNOWLEDGEBASERESOURCEREQUEST"]._serialized_end = 2353
    _globals["_UPLOADKNOWLEDGEBASEFILEREQUEST"]._serialized_start = 2356
    _globals["_UPLOADKNOWLEDGEBASEFILEREQUEST"]._serialized_end = 2546
    _globals["_DELETEKNOWLEDGEBASEREQUEST"]._serialized_start = 2549
    _globals["_DELETEKNOWLEDGEBASEREQUEST"]._serialized_end = 2700
    _globals["_QUERYKNOWLEDGEBASEREQUEST"]._serialized_start = 2703
    _globals["_QUERYKNOWLEDGEBASEREQUEST"]._serialized_end = 2884
    _globals["_KNOWLEDGEBASERESPONSE"]._serialized_start = 2886
    _globals["_KNOWLEDGEBASERESPONSE"]._serialized_end = 2970
    _globals["_KNOWLEDGEBASESERVICE"]._serialized_start = 3057
    _globals["_KNOWLEDGEBASESERVICE"]._serialized_end = 3949
# @@protoc_insertion_point(module_scope)
