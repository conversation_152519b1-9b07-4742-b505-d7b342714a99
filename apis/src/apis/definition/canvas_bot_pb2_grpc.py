# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import canvas_bot_pb2 as canvas__bot__pb2
import stream_event_pb2 as stream__event__pb2


class CanvasBotServiceStub(object):
    """{_description_} 画布智能体 API 服务"""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Dispatch = channel.unary_stream(
            "/sensorsdata.focus.v1.CanvasBotService/Dispatch",
            request_serializer=canvas__bot__pb2.GeneralCanvasRequest.SerializeToString,
            response_deserializer=stream__event__pb2.StreamEvent.FromString,
        )
        self.DesignCanvas = channel.unary_stream(
            "/sensorsdata.focus.v1.CanvasBotService/DesignCanvas",
            request_serializer=canvas__bot__pb2.GeneralCanvasRequest.SerializeToString,
            response_deserializer=stream__event__pb2.StreamEvent.FromString,
        )
        self.AdjustCanvas = channel.unary_stream(
            "/sensorsdata.focus.v1.CanvasBotService/AdjustCanvas",
            request_serializer=canvas__bot__pb2.GeneralCanvasRequest.SerializeToString,
            response_deserializer=stream__event__pb2.StreamEvent.FromString,
        )
        self.GenerateCanvas = channel.unary_stream(
            "/sensorsdata.focus.v1.CanvasBotService/GenerateCanvas",
            request_serializer=canvas__bot__pb2.GeneralCanvasRequest.SerializeToString,
            response_deserializer=stream__event__pb2.StreamEvent.FromString,
        )
        self.OptimizeCanvas = channel.unary_stream(
            "/sensorsdata.focus.v1.CanvasBotService/OptimizeCanvas",
            request_serializer=canvas__bot__pb2.GeneralCanvasRequest.SerializeToString,
            response_deserializer=stream__event__pb2.StreamEvent.FromString,
        )
        self.InsightCanvas = channel.unary_stream(
            "/sensorsdata.focus.v1.CanvasBotService/InsightCanvas",
            request_serializer=canvas__bot__pb2.GeneralCanvasRequest.SerializeToString,
            response_deserializer=stream__event__pb2.StreamEvent.FromString,
        )
        self.RetrieveCanvas = channel.unary_stream(
            "/sensorsdata.focus.v1.CanvasBotService/RetrieveCanvas",
            request_serializer=canvas__bot__pb2.GeneralCanvasRequest.SerializeToString,
            response_deserializer=stream__event__pb2.StreamEvent.FromString,
        )
        self.PredictCanvas = channel.unary_stream(
            "/sensorsdata.focus.v1.CanvasBotService/PredictCanvas",
            request_serializer=canvas__bot__pb2.GeneralCanvasRequest.SerializeToString,
            response_deserializer=stream__event__pb2.StreamEvent.FromString,
        )


class CanvasBotServiceServicer(object):
    """{_description_} 画布智能体 API 服务"""

    def Dispatch(self, request, context):
        """{_summary_} 通用接口 (识别意图 & 分发服务)"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def DesignCanvas(self, request, context):
        """{_summary_} 设计画布"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def AdjustCanvas(self, request, context):
        """{_summary_} 调整画布"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GenerateCanvas(self, request, context):
        """{_summary_} 生成画布"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def OptimizeCanvas(self, request, context):
        """{_summary_} 优化画布"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def InsightCanvas(self, request, context):
        """{_summary_} 数据解读"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def RetrieveCanvas(self, request, context):
        """{_summary_} 画布检索"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def PredictCanvas(self, request, context):
        """{_summary_} 效果预测"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")


def add_CanvasBotServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
        "Dispatch": grpc.unary_stream_rpc_method_handler(
            servicer.Dispatch,
            request_deserializer=canvas__bot__pb2.GeneralCanvasRequest.FromString,
            response_serializer=stream__event__pb2.StreamEvent.SerializeToString,
        ),
        "DesignCanvas": grpc.unary_stream_rpc_method_handler(
            servicer.DesignCanvas,
            request_deserializer=canvas__bot__pb2.GeneralCanvasRequest.FromString,
            response_serializer=stream__event__pb2.StreamEvent.SerializeToString,
        ),
        "AdjustCanvas": grpc.unary_stream_rpc_method_handler(
            servicer.AdjustCanvas,
            request_deserializer=canvas__bot__pb2.GeneralCanvasRequest.FromString,
            response_serializer=stream__event__pb2.StreamEvent.SerializeToString,
        ),
        "GenerateCanvas": grpc.unary_stream_rpc_method_handler(
            servicer.GenerateCanvas,
            request_deserializer=canvas__bot__pb2.GeneralCanvasRequest.FromString,
            response_serializer=stream__event__pb2.StreamEvent.SerializeToString,
        ),
        "OptimizeCanvas": grpc.unary_stream_rpc_method_handler(
            servicer.OptimizeCanvas,
            request_deserializer=canvas__bot__pb2.GeneralCanvasRequest.FromString,
            response_serializer=stream__event__pb2.StreamEvent.SerializeToString,
        ),
        "InsightCanvas": grpc.unary_stream_rpc_method_handler(
            servicer.InsightCanvas,
            request_deserializer=canvas__bot__pb2.GeneralCanvasRequest.FromString,
            response_serializer=stream__event__pb2.StreamEvent.SerializeToString,
        ),
        "RetrieveCanvas": grpc.unary_stream_rpc_method_handler(
            servicer.RetrieveCanvas,
            request_deserializer=canvas__bot__pb2.GeneralCanvasRequest.FromString,
            response_serializer=stream__event__pb2.StreamEvent.SerializeToString,
        ),
        "PredictCanvas": grpc.unary_stream_rpc_method_handler(
            servicer.PredictCanvas,
            request_deserializer=canvas__bot__pb2.GeneralCanvasRequest.FromString,
            response_serializer=stream__event__pb2.StreamEvent.SerializeToString,
        ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
        "sensorsdata.focus.v1.CanvasBotService", rpc_method_handlers
    )
    server.add_generic_rpc_handlers((generic_handler,))


# This class is part of an EXPERIMENTAL API.
class CanvasBotService(object):
    """{_description_} 画布智能体 API 服务"""

    @staticmethod
    def Dispatch(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_stream(
            request,
            target,
            "/sensorsdata.focus.v1.CanvasBotService/Dispatch",
            canvas__bot__pb2.GeneralCanvasRequest.SerializeToString,
            stream__event__pb2.StreamEvent.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def DesignCanvas(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_stream(
            request,
            target,
            "/sensorsdata.focus.v1.CanvasBotService/DesignCanvas",
            canvas__bot__pb2.GeneralCanvasRequest.SerializeToString,
            stream__event__pb2.StreamEvent.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def AdjustCanvas(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_stream(
            request,
            target,
            "/sensorsdata.focus.v1.CanvasBotService/AdjustCanvas",
            canvas__bot__pb2.GeneralCanvasRequest.SerializeToString,
            stream__event__pb2.StreamEvent.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GenerateCanvas(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_stream(
            request,
            target,
            "/sensorsdata.focus.v1.CanvasBotService/GenerateCanvas",
            canvas__bot__pb2.GeneralCanvasRequest.SerializeToString,
            stream__event__pb2.StreamEvent.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def OptimizeCanvas(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_stream(
            request,
            target,
            "/sensorsdata.focus.v1.CanvasBotService/OptimizeCanvas",
            canvas__bot__pb2.GeneralCanvasRequest.SerializeToString,
            stream__event__pb2.StreamEvent.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def InsightCanvas(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_stream(
            request,
            target,
            "/sensorsdata.focus.v1.CanvasBotService/InsightCanvas",
            canvas__bot__pb2.GeneralCanvasRequest.SerializeToString,
            stream__event__pb2.StreamEvent.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def RetrieveCanvas(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_stream(
            request,
            target,
            "/sensorsdata.focus.v1.CanvasBotService/RetrieveCanvas",
            canvas__bot__pb2.GeneralCanvasRequest.SerializeToString,
            stream__event__pb2.StreamEvent.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def PredictCanvas(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_stream(
            request,
            target,
            "/sensorsdata.focus.v1.CanvasBotService/PredictCanvas",
            canvas__bot__pb2.GeneralCanvasRequest.SerializeToString,
            stream__event__pb2.StreamEvent.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )
