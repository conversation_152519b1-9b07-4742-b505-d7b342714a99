# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: canvas_draft.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from sensors import audience_pb2 as sensors_dot_audience__pb2
from sensors import time_range_pb2 as sensors_dot_time__range__pb2
from sensors import condition_pb2 as sensors_dot_condition__pb2
from sensors import aggregation_pb2 as sensors_dot_aggregation__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x12\x63\x61nvas_draft.proto\x12\x14sensorsdata.focus.v1\x1a\x16sensors/audience.proto\x1a\x18sensors/time_range.proto\x1a\x17sensors/condition.proto\x1a\x19sensors/aggregation.proto"\xf4\x03\n\x0b\x43\x61nvasDraft\x12\r\n\x05\x63name\x18\x01 \x01(\t\x12\x0c\n\x04\x64\x65sc\x18\x02 \x01(\t\x12:\n\x11\x61\x63tive_time_range\x18\x03 \x01(\x0b\x32\x1f.sensorsdata.focus.v1.TimeRange\x12\x36\n\rfinished_time\x18\x04 \x01(\x0b\x32\x1f.sensorsdata.focus.v1.TimePoint\x12\x35\n\x08re_enter\x18\x05 \x01(\x0b\x32#.sensorsdata.focus.v1.ReEnterConfig\x12\x1d\n\x15\x65nable_global_control\x18\x06 \x01(\x08\x12\x37\n\x0e\x63omponent_list\x18\x07 \x03(\x0b\x32\x1f.sensorsdata.focus.v1.Component\x12@\n\x13\x63omponent_edge_list\x18\x08 \x03(\x0b\x32#.sensorsdata.focus.v1.ComponentEdge\x12@\n\x0e\x64o_not_disturb\x18\t \x01(\x0b\x32(.sensorsdata.focus.v1.DoNotDisturbConfig\x12\x41\n\x0e\x63onvert_target\x18\n \x01(\x0b\x32).sensorsdata.focus.v1.ConvertTargetConfig"\xaa\x02\n\rReEnterConfig\x12\x11\n\tunlimited\x18\x03 \x01(\x08\x12M\n\x10reentry_strategy\x18\x01 \x01(\x0e\x32\x33.sensorsdata.focus.v1.ReEnterConfig.ReEntryStrategy\x12\x43\n\rreentry_limit\x18\x02 \x01(\x0b\x32,.sensorsdata.focus.v1.SlidingWindowWithLimit"r\n\x0fReEntryStrategy\x12\x1e\n\x1a\x45NTRY_STRATEGY_UNSPECIFIED\x10\x00\x12\x0e\n\nREENTRY_NO\x10\x01\x12\x14\n\x10REENTRY_ANY_TIME\x10\x02\x12\x19\n\x15REENTRY_AFTER_EXITING\x10\x03"[\n\x16SlidingWindowWithLimit\x12\x32\n\x06window\x18\x01 \x01(\x0b\x32".sensorsdata.focus.v1.TimeInterval\x12\r\n\x05limit\x18\x02 \x01(\x05"\x9d\x05\n\x12\x44oNotDisturbConfig\x12\x1d\n\x15\x65nable_do_not_disturb\x18\x01 \x01(\x08\x12J\n\x0crepeat_cycle\x18\x03 \x01(\x0e\x32\x34.sensorsdata.focus.v1.DoNotDisturbConfig.RepeatCycle\x12L\n\tunit_list\x18\x04 \x03(\x0b\x32\x39.sensorsdata.focus.v1.DoNotDisturbConfig.DoNotDisturbUnit\x12P\n\x0f\x61\x63tion_strategy\x18\x02 \x01(\x0e\x32\x37.sensorsdata.focus.v1.DoNotDisturbConfig.ActionStrategy\x1a\xc3\x01\n\x10\x44oNotDisturbUnit\x12\x0b\n\x03\x64\x61y\x18\x01 \x01(\x05\x12\x63\n\ntime_range\x18\x02 \x01(\x0b\x32O.sensorsdata.focus.v1.DoNotDisturbConfig.DoNotDisturbUnit.DoNotDisturbTimeRange\x1a=\n\x15\x44oNotDisturbTimeRange\x12\x12\n\nstart_time\x18\x01 \x01(\t\x12\x10\n\x08\x65nd_time\x18\x02 \x01(\t"_\n\x0e\x41\x63tionStrategy\x12\x1f\n\x1b\x41\x43TION_STRATEGY_UNSPECIFIED\x10\x00\x12\x08\n\x04\x44ROP\x10\x01\x12\t\n\x05\x44\x45LAY\x10\x02\x12\x17\n\x13\x44ROP_AND_NOT_FINISH\x10\x03"U\n\x0bRepeatCycle\x12\x1c\n\x18REPEAT_CYCLE_UNSPECIFIED\x10\x00\x12\x07\n\x03\x44\x41Y\x10\x01\x12\x08\n\x04WEEK\x10\x02\x12\t\n\x05MONTH\x10\x03\x12\n\n\x06\x43USTOM\x10\x04"\xa3\x02\n\x13\x43onvertTargetConfig\x12P\n\x11\x63onvert_rule_list\x18\x01 \x03(\x0b\x32\x35.sensorsdata.focus.v1.ConvertTargetConfig.ConvertRule\x1a\xb9\x01\n\x0b\x43onvertRule\x12\x12\n\nconvert_id\x18\x01 \x01(\x05\x12\x14\n\x0c\x63onvert_name\x18\x02 \x01(\t\x12\x19\n\x11\x65nd_after_convert\x18\x03 \x01(\x08\x12\x38\n\x0e\x63onvert_window\x18\x04 \x01(\x0b\x32 .sensorsdata.focus.v1.TimeWindow\x12+\n\x0fmatch_rule_list\x18\x05 \x01(\x0b\x32\x12.AudienceEventRule"\xc2\x02\n\rComponentEdge\x12<\n\x05style\x18\x01 \x01(\x0e\x32-.sensorsdata.focus.v1.ComponentEdge.EdgeStyle\x12J\n\x11\x65ntry_source_node\x18\x02 \x01(\x0e\x32/.sensorsdata.focus.v1.ComponentEdge.EdgePassWay\x12\x1b\n\x13source_component_id\x18\x03 \x01(\x05\x12\x1b\n\x13target_component_id\x18\x04 \x01(\x05"D\n\tEdgeStyle\x12\x1a\n\x16\x45\x44GE_STYLE_UNSPECIFIED\x10\x00\x12\r\n\tDIRECTION\x10\x01\x12\x0c\n\x08RELATION\x10\x02"\'\n\x0b\x45\x64gePassWay\x12\x07\n\x03\x41LL\x10\x00\x12\x07\n\x03YES\x10\x01\x12\x06\n\x02NO\x10\x02"\xe5\x03\n\tComponent\x12\x14\n\x0c\x63omponent_id\x18\x01 \x01(\x05\x12\r\n\x05\x63name\x18\x02 \x01(\t\x12\x45\n\x0e\x63omponent_type\x18\x03 \x01(\x0e\x32-.sensorsdata.focus.v1.Component.ComponentType\x12=\n\x0f\x65ntry_component\x18\x04 \x01(\x0b\x32$.sensorsdata.focus.v1.EntryComponent\x12\x45\n\x13\x63ondition_component\x18\x05 \x01(\x0b\x32(.sensorsdata.focus.v1.ConditionComponent\x12?\n\x10\x61\x63tion_component\x18\x06 \x01(\x0b\x32%.sensorsdata.focus.v1.ActionComponent\x12\x41\n\x11\x63ontrol_component\x18\x07 \x01(\x0b\x32&.sensorsdata.focus.v1.ControlComponent"b\n\rComponentType\x12\x1e\n\x1a\x43OMPONENT_TYPE_UNSPECIFIED\x10\x00\x12\t\n\x05\x45NTRY\x10\x01\x12\r\n\tCONDITION\x10\x02\x12\n\n\x06\x41\x43TION\x10\x03\x12\x0b\n\x07\x43ONTROL\x10\x04"\x9f\x03\n\x0e\x45ntryComponent\x12K\n\nentry_type\x18\x01 \x01(\x0e\x32\x37.sensorsdata.focus.v1.EntryComponent.EntryComponentType\x12\x42\n\raudience_rule\x18\x02 \x01(\x0b\x32+.sensorsdata.focus.v1.AudienceComponentRule\x12?\n\rdo_entry_rule\x18\x03 \x01(\x0b\x32(.sensorsdata.focus.v1.EventComponentRule\x12\x43\n\x11not_do_entry_rule\x18\x04 \x01(\x0b\x32(.sensorsdata.focus.v1.EventComponentRule"v\n\x12\x45ntryComponentType\x12\x1a\n\x16\x45NTRY_TYPE_UNSPECIFIED\x10\x00\x12\x0e\n\nFIXED_TIME\x10\x01\x12\x0b\n\x07ROUTINE\x10\x02\x12\x12\n\x0eTRIGGER_ONLY_A\x10\x03\x12\x13\n\x0fTRIGGER_A_NOT_B\x10\x04"\xac\x02\n\x15\x41udienceComponentRule\x12O\n\raudience_type\x18\x01 \x01(\x0e\x32\x38.sensorsdata.focus.v1.AudienceComponentRule.AudienceType\x12\x33\n\nfixed_time\x18\x02 \x01(\x0b\x32\x1f.sensorsdata.focus.v1.TimePoint\x12\x0c\n\x04\x63ron\x18\x03 \x01(\t\x12$\n\raudience_rule\x18\x04 \x01(\x0b\x32\r.AudienceRule"Y\n\x0c\x41udienceType\x12\x1d\n\x19\x41UDIENCE_TYPE_UNSPECIFIED\x10\x00\x12\x14\n\x10\x41UDIENCE_ONETIME\x10\x01\x12\x14\n\x10\x41UDIENCE_ROUTINE\x10\x02"t\n\x12\x45ventComponentRule\x12\x36\n\x0cmatch_window\x18\x01 \x01(\x0b\x32 .sensorsdata.focus.v1.TimeWindow\x12&\n\nevent_rule\x18\x02 \x01(\x0b\x32\x12.AudienceEventRule"\xc2\x01\n\x11\x45ventPropertyRule\x12Y\n\x15\x65vent_property_params\x18\x01 \x03(\x0b\x32:.sensorsdata.focus.v1.EventPropertyRule.EventPropertyParam\x1aR\n\x12\x45ventPropertyParam\x12\n\n\x02id\x18\x01 \x01(\x05\x12\x12\n\nevent_name\x18\x02 \x01(\t\x12\x1c\n\x14\x65vent_property_field\x18\x03 \x01(\t"\xeb\x03\n\x12\x43onditionComponent\x12N\n\x0e\x63ondition_type\x18\x01 \x01(\x0e\x32\x36.sensorsdata.focus.v1.ConditionComponent.ConditionType\x12\x42\n\raudience_rule\x18\x02 \x01(\x0b\x32+.sensorsdata.focus.v1.AudienceComponentRule\x12<\n\nevent_rule\x18\x03 \x01(\x0b\x32(.sensorsdata.focus.v1.EventComponentRule\x12\x44\n\x13\x65vent_property_rule\x18\x04 \x01(\x0b\x32\'.sensorsdata.focus.v1.EventPropertyRule\x12\x19\n\x11\x65nable_send_event\x18\x05 \x01(\x08"\xa1\x01\n\rConditionType\x12\x1e\n\x1a\x43ONDITION_TYPE_UNSPECIFIED\x10\x00\x12\x13\n\x0f\x43ONDITION_EVENT\x10\x01\x12\x1e\n\x1a\x43ONDITION_EVENT_PROPERTIES\x10\x02\x12\x1c\n\x18\x43ONDITION_AUDIENCE_BATCH\x10\x03\x12\x1d\n\x19\x43ONDITION_AUDIENCE_STREAM\x10\x04"&\n\x0f\x41\x63tionComponent\x12\x13\n\x0b\x61\x63tion_desc\x18\x01 \x01(\t"\xf4\x04\n\x10\x43ontrolComponent\x12H\n\x0c\x63ontrol_type\x18\x01 \x01(\x0e\x32\x32.sensorsdata.focus.v1.ControlComponent.ControlType\x12;\n\x08splitter\x18\x02 \x01(\x0b\x32).sensorsdata.focus.v1.SplitterControlRule\x12G\n\x0epercent_branch\x18\x03 \x01(\x0b\x32/.sensorsdata.focus.v1.PercentSplitterBranchRule\x12\x41\n\x0btime_branch\x18\x04 \x01(\x0b\x32,.sensorsdata.focus.v1.TimeSplitterBranchRule\x12\x35\n\x05\x64\x65lay\x18\x05 \x01(\x0b\x32&.sensorsdata.focus.v1.DelayControlRule\x12\x19\n\x11\x65nable_send_event\x18\x06 \x01(\x08"\xfa\x01\n\x0b\x43ontrolType\x12\x1c\n\x18\x43ONTROL_TYPE_UNSPECIFIED\x10\x00\x12\x15\n\x11\x41UDIENCE_SPLITTER\x10\x01\x12\x12\n\x0e\x45VENT_SPLITTER\x10\x02\x12\x1d\n\x19\x45VENT_PROPERTIES_SPLITTER\x10\x03\x12\x14\n\x10PERCENT_SPLITTER\x10\x04\x12\x1b\n\x17PERCENT_SPLITTER_BRANCH\x10\x05\x12\x11\n\rTIME_SPLITTER\x10\x06\x12\x18\n\x14TIME_SPLITTER_BRANCH\x10\x07\x12\x0f\n\x0b\x45LSE_BRANCH\x10\x08\x12\t\n\x05\x44\x45LAY\x10\t\x12\x07\n\x03\x45ND\x10\n"(\n\x13SplitterControlRule\x12\x11\n\tab_enable\x18\x01 \x01(\x08"\xc6\x01\n\x19PercentSplitterBranchRule\x12X\n\x10\x61\x62_strategy_type\x18\x02 \x01(\x0e\x32>.sensorsdata.focus.v1.PercentSplitterBranchRule.AbStrategyType\x12\x0f\n\x07percent\x18\x01 \x01(\x05">\n\x0e\x41\x62StrategyType\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\x0b\n\x07\x43ONTROL\x10\x01\x12\x0e\n\nEXPERIMENT\x10\x02"\xfa\x01\n\x16TimeSplitterBranchRule\x12Y\n\x12time_splitter_type\x18\x01 \x01(\x0e\x32=.sensorsdata.focus.v1.TimeSplitterBranchRule.TimeSplitterType\x12\x12\n\nstart_time\x18\x02 \x01(\t\x12\x10\n\x08\x65nd_time\x18\x03 \x01(\t\x12\x0c\n\x04\x64\x61ys\x18\x04 \x03(\x05"Q\n\x10TimeSplitterType\x12\n\n\x06STATIC\x10\x00\x12\x0e\n\nPERIOD_DAY\x10\x01\x12\x0f\n\x0bPERIOD_WEEK\x10\x02\x12\x10\n\x0cPERIOD_MONTH\x10\x03"\x94\x02\n\x10\x44\x65layControlRule\x12\x35\n\x0c\x64\x65lay_config\x18\x01 \x01(\x0b\x32\x1f.sensorsdata.focus.v1.TimePoint\x12O\n\x10\x64\x65lay_end_action\x18\x02 \x01(\x0e\x32\x35.sensorsdata.focus.v1.DelayControlRule.DelayEndAction"x\n\x0e\x44\x65layEndAction\x12 \n\x1c\x44\x45LAY_END_ACTION_UNSPECIFIED\x10\x00\x12\x19\n\x15WAIT_UNTIL_NEXT_CYCLE\x10\x01\x12\x10\n\x0c\x45XIT_JOURNEY\x10\x02\x12\x17\n\x13\x44IRECT_TO_NEXT_NODE\x10\x03\x42\x1f\n\x1b\x63om.sensorsdata.focus.v1.aiP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "canvas_draft_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    _globals["DESCRIPTOR"]._options = None
    _globals["DESCRIPTOR"]._serialized_options = (
        b"\n\033com.sensorsdata.focus.v1.aiP\001"
    )
    _globals["_CANVASDRAFT"]._serialized_start = 147
    _globals["_CANVASDRAFT"]._serialized_end = 647
    _globals["_REENTERCONFIG"]._serialized_start = 650
    _globals["_REENTERCONFIG"]._serialized_end = 948
    _globals["_REENTERCONFIG_REENTRYSTRATEGY"]._serialized_start = 834
    _globals["_REENTERCONFIG_REENTRYSTRATEGY"]._serialized_end = 948
    _globals["_SLIDINGWINDOWWITHLIMIT"]._serialized_start = 950
    _globals["_SLIDINGWINDOWWITHLIMIT"]._serialized_end = 1041
    _globals["_DONOTDISTURBCONFIG"]._serialized_start = 1044
    _globals["_DONOTDISTURBCONFIG"]._serialized_end = 1713
    _globals["_DONOTDISTURBCONFIG_DONOTDISTURBUNIT"]._serialized_start = 1334
    _globals["_DONOTDISTURBCONFIG_DONOTDISTURBUNIT"]._serialized_end = 1529
    _globals[
        "_DONOTDISTURBCONFIG_DONOTDISTURBUNIT_DONOTDISTURBTIMERANGE"
    ]._serialized_start = 1468
    _globals[
        "_DONOTDISTURBCONFIG_DONOTDISTURBUNIT_DONOTDISTURBTIMERANGE"
    ]._serialized_end = 1529
    _globals["_DONOTDISTURBCONFIG_ACTIONSTRATEGY"]._serialized_start = 1531
    _globals["_DONOTDISTURBCONFIG_ACTIONSTRATEGY"]._serialized_end = 1626
    _globals["_DONOTDISTURBCONFIG_REPEATCYCLE"]._serialized_start = 1628
    _globals["_DONOTDISTURBCONFIG_REPEATCYCLE"]._serialized_end = 1713
    _globals["_CONVERTTARGETCONFIG"]._serialized_start = 1716
    _globals["_CONVERTTARGETCONFIG"]._serialized_end = 2007
    _globals["_CONVERTTARGETCONFIG_CONVERTRULE"]._serialized_start = 1822
    _globals["_CONVERTTARGETCONFIG_CONVERTRULE"]._serialized_end = 2007
    _globals["_COMPONENTEDGE"]._serialized_start = 2010
    _globals["_COMPONENTEDGE"]._serialized_end = 2332
    _globals["_COMPONENTEDGE_EDGESTYLE"]._serialized_start = 2223
    _globals["_COMPONENTEDGE_EDGESTYLE"]._serialized_end = 2291
    _globals["_COMPONENTEDGE_EDGEPASSWAY"]._serialized_start = 2293
    _globals["_COMPONENTEDGE_EDGEPASSWAY"]._serialized_end = 2332
    _globals["_COMPONENT"]._serialized_start = 2335
    _globals["_COMPONENT"]._serialized_end = 2820
    _globals["_COMPONENT_COMPONENTTYPE"]._serialized_start = 2722
    _globals["_COMPONENT_COMPONENTTYPE"]._serialized_end = 2820
    _globals["_ENTRYCOMPONENT"]._serialized_start = 2823
    _globals["_ENTRYCOMPONENT"]._serialized_end = 3238
    _globals["_ENTRYCOMPONENT_ENTRYCOMPONENTTYPE"]._serialized_start = 3120
    _globals["_ENTRYCOMPONENT_ENTRYCOMPONENTTYPE"]._serialized_end = 3238
    _globals["_AUDIENCECOMPONENTRULE"]._serialized_start = 3241
    _globals["_AUDIENCECOMPONENTRULE"]._serialized_end = 3541
    _globals["_AUDIENCECOMPONENTRULE_AUDIENCETYPE"]._serialized_start = 3452
    _globals["_AUDIENCECOMPONENTRULE_AUDIENCETYPE"]._serialized_end = 3541
    _globals["_EVENTCOMPONENTRULE"]._serialized_start = 3543
    _globals["_EVENTCOMPONENTRULE"]._serialized_end = 3659
    _globals["_EVENTPROPERTYRULE"]._serialized_start = 3662
    _globals["_EVENTPROPERTYRULE"]._serialized_end = 3856
    _globals["_EVENTPROPERTYRULE_EVENTPROPERTYPARAM"]._serialized_start = 3774
    _globals["_EVENTPROPERTYRULE_EVENTPROPERTYPARAM"]._serialized_end = 3856
    _globals["_CONDITIONCOMPONENT"]._serialized_start = 3859
    _globals["_CONDITIONCOMPONENT"]._serialized_end = 4350
    _globals["_CONDITIONCOMPONENT_CONDITIONTYPE"]._serialized_start = 4189
    _globals["_CONDITIONCOMPONENT_CONDITIONTYPE"]._serialized_end = 4350
    _globals["_ACTIONCOMPONENT"]._serialized_start = 4352
    _globals["_ACTIONCOMPONENT"]._serialized_end = 4390
    _globals["_CONTROLCOMPONENT"]._serialized_start = 4393
    _globals["_CONTROLCOMPONENT"]._serialized_end = 5021
    _globals["_CONTROLCOMPONENT_CONTROLTYPE"]._serialized_start = 4771
    _globals["_CONTROLCOMPONENT_CONTROLTYPE"]._serialized_end = 5021
    _globals["_SPLITTERCONTROLRULE"]._serialized_start = 5023
    _globals["_SPLITTERCONTROLRULE"]._serialized_end = 5063
    _globals["_PERCENTSPLITTERBRANCHRULE"]._serialized_start = 5066
    _globals["_PERCENTSPLITTERBRANCHRULE"]._serialized_end = 5264
    _globals["_PERCENTSPLITTERBRANCHRULE_ABSTRATEGYTYPE"]._serialized_start = 5202
    _globals["_PERCENTSPLITTERBRANCHRULE_ABSTRATEGYTYPE"]._serialized_end = 5264
    _globals["_TIMESPLITTERBRANCHRULE"]._serialized_start = 5267
    _globals["_TIMESPLITTERBRANCHRULE"]._serialized_end = 5517
    _globals["_TIMESPLITTERBRANCHRULE_TIMESPLITTERTYPE"]._serialized_start = 5436
    _globals["_TIMESPLITTERBRANCHRULE_TIMESPLITTERTYPE"]._serialized_end = 5517
    _globals["_DELAYCONTROLRULE"]._serialized_start = 5520
    _globals["_DELAYCONTROLRULE"]._serialized_end = 5796
    _globals["_DELAYCONTROLRULE_DELAYENDACTION"]._serialized_start = 5676
    _globals["_DELAYCONTROLRULE_DELAYENDACTION"]._serialized_end = 5796
# @@protoc_insertion_point(module_scope)
