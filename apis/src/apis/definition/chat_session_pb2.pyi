from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class IntentDomain(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    UNKNOWN: _ClassVar[IntentDomain]
    DESIGN: _ClassVar[IntentDomain]
    INSIGHT: _ClassVar[IntentDomain]
    OPTIMIZE: _ClassVar[IntentDomain]
    RETRIEVE: _ClassVar[IntentDomain]
    PREDICT: _ClassVar[IntentDomain]
UNKNOWN: IntentDomain
DESIGN: IntentDomain
INSIGHT: IntentDomain
OPTIMIZE: IntentDomain
RETRIEVE: IntentDomain
PREDICT: IntentDomain

class ChatSessionInfo(_message.Message):
    __slots__ = ("organization_id", "sfn_project_id", "project_id", "project_name", "chat_session_id", "chat_session_name")
    ORGANIZATION_ID_FIELD_NUMBER: _ClassVar[int]
    SFN_PROJECT_ID_FIELD_NUMBER: _ClassVar[int]
    PROJECT_ID_FIELD_NUMBER: _ClassVar[int]
    PROJECT_NAME_FIELD_NUMBER: _ClassVar[int]
    CHAT_SESSION_ID_FIELD_NUMBER: _ClassVar[int]
    CHAT_SESSION_NAME_FIELD_NUMBER: _ClassVar[int]
    organization_id: str
    sfn_project_id: int
    project_id: int
    project_name: str
    chat_session_id: str
    chat_session_name: str
    def __init__(self, organization_id: _Optional[str] = ..., sfn_project_id: _Optional[int] = ..., project_id: _Optional[int] = ..., project_name: _Optional[str] = ..., chat_session_id: _Optional[str] = ..., chat_session_name: _Optional[str] = ...) -> None: ...
