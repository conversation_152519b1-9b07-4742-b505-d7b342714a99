# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: canvas_msg_content.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x18\x63\x61nvas_msg_content.proto\x12\x14sensorsdata.focus.v1"\xf7\x06\n\x10\x43\x61nvasMsgContent\x12I\n\x04type\x18\x0b \x01(\x0e\x32;.sensorsdata.focus.v1.CanvasMsgContent.CanvasMsgContentType\x12/\n\x04push\x18\x01 \x01(\x0b\x32!.sensorsdata.focus.v1.PushContent\x12\x35\n\x07webhook\x18\x02 \x01(\x0b\x32$.sensorsdata.focus.v1.WebHookContent\x12\x36\n\x08text_msg\x18\x03 \x01(\x0b\x32$.sensorsdata.focus.v1.TextMsgContent\x12@\n\x12wechat_active_push\x18\x04 \x03(\x0b\x32$.sensorsdata.focus.v1.WxReplyContent\x12Z\n\x1bwechat_service_template_msg\x18\x05 \x01(\x0b\x32\x35.sensorsdata.focus.v1.WechatServiceTemplateMsgContent\x12\x62\n\x1fwechat_miniprogram_template_msg\x18\x06 \x01(\x0b\x32\x39.sensorsdata.focus.v1.WechatMiniProgramTemplateMsgContent\x12-\n\x03\x65\x64m\x18\x07 \x01(\x0b\x32 .sensorsdata.focus.v1.EdmContent\x12/\n\x04line\x18\x08 \x01(\x0b\x32!.sensorsdata.focus.v1.LineContent\x12\x39\n\x0creward_grant\x18\t \x01(\x0b\x32#.sensorsdata.focus.v1.RewardContent\x12\x0e\n\x06\x63ommon\x18\n \x01(\t"\xca\x01\n\x14\x43\x61nvasMsgContentType\x12\x08\n\x04PUSH\x10\x00\x12\x0b\n\x07WEBHOOK\x10\x01\x12\x0c\n\x08TEXT_MSG\x10\x02\x12\x16\n\x12WECHAT_ACTIVE_PUSH\x10\x03\x12\x1f\n\x1bWECHAT_SERVICE_TEMPLATE_MSG\x10\x04\x12#\n\x1fWECHAT_MINIPROGRAM_TEMPLATE_MSG\x10\x05\x12\x07\n\x03\x45\x44M\x10\x06\x12\x08\n\x04LINE\x10\x07\x12\x10\n\x0cREWARD_GRANT\x10\x08\x12\n\n\x06\x43OMMON\x10\t"-\n\x0eNameValueParam\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t"\xdb\x03\n\x0bPushContent\x12!\n\x19\x66reemarker_syntax_version\x18\x01 \x01(\x05\x12\r\n\x05title\x18\x02 \x01(\t\x12\x0f\n\x07\x63ontent\x18\x03 \x01(\t\x12\x43\n\x0clanding_type\x18\x04 \x01(\x0e\x32-.sensorsdata.focus.v1.PushContent.LandingType\x12?\n\x11\x63ustomized_fields\x18\x05 \x03(\x0b\x32$.sensorsdata.focus.v1.NameValueParam\x12\x19\n\x11notification_icon\x18\x06 \x01(\t\x12\x43\n\x10\x61\x64vanced_setting\x18\x07 \x01(\x0b\x32).sensorsdata.focus.v1.PushAdvancedSetting\x12M\n\x14over_length_strategy\x18\x08 \x01(\x0e\x32/.sensorsdata.focus.v1.ContentOverLengthStrategy\x12\x1d\n\x15\x65nable_in_app_message\x18\t \x01(\x08"5\n\x0bLandingType\x12\x0c\n\x08OPEN_APP\x10\x00\x12\x08\n\x04LINK\x10\x01\x12\x0e\n\nCUSTOMIZED\x10\x02"\xfb\x02\n\x13PushAdvancedSetting\x12\x12\n\nchannel_id\x18\x01 \x01(\t\x12\x0e\n\x06\x65nable\x18\x02 \x01(\x08\x12G\n\x12notification_style\x18\x03 \x01(\x0b\x32+.sensorsdata.focus.v1.PushNotificationStyle\x12\x43\n\x10notification_tip\x18\x04 \x01(\x0b\x32).sensorsdata.focus.v1.PushNotificationTip\x12<\n\x0cmanufacturer\x18\x05 \x01(\x0b\x32&.sensorsdata.focus.v1.PushManufacturer\x12\x39\n\x0bmessage_ttl\x18\x06 \x01(\x0b\x32$.sensorsdata.focus.v1.PushMessageTtl\x12\x39\n\x03ios\x18\x07 \x01(\x0b\x32,.sensorsdata.focus.v1.PushAdvancedSettingIOS"\xdf\x01\n\x15PushNotificationStyle\x12P\n\x05style\x18\x01 \x01(\x0e\x32\x41.sensorsdata.focus.v1.PushNotificationStyle.NotificationStyleType\x12\x10\n\x08\x62ig_text\x18\x02 \x01(\t\x12\x13\n\x0b\x62ig_picture\x18\x03 \x01(\t"M\n\x15NotificationStyleType\x12\x08\n\x04NONE\x10\x00\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x01\x12\x0c\n\x08\x42IG_TEXT\x10\x02\x12\x0f\n\x0b\x42IG_PICTURE\x10\x03"\xcf\x01\n\x13PushNotificationTip\x12G\n\nsound_type\x18\x01 \x01(\x0e\x32\x33.sensorsdata.focus.v1.PushNotificationTip.SoundType\x12\x14\n\x0c\x63ustom_sound\x18\x02 \x01(\t\x12\x0f\n\x07vibrate\x18\x03 \x01(\x05\x12\r\n\x05light\x18\x04 \x01(\x05"9\n\tSoundType\x12\x08\n\x04NONE\x10\x00\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x01\x12\n\n\x06\x43USTOM\x10\x02\x12\t\n\x05\x43LOSE\x10\x03"\xcf\x01\n\x10PushManufacturer\x12\x0e\n\x06\x65nable\x18\x01 \x01(\x08\x12\x12\n\nskip_quota\x18\x02 \x01(\x08\x12\x1c\n\x14\x65nabled_manufacturer\x18\x03 \x03(\t\x12\x1c\n\x14jiguang_distribution\x18\x04 \x01(\t\x12\x15\n\rgetui_android\x18\x05 \x01(\x05\x12\x44\n\x13third_party_channel\x18\x06 \x01(\x0b\x32\'.sensorsdata.focus.v1.ThirdPartyChannel"\xfe\x03\n\x11ThirdPartyChannel\x12<\n\x05honor\x18\x01 \x01(\x0b\x32-.sensorsdata.focus.v1.ThirdPartyChannel.Honor\x12>\n\x06huawei\x18\x02 \x01(\x0b\x32..sensorsdata.focus.v1.ThirdPartyChannel.Huawei\x12:\n\x04oppo\x18\x03 \x01(\x0b\x32,.sensorsdata.focus.v1.ThirdPartyChannel.Oppo\x12:\n\x04vivo\x18\x04 \x01(\x0b\x32,.sensorsdata.focus.v1.ThirdPartyChannel.Vivo\x12>\n\x06xiaomi\x18\x05 \x01(\x0b\x32..sensorsdata.focus.v1.ThirdPartyChannel.Xiaomi\x1a\x1b\n\x05Honor\x12\x12\n\nimportance\x18\x01 \x01(\t\x1a\x42\n\x06Huawei\x12\x12\n\nimportance\x18\x01 \x01(\t\x12\x12\n\nchannel_id\x18\x02 \x01(\t\x12\x10\n\x08\x63\x61tegory\x18\x03 \x01(\t\x1a\x1a\n\x04Oppo\x12\x12\n\nchannel_id\x18\x01 \x01(\t\x1a\x18\n\x04Vivo\x12\x10\n\x08\x63\x61tegory\x18\x01 \x01(\t\x1a\x1c\n\x06Xiaomi\x12\x12\n\nchannel_id\x18\x01 \x01(\t"B\n\x0ePushMessageTtl\x12\x0e\n\x06\x65nable\x18\x01 \x01(\x08\x12\x12\n\nttl_second\x18\x02 \x01(\x05\x12\x0c\n\x04unit\x18\x03 \x01(\t"\x99\x01\n\x16PushAdvancedSettingIOS\x12\x11\n\tsub_title\x18\x01 \x01(\t\x12\x14\n\x0c\x62\x61\x64ge_number\x18\x02 \x01(\t\x12\x11\n\tthread_id\x18\x03 \x01(\t\x12\x43\n\x10notification_tip\x18\x04 \x01(\x0b\x32).sensorsdata.focus.v1.PushNotificationTip"\xa7\x02\n\x0eWebHookContent\x12!\n\x19\x66reemarker_syntax_version\x18\x01 \x01(\x05\x12\x46\n\x0bplan_params\x18\x02 \x03(\x0b\x32\x31.sensorsdata.focus.v1.WebHookContent.ContentParam\x12M\n\x14over_length_strategy\x18\x03 \x01(\x0e\x32/.sensorsdata.focus.v1.ContentOverLengthStrategy\x1a[\n\x0c\x43ontentParam\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05\x63name\x18\x02 \x01(\t\x12\r\n\x05value\x18\x03 \x01(\t\x12\x10\n\x08required\x18\x04 \x01(\x08\x12\r\n\x05\x65xtra\x18\x05 \x01(\t"\xf4\x01\n\x0eTextMsgContent\x12!\n\x19\x66reemarker_syntax_version\x18\x01 \x01(\x05\x12\x13\n\x0btemplate_id\x18\x02 \x01(\t\x12\x41\n\x13template_param_list\x18\x03 \x03(\x0b\x32$.sensorsdata.focus.v1.NameValueParam\x12\x18\n\x10template_content\x18\x04 \x01(\t\x12M\n\x14over_length_strategy\x18\x05 \x01(\x0e\x32/.sensorsdata.focus.v1.ContentOverLengthStrategy"\xb5\x05\n\x1fWechatServiceTemplateMsgContent\x12\x13\n\x0btemplate_id\x18\x01 \x01(\t\x12\x16\n\x0etemplate_title\x18\x02 \x01(\t\x12\x18\n\x10template_content\x18\x03 \x01(\t\x12[\n\x07\x63ontent\x18\x04 \x03(\x0b\x32J.sensorsdata.focus.v1.WechatServiceTemplateMsgContent.TemplateContentParam\x12\x18\n\x10primary_industry\x18\x05 \x01(\t\x12\x17\n\x0f\x64\x65puty_industry\x18\x06 \x01(\t\x12W\n\x0clanding_type\x18\x07 \x01(\x0e\x32\x41.sensorsdata.focus.v1.WechatServiceTemplateMsgContent.LandingType\x12\x0b\n\x03url\x18\x08 \x01(\t\x12[\n\x0bminiprogram\x18\t \x01(\x0b\x32\x46.sensorsdata.focus.v1.WechatServiceTemplateMsgContent.MiniProgramParam\x12M\n\x14over_length_strategy\x18\n \x01(\x0e\x32/.sensorsdata.focus.v1.ContentOverLengthStrategy\x1a\x41\n\x14TemplateContentParam\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\x12\r\n\x05\x63olor\x18\x03 \x01(\t\x1a\x34\n\x10MiniProgramParam\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\x12\x10\n\x08pagepath\x18\x02 \x01(\t"0\n\x0bLandingType\x12\x06\n\x02H5\x10\x00\x12\x0f\n\x0bMINIPROGRAM\x10\x01\x12\x08\n\x04NONE\x10\x02"\xf8\x02\n#WechatMiniProgramTemplateMsgContent\x12\x13\n\x0btemplate_id\x18\x01 \x01(\t\x12\x16\n\x0etemplate_title\x18\x02 \x01(\t\x12\x0c\n\x04page\x18\x03 \x01(\t\x12\x0c\n\x04lang\x18\x04 \x01(\t\x12\r\n\x05state\x18\x05 \x01(\t\x12\x18\n\x10template_content\x18\x06 \x01(\t\x12\\\n\x04\x64\x61ta\x18\x07 \x03(\x0b\x32N.sensorsdata.focus.v1.WechatMiniProgramTemplateMsgContent.TemplateContentParam\x12M\n\x14over_length_strategy\x18\x08 \x01(\x0e\x32/.sensorsdata.focus.v1.ContentOverLengthStrategy\x1a\x32\n\x14TemplateContentParam\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t"\xbf\x04\n\x0eWxReplyContent\x12L\n\x08msg_type\x18\x01 \x01(\x0e\x32:.sensorsdata.focus.v1.WxReplyContent.WxReplyContentMsgType\x12-\n\x04text\x18\x02 \x01(\x0b\x32\x1f.sensorsdata.focus.v1.WxTextMsg\x12/\n\x05image\x18\x03 \x01(\x0b\x32 .sensorsdata.focus.v1.WxImageMsg\x12\x30\n\x08msg_menu\x18\x04 \x01(\x0b\x32\x1e.sensorsdata.focus.v1.MenuTree\x12\x32\n\x07mp_news\x18\x05 \x01(\x0b\x32!.sensorsdata.focus.v1.WxMpNewsMsg\x12-\n\x04news\x18\x06 \x01(\x0b\x32\x1f.sensorsdata.focus.v1.WxNewsMsg\x12\x30\n\x07wx_card\x18\x07 \x01(\x0b\x32\x1f.sensorsdata.focus.v1.WxCardMsg\x12\x41\n\x11mini_program_page\x18\x08 \x01(\x0b\x32&.sensorsdata.focus.v1.WxMiniProgramMsg"u\n\x15WxReplyContentMsgType\x12\x08\n\x04TEXT\x10\x00\x12\t\n\x05IMAGE\x10\x01\x12\x0c\n\x08MSG_MENU\x10\x02\x12\x0b\n\x07MP_NEWS\x10\x03\x12\x08\n\x04NEWS\x10\x04\x12\x0b\n\x07WX_CARD\x10\x05\x12\x15\n\x11MINI_PROGRAM_PAGE\x10\x06"\x1c\n\tWxTextMsg\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\t"Y\n\nWxImageMsg\x12\n\n\x02id\x18\x01 \x01(\t\x12\x10\n\x08media_id\x18\x02 \x01(\t\x12\x0c\n\x04temp\x18\x03 \x01(\x05\x12\x12\n\nmedia_type\x18\x04 \x01(\t\x12\x0b\n\x03url\x18\x05 \x01(\t"\x9b\x02\n\x08MenuTree\x12\n\n\x02id\x18\x01 \x01(\x05\x12\r\n\x05\x63name\x18\x02 \x01(\t\x12\x19\n\x11\x63reate_user_cname\x18\x03 \x01(\t\x12\x18\n\x10\x63reate_user_name\x18\x04 \x01(\t\x12\x1a\n\x12\x63reated_by_user_id\x18\x05 \x01(\x05\x12\x18\n\x10\x63reated_by_roles\x18\x06 \x03(\x05\x12\x13\n\x0b\x63reate_time\x18\x07 \x01(\t\x12\x19\n\x11update_user_cname\x18\x08 \x01(\t\x12\x18\n\x10update_user_name\x18\t \x01(\t\x12\x13\n\x0bupdate_time\x18\n \x01(\t\x12*\n\x04menu\x18\x0b \x01(\x0b\x32\x1c.sensorsdata.focus.v1.WxMenu"\x93\x02\n\x06WxMenu\x12\n\n\x02id\x18\x01 \x01(\t\x12\x11\n\toption_id\x18\x02 \x01(\t\x12\x33\n\x04type\x18\x03 \x01(\x0e\x32%.sensorsdata.focus.v1.WxMenu.MenuType\x12\r\n\x05title\x18\x04 \x01(\t\x12\x0c\n\x04\x64\x61ta\x18\x05 \x03(\t\x12.\n\x08\x63hildren\x18\x06 \x03(\x0b\x32\x1c.sensorsdata.focus.v1.WxMenu"h\n\x08MenuType\x12\x0c\n\x08MSG_MENU\x10\x00\x12\x08\n\x04TEXT\x10\x01\x12\t\n\x05IMAGE\x10\x02\x12\x08\n\x04NEWS\x10\x03\x12\x0b\n\x07MP_NEWS\x10\x04\x12\x15\n\x11MINI_PROGRAM_PAGE\x10\x05\x12\x0b\n\x07WX_CARD\x10\x06"\xe5\x03\n\x0bWxMpNewsMsg\x12\x10\n\x08media_id\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\x12\r\n\x05title\x18\x03 \x01(\t\x12\x0e\n\x06\x61uthor\x18\x04 \x01(\t\x12\x16\n\x0ethumb_media_id\x18\x05 \x01(\t\x12\x17\n\x0fthumb_media_url\x18\x06 \x01(\t\x12\x0e\n\x06\x64igest\x18\x07 \x01(\t\x12\x16\n\x0eshow_cover_pic\x18\x08 \x01(\x05\x12\x0f\n\x07\x63ontent\x18\t \x01(\t\x12\x1a\n\x12\x63ontent_source_url\x18\n \x01(\t\x12\x10\n\x08is_multi\x18\x0b \x01(\x08\x12\x45\n\tnews_item\x18\x0c \x03(\x0b\x32\x32.sensorsdata.focus.v1.WxMpNewsMsg.WxNewsItemDetail\x1a\xb8\x01\n\x10WxNewsItemDetail\x12\n\n\x02id\x18\x01 \x01(\x05\x12\r\n\x05title\x18\x02 \x01(\t\x12\x17\n\x0fthumb_media_url\x18\x03 \x01(\t\x12\x16\n\x0eshow_cover_pic\x18\x04 \x01(\x05\x12\x0e\n\x06\x61uthor\x18\x05 \x01(\t\x12\x0e\n\x06\x64igest\x18\x06 \x01(\t\x12\x0f\n\x07\x63ontent\x18\x07 \x01(\t\x12\x0b\n\x03url\x18\x08 \x01(\t\x12\x1a\n\x12\x63ontent_source_url\x18\t \x01(\t"L\n\tWxNewsMsg\x12\r\n\x05title\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x0b\n\x03url\x18\x03 \x01(\t\x12\x0e\n\x06picurl\x18\x04 \x01(\t"i\n\tWxCardMsg\x12\x0f\n\x07\x63\x61rd_id\x18\x01 \x01(\t\x12\x12\n\ncard_title\x18\x02 \x01(\t\x12\x12\n\nbrand_name\x18\x03 \x01(\t\x12\x10\n\x08logo_url\x18\x04 \x01(\t\x12\x11\n\tcard_type\x18\x05 \x01(\t"\x9f\x01\n\x10WxMiniProgramMsg\x12\r\n\x05title\x18\x01 \x01(\t\x12\r\n\x05\x61ppid\x18\x02 \x01(\t\x12\x10\n\x08pagepath\x18\x03 \x01(\t\x12\x16\n\x0ethumb_media_id\x18\x04 \x01(\t\x12\x18\n\x10thumb_media_type\x18\x05 \x01(\t\x12\x12\n\nthumb_temp\x18\x06 \x01(\x05\x12\x15\n\rthumb_pic_url\x18\x07 \x01(\t"\xf5\x02\n\nEdmContent\x12\x11\n\tfrom_name\x18\x01 \x01(\t\x12\x0f\n\x07subject\x18\x02 \x01(\t\x12:\n\x04type\x18\x03 \x01(\x0e\x32,.sensorsdata.focus.v1.EdmContent.ContentType\x12\x0f\n\x07\x63ontent\x18\x04 \x01(\t\x12\x13\n\x0btemplate_id\x18\x05 \x01(\t\x12\x15\n\rtemplate_name\x18\x06 \x01(\t\x12\x18\n\x10template_subject\x18\x07 \x01(\t\x12!\n\x19\x66reemarker_syntax_version\x18\x08 \x01(\x05\x12M\n\x14over_length_strategy\x18\t \x01(\x0e\x32/.sensorsdata.focus.v1.ContentOverLengthStrategy">\n\x0b\x43ontentType\x12\x08\n\x04TEXT\x10\x00\x12\r\n\tRICH_TEXT\x10\x01\x12\x08\n\x04HTML\x10\x02\x12\x0c\n\x08TEMPLATE\x10\x03"\x1e\n\x0bLineContent\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\t"\x82\x01\n\rRewardContent\x12\x41\n\x07\x63ontent\x18\x01 \x03(\x0b\x32\x30.sensorsdata.focus.v1.RewardContent.ContentEntry\x1a.\n\x0c\x43ontentEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01*B\n\x19\x43ontentOverLengthStrategy\x12\x08\n\x04\x46ULL\x10\x00\x12\x0c\n\x08TRUNCATE\x10\x01\x12\r\n\tDONOTHING\x10\x02\x42\x1f\n\x1b\x63om.sensorsdata.focus.v1.aiP\x01\x62\x06proto3'
)

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "canvas_msg_content_pb2", _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
    _globals["DESCRIPTOR"]._options = None
    _globals["DESCRIPTOR"]._serialized_options = (
        b"\n\033com.sensorsdata.focus.v1.aiP\001"
    )
    _globals["_REWARDCONTENT_CONTENTENTRY"]._options = None
    _globals["_REWARDCONTENT_CONTENTENTRY"]._serialized_options = b"8\001"
    _globals["_CONTENTOVERLENGTHSTRATEGY"]._serialized_start = 7489
    _globals["_CONTENTOVERLENGTHSTRATEGY"]._serialized_end = 7555
    _globals["_CANVASMSGCONTENT"]._serialized_start = 51
    _globals["_CANVASMSGCONTENT"]._serialized_end = 938
    _globals["_CANVASMSGCONTENT_CANVASMSGCONTENTTYPE"]._serialized_start = 736
    _globals["_CANVASMSGCONTENT_CANVASMSGCONTENTTYPE"]._serialized_end = 938
    _globals["_NAMEVALUEPARAM"]._serialized_start = 940
    _globals["_NAMEVALUEPARAM"]._serialized_end = 985
    _globals["_PUSHCONTENT"]._serialized_start = 988
    _globals["_PUSHCONTENT"]._serialized_end = 1463
    _globals["_PUSHCONTENT_LANDINGTYPE"]._serialized_start = 1410
    _globals["_PUSHCONTENT_LANDINGTYPE"]._serialized_end = 1463
    _globals["_PUSHADVANCEDSETTING"]._serialized_start = 1466
    _globals["_PUSHADVANCEDSETTING"]._serialized_end = 1845
    _globals["_PUSHNOTIFICATIONSTYLE"]._serialized_start = 1848
    _globals["_PUSHNOTIFICATIONSTYLE"]._serialized_end = 2071
    _globals["_PUSHNOTIFICATIONSTYLE_NOTIFICATIONSTYLETYPE"]._serialized_start = 1994
    _globals["_PUSHNOTIFICATIONSTYLE_NOTIFICATIONSTYLETYPE"]._serialized_end = 2071
    _globals["_PUSHNOTIFICATIONTIP"]._serialized_start = 2074
    _globals["_PUSHNOTIFICATIONTIP"]._serialized_end = 2281
    _globals["_PUSHNOTIFICATIONTIP_SOUNDTYPE"]._serialized_start = 2224
    _globals["_PUSHNOTIFICATIONTIP_SOUNDTYPE"]._serialized_end = 2281
    _globals["_PUSHMANUFACTURER"]._serialized_start = 2284
    _globals["_PUSHMANUFACTURER"]._serialized_end = 2491
    _globals["_THIRDPARTYCHANNEL"]._serialized_start = 2494
    _globals["_THIRDPARTYCHANNEL"]._serialized_end = 3004
    _globals["_THIRDPARTYCHANNEL_HONOR"]._serialized_start = 2825
    _globals["_THIRDPARTYCHANNEL_HONOR"]._serialized_end = 2852
    _globals["_THIRDPARTYCHANNEL_HUAWEI"]._serialized_start = 2854
    _globals["_THIRDPARTYCHANNEL_HUAWEI"]._serialized_end = 2920
    _globals["_THIRDPARTYCHANNEL_OPPO"]._serialized_start = 2922
    _globals["_THIRDPARTYCHANNEL_OPPO"]._serialized_end = 2948
    _globals["_THIRDPARTYCHANNEL_VIVO"]._serialized_start = 2950
    _globals["_THIRDPARTYCHANNEL_VIVO"]._serialized_end = 2974
    _globals["_THIRDPARTYCHANNEL_XIAOMI"]._serialized_start = 2976
    _globals["_THIRDPARTYCHANNEL_XIAOMI"]._serialized_end = 3004
    _globals["_PUSHMESSAGETTL"]._serialized_start = 3006
    _globals["_PUSHMESSAGETTL"]._serialized_end = 3072
    _globals["_PUSHADVANCEDSETTINGIOS"]._serialized_start = 3075
    _globals["_PUSHADVANCEDSETTINGIOS"]._serialized_end = 3228
    _globals["_WEBHOOKCONTENT"]._serialized_start = 3231
    _globals["_WEBHOOKCONTENT"]._serialized_end = 3526
    _globals["_WEBHOOKCONTENT_CONTENTPARAM"]._serialized_start = 3435
    _globals["_WEBHOOKCONTENT_CONTENTPARAM"]._serialized_end = 3526
    _globals["_TEXTMSGCONTENT"]._serialized_start = 3529
    _globals["_TEXTMSGCONTENT"]._serialized_end = 3773
    _globals["_WECHATSERVICETEMPLATEMSGCONTENT"]._serialized_start = 3776
    _globals["_WECHATSERVICETEMPLATEMSGCONTENT"]._serialized_end = 4469
    _globals[
        "_WECHATSERVICETEMPLATEMSGCONTENT_TEMPLATECONTENTPARAM"
    ]._serialized_start = 4300
    _globals[
        "_WECHATSERVICETEMPLATEMSGCONTENT_TEMPLATECONTENTPARAM"
    ]._serialized_end = 4365
    _globals["_WECHATSERVICETEMPLATEMSGCONTENT_MINIPROGRAMPARAM"]._serialized_start = (
        4367
    )
    _globals["_WECHATSERVICETEMPLATEMSGCONTENT_MINIPROGRAMPARAM"]._serialized_end = 4419
    _globals["_WECHATSERVICETEMPLATEMSGCONTENT_LANDINGTYPE"]._serialized_start = 4421
    _globals["_WECHATSERVICETEMPLATEMSGCONTENT_LANDINGTYPE"]._serialized_end = 4469
    _globals["_WECHATMINIPROGRAMTEMPLATEMSGCONTENT"]._serialized_start = 4472
    _globals["_WECHATMINIPROGRAMTEMPLATEMSGCONTENT"]._serialized_end = 4848
    _globals[
        "_WECHATMINIPROGRAMTEMPLATEMSGCONTENT_TEMPLATECONTENTPARAM"
    ]._serialized_start = 4300
    _globals[
        "_WECHATMINIPROGRAMTEMPLATEMSGCONTENT_TEMPLATECONTENTPARAM"
    ]._serialized_end = 4350
    _globals["_WXREPLYCONTENT"]._serialized_start = 4851
    _globals["_WXREPLYCONTENT"]._serialized_end = 5426
    _globals["_WXREPLYCONTENT_WXREPLYCONTENTMSGTYPE"]._serialized_start = 5309
    _globals["_WXREPLYCONTENT_WXREPLYCONTENTMSGTYPE"]._serialized_end = 5426
    _globals["_WXTEXTMSG"]._serialized_start = 5428
    _globals["_WXTEXTMSG"]._serialized_end = 5456
    _globals["_WXIMAGEMSG"]._serialized_start = 5458
    _globals["_WXIMAGEMSG"]._serialized_end = 5547
    _globals["_MENUTREE"]._serialized_start = 5550
    _globals["_MENUTREE"]._serialized_end = 5833
    _globals["_WXMENU"]._serialized_start = 5836
    _globals["_WXMENU"]._serialized_end = 6111
    _globals["_WXMENU_MENUTYPE"]._serialized_start = 6007
    _globals["_WXMENU_MENUTYPE"]._serialized_end = 6111
    _globals["_WXMPNEWSMSG"]._serialized_start = 6114
    _globals["_WXMPNEWSMSG"]._serialized_end = 6599
    _globals["_WXMPNEWSMSG_WXNEWSITEMDETAIL"]._serialized_start = 6415
    _globals["_WXMPNEWSMSG_WXNEWSITEMDETAIL"]._serialized_end = 6599
    _globals["_WXNEWSMSG"]._serialized_start = 6601
    _globals["_WXNEWSMSG"]._serialized_end = 6677
    _globals["_WXCARDMSG"]._serialized_start = 6679
    _globals["_WXCARDMSG"]._serialized_end = 6784
    _globals["_WXMINIPROGRAMMSG"]._serialized_start = 6787
    _globals["_WXMINIPROGRAMMSG"]._serialized_end = 6946
    _globals["_EDMCONTENT"]._serialized_start = 6949
    _globals["_EDMCONTENT"]._serialized_end = 7322
    _globals["_EDMCONTENT_CONTENTTYPE"]._serialized_start = 7260
    _globals["_EDMCONTENT_CONTENTTYPE"]._serialized_end = 7322
    _globals["_LINECONTENT"]._serialized_start = 7324
    _globals["_LINECONTENT"]._serialized_end = 7354
    _globals["_REWARDCONTENT"]._serialized_start = 7357
    _globals["_REWARDCONTENT"]._serialized_end = 7487
    _globals["_REWARDCONTENT_CONTENTENTRY"]._serialized_start = 7441
    _globals["_REWARDCONTENT_CONTENTENTRY"]._serialized_end = 7487
# @@protoc_insertion_point(module_scope)
