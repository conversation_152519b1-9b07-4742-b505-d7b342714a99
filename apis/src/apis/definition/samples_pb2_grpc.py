# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import samples_pb2 as samples__pb2


class DemoServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.DemoRequest = channel.unary_unary(
            "/focus.v1.DemoService/DemoRequest",
            request_serializer=samples__pb2.DemoMessage.SerializeToString,
            response_deserializer=samples__pb2.DemoMessage.FromString,
        )


class DemoServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def DemoRequest(self, request, context):
        """示例的 service"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")


def add_DemoServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
        "DemoRequest": grpc.unary_unary_rpc_method_handler(
            servicer.DemoRequest,
            request_deserializer=samples__pb2.DemoMessage.FromString,
            response_serializer=samples__pb2.DemoMessage.SerializeToString,
        ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
        "focus.v1.DemoService", rpc_method_handlers
    )
    server.add_generic_rpc_handlers((generic_handler,))


# This class is part of an EXPERIMENTAL API.
class DemoService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def DemoRequest(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/focus.v1.DemoService/DemoRequest",
            samples__pb2.DemoMessage.SerializeToString,
            samples__pb2.DemoMessage.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )
