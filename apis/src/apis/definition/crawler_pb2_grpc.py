# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import crawler_pb2 as crawler__pb2


class CrawlerServiceStub(object):
    """{_description_} 爬虫服务"""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.analyzeUrls = channel.unary_unary(
            "/sensorsdata.focus.v1.CrawlerService/analyzeUrls",
            request_serializer=crawler__pb2.AnalyzeUrlRequest.SerializeToString,
            response_deserializer=crawler__pb2.AnalyzeUrlResponse.FromString,
        )
        self.crawlUrls = channel.unary_unary(
            "/sensorsdata.focus.v1.CrawlerService/crawlUrls",
            request_serializer=crawler__pb2.CrawlUrlRequest.SerializeToString,
            response_deserializer=crawler__pb2.CrawlUrlResponse.FromString,
        )


class CrawlerServiceServicer(object):
    """{_description_} 爬虫服务"""

    def analyzeUrls(self, request, context):
        """{_summary_} 识别网页地址"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def crawlUrls(self, request, context):
        """{_summary_} 触发爬取网页 (用于"不主动更新"的网页)"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")


def add_CrawlerServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
        "analyzeUrls": grpc.unary_unary_rpc_method_handler(
            servicer.analyzeUrls,
            request_deserializer=crawler__pb2.AnalyzeUrlRequest.FromString,
            response_serializer=crawler__pb2.AnalyzeUrlResponse.SerializeToString,
        ),
        "crawlUrls": grpc.unary_unary_rpc_method_handler(
            servicer.crawlUrls,
            request_deserializer=crawler__pb2.CrawlUrlRequest.FromString,
            response_serializer=crawler__pb2.CrawlUrlResponse.SerializeToString,
        ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
        "sensorsdata.focus.v1.CrawlerService", rpc_method_handlers
    )
    server.add_generic_rpc_handlers((generic_handler,))


# This class is part of an EXPERIMENTAL API.
class CrawlerService(object):
    """{_description_} 爬虫服务"""

    @staticmethod
    def analyzeUrls(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/sensorsdata.focus.v1.CrawlerService/analyzeUrls",
            crawler__pb2.AnalyzeUrlRequest.SerializeToString,
            crawler__pb2.AnalyzeUrlResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def crawlUrls(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/sensorsdata.focus.v1.CrawlerService/crawlUrls",
            crawler__pb2.CrawlUrlRequest.SerializeToString,
            crawler__pb2.CrawlUrlResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )
