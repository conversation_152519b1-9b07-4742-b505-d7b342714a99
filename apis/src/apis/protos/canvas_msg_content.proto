syntax = "proto3";
package sensorsdata.focus.v1;

option java_multiple_files = true;
option java_package = "com.sensorsdata.focus.v1.ai";

// 推送内容
message CanvasMsgContent {
  // 推送内容类型
  enum CanvasMsgContentType {
    PUSH = 0;
    WEBHOOK = 1;
    TEXT_MSG = 2;
    WECHAT_ACTIVE_PUSH = 3;
    WECHAT_SERVICE_TEMPLATE_MSG = 4;
    WECHAT_MINIPROGRAM_TEMPLATE_MSG = 5;
    EDM = 6;
    LINE = 7;
    REWARD_GRANT = 8;
    COMMON = 9;
  }

  CanvasMsgContentType type = 11;
  // AppPush
  PushContent push = 1;
  // Webhook
  WebHookContent webhook = 2;
  // 短信
  TextMsgContent text_msg = 3;
  // 微信 48 小时活跃推送
  repeated WxReplyContent wechat_active_push = 4;
  // 微信的推送消息
  WechatServiceTemplateMsgContent wechat_service_template_msg = 5;
  // 微信小程序模版消息
  WechatMiniProgramTemplateMsgContent wechat_miniprogram_template_msg = 6;
  // 邮件
  EdmContent edm = 7;
  // Line
  LineContent line = 8;
  // 权益发放
  RewardContent reward_grant = 9;
  // 通用内容, 非上述类型都用 COMMON
  string common = 10;
}

// 参数名值对
message NameValueParam {
  // 参数名
  string name = 1;
  // 参数值
  string value = 2;
}

// 通道文案超长处理策略
enum ContentOverLengthStrategy {
  // 不做任何处理
  FULL = 0;
  // 截断文案
  TRUNCATE = 1;
  // 不做任何处理
  DONOTHING = 2;
}

// -------------------- AppPush --------------------
// App Push 的推送内容
message PushContent {
  enum LandingType {
    //  打开 APP
    OPEN_APP = 0;
    // 打开URL（浏览器里）
    LINK = 1;
    // 透传消息
    CUSTOMIZED = 2;
  }

  // freemarker 语法解析版本
  int32 freemarker_syntax_version = 1;
  // 推送的标题
  string title = 2;
  // 推送的内容
  string content = 3;
  // 推送着陆类型
  LandingType landing_type = 4;
  // 自定义字段
  repeated NameValueParam customized_fields = 5;
  // 通知栏大图标 URL 或 media_id, 有些通道需要 media_id 才能支持
  string notification_icon = 6;
  // 推送高级设置
  PushAdvancedSetting advanced_setting = 7;
  // 文案超长策略
  ContentOverLengthStrategy over_length_strategy = 8;
  // 是否开启应用内提醒
  bool enable_in_app_message = 9;
}

// Push 高级设置
message PushAdvancedSetting {
  // 一组铃声、震动的组合
  string channel_id = 1;
  // 是否启用
  bool enable = 2;
  // 通知栏样式
  PushNotificationStyle notification_style = 3;
  // 通知栏提示
  PushNotificationTip notification_tip = 4;
  // 厂商通道下发设置
  PushManufacturer manufacturer = 5;
  // 离线消息设置
  PushMessageTtl message_ttl = 6;
  // IOS App Push 的推送内容
  PushAdvancedSettingIOS ios = 7;
}

// 通知栏样式
message PushNotificationStyle {
  enum NotificationStyleType {
    // 不设置
    NONE = 0;
    // 默认样式
    DEFAULT = 1;
    // 大文本样式
    BIG_TEXT = 2;
    // 大图片样式
    BIG_PICTURE = 3;
  }

  // 通知栏样式类型
  NotificationStyleType style = 1;
  // 大文本内容
  string big_text = 2;
  // 大图片 URL 或 media_id
  string big_picture = 3;
}

// 通知提醒
message PushNotificationTip {
  enum SoundType {
    NONE = 0;
    DEFAULT = 1;
    CUSTOM = 2;
    CLOSE = 3;
  }

  // 通知提示音类类型
  SoundType sound_type = 1;
  // 自定义提示音
  string custom_sound = 2;
  // -1 表示不设置、0 表否、1 表示 是
  int32 vibrate = 3;
  // -1 表示不设置、0 表否、1 表示 是
  int32 light = 4;
}

// 厂商通道配置
message PushManufacturer {
  // 是否启用
  bool enable = 1;
  // 是否扣减接口调用次数
  bool skip_quota = 2;
  // 极光和个推,启用的厂商: xiaomi、huawei、vivo、oppo、meizu
  repeated string enabled_manufacturer = 3;
  // 极光下发策略：
  // first_ospush: 表示推送优先走厂商通道下发，无效走极光通道下发
  // spush: 表示推送强制走厂商通道下发
  // jpush: 表示推送强制走极光通道下发
  // secondary_push: 表示推送优先走极光，极光不在线再走厂商，厂商作为辅助【建议方式】
  string jiguang_distribution = 4;
  // 个推推送策略：
  // 1: 表示该消息在用户在线时推送个推通道，用户离线时推送厂商通道【默认】
  // 2: 表示该消息只通过厂商通道策略下发，不考虑用户是否在线
  // 3: 表示该消息只通过个推通道下发，不考虑用户是否在线
  // 4: 表示该消息优先从厂商通道下发，若消息内容在厂商通道代发失败后会从个推通道下发
  int32 getui_android = 5;
  // 第三方厂商配置：支持 honor、huawei、oppo、vivo、xiaomi
  ThirdPartyChannel third_party_channel = 6;
}

// 第三方厂商配置
message ThirdPartyChannel {
  message Honor {
    // 华为、荣耀通知栏消息智能分类
    string importance = 1;
  }

  message Huawei {
    // 华为、荣耀通知栏消息智能分类
    string importance = 1;
    // 通知栏消息分类
    string channel_id = 2;
    // 华为、vivo 厂商消息场景标识
    string category = 3;
  }

  message Oppo {
    // 通知栏消息分类
    string channel_id = 1;
  }

  message Vivo {
    // 华为、vivo 厂商消息场景标识
    string category = 1;
  }

  message Xiaomi {
    // 通知栏消息分类
    string channel_id = 1;
  }

  Honor honor = 1;
  Huawei huawei = 2;
  Oppo oppo = 3;
  Vivo vivo = 4;
  Xiaomi xiaomi = 5;
}


// 离线消息时长
message PushMessageTtl {
  // 是否启用
  bool enable = 1;
  // 离线时长, 单位秒
  int32 ttl_second = 2;
  // 时长单位、仅前端使用
  string unit = 3;
}

// IOS App Push 额外设置
message PushAdvancedSettingIOS {
  // 子标题
  string sub_title = 1;
  // 角标：APP 图标左上方红色的通知提醒数字
  string badge_number = 2;
  // 通知分组
  string thread_id = 3;
  // 通知栏提示
  PushNotificationTip notification_tip = 4;
}

// -------------------- WEBHOOK --------------------
// webhook 的推送内容
message WebHookContent {
  // 内容参数
  message ContentParam {
    // 参数名
    string name = 1;
    // 参数显示名
    string cname = 2;
    // 参数值
    string value = 3;
    // 是否是必填项
    bool required = 4;
    // 额外字段
    string extra = 5;
  }

  // freemarker 语法解析版本
  int32 freemarker_syntax_version = 1;
  // 计划参数
  repeated ContentParam plan_params = 2;
  // 通道文案超长策略
  ContentOverLengthStrategy over_length_strategy = 3;
}

// -------------------- TEXT_MSG --------------------
// 短信的推送内容
message TextMsgContent {
  // freemarker 语法解析版本
  int32 freemarker_syntax_version = 1;
  // 短信的模板 id
  string template_id = 2;
  // 模板参数列表
  repeated NameValueParam template_param_list = 3;
  // 模板内容，需要从短信平台复制过来
  string template_content = 4;
  // 通道文案超长策略
  ContentOverLengthStrategy over_length_strategy = 5;
}

// -------------------- WECHAT_SERVICE_TEMPLATE_MSG --------------------
// 微信模板的推送内容
message WechatServiceTemplateMsgContent {
  // 消息着陆类型
  enum LandingType {
    // 打开 H5 页面
    H5 = 0;
    // 跳转小程序
    MINIPROGRAM = 1;
    // 无跳转
    NONE = 2;
  }

  // 模板内容参数
  message TemplateContentParam {
    // 模板参数的 keyword，如 { {result.DATA} } 中的 result
    string key = 1;
    // 模板参数的取值
    string value = 2;
    // 模板内容字体颜色，不填默认为黑色
    string color = 3;
  }

  // 小程序参数
  message MiniProgramParam {
    // 所需跳转到的小程序 appid（该小程序 appid 必须与发模板消息的公众号是绑定关联关系，暂不支持小游戏）
    string app_id = 1;
    // 所需跳转到小程序的具体页面路径，支持带参数,（示例 index?foo=bar ），要求该小程序已发布，暂不支持小游戏
    string pagepath = 2;
  }

  // 微信模板 id
  string template_id = 1;
  // 模板标题
  string template_title = 2;
  // 模板内容
  string template_content = 3;
  // 模板内容参数
  repeated TemplateContentParam content = 4;
  // 一级行业
  string primary_industry = 5;
  // 二级行业
  string deputy_industry = 6;
  // 着陆类型
  LandingType landing_type = 7;
  // 模板跳转链接（海外帐号没有跳转能力）
  string url = 8;
  // 小程序参数
  MiniProgramParam miniprogram = 9;
  // 文案超长策略
  ContentOverLengthStrategy over_length_strategy = 10;
}

// -------------------- WECHAT_MINI_PROGRAM_TEMPLATE_MSG --------------------
// 微信小程序模板的推送内容
message WechatMiniProgramTemplateMsgContent {
  // 模板内容参数
  message TemplateContentParam {
    // 模板参数的 keyword，如 { {result.DATA} } 中的 result
    string key = 1;
    // 模板参数的取值
    string value = 2;
  }

  // 模板 id
  string template_id = 1;
  // 模板 title
  string template_title = 2;
  // 点击模板卡片后的跳转页面，仅限本小程序内的页面。支持带参数,（示例index?foo=bar）。该字段不填则模板无跳转
  string page = 3;
  // 进入小程序查看”的语言类型，支持zh_CN(简体中文)、en_US(英文)、zh_HK(繁体中文)、zh_TW(繁体中文)，默认为zh_CN
  string lang = 4;
  // 跳转小程序类型：developer为开发版；trial为体验版；formal为正式版；默认为正式版
  string state = 5;
  // 模板内容
  string template_content = 6;
  // 模板内容参数
  repeated TemplateContentParam data = 7;
  // 文案超长策略
  ContentOverLengthStrategy over_length_strategy = 8;
}

// -------------------- WECHAT_ACTIVE_PUSH --------------------
// 微信回复的推动内容
message WxReplyContent {
  // 消息类型
  enum WxReplyContentMsgType {
    // 文字消息
    TEXT = 0;
    // 图片消息
    IMAGE = 1;
    // 菜单会话
    MSG_MENU = 2;
    // 微信图文消息
    MP_NEWS = 3;
    // 外部页面消息
    NEWS = 4;
    // 微信卡券消息
    WX_CARD = 5;
    // 微信小程序消息
    MINI_PROGRAM_PAGE = 6;
  }

  WxReplyContentMsgType msg_type = 1;
  // 文字消息
  WxTextMsg text = 2;
  // 图片消息
  WxImageMsg image = 3;
  // 菜单会话
  MenuTree msg_menu = 4;
  // 微信图文消息
  WxMpNewsMsg mp_news = 5;
  // 外部页面消息
  WxNewsMsg news = 6;
  // 微信卡券消息
  WxCardMsg wx_card = 7;
  // 微信小程序消息
  WxMiniProgramMsg mini_program_page = 8;
}

// 文本消息
message WxTextMsg {
  string content = 1;
}

// 图片消息
message WxImageMsg {
  // 图片资源 id
  string id = 1;
  // 素材 id
  string media_id = 2;
  // 临时标记 [0 非临时][1 临时]
  int32 temp = 3;
  // 素材类型
  string media_type = 4;
  // 请求链接
  string url = 5;
}

// 菜单会话
message MenuTree {
  // 菜单 id
  int32 id = 1;
  // 菜单名称
  string cname = 2;
  // 创建菜单的用户名
  string create_user_cname = 3;
  // 创建菜单的用户
  string create_user_name = 4;
  // 创建菜单的用户 id
  int32 created_by_user_id = 5;
  // 创建菜单的角色列表
  repeated int32 created_by_roles = 6;
  // 创建时间，格式为 [YYYY-MM-DD hh:mm:ss]
  string create_time = 7;
  // 最近一次修改菜单的用户名
  string update_user_cname = 8;
  // 最近一次修改菜单的用户名
  string update_user_name = 9;
  // 修改时间，格式为 [YYYY-MM-DD hh:mm:ss]
  string update_time = 10;
  // 菜单数据
  WxMenu menu = 11;
}

// 菜单会话 - 菜单数据
message WxMenu {
  // 菜单类型
  enum MenuType {
    MSG_MENU = 0;
    TEXT = 1;
    IMAGE = 2;
    NEWS = 3;
    MP_NEWS = 4;
    MINI_PROGRAM_PAGE = 5;
    WX_CARD = 6;
  }

  // 前端生成的菜单 id 主要用于前端绘画菜单树
  string id = 1;
  // 选项 id 主要用于表示菜单的选项
  string option_id = 2;
  // 菜单类型
  MenuType type = 3;
  // 标题
  string title = 4;
  // 不同类型消息的元数据，由于各不相同统一采用 object 标识
  repeated string data = 5;
  // 子菜单
  repeated WxMenu children = 6;
}

// 图文消息
message WxMpNewsMsg {
  message WxNewsItemDetail {
    // 消息在数据库中的主键 id
    int32 id = 1;
    // 文章标题
    string title = 2;
    // 单图文的缩略图地址
    string thumb_media_url = 3;
    // 是否展示封面， [0 展示 1 不展示]
    int32 show_cover_pic = 4;
    // 文章作者
    string author = 5;
    // 文章内容的摘要
    string digest = 6;
    // 文章实际内容
    string content = 7;
    // 图文页的 url
    string url = 8;
    // 原文地址，用以预览
    string content_source_url = 9;
  }

  // 微信图文素材 id
  string media_id = 1;
  // 图文页的 url
  string url = 2;
  // 标题
  string title = 3;
  // 作者
  string author = 4;
  // 图文消息的封面图片素材 id
  string thumb_media_id = 5;
  // 图文消息的封面图片 url
  string thumb_media_url = 6;
  // 图文消息的摘要
  string digest = 7;
  // 是否展示封面 [0 展示][1 不展示]
  int32 show_cover_pic = 8;
  // 图文消息的具体内容
  string content = 9;
  // 点击“阅读原文”后的地址
  string content_source_url = 10;
  // 是否是多图文
  bool is_multi = 11;
  // 微信图文 news_item 内容
  repeated WxNewsItemDetail news_item = 12;
}

// 外部页面消息
message WxNewsMsg {
  // 标题
  string title = 1;
  // 描述
  string description = 2;
  // 文章链接
  string url = 3;
  // 图片链接
  string picurl = 4;
}

// 微信卡券消息
message WxCardMsg {
  // 微信卡券 id
  string card_id = 1;
  // 卡券名称
  string card_title = 2;
  // 公众号名称（商户名字)
  string brand_name = 3;
  // 卡券的商户 logo
  string logo_url = 4;
  // 卡券类型
  string card_type = 5;
}

// 微信小程序消息
message WxMiniProgramMsg {
  // 标题
  string title = 1;
  // 小程序 appid
  string appid = 2;
  // 小程序的页面路径
  string pagepath = 3;
  // 小程序消息图片的媒体 id
  string thumb_media_id = 4;
  // 小程序消息图片类型
  string thumb_media_type = 5;
  // 临时标记 [0 非临时][1 临时]
  int32 thumb_temp = 6;
  // 小程序消息图片 url
  string thumb_pic_url = 7;
}

// -------------------- EDM --------------------
// 邮件的推送内容
message EdmContent {
  enum ContentType {
    TEXT = 0;
    RICH_TEXT = 1;
    HTML = 2;
    TEMPLATE = 3;
  }

  // 发件人名称
  string from_name = 1;
  // 邮件主题（支持属性替换）
  string subject = 2;
  // 发送内容类型
  ContentType type = 3;
  // 发送内容
  string content = 4;
  // 模版ID
  string template_id = 5;
  // 模版名称
  string template_name = 6;
  // 模版主题
  string template_subject = 7;
  // freemarker 语法解析版本
  int32 freemarker_syntax_version = 8;
  // 文案超长策略
  ContentOverLengthStrategy over_length_strategy = 9;
}

// -------------------- LINE --------------------
// Line 的推送内容
message LineContent {
  string content = 1;
}

// -------------------- REWARD_GRANT --------------------
// 权益发放 的推送内容
message RewardContent {
  map<string, string> content = 1;
}
