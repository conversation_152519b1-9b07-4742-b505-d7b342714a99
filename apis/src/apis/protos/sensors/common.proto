// Copyright 2020 SensorsData

syntax = "proto3";

package sensorsdata.focus.v1;

import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

// Language Options
option java_multiple_files = true;
option java_package = "com.sensorsdata.focus.v1.ai";

// 所有产品组件的定义，和以下两个概念基本对应：
// 1. sensors-apis 的二级目录
// 2. 可以在私有云平台上进行部署的一个产品组件
//
// 须写全称，编号不能修改
enum ProductComponentCode {
  // 允许业务单元改名, 所以允许 alias
  // option allow_alias = true;

  PRODUCT_COMPONENT_CODE_UNSPECIFIED = 0;

  // 后台组件
  SENSORS_DATAFLOW = 2;
  SENSORS_CLOUD_ATLAS = 3;
  SENSORS_BUSINESS_PLATFORM = 4;

  // 产品线组件
  SENSORS_ANALYTICS = 1;
  SENSORS_PERSONAS = 5;
  SENSORS_FOCUS = 6;
  SENSORS_RECOMMENDER = 7;
  SENSORS_JOURNEY = 8;
  SENSORS_DATA_GOVERNOR = 9;
  SENSORS_INFINITY = 10;
  SENSORS_HUBBLE = 11;
  SENSORS_DIVINER = 12;
  SENSORS_CYBER = 13;
  SENSORS_DATA_HORIZON = 14;
  SENSORS_EDGE = 15;
  SENSORS_DATA_INTEGRATOR = 16;
}

// 用户属性值的数据类型
// 产品端属性类型定义: https://manual.sensorsdata.cn/sa/latest/%E6%95%B0%E6%8D%AE%E6%A0%BC%E5%BC%8F-7545142.html#id-.%E6%95%B0%E6%8D%AE%E6%A0%BC%E5%BC%8Fv1.17-%E7%A5%9E%E7%AD%96%E5%88%86%E6%9E%90%E5%B1%9E%E6%80%A7%E6%95%B0%E6%8D%AE%E7%B1%BB%E5%9E%8B%E5%AE%9A%E4%B9%89
enum DataType {
  DATA_TYPE_UNSPECIFIED = 0;
  BOOL = 1;       // 对应取值 bool_value
  INT = 2;        // 对应取值 int_value
  NUMBER = 3;     // 对应取值 number_value
  STRING = 4;     // 对应取值 string_value
  LIST = 5;       // 对应取值 list_value
  DATETIME = 6;   // 对应取值 datetime_value
  // 添加 infinity v2 有的 DATE， BIGINT 和 DECIMAL 字段
  DATE = 7;       // 对应取值 date_value
  BIGINT = 8;     // 对应取值 bigint_value
  DECIMAL = 9;    // 对应取值 decimal_value
}

// 通用的静态值
message Literal {
  DataType data_type = 1;
  google.protobuf.BoolValue bool_value = 11;
  google.protobuf.Int64Value int_value = 12;
  // Number 类型实际上可能是整数也可能是小数，考虑到性能和兼容性因素，这里使用 double 而不是一个复合的 Decimal 类型来标识。
  // 而 double 类型十进制的有效位数是 15 位，绝大部分情况下都是够用的。
  // TODO 后续考虑增加单独的 Decimal 类型以满足真正的高精度需求
  google.protobuf.DoubleValue number_value = 14;
  google.protobuf.StringValue string_value = 15;
  // protolint:disable:next REPEATED_FIELD_NAMES_PLURALIZED
  repeated google.protobuf.StringValue list_value = 16;
  // 一个 UTC 时间戳，在 JSON 序列化时会变成字符串类型存储
  google.protobuf.Timestamp datetime_value = 17;
  google.protobuf.StringValue date_value = 18;
  google.protobuf.Int64Value bigint_value = 19;
  google.protobuf.StringValue decimal_value = 20;

}

// 时间单位
enum DateTimeUnit {
  DATE_TIME_UNIT_UNSPECIFIED = 0;
  MILLISECOND = 1;
  SECOND = 2;
  MINUTE = 3;
  HOUR = 4;
  DAY = 5;
  WEEK = 6;
  MONTH = 7;
  YEAR = 8;
}

// 对一个时间点进行 trunc（取整）的粒度，等价于一般 SQL 中 trunc 函数的参数
enum DateTimeTruncUnit {
  TRUNC_UNIT_UNSPECIFIED = 0;
  TRUNC_HOUR = 1;
  TRUNC_DAY = 2;
  TRUNC_WEEK = 3;
  TRUNC_MONTH = 4;
  TRUNC_YEAR = 5;
  TRUNC_BIGBANG = 10; // 上线至今
}
