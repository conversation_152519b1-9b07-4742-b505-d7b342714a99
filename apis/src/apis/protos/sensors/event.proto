// Copyright 2020 SensorsData

syntax = "proto3";

package sensorsdata.focus.v1;

import "sensors/condition.proto";
import "sensors/time_range.proto";

option java_multiple_files = true;
option java_package = "com.sensorsdata.focus.v1.ai";


/***************************************/
/*                                     */
/*       Event Series Definition       */
/*                                     */
/***************************************/

// 一个事件带上一组过滤条件, 有时间窗口
message EventSeries {
  string event_name = 1;
  TimeRange time_range = 2;
  CompoundFilterCondition condition = 3;
}
