// Copyright 2020 SensorsData

syntax = "proto3";

package sensorsdata.focus.v1;

import "sensors/common.proto";
import "sensors/time_range.proto";

// Java Language Options
option java_multiple_files = true;
option java_package = "com.sensorsdata.focus.v1.ai";


/************************************/
/*                                  */
/*       Condition Definition       */
/*                                  */
/************************************/

// 参考 https://manual.sensorsdata.cn/sa/latest/tech_super_api_query-7552445.html#id-.%E6%9F%A5%E8%AF%A2APIv2.0-%E7%AD%9B%E9%80%89%E8%A1%A8%E8%BE%BE%E5%BC%8F
// {_description_}所有目前支持的条件判断函数
enum PredicateFunctionType {
  option allow_alias = true;

  PREDICATE_FUNCTION_TYPE_UNSPECIFIED = 0;
  // 类型通用
  IS_SET = 1;
  is_set = 1 [deprecated = true];
  isSet = 1 [deprecated = true];

  IS_NOT_SET = 2;

  EQUAL = 11;
  NOT_EQUAL = 12;

  // 用于表示 值 是否存在于集合里
  IN = 37;
  NOT_IN = 38;

  // Number / Int / Datetime
  // 对于 Datetime 类型，可以使用 TimePoint 类型的参数来完成复杂的相对时间条件的描述
  LESS = 13;
  LESS_EQUAL = 14;
  GREATER = 15;
  GREATER_EQUAL = 16;
  // 左右都是闭区间
  BETWEEN = 17;
  // 左闭右开区间
  RIGHT_OPEN_BETWEEN = 18;
  // 用于实现分流，按照指定的属性值进行 hash ，再按照 100 取模后，值在指定区间范围内返回 true，否则返回 false
  // 参数有 3 个：属性名称 propertyName ，区间值 lowerValue 和 upperValue
  // Math.floorMod(Math.abs(hash(propertyName 的值)), 100) 的值在【lowerValue ， upperValue）左闭右开区间，则返回 true， 否则返回 false
  // 注：propertyName 如果使用 userId，则不再进行 hash 运算
  HASH_BETWEEN = 19;
  // 左开右闭区间
  LEFT_OPEN_BETWEEN = 47;
  // 左右都是开区间
  OPEN_BETWEEN = 48;

  // Boolean
  IS_TRUE = 21;
  IS_FALSE = 22;

  // String
  CONTAIN = 31;
  NOT_CONTAIN = 32;
  IS_EMPTY = 33;
  IS_NOT_EMPTY = 34;
  RLIKE = 35;
  NOT_RLIKE = 36;

  // List
  INCLUDE = 41;
  NOT_INCLUDE = 42;
  ABSOLUTE_BETWEEN = 43;
  RELATIVE_WITHIN = 44;
  RELATIVE_BETWEEN = 45;
  RELATIVE_EVENT_TIME = 46;
  RELATIVE_BEFORE = 56;
  // LIST 新增
  // 是否交集
  INTERSECTS = 50;
  NOT_INTERSECTS = 51;
  // 是否子集
  SUBSET_OF = 52;
  NOT_SUBSET_OF = 53;
  // 是否完全包含
  SUPERSET_OF = 54;
  NOT_SUPERSET_OF = 55;

}

enum PredicateFunctionParamType {
  FUNCTION_PARAM_TYPE_UNSPECIFIED = 0;
  // {_description_}字段
  FIELD = 1;
  // {_description_}常量
  VALUE = 2;
  // {_description_}变量
  VARIABLE = 3;
  // {_description_}时间点
  TIME_POINT = 4;
  // {_description_}表达式
  EXPRESSION = 5;
}

// {_description_}
// ```
// 函数的参数，有几种可能
// 1. 一个 field, 如 events.$Anything.$city
// 2. 一个 Literal 的常量
// 3. 一个变量, 如 $ENTRY_TIME, 需要在执行上下文里进行替换
// 4. 一个 TimePoint, 代表一个相对或绝对的时间点, 用于时间类型的条件
// 5. 一个 expression user.p1/user.p2+user.p3
// ```
message PredicateFunctionParam {
  // {_description_}函数的参数类型
  // {_example_}FIELD
  PredicateFunctionParamType param_type = 1;
  // {_description_}字段名
  // {_example_}#ResourceId
  string field = 2;
  // {_description_}常量值
  Literal value = 3;
  // {_description_}变量，需要在执行上下文里进行替换
  // {_example_}$ENTRY_TIME
  string variable = 4;
  // {_description_}代表一个相对或绝对的时间点, 用于时间类型的条件
  TimePoint time_point = 5;
  // {_description_}自定义表达式
  // {_example_}user.p1/user.p2+user.p3
  string expression = 6;
}

// {_description_}逻辑操作符
enum LogicalOperator {
  option allow_alias = true;
  LOGICAL_OPERATOR_UNSPECIFIED = 0;
  // {_description_}与
  AND = 1;
  // {_description_}或
  OR = 2;

  // 别名兼容
  and = 1 [deprecated = true];
  or = 2 [deprecated = true];
}

// {_description_}单个过滤条件
// {_example_}:
//  EQUAL(field(events.$Anything.$city), Literal('北京'), Literal('上海'))
//  IS_SET(field(profile.$tag1))
message FilterCondition {
  // {_description_}过滤条件的判断函数
  // {_example_}EQUAL
  PredicateFunctionType function = 1;
  // {_description_}函数的参数
  repeated PredicateFunctionParam params = 2;
  // {_description_}当前筛选条件所处的位置下标，主要为了保证前端展示顺序的稳定
  // {_example_}10
  int32 index = 3;
}

// {_description_}复合过滤条件，用于描述 condition 或 compound_filter 之间的关系
message CompoundFilterCondition {
  // {_description_}逻辑运算法
  // {_example_}AND
  LogicalOperator operator = 1;
  // {_description_}过滤条件列表，只有最后一级才有
  repeated FilterCondition conditions = 2;
  // {_description_}嵌套的下一级复合过滤条件
  repeated CompoundFilterCondition compound_conditions = 3;
  // {_description_}当前筛选条件所处的位置下标，主要为了保证前端展示顺序的稳定
  // {_example_}10
  int32 index = 4;
}
