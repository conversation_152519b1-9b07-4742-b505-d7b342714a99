// Copyright 2020 SensorsData

syntax = "proto3";

package sensorsdata.focus.v1;

option java_multiple_files = true;
option java_package = "com.sensorsdata.focus.v1.ai";


/***********************************************/
/*                                             */
/*       Aggregation Function Definition       */
/*                                             */
/***********************************************/

// 见 com.sensorsdata.queryengine.common.AggregatorType
// 注：此类附加多种方法，例结合参数获取中英文显示名
enum AggregatorType {
  AGGREGATOR_TYPE_UNSPECIFIED = 0;
  COUNT = 1;
  AVG = 2;
  MAX = 3;
  MIN = 4;
  SUM = 5;
  UNIQUE_COUNT = 6;
  UNIQUE_AVG = 7;
  BOUNCE_RATE = 8;
  EXIT_RATE = 9;
  UNIQUE_COUNT_APPROX = 10;
  COUNT_PERCENT = 111;
  RANK_ASC = 12;
  RANK_DESC = 13;
  PERCENT_RANK_ASC = 14;
  PERCENT_RANK_DESC = 15;
  SESSION_COUNT = 16;
  FIRST_TIME = 17;
  LAST_TIME = 18;
  FIRST_TIME_INTERVAL = 19;
  LAST_TIME_INTERVAL = 20;
  GROUP_CONCAT = 21;
  UNIQUE_COUNT_BITMAP = 22;
  UNIQUE_COUNT_APPROX_ORDINARY = 23;
  COUNT_DISTINCT = 24;
  LTV = 25;
  LTV_AVG = 26;

  // 下面几个RANK类型，用于表示 "TOP N" 的几种情况。这些类型解析时会被拆分成多个类型组合
  // 例如：COUNT_RANK 会解析为 COUNT +  RANK/PERCENT_RANK 的组合
  // 总次数 TOP N
  COUNT_RANK = 30;
  // 均值 TOP N
  AVG_RANK = 31;
  // 最大值 TOP N
  MAX_RANK = 32;
  // 最小值 TOP N
  MIN_RANK= 33;
  // 求和 TOP N
  SUM_RANK = 34;

  // 支持 TOP N 的场景，将聚合和排序在一起描述
  COUNT_RANK_ASC = 40;
  COUNT_RANK_DESC = 41;
  COUNT_PERCENT_RANK_ASC = 42;
  COUNT_PERCENT_RANK_DESC = 43;
  AVG_RANK_ASC = 44;
  AVG_RANK_DESC = 45;
  AVG_PERCENT_RANK_ASC = 46;
  AVG_PERCENT_RANK_DESC = 47;
  MAX_RANK_ASC = 48;
  MAX_RANK_DESC = 49;
  MAX_PERCENT_RANK_ASC = 50;
  MAX_PERCENT_RANK_DESC = 51;
  MIN_RANK_ASC = 52;
  MIN_RANK_DESC = 53;
  MIN_PERCENT_RANK_ASC = 54;
  MIN_PERCENT_RANK_DESC = 55;
  SUM_RANK_ASC = 56;
  SUM_RANK_DESC = 57;
  SUM_PERCENT_RANK_ASC = 58;
  SUM_PERCENT_RANK_DESC = 59;
}
