// Copyright 2020 SensorsData

syntax = "proto3";

package sensorsdata.focus.v1;

import "google/protobuf/timestamp.proto";
import "sensors/common.proto";

option java_multiple_files = true;
option java_package = "com.sensorsdata.focus.v1.ai";

/*************************************/
/*                                   */
/*       Scheduling Definition       */
/*                                   */
/*************************************/

// 静态触发
// $TASK_TIME 永远为此处设置的 time
message StaticTrigger {
  google.protobuf.Timestamp time = 1;
}

// 周期调度触发
// 整点调度, 每次触发时间设置为 $TASK_TIME
// 若 TimeRange 里包含 RELATIVE 类型, 且 base_time 设置为 $TASK_TIME, 则将每次触发时间设置为 $TASK_TIME
message PeriodicTrigger {
  // 周期调度的生效时间，可以早于创建时间，这样会触发数据补齐
  google.protobuf.Timestamp start_time = 1;
  // 周期调度的失效时间，超过此时间之后不再进行周期触发
  google.protobuf.Timestamp end_time = 2;

  // interval_size + interval_unit 定义了调度间隔, 如 3 DAY
  int32 interval_size = 5;
  DateTimeUnit interval_unit = 3;
  // 时间点的取整方式, 一般只会是 TRUNC_HOUR 或 TRUNC_DAY
  DateTimeTruncUnit trunc_unit = 4;
}

// 周期时间窗口的触发器定义
// 理论上可以覆盖 PeriodicTrigger, 实现更复杂的调度周期
message CronTrigger {
  // cron 表达式，例如 "0 6 21 * * ? *"
  string crontab_exp = 1;
  // 周期调度的生效时间，可以早于创建时间，这样会触发数据补齐
  google.protobuf.Timestamp start_time = 2;
  // 周期调度的失效时间，超过此时间之后不再进行周期触发
  google.protobuf.Timestamp end_time = 3;
}

// 数据同步任务特殊的触发器
// ** 其他地方不要用 **
message SyncJobTrigger {
  // 执行时间点的秒数, 取值时仅取日内时间点, 如: "13:21:13" 的秒为 48073
  int32 seconds_of_day = 1;
  // 执行日, DAY: "每日", WEEK: "每周", MONTH: "每月"
  DateTimeUnit execute_unit = 2;
  // 当 unit 为 WEEK 或 MONTH 时, 需要指定 days. MONDAY = 1, SUNDAY = 7
  // 当 unit 为 MONTH 时, day = -1 表示月度最后一天
  repeated int32 execute_days = 4;
  // 执行月份
  repeated int32 execute_months = 5;
}

message Trigger {
  enum TriggerType {
    TRIGGER_TYPE_UNSPECIFIED = 0;
    // 手动触发, 手动触发的任务, $TASK_TIME 为触发时的当前时间
    MANUAL = 1;
    PERIODIC = 2;
    CRON = 3;
    STATIC = 4;
    DYNAMIC = 5;
    SYNC_JOB = 6;
  }
  TriggerType trigger_type = 1;
  PeriodicTrigger periodic_trigger = 2;
  CronTrigger cron_trigger = 3;
  StaticTrigger static_trigger = 4;
  SyncJobTrigger sync_job_trigger = 5;
}

message TriggerExternalInfo {
  // 触发本次任务的主体
  oneof trigger_subject_id {
    int32 account_id = 1;
    int32 schedule_id = 2;
  }
  // 触发本次任务的原因
  string triggered_reason = 3;
}
