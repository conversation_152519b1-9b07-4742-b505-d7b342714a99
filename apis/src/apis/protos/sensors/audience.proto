syntax = "proto3";

import "sensors/condition.proto";

// 属性筛选条件
message PropertyFilterCondition {
  // 属性筛选函数枚举
  enum PropertyFilterFunction {
    option allow_alias = true;
    PROPERTY_FILTER_FUNCTION_UNSPECIFIED = 0;
    IS_SET = 1;
    is_set = 1 [deprecated = true];
    isSet = 1 [deprecated = true];

    NOT_SET = 2;
    not_set = 2 [deprecated = true];
    notSet = 2 [deprecated = true];

    IS_TRUE = 3;
    is_true = 3 [deprecated = true];
    isTrue = 3 [deprecated = true];

    IS_FALSE = 4;
    is_false = 4 [deprecated = true];
    isFalse = 4 [deprecated = true];

    LESS = 5;
    less = 5 [deprecated = true];

    EQUAL = 6;
    equal = 6 [deprecated = true];

    GREATER = 7;
    greater = 7 [deprecated = true];

    NOT_EQUAL = 8;
    not_equal = 8 [deprecated = true];
    notEqual = 8 [deprecated = true];

    BETWEEN = 9;
    between = 9 [deprecated = true];

    CONTAIN = 10;
    contain = 10 [deprecated = true];

    NOT_CONTAIN = 11;
    not_contain = 11 [deprecated = true];
    notContain = 11 [deprecated = true];

    IS_EMPTY = 12;
    is_empty = 12 [deprecated = true];
    isEmpty = 12 [deprecated = true];

    IS_NOT_EMPTY = 13;
    is_not_empty = 13 [deprecated = true];
    notEmpty = 13 [deprecated = true];
    isNotEmpty = 13 [deprecated = true];

    RLIKE = 14;
    rlike = 14 [deprecated = true];
    rLike = 14 [deprecated = true];

    NOT_RLIKE = 15;
    not_rlike = 15 [deprecated = true];
    notRLike = 15 [deprecated = true];

    IN = 16;
    in = 16 [deprecated = true];

    NOT_IN = 17;
    not_in = 17 [deprecated = true];
    notIn = 17 [deprecated = true];

    RELATIVE_BETWEEN = 18;
    relative_between = 18 [deprecated = true];
    relativeBetween = 18 [deprecated = true];

    RELATIVE_AFTER = 19;
    relative_after = 19 [deprecated = true];
    relativeAfter = 19 [deprecated = true];

    RELATIVE_BEFORE = 20;
    relative_before = 20 [deprecated = true];
    relativeBefore = 20 [deprecated = true];
  }
  string field = 1;
  PropertyFilterFunction function = 2;
  repeated string params = 3;
}

// 属性筛选
message PropertyFilter {
  sensorsdata.focus.v1.LogicalOperator relation = 1;
  repeated PropertyFilterCondition conditions = 2;
}

// 用户属性筛选规则
message AudienceProfileRule {
  PropertyFilter property_filter = 1;
}

// 事件指标
message AudienceEventMeasure {
  // 事件指标聚合函数枚举
  enum EventMeasureAggregator {
    option allow_alias = true;
    EVENT_MEASURE_AGGREGATOR_UNSPECIFIED = 0;
    GENERAL = 1;
    general = 1 [deprecated = true];

    UNIQUE = 2;
    unique = 2 [deprecated = true];

    SUM = 3;
    sum = 3 [deprecated = true];

    AVG = 4;
    avg = 4 [deprecated = true];

    MAX = 5;
    max = 5 [deprecated = true];

    MIN = 6;
    min = 6 [deprecated = true];
  }
  string event = 1;
  EventMeasureAggregator aggregator = 2;
  string aggr_field = 3;
  PropertyFilter filter = 4;
}

// 时间范围
message AudienceTimeRange {
  string function = 1;
  repeated string params = 2;
}



// 单个事件筛选条件
message AudienceEventMeasureCondition {
  // 事件指标过滤函数枚举
  enum EventMeasureFilterFunction {
    option allow_alias = true;
    EVENT_MEASURE_FILTER_FUNCTION_UNSPECIFIED = 0;
    LESS = 1;
    less = 1 [deprecated = true];

    EQUAL = 2;
    equal = 2 [deprecated = true];

    GREATER = 3;
    greater = 3 [deprecated = true];

    NOT_EQUAL = 4;
    not_equal = 4 [deprecated = true];
    notEqual = 4 [deprecated = true];

    GREATER_EQUAL = 5;
    greater_equal = 5 [deprecated = true];
    greaterEqual = 5 [deprecated = true];

    LESS_EQUAL = 6;
    less_equal = 6 [deprecated = true];
    lessEqual = 6 [deprecated = true];

    BETWEEN = 7;
    between = 7 [deprecated = true];

    TOP_PERCENT = 8;
    top_percent = 8 [deprecated = true];
    topPercent = 8 [deprecated = true];

    BOTTOM_PERCENT = 9;
    bottom_percent = 9 [deprecated = true];
    bottomPercent = 9 [deprecated = true];

    RANK_PERCENT_BETWEEN = 10;
    rank_percent_between = 10 [deprecated = true];
    rankPercentBetween = 10 [deprecated = true];

    TOP_N = 11;
    top_n = 11 [deprecated = true];
    topN = 11 [deprecated = true];

    BOTTOM_N = 12;
    bottom_n = 12 [deprecated = true];
    bottomN = 12 [deprecated = true];

    RANK_N_BETWEEN = 13;
    rank_n_between = 13 [deprecated = true];
    rankNBetween = 13 [deprecated = true];
  }

  AudienceEventMeasure measure = 1;
  AudienceTimeRange time_range = 2;
  EventMeasureFilterFunction function = 3;
  repeated string params = 4;
}

// 事件筛选条件
message AudienceEventCondition {
  string event = 1;
  PropertyFilter filter = 2;
}

// 事件筛选规则
message AudienceEventRule {
  sensorsdata.focus.v1.LogicalOperator relation = 1;
  repeated AudienceEventMeasureCondition events = 2;
}

// 单个事件序列筛选规则
message SingleAudienceEventSequenceRule {
  AudienceTimeRange time_range = 1;
  repeated AudienceEventCondition steps = 2;
}

// 事件序列筛选规则
message AudienceEventSequenceRule {
  sensorsdata.focus.v1.LogicalOperator relation = 1;
  repeated SingleAudienceEventSequenceRule multi_sequence_rules = 2;
}

// 受众规则
message AudienceRule {
  bool select_all = 1;
  sensorsdata.focus.v1.LogicalOperator relation = 2;
  AudienceProfileRule profile_rule = 3;
  AudienceEventRule event_rule = 4;
  AudienceEventSequenceRule event_sequence_rule = 5;
}