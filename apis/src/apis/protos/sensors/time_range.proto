// Copyright 2020 SensorsData

syntax = "proto3";

package sensorsdata.focus.v1;

import "google/protobuf/timestamp.proto";
import "sensors/common.proto";

option java_multiple_files = true;
option java_package = "com.sensorsdata.focus.v1.ai";

/*************************************/
/*                                   */
/*       Time Range Definition       */
/*                                   */
/*************************************/

// 类似于 SQL 中 INTERVAL 语句的定义，代表一个时间间隔
// 例如 size = 2 且 unit = SECOND，那么等价于 SQL 中的 "INTERVAL 2 SECOND"
message TimeInterval {
  int32 size = 1;
  DateTimeUnit unit = 2;
}

// 表示一个时间点，可能是一个静态时间点，也可能是动态（相对某个 base_time）
// 如果是 STATIC，取值就是 static_time
// 如果是 RELATIVE，等价于 SQL 中的 trunc($base_timer) + INTERVAL Expr
// 如果是 EXPRESSION，可以是一个 EQL 表达式
message TimePoint {
  // 时间点类型，静态 or 动态
  enum TimePointType {
    TIME_POINT_TYPE_UNSPECIFIED = 0;
    STATIC = 1;
    RELATIVE = 2;
    EXPRESSION = 3;
  }
  TimePointType type = 1;
  // 对时间点进行 Trunc，以便在计算 RELATIVE 类型的 TimePoint 时能够获取一个整点
  // 如果 trunc_unit = 0 则表示不 trunc
  DateTimeTruncUnit trunc_unit = 2;
  // 对于 STATIC 类型有效，表示一个静态时间点
  google.protobuf.Timestamp static_time = 3;
  // 对于 RELATIVE 类型有效，代表一个相对时间点
  message RelativeTimePoint {
    // basetime 是个变量, 如 "$ENTRY_TIME" 或 "$TASK_TIME"
    string base_time = 1;
    TimeInterval time_interval = 2;
  }
  RelativeTimePoint relative_time = 5;
  string expression = 6;
}

// 表示一个时间范围，根据 TimePoint 的类型，可能是静态也可能是动态。如果不设置 start_time/end_time，则表示没有起点或终点。
// 区间为严格的前闭后开，即：[start_time, end_time)
// 例如：
// start_time = {type: RELATIVE, trunc_unit: DAY, base_time: $TASK_TIME, time_interval: -7}
// end_time = {type: RELATIVE, trunc_unit: DAY, base_time: $TASK_TIME, time_interval: -1}
// 当 $TASK_TIME 等于 2020-07-08 00:00:00.000 时，start_time = 2020-07-01 00:00:00.000，end_time = 2020-07-07 00:00:00.000
// 最终的时间范围是：[2020-07-01 00:00:00.000, 2020-07-07 00:00:00.000)
message TimeRange {
  TimePoint start_time = 1;
  TimePoint end_time = 2;
}

message TimeWindow {
  int32 value = 1;
  string unit = 2;
}