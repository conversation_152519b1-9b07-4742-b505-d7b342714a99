syntax = "proto3";
package sensorsdata.focus.v1;

option java_multiple_files = true;
option java_package = "com.sensorsdata.focus.v1.ai";

// 引入 SteamEvent 流式数据返回结构
import "stream_event.proto";
// 引入 ChatSession 会话数据结构
import "chat_session.proto";


// {_description_} portal 智能体 API 服务
service PortalBotService {
  // {_summary_} 思路检索接口
  rpc RetrievalIdeas(RetrievalIdeasRequest) returns (RetrievalIdeasResponse) {}

  // {_summary_} 对话接口
  rpc Chat(GeneralPortalRequest) returns (stream StreamEvent) {}
}

message RetrievalIdeasRequest {
  enum Scenario {
    UNSPECIFIED = 0;
    INSIGHT_STRATEGY = 1;
    INSIGHT_GOAL = 2;
    DATA_ANALYSE = 3;
  }

  // {_description_} 对话信息
  ChatSessionInfo chat_session_info = 1;
  // {_description_} 检索语句
  string query = 2;
  // {_description_} 检索匹配个数
  int32 limit = 3;
  // {_description_} 检索场景
  Scenario scenario = 4;
}

message RetrievalIdea {
  message Refer {
    // {_description_} 参考文档 id
    int32 id = 1;
    // {_description_} 参考文档名称
    string cname = 2;
    // {_description_} 参考文档链接
    string url = 3;
  }

  message IdeaUnit {
    // {_description_} 单元标题（有 title 的前端按列表展示，没有 title 的把所有 unit 拼成大段文本展示）
    string title = 1;
    // {_description_} 单元内容（纯文本返回，阈值可在文本中调整）
    string content = 2;
    // {_description_} 单元参考文档
    repeated Refer refers = 3;
    // {_description_} 子级单元
    repeated IdeaUnit children_units = 4;
  }

  // {_description_} 思路单元
  repeated IdeaUnit units = 1;
  // {_description_} 思路参考文档
  repeated Refer refers = 3;
}

message RetrievalIdeasResponse {
  // {_description_} 搜索结果
  repeated RetrievalIdea ideas = 1;
}

message InsightParams {
  enum InsightLevel {
    // {_description_} 未指定
    UNSPECIFIED = 0;
    // {_description_} 场景
    SCENE = 1;
    // {_description_} 子场景
    SUB_SCENE = 2;
    // {_description_} 业务目标
    BUSINESS_GOAL = 3;
    // {_description_} 策略用例
    CAMPAIGN_CASE = 4;
  }

  // {_description_} 统计开始时间
  string start_time = 1;
  // {_description_} 统计结束时间
  string end_time = 2;
  // {_description_} 分析层级
  InsightLevel level = 3;
  // {_description_} 场景 id
  int32 scene_id = 4;
  // {_description_} 子场景 id
  int32 sub_scene_id = 5;
  // {_description_} 业务目标 id
  int32 business_goal_id = 6;
  // {_description_} 业务目标值
  string business_goal_value = 7;
  // {_description_} 策略用例 id
  int32 campaign_case_id = 8;
  // {_description_} 当前层级包含的所有策略
  repeated Strategies strategies = 9;
}

message Strategies {
  enum StrategyType {
    // {_description_} 计划
    PLAN = 0;
    // {_description_} 画布
    CANVAS = 1;
    // {_description_} 资源位
    SECTION = 2;
  }

  // {_description_} 策略类型
  StrategyType type = 1;
  // {_description_} 策略 id
  repeated string ids = 2;
}

message PortalMessage {
  enum MessageGenerator {
    // {_description_} 系统消息
    SYSTEM = 0;
    // {_description_} 用户消息
    USER = 1;
    // {_description_} 智能体消息
    BOT = 2;
  }

  enum MessageType {
    // {_description_} 文本消息
    TEXT = 0;
    // {_description_} markdown 消息
    MARKDOWN = 1;
    // {_description_} 表格消息
    TABLE = 2;
    // {_description_} 图表消息
    CHART = 3;
    // {_description_} 实体关系消息
    ENTITY_RELATION = 4;
    // {_description_} 策略洞察消息
    INSIGHT_STRATEGY = 5;
    // {_description_} 目标洞察消息
    INSIGHT_GOAL = 6;
    // {_description_} 分析思路消息
    ANALYSE_IDEA = 7;
    // {_description_} 推荐问题
    RECOMMEND_QUESTION = 8;
  }

  // {_description_} 额外附带能力
  message Capability {
    // {_description_} 获取思路
    bool retrieval_idea = 1;
  }

  // {_description_} 消息来源
  MessageGenerator generator = 1;
  // {_description_} 消息类型
  MessageType type = 2;
  // {_description_} text/markdown 消息
  string text = 3;
  // {_description_} 策略洞察消息
  InsightParams insight_strategy = 4;
  // {_description_} 目标洞察消息
  InsightParams insight_goal = 5;
  // {_description_} 分析思路回传 （将 RetrievalIdeasResponse 转为 markdown 文本）
  string analyse_idea = 6;
  // {_description_} 额外附带能力
  Capability capability = 7;
}

message GeneralPortalRequest {
  // {_description_} 对话信息
  ChatSessionInfo chat_session_info = 1;
  // {_description_} 所有对话消息
  repeated PortalMessage messages = 2;
}
