[project]
name = "apis"
version = "1.0.0"
classifiers = ["Private :: Do Not Upload"]
description = ""
authors = [
    { name = "<EMAIL>" }
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "protobuf==4.25.5",
    "wsproto==1.2.0",
    "grpcio==1.62.3",
    "grpcio-tools==1.62.3",
    "black==25.1.0",
]

[tool.setuptools]
packages = ["apis"]

[tool.setuptools.package-dir]
apis = "src/apis"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
