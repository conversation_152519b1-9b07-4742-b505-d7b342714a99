import dotenv
import os

dotenv.load_dotenv()

# database
POSTGRES_HOSTS = os.getenv("POSTGRES_HOSTS", "postgres")
POSTGRES_PORT = os.getenv("POSTGRES_PORT", "5432")
POSTGRES_DB = os.getenv("POSTGRES_DB", "sensors_ai")
POSTGRES_USER = os.getenv("POSTGRES_USER", "postgres")
POSTGRES_PASSWORD = os.getenv("POSTGRES_PASSWORD", "SensorsAI!")

DATABASE_URL = f"postgresql+asyncpg://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOSTS}:{POSTGRES_PORT}/{POSTGRES_DB}"
