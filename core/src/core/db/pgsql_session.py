import asyncio

from sqlalchemy import NullPool
from sqlalchemy.ext.asyncio import (
    AsyncSession,
    create_async_engine,
    async_scoped_session,
    AsyncEngine,
    async_sessionmaker
)
from sqlalchemy.orm import sessionmaker

from core.env import DATABASE_URL
from common import tools

log = tools.get_logger(name=__name__)


def create_new_engine(pool_size: int = 5, max_overflow: int = 3):
    engine = create_async_engine(
        DATABASE_URL,
        echo=False,
        future=True,
        pool_pre_ping=True,
        pool_size=pool_size,
        max_overflow=max_overflow,
    )
    log.info(f"pgsql link info:{DATABASE_URL}")
    return engine


def create_scoped_session():
    engine = create_async_engine(
        DATABASE_URL,
        echo=False,
        future=True,
        poolclass=NullPool
    )
    async_session_factory = async_sessionmaker(
        bind=engine,
        expire_on_commit=False
    )
    return async_scoped_session(
        session_factory=async_session_factory,
        scopefunc=asyncio.current_task
    )


def create_new_session(engine: AsyncEngine = None):
    if engine is None:
        engine = create_new_engine()
    return sessionmaker(
        bind=engine,
        class_=AsyncSession,
        expire_on_commit=False
    )


# 主线程的连接池
main_thread_engine = create_new_engine(pool_size=50, max_overflow=30)
async_session = create_new_session(main_thread_engine)

# 非主线程使用
scoped_async_session = create_scoped_session()


async def auto_create_tables():
    # 自动建表

    from core import models  # 不要删这一行，删了不会自动建表
    from core.models.base_model import Base
    async def create_tables():
        async with main_thread_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
            log.info('Create Tables Success.')

    try:
        await create_tables()
    except  Exception as e:
        log.error(f"create tables error", e)
