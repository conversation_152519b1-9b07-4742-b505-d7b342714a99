import inspect
from typing import As<PERSON><PERSON><PERSON>ator, Coroutine
from sqlalchemy.ext.asyncio import AsyncSession

from functools import wraps
from contextvars import Context<PERSON><PERSON>
from typing import Callable, Any

from .pgsql_session import scoped_async_session
from common import tools

logger = tools.get_logger(__name__)

_sensors_async_db: ContextVar[AsyncSession | None] = ContextVar('_sensors_async_db', default=None)


def transactional(func: Callable[..., Coroutine[Any, Any, Any]]) -> Callable[..., Coroutine[Any, Any, Any]]:
    is_async_gen = inspect.isasyncgenfunction(func)

    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        async_db = _sensors_async_db.get()
        need_close = False

        if not async_db:
            logger.debug(f"create new async db session for function: {func}")
            async_db = scoped_async_session()
            _sensors_async_db.set(async_db)
            need_close = True

        try:
            has_db_params = False
            if args:
                for i, arg in enumerate(args):
                    if arg is not None and isinstance(arg, AsyncSession):
                        has_db_params = True
            if not has_db_params:
                if 'db' not in kwargs:
                    kwargs['db'] = async_db

            result = await func(*args, **kwargs)

            if need_close:
                await async_db.commit()

            return result
        except Exception as e:
            logger.error(f"transaction error", exc_info=True)
            if need_close and async_db.is_active:
                await async_db.rollback()
            raise e
        finally:
            if need_close:
                await async_db.close()
                _sensors_async_db.set(None)

    @wraps(func)
    async def async_gen_wrapper(*args, **kwargs) -> AsyncGenerator:
        """处理异步生成器函数"""
        async_db = _sensors_async_db.get()
        need_close = False

        if not async_db:
            logger.debug(f"create new async db session for function: {func}")
            async_db = scoped_async_session()
            _sensors_async_db.set(async_db)
            need_close = True

        try:
            has_db_params = False
            if args:
                for i, arg in enumerate(args):
                    if arg is not None and isinstance(arg, AsyncSession):
                        has_db_params = True
            if not has_db_params:
                if 'db' not in kwargs:
                    kwargs['db'] = async_db
            gen = func(*args, **kwargs)

            async for item in gen:
                yield item

            if need_close:
                await async_db.commit()
        except Exception as e:
            logger.error(f"transaction with async generator error", exc_info=True)
            if need_close and async_db.is_active:
                await async_db.rollback()
            raise
        finally:
            if need_close:
                await async_db.close()
                _sensors_async_db.set(None)

    return async_gen_wrapper if is_async_gen else async_wrapper
