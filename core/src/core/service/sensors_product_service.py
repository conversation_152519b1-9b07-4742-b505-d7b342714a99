from datetime import datetime
from typing import List

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func, and_, or_
from core.db.transaction import transactional
from core.models import SensorsProductModel


class SensorsProductService:
    def __init__(self, **kwargs):
        self.kwargs = kwargs

    @transactional
    async def get_sensors_product(
            self,
            product_name: str,
            product_manual_version: str,
            db: AsyncSession = None
    ) -> SensorsProductModel | None:
        query = select(SensorsProductModel).where(
            and_(
                SensorsProductModel.product_name == product_name,
                SensorsProductModel.product_manual_version == product_manual_version,
                SensorsProductModel.is_deleted == False
            )
        ).limit(1)
        items = await db.execute(query)
        return items.scalar_one_or_none()

    @transactional
    async def get_all(self, db: AsyncSession = None) -> List[SensorsProductModel]:
        query = select(SensorsProductModel).where(
            SensorsProductModel.is_deleted == False
        )
        items = await db.execute(query)
        return list(items.scalars().all())

    @transactional
    async def save(self, model: SensorsProductModel, db: AsyncSession = None):
        existed_model = await self.get_sensors_product(
            model.product_name,
            model.product_manual_version,
        )
        if existed_model:
            model.id = existed_model.id
            exclude_columns = {'id', 'version', 'create_time', 'update_time', 'is_deleted'}
            for column, value in model.dict().items():
                if column not in exclude_columns:
                    setattr(existed_model, column, value)
            existed_model.version = existed_model.version + 1
            existed_model.update_time = datetime.now()
        else:
            db.add(model)

    @transactional
    async def delete(self, id: int, db: AsyncSession = None):
        query = delete(SensorsProductModel).where(
            SensorsProductModel.id == id
        )
        await db.execute(query)
