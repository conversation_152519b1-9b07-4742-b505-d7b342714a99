from datetime import datetime
from typing import List

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func, and_, or_
from core.db.transaction import transactional
from core.models import IndustryModel


class IndustryService:
    def __init__(self, **kwargs):
        self.kwargs = kwargs

    @transactional
    async def list_all(self, db: AsyncSession = None) -> List[IndustryModel]:
        query = select(IndustryModel).where(IndustryModel.is_deleted == False)
        items = await db.execute(query)
        result = items.scalars().all()
        if result:
            return list(result)
        else:
            return []

    @transactional
    async def get_by_name(self, name: str, db: AsyncSession = None) -> IndustryModel | None:
        query = select(IndustryModel).where(
            and_(
                IndustryModel.industry == name,
                IndustryModel.is_deleted == False
            )
        )
        items = await db.execute(query)
        result = items.scalars().first()
        return result if result else None

    @transactional
    async def save(self, model: IndustryModel, db: AsyncSession = None):
        existed_model = await self.get_by_name(model.industry)
        if existed_model:
            model.id = existed_model.id
            exclude_columns = {'id', 'version', 'create_time', 'update_time', 'is_deleted'}
            for column, value in model.dict().items():
                if column not in exclude_columns:
                    setattr(existed_model, column, value)
            existed_model.version = existed_model.version + 1
            existed_model.update_time = datetime.now()
        else:
            db.add(model)

    @transactional
    async def delete(self, id: int, db: AsyncSession = None):
        query = delete(IndustryModel).where(
            IndustryModel.id == id
        )
        await db.execute(query)
