from datetime import datetime
from typing import List

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func, and_, or_
from core.db.transaction import transactional
from core.models import TenantProjectModel


class TenantProjectService:
    def __init__(self, **kwargs):
        self.kwargs = kwargs

    @transactional
    async def get_tenant_project(
            self,
            organization_id: str,
            project_id: int,
            db: AsyncSession = None
    ) -> TenantProjectModel | None:
        query = select(TenantProjectModel).where(
            and_(
                TenantProjectModel.organization_id == organization_id,
                TenantProjectModel.project_id == project_id,
                TenantProjectModel.is_deleted == False
            )
        ).limit(1)
        items = await db.execute(query)
        return items.scalar_one_or_none()

    @transactional
    async def get_by_id(
            self,
            id: int,
            db: AsyncSession = None
    ) -> TenantProjectModel | None:
        query = select(TenantProjectModel).where(
            and_(
                TenantProjectModel.id == id,
                TenantProjectModel.is_deleted == False
            )
        ).limit(1)
        items = await db.execute(query)
        return items.scalar_one_or_none()

    @transactional
    async def get_all(self, db: AsyncSession = None) -> List[TenantProjectModel]:
        query = select(TenantProjectModel).where(
            TenantProjectModel.is_deleted == False
        )
        items = await db.execute(query)
        return list(items.scalars().all())

    @transactional
    async def save(self, model: TenantProjectModel, db: AsyncSession = None):
        existed_model = await self.get_tenant_project(
            model.organization_id,
            model.project_id,
        )
        if existed_model:
            model.id = existed_model.id
            exclude_columns = {'id', 'version', 'create_time', 'update_time', 'is_deleted'}
            for column, value in model.dict().items():
                if column not in exclude_columns:
                    setattr(existed_model, column, value)
            existed_model.version = existed_model.version + 1
            existed_model.update_time = datetime.now()
        else:
            db.add(model)

    @transactional
    async def delete(self, id: int, db: AsyncSession = None):
        query = delete(TenantProjectModel).where(
            TenantProjectModel.id == id
        )
        await db.execute(query)
