from datetime import datetime
from typing import List, Dict, Tuple

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func, and_, or_, distinct
from core.db.transaction import transactional
from core.models import PromptModel


class PromptService:
    def __init__(self, **kwargs):
        self.kwargs = kwargs

    @transactional
    async def get_prompt(self, business: str, prompt_code: str, db: AsyncSession = None) -> PromptModel | None:
        """根据业务标识和prompt代码获取prompt"""
        query = select(PromptModel).where(
            and_(
                PromptModel.business == business,
                PromptModel.prompt_code == prompt_code,
                PromptModel.is_deleted == False,
            )
        )
        result = await db.execute(query)
        return result.scalars().first()

    @transactional
    async def get_prompt_by_id(self, prompt_id: int, db: AsyncSession = None) -> PromptModel | None:
        """根据ID获取prompt"""
        query = select(PromptModel).where(
            and_(
                PromptModel.id == prompt_id,
                PromptModel.is_deleted == False,
            )
        )
        result = await db.execute(query)
        return result.scalars().first()

    @transactional
    async def find_business_cname(self, business: str, db: AsyncSession = None) -> str | None:
        query = select(PromptModel).where(
            and_(
                PromptModel.business == business,
                PromptModel.is_deleted == False
            )
        )
        result = await db.execute(query)
        result = result.scalars().first()
        return result.business_cname if result else None

    @transactional
    async def get_all_businesses(self, db: AsyncSession = None) -> List[Tuple[str, str]]:
        """获取所有业务标识和名称"""
        query = select(
            distinct(PromptModel.business),
            PromptModel.business_cname
        ).where(PromptModel.is_deleted == False)
        result = await db.execute(query)
        return result.all()

    @transactional
    async def get_prompts_by_business(self, business: str, db: AsyncSession = None) -> List[PromptModel]:
        """根据业务标识获取所有prompt"""
        query = select(PromptModel).where(
            and_(
                PromptModel.business == business,
                PromptModel.is_deleted == False,
            )
        ).order_by(PromptModel.create_time.desc())
        result = await db.execute(query)
        return result.scalars().all()

    @transactional
    async def create_prompt(self, prompt: PromptModel, db: AsyncSession = None):
        """创建新的prompt"""
        prompt.create_time = datetime.now()
        prompt.update_time = datetime.now()
        db.add(prompt)
        await db.flush()
        return prompt

    @transactional
    async def update_prompt(self, prompt_id: int, db: AsyncSession = None, **kwargs):
        """更新prompt"""
        kwargs['update_time'] = datetime.now()
        query = update(PromptModel).where(
            and_(
                PromptModel.id == prompt_id,
                PromptModel.is_deleted == False,
            )
        ).values(**kwargs)
        await db.execute(query)

    @transactional
    async def delete_prompt(self, prompt_id: int, db: AsyncSession = None):
        """软删除prompt"""
        query = update(PromptModel).where(
            PromptModel.id == prompt_id
        ).values(
            is_deleted=True,
            update_time=datetime.now()
        )
        await db.execute(query)

    @transactional
    async def check_prompt_code_exists(self, business: str, prompt_code: str, exclude_id: int = None,
                                       db: AsyncSession = None) -> bool:
        """检查prompt代码是否已存在"""
        conditions = [
            PromptModel.business == business,
            PromptModel.prompt_code == prompt_code,
            PromptModel.is_deleted == False,
        ]
        if exclude_id:
            conditions.append(PromptModel.id != exclude_id)

        query = select(func.count(PromptModel.id)).where(and_(*conditions))
        result = await db.execute(query)
        count = result.scalar()
        return count > 0
