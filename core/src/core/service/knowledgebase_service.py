from datetime import datetime
from typing import List

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func, and_, or_
from core.db.transaction import transactional
from core.models import KnowledgebaseModel


class KnowledgebaseService:
    def __init__(self, **kwargs):
        self.kwargs = kwargs

    @transactional
    async def get_all_knowledgebase(self, db: AsyncSession = None) -> List[KnowledgebaseModel]:
        query = select(KnowledgebaseModel).where(KnowledgebaseModel.is_deleted == False)
        items = await db.execute(query)
        result = items.scalars().all()
        if result:
            return list(result)
        else:
            return []

    @transactional
    async def get_all_knowledgebase_by_type(self, index_type: str, db: AsyncSession = None) -> List[KnowledgebaseModel]:
        query = select(KnowledgebaseModel).where(
            and_(
                KnowledgebaseModel.is_deleted == False,
                KnowledgebaseModel.index_type == index_type
            )
        )
        items = await db.execute(query)
        result = items.scalars().all()
        if result:
            return list(result)
        else:
            return []

    @transactional
    async def save_knowledgebase(
            self,
            knowledgebase: KnowledgebaseModel,
            db: AsyncSession = None
    ) -> KnowledgebaseModel:
        name = knowledgebase.name

        existed_kb = await self.get_knowledgebase_by_name(name)
        if existed_kb:
            knowledgebase.id = existed_kb.id
            exclude_columns = {'id', 'version', 'create_time', 'update_time', 'is_deleted'}
            for column, value in knowledgebase.dict().items():
                if column not in exclude_columns:
                    setattr(existed_kb, column, value)
            existed_kb.version = existed_kb.version + 1
            existed_kb.update_time = datetime.now()
            return knowledgebase

        db.add(knowledgebase)
        return knowledgebase

    @transactional
    async def get_knowledgebase_by_name(
            self,
            name: str,
            db: AsyncSession = None
    ) -> KnowledgebaseModel | None:
        query = select(KnowledgebaseModel).where(
            and_(
                KnowledgebaseModel.is_deleted == False,
                KnowledgebaseModel.name == name
            )
        ).limit(1)
        items = await db.execute(query)
        return items.scalar_one_or_none()

    @transactional
    async def get_knowledgebase(
            self,
            id: int,
            db: AsyncSession = None
    ) -> KnowledgebaseModel | None:
        query = select(KnowledgebaseModel).where(
            and_(KnowledgebaseModel.is_deleted == False, KnowledgebaseModel.id == id)
        ).limit(1)
        items = await db.execute(query)
        return items.scalar_one_or_none()

    @transactional
    async def delete(self, id: int, db: AsyncSession = None):
        query = delete(KnowledgebaseModel).where(KnowledgebaseModel.id == id)
        await db.execute(query)
