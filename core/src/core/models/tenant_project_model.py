from sqlalchemy import Column, String, Integer, Text, Boolean, BigInteger
from .base_model import BaseModel


class TenantProjectModel(BaseModel):
    __tablename__ = 'tenant_project'

    tenant_cname = Column(String(255), nullable=True, default="", comment='租户名称')
    organization_id = Column(String(255), nullable=False, default="", comment='租户id标识')
    cdp_host = Column(String(255), nullable=False, default="", comment='CDP 实例地址')
    project_name = Column(String(255), nullable=False, default="", comment='项目名称')
    project_id = Column(BigInteger, nullable=False, comment='项目 ID')
    api_key = Column(String(255), nullable=False, default="", comment='CDP 实例 OpenAPI Key')
    product_versions = Column(Text, nullable=True, default=None, comment='产品版本: {"sa":"3.0.0", ...}')
    industry = Column(String(255), nullable=True, default=0, comment='所述行业代码')
