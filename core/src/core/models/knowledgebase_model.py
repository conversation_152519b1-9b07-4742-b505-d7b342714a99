from sqlalchemy import Column, String, Integer, Text, Boolean
from .base_model import BaseModel


class KnowledgebaseModel(BaseModel):
    __tablename__ = 'knowledgebase'

    cname = Column(String(255), nullable=True, default="", comment='知识库名称')
    name = Column(String(255), nullable=False, default="", unique=True, comment='知识库标识')
    description = Column(Text, nullable=True, default="", comment='知识库使用描述')
    base_dir = Column(String(255), nullable=False, default="", comment='知识库文件存储路径')
    index_type = Column(
        String(255), nullable=False, default="SIMPLE",
        comment='索引类型 SIMPLE | GRAPH | PRESET_SENSORS | PRESET_COMMON | PRESET_INDUSTRY | PRESET_CUSTOMER'
    )
    config = Column(Text, nullable=False, default="{}", comment='知识库配置 config json')
    custom_knowledge_class = Column(
        Text, nullable=True, default=None,
        comment='知识库自定义知识类，设置为空则使用默认的知识库类实现'
    )
