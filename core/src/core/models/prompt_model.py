from sqlalchemy import Column, String, Integer, Text, Boolean
from .base_model import BaseModel


class PromptModel(BaseModel):
    __tablename__ = 'prompt'

    business = Column(String(255), nullable=False, default="", comment='业务标识，比如 SF')
    business_cname = Column(String(255), nullable=False, default="", comment='业务标识名称，比如：神策智能运营')
    prompt_name = Column(String(255), nullable=False, comment='prompt 模板名称，比如：系统提示语')
    prompt_desc = Column(Text, nullable=True, comment='prompt 模板描述')
    prompt_code = Column(String(255), nullable=False, comment='prompt 模板代码标识，比如：SYSTEM_PROMPT')
    prompt_content = Column(Text, nullable=False, comment='prompt 模板内容, jinjia2 模板')
