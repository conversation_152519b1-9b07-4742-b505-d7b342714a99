from datetime import datetime
from sqlalchemy import Column, BigInteger, DateTime, Boolean, Sequence
from sqlalchemy.ext.declarative import declarative_base

# 正确创建Base实例
Base = declarative_base()


class BaseModel(Base):
    __abstract__ = True

    id = Column(
        BigInteger, Sequence('id_seq', start=1, increment=1, optional=True), primary_key=True, index=True
    )
    version = Column(BigInteger, default=1, nullable=False)
    create_time = Column(DateTime, default=lambda: datetime.now().replace(tzinfo=None), nullable=False)
    update_time = Column(DateTime, default=lambda: datetime.now().replace(tzinfo=None), nullable=False)
    is_deleted = Column(Boolean, default=False, nullable=False)

    def dict(self):
        """convert current model as dict"""
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}
