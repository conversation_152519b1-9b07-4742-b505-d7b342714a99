from sqlalchemy import Column, String, Integer, Text, Boolean, BigInteger
from .base_model import BaseModel


class SensorsProductModel(BaseModel):
    __tablename__ = 'sensors_product'

    product_name = Column(String(255), nullable=False, default="", comment='产品名称，比如：SA')
    product_cname = Column(String(255), nullable=False, default="", comment='产品中文名称，比如：神策分析')
    product_manual_version = Column(String(255), nullable=False, default="", comment='产品手册版本')
    product_manual_page_url = Column(String(255), nullable=False, default="", comment='产品手册第一页的页面 url')
