[project]
name = "core"
version = "1.0.0"
classifiers = ["Private :: Do Not Upload"]
description = ""
authors = [
    { name = "<EMAIL>" }
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "sqlalchemy>=1.4.0",
    "asyncpg>=0.27.0",
    "sqlalchemy-serializer>=1.4.12",
    "jinja2>=3.1.6",
]

[tool.setuptools]
packages = ["core"]

[tool.setuptools.package-dir]
core = "src/core"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
