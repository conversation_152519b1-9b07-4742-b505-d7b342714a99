[project]
name = "framework"
version = "1.0.0"
classifiers = ["Private :: Do Not Upload"]
description = ""
authors = [
    { name = "<EMAIL>" }
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "common",
    "core",
    "dotenv>=0.9.9",
    "fastembed>=0.4.2",
    "future>=1.0.0",
    "graspologic>=3.4.2.dev5",
    "httpx>=0.28.1",
    "langchain-community>=0.3.5",
    "langchain-ollama>=0.2.3",
    "llama-index-core>=0.12.4, <=0.12.37",
    "llama-index-graph-stores-nebula>=0.4.0",
    "llama-index-vector-stores-qdrant>=0.4.0",
    "nebula3-python>=3.8.3",
    "networkx>=3.4.2",
    "nltk>=3.9.1",
    "ollama>=0.4.9",
    "openai>=1.82.0",
    "pandas>=2.2.3",
    "pydantic>=2.11.5",
    "qdrant-client>=1.12.1",
    "requests>=2.32.3",
    "scipy>=1.12.0",
    "tenacity>=9.1.2",
    "unstructured>=0.17.2",
]

[tool.uv.sources]
common = { workspace = true }
core = { workspace = true }

[tool.setuptools]
packages = ["framework"]

[tool.setuptools.package-dir]
framework = "src/framework"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
