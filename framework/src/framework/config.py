import os
from pathlib import Path

import dotenv

dotenv.load_dotenv()
DATA_PATH = Path(os.environ.get('DATA_PATH', str(Path().home() / "data")))

prompt_path = str(DATA_PATH / "prompts")

model_config = [
    {
        'model': 'gpt-4o',
        'api_key': 'sk-MDBIAn59vWo8TkTrGwuxJw',
        'base_url': 'https://llmapi.sensorsdata.cn/v1',
        'type': 'openai_chat',
        'model_capabilities': {
            "vision": True,
            "function_calling": True,
            "json_output": True,
        },
        'input_token_limit': 1048576,
        'output_token_limit': 65535,
    },
    {
        'model': 'deepseek-v3-volces',
        'api_key': 'sk-MDBIAn59vWo8TkTrGwuxJw',
        'base_url': 'https://llmapi.sensorsdata.cn/v1',
        'type': 'openai_chat',
        'model_capabilities': {
            "vision": True,
            "function_calling": True,
            "json_output": True,
        },
        'input_token_limit': 1000 * 128,
        'output_token_limit': 1000 * 8,
    }
]

# 不同场景使用的模型
scenario_reference_model = {
    'default': 0,
    'single_aud_rule_parse': 1,
    "canvas_predict": 0,
}

llm_config = model_config[scenario_reference_model['default']]

knowledge_config = {
    'data_root': os.environ.get('DATA_PATH', '/data') + '/knowledge',
    "sensors_knowledge_config": {
        "graphrag_envs": {
            'chunk_size': llm_config['input_token_limit'] / 5,
            'chunk_overlap': 400,
            'response_type': 'Multiple Paragraphs',
        },
    },
    "vector_storage_config": {
        # 'model': 'BAAI/bge-m3',
        'model': 'ollama:bge-m3',
        'type': 'sensors',
        'db_url': 'http://**********:6333',
        'vector_size': 1024
    },
    "tokenizer_config": {
        'encoding_name': 'cl100k_base',
    },
    "graph_storage_config": {
        "hosts": [('**********', 8003)],
        "max_connection_pool_size": 10,
        "user": "root",
        "password": "123456",
    }
}

vector_storage_config = knowledge_config["vector_storage_config"]
graph_storage_config = knowledge_config["graph_storage_config"]

DEBUG_MODE = bool(os.environ.get('DEBUG_MODE', False))
