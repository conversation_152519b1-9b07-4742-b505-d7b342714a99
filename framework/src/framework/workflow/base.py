from asyncio import iscoroutine
from inspect import iscoroutinefunction
from typing import Callable, Optional, Any

from llama_index.core.workflow import Workflow
from llama_index.core.workflow.service import ServiceManager

from common import tools

log = tools.get_logger()


class WorkFlowWithCallback(Workflow):
    def __init__(
            self,
            callback: Callable[[Any], None] | None = None,
            timeout: Optional[float] = 10.0,
            disable_validation: bool = False,
            verbose: bool = False,
            service_manager: Optional[ServiceManager] = None,
            num_concurrent_runs: Optional[int] = None,
            **kwargs
    ):
        super(WorkFlowWithCallback, self).__init__(
            timeout=timeout, disable_validation=disable_validation,
            verbose=verbose, service_manager=service_manager,
            num_concurrent_runs=num_concurrent_runs
        )
        self.callback = callback

    async def _callback(self, content: Any):
        try:
            log.info(content)
            if self.callback is None:
                return
            if iscoroutinefunction(self.callback):
                response = await self.callback(content)
            else:
                response = self.callback(content)

            if response is None:
                return

            if iscoroutine(response):
                await response
        except BaseException as e:
            log.warning('callback error.', e)
