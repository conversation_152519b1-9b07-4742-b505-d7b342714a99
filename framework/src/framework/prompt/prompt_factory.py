# 提示词获取器
from datetime import datetime

from jinja2 import Template

from core.models import PromptModel
from core.service.prompt_service import PromptService
from common import tools


def _static_properties() -> dict:
    props = {
        "curr_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    return props


class PromptFactory:
    def __init__(self, **kwargs):
        self.kwargs = kwargs

    @classmethod
    def create_or_get_prompt(
            cls,
            business: str,
            prompt_code: str,
            business_cname: str | None = None,
            prompt_name: str | None = None,
            prompt_desc: str | None = None,
            prompt_content: str | None = None,
    ) -> Template:
        return tools.asyncio_run(
            cls.async_create_or_get_prompt,
            business=business,
            prompt_code=prompt_code,
            business_cname=business_cname,
            prompt_name=prompt_name,
            prompt_desc=prompt_desc,
            prompt_content=prompt_content,
        )

    @classmethod
    async def async_create_or_get_prompt(
            cls,
            business: str,
            prompt_code: str,
            business_cname: str | None = None,
            prompt_name: str | None = None,
            prompt_desc: str | None = None,
            prompt_content: str | None = None,
    ) -> Template:
        prompt_service = PromptService()
        prompt = await prompt_service.get_prompt(business=business, prompt_code=prompt_code)
        if prompt is not None:
            return Template(prompt.prompt_content)

        # create prompt
        if not prompt_content:
            prompt_content = "You are a helpful assistant"
        if not business_cname:
            business_cname = await prompt_service.find_business_cname(business=business)
            if not business_cname:
                business_cname = business
        if not prompt_name:
            prompt_name = prompt_code
        if not prompt_desc:
            prompt_desc = ""

        await prompt_service.create_prompt(
            prompt=PromptModel(
                business=business,
                business_cname=business_cname,
                prompt_code=prompt_code,
                prompt_name=prompt_name,
                prompt_desc=prompt_desc,
                prompt_content=prompt_content
            )
        )
        return Template(prompt_content)

    @classmethod
    def render_prompt(cls, template: str | Template, **kwargs) -> str:
        if isinstance(template, str):
            template = Template(template)
        kwargs = kwargs | _static_properties()
        return template.render(**kwargs)
