from abc import ABC
from typing import Any, Dict, List, Union
from dataclasses import field, dataclass
from pydantic import BaseModel
from qdrant_client.conversions.common_types import Filter

from framework.models.embeddings import TextEmbeddingFactory
from framework.config import vector_storage_config
from common import tools

log = tools.get_logger()


class VectorDocument(BaseModel):
    """A document that is stored in vector storage."""

    id: str | int
    """unique id for the document"""

    vector: list[float] | list[list[float]] | None

    attributes: Dict[str, Any] = field(default_factory=Dict)
    """store any additional metadata, e.g. title, date ranges, etc"""


class VectorSearchResult(BaseModel):
    """search result model"""
    score: float | None

    document: VectorDocument | None


class LarkVectorStore(ABC):
    """向量数据库存储客户端"""

    def __init__(
            self,
            collection_name: str,
            model: str = vector_storage_config['model'],
            vector_store_size: int = vector_storage_config['vector_size'],
            **kwargs: Any
    ):
        self.collection_name = collection_name
        self.kwargs = kwargs
        self.vector_store_size = vector_store_size
        self.embedding_model = model
        self.embedder = TextEmbeddingFactory.get_text_embedding(
            model_name=self.embedding_model)

    def connect(self, **kwargs: Any) -> None:
        """lazy connect to db"""
        ...

    def load_documents(self, documents: list[VectorDocument], overwrite: bool = True) -> None:
        """load documents from texts to db"""
        ...

    def load_text(self, document_id: str, payload: Dict[str, Any], embed_keys: set[str], ) -> None:
        """load single text"""
        ...

    def batch_load_text(
            self,
            document_ids: List[str],
            payloads: List[Dict[str, Any]],
            embed_keys: set[str],
    ) -> None:
        """batch load documents without embedding"""
        ...

    def similarity_search_by_vector(
            self,
            query_embedding: list[float],
            query_filter: Filter | None = None,
            k: int = 10,
            **kwargs: Any
    ) -> list[VectorSearchResult]:
        """search by vectors"""
        ...

    def similarity_search_by_text(
            self,
            text: str,
            query_filter: Filter | None = None,
            k: int = 10,
            **kwargs: Any
    ) -> list[VectorSearchResult]:
        """search by texts"""
        ...

    def filter(
            self,
            query_filter: Filter | None = None,
            k: int = 10,
            **kwargs: Any
    ) -> list[VectorDocument]:
        """search by filters"""
        ...

    def filter_by_id(self, include_ids: list[str] | list[int]) -> List[VectorDocument]:
        """filter by id"""
        ...

    def search_by_id(self, id: str) -> VectorDocument | None:
        """search by id"""
        ...

    def delete_by_ids(self, ids: List[str]):
        """delete by ids"""
        ...

    def clear_all_data(self):
        """clean all data of the collection"""
        ...


class LarkVectorStoreFactory:

    @classmethod
    def get_vector_store(
            cls,
            collection_name: str,
            store_config: Union[Dict[str, Any], None] = None,
            **kwargs
    ) -> LarkVectorStore:
        if not store_config:
            store_config = vector_storage_config
        from .qdrant_vector_store import QdrantVectorStore
        return QdrantVectorStore(collection_name=collection_name, **store_config, **kwargs)
