# 知识存储
import json
from typing import Any, Dict, List

from qdrant_client import QdrantClient, models
from qdrant_client.conversions.common_types import VectorParams, PointStruct, Filter
from qdrant_client.http.models import Distance, MultiVectorConfig, MultiVectorComparator

from common import tools
from framework.vector_store import LarkVectorStore, VectorDocument, VectorSearchResult
from framework.config import vector_storage_config

log = tools.get_logger()


def _get_batches_1(objs: List[Any] | List[List[Any]], batch_size: int) -> List:
    if not objs:
        return []
    for obj in objs:
        yield [obj]


def _get_batches(objs: List[Any] | List[List[Any]], batch_size: int) -> List:
    if not objs:
        return []

    size = 0
    from_index = 0
    for i in range(len(objs)):
        new_size = size
        if isinstance(objs[i], List):
            new_size += len(objs[i])
        else:
            new_size += 1
        if new_size > batch_size:
            size = len(objs[i]) if isinstance(objs[i], List) else 1
            yield objs[from_index:i]
            from_index = i
        elif i == len(objs) - 1:
            yield objs[from_index:]
        else:
            size = new_size


class QdrantVectorStore(LarkVectorStore):
    """ 基于 Qdrant 实现的向量数据库，相关评估文档：https://doc.sensorsdata.cn/pages/viewpage.action?pageId=547232071 """

    def __init__(
            self,
            collection_name: str,
            model: str = vector_storage_config['model'],
            vector_store_size: int = vector_storage_config['vector_size'],
            db_url: str = vector_storage_config['db_url'],
            api_key: str | None = vector_storage_config.get('api_key', None),
            **kwargs,
    ):
        super().__init__(collection_name=collection_name,
                         model=model,
                         vector_store_size=vector_store_size,
                         db_url=db_url,
                         api_key=api_key,
                         **kwargs,
                         )
        self.db_url = db_url
        self.api_key = api_key

    def connect(self) -> None:
        if not self.db_url:
            raise RuntimeError('db_url not found.')
        self._client = QdrantClient(
            url=self.db_url,
            api_key=self.api_key,
        )
        if not self._client.collection_exists(collection_name=self.collection_name):
            self._client.create_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(
                    size=self.vector_store_size,
                    distance=Distance.COSINE,
                    multivector_config=MultiVectorConfig(comparator=MultiVectorComparator.MAX_SIM)
                )
            )

    def _get_point_struct(self, document: VectorDocument) -> PointStruct:
        """自动转换，document_id 也自动转一下"""
        result = PointStruct(
            id=document.id,
            vector=document.vector,
            payload=document.attributes,
        )

        if document.id is not None:
            if isinstance(document.id, str):
                if len(document.id) > 32:
                    document.id = document.id[-32:]
                elif len(document.id) < 32:
                    document.id = document.id + '0' * (32 - len(document.id))
                result.id = tools.uuid4_no_underline_to_uuid(document.id)
        return result

    def load_documents(self, documents: list[VectorDocument], overwrite: bool = True) -> None:
        if not documents or len(documents) == 0:
            return

        data = [
            self._get_point_struct(document=document)
            for document in documents
            if document.vector is not None
        ]
        upsert_batch_size = 20
        for part_data in _get_batches(data, upsert_batch_size):
            self._client.upsert(
                collection_name=self.collection_name,
                wait=True,
                points=part_data,
            )

    def load_text(self, document_id: str, payload: Dict[str, Any], embed_keys: set[str], ) -> None:
        self.batch_load_text(
            document_ids=[document_id],
            payloads=[payload],
            embed_keys=embed_keys,
        )

    def batch_load_text(
            self,
            document_ids: List[str],
            payloads: List[Dict[str, Any]],
            embed_keys: set[str],
    ) -> None:
        if not payloads or not embed_keys or not document_ids:
            log.warning('document_ids and payloads and embed_keys can not be empty.')
            return
        embed_keys = list(embed_keys)
        embed_texts = []
        for payload in payloads:
            txt_list = []
            for key in embed_keys:
                txt = payload.get(key, None)
                if not txt:
                    continue
                if isinstance(txt, str):
                    if txt.strip():
                        txt_list.append(txt.strip())
                elif isinstance(txt, list):
                    txt_list.extend([t for t in txt if isinstance(t, str) and t.strip()])
                else:
                    txt_list.append(json.dumps(txt, ensure_ascii=False))
            embed_texts.append(txt_list)

        batch_size = self.embedder.suggested_batch_size()

        doc_index = 0
        for embed_text_part in _get_batches(embed_texts, batch_size):
            temp_texts = []
            for t in embed_text_part:
                temp_texts.extend(t)
            flat_vectors = self.embedder.batch_embed(texts=temp_texts)
            vectors = []
            v_index = 0
            for t in embed_text_part:
                vectors.append(flat_vectors[v_index:v_index + len(t)])
                v_index += len(t)
            doc_id_parts = document_ids[doc_index:doc_index + len(embed_text_part)]
            payload_parts = payloads[doc_index:doc_index + len(embed_text_part)]
            doc_index += len(embed_text_part)
            data = []
            for doc_id, payload, vec in zip(doc_id_parts, payload_parts, vectors):
                d = VectorDocument.model_validate({
                    'id': doc_id,
                    'vector': vec,
                    'attributes': payload
                })
                data.append(d)
            self.load_documents(documents=data)

    def similarity_search_by_vector(
            self,
            query_embedding: list[float],
            query_filter: Filter | None = None,
            k: int = 10,
            **kwargs: Any
    ) -> list[VectorSearchResult]:
        search_params = None
        if kwargs and 'search_params' in kwargs:
            search_params = models.SearchParams(**(kwargs['search_params']))
        points = self._client.query_points(
            collection_name=self.collection_name,
            query=query_embedding,
            query_filter=query_filter,
            with_payload=True,
            with_vectors=False,
            search_params=search_params,
            limit=k
        ).points
        results = []
        if not points:
            return results
        for point in points:
            vector_result = VectorSearchResult(
                score=point.score,
                document=VectorDocument(id=point.id, attributes=point.payload, vector=None),
            )
            results.append(vector_result)
        return results

    def similarity_search_by_text(
            self,
            text: str,
            query_filter: Filter | None = None,
            k: int = 10,
            **kwargs: Any
    ) -> list[VectorSearchResult]:
        """Perform a similarity search using a given input text."""
        text_embedder = self.embedder.embed
        query_embedding = text_embedder(text)
        if query_embedding:
            return self.similarity_search_by_vector(query_embedding, query_filter=query_filter, k=k)
        return []

    def filter(
            self,
            query_filter: Filter | None = None,
            k: int = 10,
            **kwargs: Any
    ) -> list[VectorDocument]:
        points = self._client.query_points(
            collection_name=self.collection_name,
            query_filter=query_filter,
            with_payload=True,
            with_vectors=False,
        ).points
        results = []
        if not points:
            return results
        for point in points:
            document = VectorDocument(id=point.id, attributes=point.payload, vector=None)
            results.append(document)
        return results

    def filter_by_id(self, include_ids: list[str] | list[int]) -> List[VectorDocument]:
        if len(include_ids) == 0:
            return []
        if not isinstance(include_ids[0], str):
            include_ids = [str(_) for _ in include_ids]

        query_filter = models.Filter(
            must=models.HasIdCondition(has_id=include_ids)
        )
        return self.filter(query_filter=query_filter, k=100_000)

    def search_by_id(self, id: str) -> VectorDocument | None:
        docs = self.filter_by_id(include_ids=[id])
        if docs:
            return docs[0]
        return None

    def clear_all_data(self):
        log.warning(f'clear all data of collection {self.collection_name}!!!')

        # delete old collection
        self._client.delete_collection(self.collection_name)

        # create new collection
        self._client.create_collection(
            collection_name=self.collection_name,
            # vectors_config={'default': VectorParams(size=self.vector_store_size, distance=Distance.COSINE)}
            vectors_config=VectorParams(
                size=self.vector_store_size,
                distance=Distance.COSINE,
                multivector_config=MultiVectorConfig(comparator=MultiVectorComparator.MAX_SIM)
            )
        )
