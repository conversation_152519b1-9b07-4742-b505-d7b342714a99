from typing import List

from pydantic import BaseModel


class SensorsDocMetadata(BaseModel):
    file_name: str
    id: str | None = None
    url: str | None = None
    title: str | None = None
    content_sign: str | None = None  # 内容签名，更新的时候使用
    batch_id: int  # 表示数据更新的批次号


class SensorsDocsMetadata(BaseModel):
    batch_id: int
    indexed_batch_id: int = 0
    start_time: str | None = None
    end_time: str | None = None
    documents: List[SensorsDocMetadata]

    def get_doc(self, id: str | None = None, url: str | None = None, ) -> SensorsDocMetadata | None:
        for doc in self.documents:
            if (id and doc.id == id) or (url and doc.url == url):
                return doc
        return None

    def delete_doc(self, id: str | None = None, url: str | None = None, ) -> None:
        exist_doc = self.get_doc(id=id, url=url)
        if exist_doc:
            self.documents.remove(exist_doc)

    def upsert_doc(self, doc: SensorsDocMetadata) -> None:
        if doc:
            self.delete_doc(id=doc.id, url=doc.url)
            self.documents.append(doc)
