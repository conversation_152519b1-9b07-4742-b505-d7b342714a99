"""画布模板推荐工具"""
import json
from pathlib import Path

from typing import Any, Type, List, Dict

from jinja2 import Template
from pydantic import BaseModel, Field
from langchain_core.tools import BaseTool

from framework.prompt import PromptFactory
from framework.models import BaseLLM, ModelsFactory
from common import tools

log = tools.get_logger()

SUMMARY_STRUCTURE_PROMPT = """## 职责描述
你是一个智能营销助手，给你一个流程画布的营销模板，将模板总结为自然语言描述的步骤比如：
```
监测转化目标：追踪用户触达后7天内客户的绑卡、微信银行开通动账提醒情况
流程画布结构：
  1. 触发条件：选取用户活跃较高的时间段（如早上九点），定时重复对受众进行触发，如每个用户仅可发生一次开户行为，可以不允许重入
  2. 通过用户的手机银行绑卡和微信银行开通动帐提醒的情况对受众进行分流
  3. 圈选手机银行未绑卡 且 微信未开通动账提醒的客群
  4. ...
```

务必注意：结构描述包含「监测转化目标」和「流程画布结构」两个部分，你必须将结构描述放在 markdown 的代码块中！

## 额外知识
为了更好的理解流程画布模板，你可能需要以下知识：
流程画布可以用来规划编排个性化的用户旅程，并自动化的执行各个阶段的营销动作。相比于多个独立的点阵式的营销活动的触达，流程画布保证了用户旅程流转的顺畅，以及用户体验的一致性。能够在合适的时间、给合适的人、用合适的触达通道、以合适的频次发送合适的内容，并回收每个营销阶段的执行数据用以衡量活动效果，打造运营闭环。
流程画布通过组合组件来编排用户旅程，实现对目标受众的圈选、流转的控制、营销内容的触达。同时支持设置全局的触达频控、勿扰设置保障用户体验，活动级别的目标设置追踪活动效果。
流程画布模板是用来创建流程画布的模板，是由画布转化监控指标（convert_rule_list）、组件信息（component_list）、组件之间的关联关系（component_edge_list）等组成，用户从进入组件开始，在组件之间流转。
组件类型和他们的可配置项如下：
* 进入：type=ENTER，包括定时单次进入（sub_type=FIXED_TIME）、定时重复进入（sub_type=ROUTINE）、完成事件进入（sub_type=TRIGGER_ONLY_A）、未完成事件进入（即完成A未完成B，sub_type=TRIGGER_A_NOT_B）四种类型。定时单次进入需要配置进入时间（fixed_time）、结束时间（schedule_end）、受众条件（audience）、以及组件名称（name）；定时重复进入需要配置开始时间和运行周期（每天、周、月几点开始运行，cron）、结束时间、受众条件、以及组件名称；完成事件进入需要配置开始和结束时间、事件规则（event，比如：完成APP点击事件且点击时间在上午10点之后）、受众规则（audience）、是否允许重入、以及组件名称；未完成事件指的是在完成某个事件之后一段时间未完成另外一个事件，在完成事件进入的规则基础上添加一段时间未完成另外一个事件的规则（event）。
* 受众判定：type=CONDITION, 包含批受众判定（sub_type=ROUTINE | FIXED_TIME，ROUTINE表示定时重复计算，FIXED_TIME 表示定时单次计算），受众就是根据一定 user 和 events 序列规则圈选的一组人群。比如圈选规则可以为「年龄大于10岁，且最近2个月做过登录APP事件」为一个规则。受众判定即为将上层组件流转过来的用户按照一定规则进行判定，判定满足规则则继续往下流转，否则结束。受众判定组件中需要配置受众规则。
* 事件判定：type=CONDITION, sub_type=EVENT, 即为将上层组件流转下来的用户按照实时发生的事件规则进行判定，需要注意这里是实时事件的判定，比如「5分钟内做过APP启动事件且启动客户端为IOS客户端」是一个有效的实时事件判定规则。事件判定组件中需要配置事件规则。
* 事件分流：type=CONTROL, sub_type=EVENT_SPLITTER, 将上层组件流转下来的用户按照实时发生的事件规则进行分流，并流转到符合事件判定条件的子节点，事件分流可以有多个分支，分支节点为「事件判定」组件或者 sub_type=ELSE_BRANCH 的组件，每个分支独立判断事件，比如「5分钟内发生APP启动事件，则走分支1，,5分钟内发送注册事件则走分支2...」，sub_type=ELSE_BRANCH 的子节点表示前面的事件都不符合则流转到这个节点。
* 进入事件分流：type=CONTROL, sub_type=EVENT_PROPERTIES_SPLITTER，该分流器只能在进入(ENTER)组件为完成事件进入或者未完成事件进入时可用，表示根据进入的事件属性进行判定和流转，判定和流转逻辑与事件分流一致。
* 受众分流：type=CONTROL, sub_type=AUDIENCE_SPLITTER, 与事件分流类似，不同之处在于受众分流的每个子节点是按照受众进行判定的。子节点仅支持「受众判定」组件或者 sub_type=ELSE_BRANCH 的子节点。sub_type=ELSE_BRANCH 表示默认分支，即前面的条件都不符合流转到这个分支（对所有分流器都适用）。
* 比例分流：type=CONTROL, sub_type=PERCENT_SPLITTER, 将上层组件流转下来的用户按照流量比例进行切分，一般是用于 AB 测试使用，当作为 AB 测试的时候，第一个分支为对照组，其余分支为实验组，不同分支流量之和为 100%。比例分流组件主要需要配置各个分支的流量分配。比例分流子节点的 type=CONTROL, sub_type=PERCENT_SPLITTER_BRANCH
* 时段分流：type=CONTROL, sub_type=TIME_SPLITTER, 将上层组件流转下来的用户按照流转的时间段进行划分，比如流转到该组件的时间为3点到5点，则流转到分支1，否则流转到分支2等，每个分支子节点上有对应的满足时段信息。时段分流子节点的 sub_type=TIME_SPLITTER_BRANCH。
* 时间控制：type=CONTROL, sub_type=DELAY, 时间控制组件一般是用来做时间延迟使用的，表示从上层流转下来的用户需要按照一定的规则延迟到一定的时间后才继续向下流转。延迟规则包括等待一段时间和等待至指定时间两种：等待一段时间可以配置等待XX天/小时/分钟，并且可以添加固定时刻后流转，比如等待1天，或者等待1天后在12:00继续流转；等待至指定时间需要配置等待至每天/周/月的几点几分、周几+几点几分、几号+几点几分，或者可以指定一个固定日期的固定时刻。
* 营销动作：type=ACTION, sub_type 根据通道不同而不同, 营销动作组件需要配置营销内容，以及勿扰策略。比如发的优惠券详情或者发送的 SMS 详情等，勿扰指的是在特定时间段内不打扰用户，并选择该特定时间内的营销内容是勿扰结束后发送还是直接丢弃不发送。不同的营销动作配置内容不一样，发送内容略有差异。
* 结束：type=CONTROL, sub_type=END, 作为结束节点存在，结束后用户会退出画布。
  除此之外，画布还需要配置一些营销目标（可选），营销目标可以有多个，每个营销目标使用实时事件的方式监控，比如：在 5 分钟内完成APP启动事件，且APP为 IOS 版本，则算完成一次目标，或者在 2024-08-20 00:00:00 之前完成APP启动事件，且APP为 IOS 版本，则算完成一次目标。可以选择在完成目标的时候用户是否直接退出画布。

## 开始
流程画布模板如下：
```json
__CANVAS_TEMPLATE__
```

请回复：
"""


async def get_canvas_template_recommend_summary_structure_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="神策智能运营",
        prompt_code="CANVAS_TEMPLATE_SUMMARY_STRUCTURE_PROMPT",
        prompt_name="画布模板总结提示词",
        prompt_desc="画布模板总结提示词",
        prompt_content=SUMMARY_STRUCTURE_PROMPT,
    )
    return template


CANVAS_DESC_TEMPLATE = """biz_no: {{biz_no}}
cname: {{cname}}
模板描述: {{template_description}}
转化目标: {{template_convert_setting_desc}}
功能说明: {{template_intent}}
"""


async def get_canvas_template_canvas_desc_template() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="神策智能运营",
        prompt_code="CANVAS_TEMPLATE_DESC_PROMPT",
        prompt_name="画布模板描述模板",
        prompt_desc="画布模板描述提示词",
        prompt_content=CANVAS_DESC_TEMPLATE,
    )
    return template


CANVAS_TEMPLATE_RECOMMEND_PROMPT = """## 职责描述
你是一个智能营销助手，给你一些营销流程画布的模板，希望你能从中推荐之多 __RECOMMEND_COUNT__ 个符合用户需求的模板。
如果用户指明需要哪个 id 的模板，则按照要求的格式给出该 id 即可。

## 营销模板
下面每个代码块都是一个营销模板：
***
__TEMPLATES_DESC__

## 回复要求
你必须严格按照以下格式回复：
```json
["<biz_no_1>", "<biz_no_2>"]
```
你必须将回复内容放在 json array 中，并且将 json array 包含在 markdown 代码块中。
回复中的 <biz_no_xxx> 需要替换成你实际推荐的模板 biz_no。
除了上述要求的内容，你不能回复其他的任何内容！

示例回复：
```json
["c1aad094c9004bb5ab4a41b9266a2100", "2fbd09c7293e41de950e262909f0bbdf"]
```

## 用户需求
按照以下用户需求推荐：
***
__USER_QUERY__
"""


async def get_canvas_template_recommend_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="神策智能运营",
        prompt_code="CANVAS_TEMPLATE_RECOMMEND_PROMPT",
        prompt_name="画布模板推荐提示词",
        prompt_desc="画布模板推荐提示词",
        prompt_content=CANVAS_TEMPLATE_RECOMMEND_PROMPT,
    )
    return template


class CanvasTemplates:
    def __init__(self, file: Path, llm: BaseLLM):
        self.template_file = file
        self.llm = llm
        self.templates: Dict[str, Dict] = {}
        self.templates_desc: Dict[str, str] = {}
        self.templates_structure_desc: Dict[str, str] = {}

    def reindex_templates(self):
        if not self.template_file.exists():
            log.warn(f'No file found: {self.template_file}')
            return
        content = self.template_file.read_text(encoding='utf-8')
        content = json.loads(content)
        canvas_templates = content['canvas_templates']

        self.templates.clear()
        for template in canvas_templates:
            self.templates[template['biz_no']] = template

        self.templates_desc.clear()
        template_desc_prompt_template = tools.asyncio_run(get_canvas_template_canvas_desc_template)
        for template in canvas_templates:
            self.templates_desc[template['biz_no']] = template_desc_prompt_template.render(
                biz_no=template['biz_no'],
                cname=template['cname'],
                template_description=template['template_description'],
                template_convert_setting_desc=template['template_convert_setting_desc'],
                template_intent=template['template_intent'],
            )

        self.templates_structure_desc.clear()
        for template in canvas_templates:
            self.templates_structure_desc[template['biz_no']] = self._summary_template_structure(template)

        log.info('reindex canvas templates done.')

    def _summary_template_structure(self, template: Dict[str, Any]) -> str:
        template = template.copy()
        del template['events_snapshot']
        del template['event_props_snapshot']
        del template['user_props_snapshot']
        del template['channels_snapshot']
        summary_structure_prompt = tools.asyncio_run(get_canvas_template_recommend_summary_structure_prompt).render()
        summary_structure_prompt = summary_structure_prompt.replace(
            '__CANVAS_TEMPLATE__',
            json.dumps(template, indent=4, ensure_ascii=False))
        messages = [
            {
                'role': 'system',
                'content': summary_structure_prompt
            }
        ]
        response = self.llm.complete(input=messages)
        return tools.extract_code_from_text(response.content)


# 画布模板全局缓存, file_name -> templates
canvas_templates_cache: Dict[str, CanvasTemplates] = {}


class CanvasRecommendModel(BaseModel):
    """Input schema for CanvasTemplateRecommendTool tool."""

    query: str = Field(
        description="需要推荐的需求说明，一般需要说明清楚需要推荐的场景、时机、业务等",
        examples=[
            "请推荐关键日营销相关模板，只需要模板描述",
            "我需要针对门店的首日首次进行拉新，请推荐画布模板，需要模板详情"
        ]
    )


class CanvasTemplateRecommendTool(BaseTool):
    name: str = "CanvasTemplateRecommendTool"
    description: str = "画布模板推荐，根据行业属性、业务需求，推荐对应场景的画布模板，也可根据模板的 biz_id 直接给出模板结构，请指明需要模板描述还是模板详情。注意：推荐的结果是否符合要求需要自行判断"
    args_schema: Type[BaseModel] = CanvasRecommendModel

    template_file_path: Path
    recommend_count: int = 3
    llm: BaseLLM = ModelsFactory.get_llm()

    def _get_templates(self) -> CanvasTemplates:
        templates = canvas_templates_cache.get(str(self.template_file_path.absolute()), None)
        if templates is None:
            templates = CanvasTemplates(file=self.template_file_path, llm=self.llm)
            templates.reindex_templates()
            canvas_templates_cache[str(self.template_file_path.absolute())] = templates
        return templates

    @property
    def template_descriptions(self) -> str:
        templates = self._get_templates()
        contents = []
        for biz_no, description in templates.templates_desc.items():
            contents.append(f'```\n{description}\n```')
        return '\n'.join(contents)

    async def _arun(
            self,
            query: str,
            **kwargs: Any
    ) -> str:
        template = await get_canvas_template_recommend_prompt()
        # 推荐模板 ID
        recommend_system_prompt = template.render()
        recommend_system_prompt = recommend_system_prompt.replace('__RECOMMEND_COUNT__', str(self.recommend_count))
        recommend_system_prompt = recommend_system_prompt.replace('__TEMPLATES_DESC__', self.template_descriptions)
        recommend_system_prompt = recommend_system_prompt.replace('__USER_QUERY__', query)
        messages = [
            {'role': 'system', 'content': recommend_system_prompt}
        ]
        response = await self.llm.acomplete(input=messages)
        response = response.content
        log.info(f"recommend template response: {response}")
        biz_nos = []
        try:
            code = tools.extract_code_from_text(response)
            biz_nos.extend(json.loads(code))
        except BaseException as e:
            log.error('get biz NOs from llm error.', e)
            return 'Recommend failed, please retry if you wanted.'

        if not biz_nos:
            return 'No matched canvas template found.'

        templates = self._get_templates()
        canvas_descriptions = []
        for biz_no in biz_nos:
            desc = templates.templates_desc.get(biz_no, None)
            structure = templates.templates_structure_desc.get(biz_no, None)
            description = []
            if desc is not None:
                description.append(desc)
            if structure is not None:
                description.append('画布结构:')
                description.append(structure)
            if description:
                canvas_descriptions.append('\n'.join(description))

        result = {'canvas_infos': canvas_descriptions}
        need_detail = await self._acheck_need_detail(query=query, recommend_details=canvas_descriptions)
        if not need_detail:
            return result

        canvas_details = []
        for biz_no in biz_nos:
            canvas_detail = templates.templates.get(biz_no, None)
            if canvas_detail is not None:
                canvas_details.append(canvas_detail)
        result['canvas_config_details'] = canvas_details
        return result

    async def _acheck_need_detail(self, query: str, recommend_details: List[str]) -> bool:
        system_prompt = "根据用户需求和当前推荐结果，判断是否需要进一步给用户详细的画布结构（json 表示的原始画布配置），如果需要，则返回 YES，否则返回 NO。注意大小写"
        messages = [
            {'role': 'system', 'content': system_prompt},
            {'role': 'user', 'content': query},
            {'role': 'assistant', 'content': '已推荐如下画布模板及画布流程描述：\n' + '\n'.join(recommend_details)},
            {'role': 'user',
             'content': f'我的需求（{query}）是否还需要向我回复详细的画布结构？回复 YES 或者 NO：'},
        ]
        response = await self.llm.acomplete(input=messages)
        log.info(f'check need detail: {response.content}')
        if 'YES' in response.content:
            return True
        return False

    def _check_need_detail(self, query: str, recommend_details: List[str]) -> bool:
        system_prompt = "根据用户需求和当前推荐结果，判断是否需要进一步给用户详细的画布结构（json 表示的原始画布配置），如果需要，则返回 YES，否则返回 NO。注意大小写"
        messages = [
            {'role': 'system', 'content': system_prompt},
            {'role': 'user', 'content': query},
            {'role': 'assistant', 'content': '已推荐如下画布模板及画布流程描述：\n' + '\n'.join(recommend_details)},
            {'role': 'user',
             'content': f'我的需求（{query}）是否还需要向我回复详细的画布结构？回复 YES 或者 NO：'},
        ]
        response = self.llm.complete(input=messages)
        log.info(f'check need detail: {response.content}')
        if 'YES' in response.content:
            return True
        return False

    def _run(
            self,
            query: str,
            **kwargs: Any,
    ) -> Any:
        # 推荐模板 ID
        recommend_system_prompt = tools.asyncio_run(get_canvas_template_recommend_prompt).render()
        recommend_system_prompt = recommend_system_prompt.replace('__RECOMMEND_COUNT__', str(self.recommend_count))
        recommend_system_prompt = recommend_system_prompt.replace('__TEMPLATES_DESC__', self.template_descriptions)
        recommend_system_prompt = recommend_system_prompt.replace('__USER_QUERY__', query)
        messages = [
            {'role': 'system', 'content': recommend_system_prompt}
        ]
        response = self.llm.complete(input=messages)
        response = response.content
        log.info(f"recommend template response: {response}")
        biz_nos = []
        try:
            code = tools.extract_code_from_text(response)
            biz_nos.extend(json.loads(code))
        except BaseException as e:
            log.error('get biz NOs from llm error.', e)
            return 'Recommend failed, please retry if you wanted.'

        if not biz_nos:
            return 'No matched canvas template found.'

        templates = self._get_templates()
        canvas_descriptions = []
        for biz_no in biz_nos:
            desc = templates.templates_desc.get(biz_no, None)
            structure = templates.templates_structure_desc.get(biz_no, None)
            description = []
            if desc is not None:
                description.append(desc)
            if structure is not None:
                description.append('画布结构:')
                description.append(structure)
            if description:
                canvas_descriptions.append('\n'.join(description))

        result = {'canvas_infos': canvas_descriptions}
        if not self._check_need_detail(query=query, recommend_details=canvas_descriptions):
            return result

        canvas_details = []
        for biz_no in biz_nos:
            canvas_detail = templates.templates.get(biz_no, None)
            if canvas_detail is not None:
                canvas_details.append(canvas_detail)
        result['canvas_config_details'] = canvas_details
        return result


__all__ = ["CanvasTemplateRecommendTool"]
