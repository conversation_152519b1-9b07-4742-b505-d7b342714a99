"""Interface for tools."""

from typing import List, Optional

from langchain_core.callbacks import (
    AsyncCallbackManagerForToolRun,
    CallbackManagerForToolRun,
)
from langchain.agents.tools import InvalidTool as LangChainInvalidTool


class InvalidTool(LangChainInvalidTool):  # type: ignore[override]
    """Tool that is run when invalid tool name is encountered by agent."""

    def _run(
            self,
            requested_tool_name: str,
            available_tool_names: List[str],
            run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Use the tool."""
        available_tool_names_str = ", ".join([tool for tool in available_tool_names])
        return (
            f"`{requested_tool_name}` is not a valid tool, "
            f"try one of [{available_tool_names_str}] or give the final answer."
        )

    async def _arun(
            self,
            requested_tool_name: str,
            available_tool_names: List[str],
            run_manager: Optional[AsyncCallbackManagerForToolRun] = None,
    ) -> str:
        """Use the tool asynchronously."""
        available_tool_names_str = ", ".join([tool for tool in available_tool_names])
        return (
            f"`{requested_tool_name}` is not a valid tool, "
            f"try one of [{available_tool_names_str}] or give the final answer."
        )


__all__ = ["InvalidTool"]
