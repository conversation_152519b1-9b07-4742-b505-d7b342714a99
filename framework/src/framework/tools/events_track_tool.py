from typing import Type, Any

import sensorsanalytics
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from common import tools

# SA_SERVER_URL = "https://sfndemo-aws-singapore-01.sensorsdata.cn/sa?project=default"
SAMPLE_EVENT = """{
    "distinct_id": "xxx",
    "event_name": "$PlanMsgArrived",
    "properties": {
        "$sf_plan_id": "123",
        "$sf_component_id": "1",
        ...
    }
}"""


class TrackCodeModel(BaseModel):
    """Input schema for SensorsEventTrackTool tool."""

    python_code: str = Field(
        description=("需要运行的 python 代码，在代码中可以通过循环的方式调用 track(event:dict[str,Any]) 方法，"
                     "每调用一次均为一次上报，代码中不能引用其他的库，只能使用 track 方法."
                     f"一个示例的 event 结构:{SAMPLE_EVENT}"
                     "注意，distinct_id 是必须的，event_name 也是必须的，properties 中只能包含事件属性，属性名是英文的\n"
                     "每一个 event 结构表示一个人的一条事件，如果有多个人，请循环多次调用 track！"
                     "注意，你不能自定义 track 方法，你只能调用 track 方法！"
                     "代码需要包含在 markdown 代码块中！")
    )


def _run_code(server_url: str, code):
    consumer = sensorsanalytics.DefaultConsumer(server_url)
    sa = sensorsanalytics.SensorsAnalytics(consumer)

    def track(event: dict[str, Any]):
        sa.track(**event)

    exec(code, {'track': track})


class SensorsEventTrackTool(BaseTool):
    name: str = "SensorsEventTrackTool"
    description: str = "神策数据提供的事件上报工具，通过一段代码调用来批量实现事件上报，提供的 python 代码必须放在 markdown 代码块中！"
    args_schema: Type[BaseModel] = TrackCodeModel

    server_url: str

    def _run(
            self,
            python_code: str,
            **kwargs: Any,
    ) -> Any:
        wrapped_code = tools.extract_code_from_text(python_code)
        if wrapped_code.strip():
            python_code = wrapped_code
        _run_code(self.server_url, python_code)
        return 'Done'

    async def _arun(self, **kwargs: Any) -> str:
        return self._run(**kwargs)
