from typing import Type, Any

from langchain_core.tools import BaseTool
from openai import BaseModel
from pydantic import Field

from framework.agents.schema import Conversation, Role
from framework.knowledge import RagClient
from common import tools


class RetrieveModel(BaseModel):
    """Input schema for KnowledgeBaseTool tool."""

    query: str = Field(
        description=f"需要检索的内容描述",
    )


class KnowledgeBaseTool(BaseTool):
    name: str = "KnowledgeBaseTool"
    args_schema: Type[BaseModel] = RetrieveModel

    description: str
    knowledge_base: RagClient

    def _run(
            self,
            query: str,
            **kwargs: Any,
    ) -> Any:
        conversation = tools.asyncio_run(lambda: self.knowledge_base.simple_search(history=[
            Conversation(role=Role.USER, content=query)
        ]))
        return conversation.content

    async def _arun(self, **kwargs: Any) -> str:
        return self._run(**kwargs)
