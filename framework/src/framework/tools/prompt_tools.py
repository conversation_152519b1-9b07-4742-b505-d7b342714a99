from typing import List

from langchain_core.tools import BaseTool

from common.tools import text_tools as text_tools
from framework.utils import pydantic_utils


def gen_tools_desc(tools: List[BaseTool]) -> str:
    tools_desc = []
    for tool in tools:
        tool_args_desc = pydantic_utils.gen_pydantic_json_desc(tool.get_input_schema())
        tool_desc = (f"工具名称: {tool.name}\n"
                     f"工具描述:\n{text_tools.indent_multi_line(2, tool.description)}\n"
                     f"工具参数:\n{text_tools.indent_multi_line(2, tool_args_desc)}")
        tools_desc.append(tool_desc)
    return "\n".join(tools_desc)
