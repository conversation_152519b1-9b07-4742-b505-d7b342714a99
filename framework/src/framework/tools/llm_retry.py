from typing import Type, Callable, Any

from langchain_core.messages import HumanMessage
from pydantic import BaseModel, ValidationError

from common import tools
from framework.llm_exceptions import LLMResultValidationException
from framework.utils.parse_utils import ParseUtils
from framework.models import BaseLLM
from framework.models.llm import LanguageModelInput

log = tools.get_logger()


async def pydantic_chat_with_retry(llm: BaseLLM,
                                   output_format: Type[BaseModel],
                                   with_sys_prompt_messages: LanguageModelInput,
                                   retry_times: int = 3) -> (BaseModel, int):
    return await chat_with_retry(
        llm=llm,
        output_parser=lambda text: ParseUtils.extract_model_from_text(text=text, model=output_format),
        with_sys_prompt_messages=with_sys_prompt_messages,
        retry_times=retry_times)


async def chat_with_retry(llm: BaseLLM,
                          output_parser: Callable[[str], Any],
                          with_sys_prompt_messages: LanguageModelInput,
                          retry_times: int = 3) -> (Any, int):
    if isinstance(with_sys_prompt_messages, list):
        origin_messages = with_sys_prompt_messages
    else:
        origin_messages = [with_sys_prompt_messages]
    input_messages = origin_messages
    llm_response = None
    retries = 0
    while retries <= retry_times:
        try:
            llm_response = (await llm.acomplete(input_messages)).content
            return output_parser(llm_response), retries
        except LLMResultValidationException as e:
            retries += 1
            if retries == retry_times:
                log.warn(
                    f"failed to parse llm response, response format error. [llm_response='{llm_response}', retry_times='{retry_times}']",
                    exc_info=e)
                raise e
            else:
                log.debug(
                    f"failed to parse llm response, response format error, then retry. [llm_response='{llm_response}']",
                    exc_info=e)
            input_messages = origin_messages.copy()
            error_message = f"结果错误: {e.message}, 请按照要求重新输出" if e.message else "结果错误, 请按照要求重新输出"
            input_messages.append(HumanMessage(content=error_message))
