"""Interface for tools."""
import requests
from typing import Any, Type, List

from numba import UnsupportedError
from pydantic import BaseModel, Field
from langchain_core.tools import BaseTool
from requests import HTTPError
from requests.exceptions import InvalidJSONError

from common import tools

log = tools.get_logger()


class QueryMode:
    QUERY_USERS = 'query_user_fields'
    QUERY_EVENTS = 'query_events_schemas'
    QUERY_EVENTS_FIELDS = 'query_event_fields'
    USER_GROUPS = 'query_user_groups'

    mode_descriptions = {
        QUERY_USERS: '获取用户属性(含标签)定义/名称/类型等',
        QUERY_EVENTS: '获取埋点上报的事件列表',
        QUERY_EVENTS_FIELDS: '获取某个事件的属性列表定义/名称/类型等',
        USER_GROUPS: '查询所有用户分群信息列表'
    }


class SensorsMetadataModel(BaseModel):
    """Input schema for SensorsMetadataTool tool."""

    query_mode: str = Field(
        default=QueryMode.QUERY_USERS,
        description=f"要查询的模式，可选项：{'；'.join(k + ':' + v for k, v in QueryMode.mode_descriptions.items())}",
        examples=[_ for _ in QueryMode.mode_descriptions.keys()]
    )
    event_names: List[str] = Field(
        default=None,
        description=f"要查询事件属性的事件 name 列表，当query_mode为{QueryMode.QUERY_EVENTS_FIELDS}时使用",
    )


class SensorsMetadataTool(BaseTool):
    name: str = "SensorsMetadataTool"
    description: str = ("查询神策数据平台（CDP）的用户属性、事件及事件属性、标签、分群等元数据，查询内容包括："
                        "1）用户属性字段(含用户的标签)及说明；"
                        "2）埋点的用户事件及事件属性，用户可以有很多个事件，不同的事件属性可能不同；"
                        "3）计算的用户分群。")
    args_schema: Type[BaseModel] = SensorsMetadataModel

    cdp_host: str
    project_id: int
    project_name: str
    api_key: str

    def _run(
            self,
            query_mode: str,
            event_names: List[str] = None,
            **kwargs: Any,
    ) -> Any:
        if query_mode == QueryMode.QUERY_USERS:
            return self._query_users()
        if query_mode == QueryMode.QUERY_EVENTS:
            return self._query_events()
        if query_mode == QueryMode.QUERY_EVENTS_FIELDS:
            return self._query_events_props(events=event_names)
        if query_mode == QueryMode.USER_GROUPS:
            return self._query_user_groups()

        raise UnsupportedError(f'Unsupported mode: {query_mode}')

    async def _arun(self, **kwargs: Any) -> str:
        return self._run(**kwargs)

    @property
    def http_headers(self):
        return {'api-key': self.api_key, 'sensorsdata-project': self.project_name}

    def _query_event_props(self, event: str) -> Any:
        if not event.startswith('events.'):
            event = f'events.{event}'
        response = requests.post(
            url=f'{self.cdp_host}/api/v3/horizon/v1/schema/event/field/list',
            headers=self.http_headers,
            json={'project_id': self.project_id, 'schema_name': event},
        )
        if response.status_code != 200:
            raise HTTPError(
                f'Got error code. http_status={response.status_code}\n{response.content.decode(encoding="utf-8")}')
        try:
            response = response.json()
            data = response['data']['fields']
            return [
                {
                    'name': d['name'],
                    'display_name': d['display_name'],
                    'data_type': d['data_type']['type']
                }
                for d in data
            ]
        except:
            raise InvalidJSONError('parse response error.')

    def _query_events_props(self, events: List[str]) -> Any:
        try:
            return self._query_sdh_events_props(events)
        except BaseException as e:
            log.warning('query sdh events props failed. try inf events.')
        return self._query_inf_events_props(events)

    def _query_inf_events_props(self, events: List[str]) -> Any:
        if not events:
            return 'No events found.'
        response = requests.post(
            url=f'{self.cdp_host}/api/v3/analytics/v1/property-meta/event-properties',
            headers=self.http_headers,
            json={'events': events},
        )
        if response.status_code != 200:
            raise HTTPError(
                f'Got error code. http_status={response.status_code}\n{response.content.decode(encoding="utf-8")}')
        try:
            response = response.json()
            response = response['data']['event_properties']
            return [{'event_name': e['event_define']['name'], 'properties': e['properties']} for e in response]
        except:
            raise InvalidJSONError('parse response error.')

    def _query_sdh_events_props(self, events: List[str]) -> Any:
        if not events:
            return 'No events found.'
        results = []
        for event in events:
            results.append({
                'event_name': event,
                'properties': self._query_event_props(event=event)
            })
        return results

    def _query_events(self) -> Any:
        try:
            return self._query_sdh_events()
        except BaseException as e:
            log.warning('query sdh events failed. try inf events.')
        return self._query_inf_events()

    def _query_inf_events(self) -> Any:
        response = requests.get(
            url=f'{self.cdp_host}/api/v3/analytics/v1/event-meta/events/all',
            headers=self.http_headers
        )
        if response.status_code != 200:
            raise HTTPError(
                f'Got error code. http_status={response.status_code}\n{response.content.decode(encoding="utf-8")}')
        try:
            response = response.json()
            return response['data']['events']
        except:
            raise InvalidJSONError('parse response error.')

    def _query_sdh_events(self) -> Any:
        response = requests.post(
            url=f'{self.cdp_host}/api/v3/horizon/v1/schema/event/list',
            headers=self.http_headers,
            json={'project_id': self.project_id, 'page_size': 10_000},
        )
        if response.status_code != 200:
            raise HTTPError(
                f'Got error code. http_status={response.status_code}\n{response.content.decode(encoding="utf-8")}')
        try:
            response = response.json()
            data = response['data']['schemas']
            return data
        except:
            raise InvalidJSONError('parse response error.')

    def _query_users(self) -> Any:
        try:
            return self._query_sdh_users()
        except BaseException as e:
            log.warning('query sdh users failed. try inf users.')
        return self._query_inf_users()

    def _query_inf_users(self) -> Any:
        data = []
        profile_response = requests.get(
            url=f'{self.cdp_host}/api/v3/analytics/v1/property-meta/user-properties/all',
            headers=self.http_headers
        )
        if profile_response.status_code != 200:
            raise HTTPError(
                f'Got error code. http_status={profile_response.status_code}\n{profile_response.content.decode(encoding="utf-8")}')
        try:
            profile_response = profile_response.json()
            data.extend(profile_response['data']['user_properties'])
        except:
            raise InvalidJSONError('parse response error.')

        tag_response = requests.get(
            url=f'{self.cdp_host}/api/v3/analytics/v1/property-meta/user-tags/dir',
            headers=self.http_headers
        )
        if tag_response.status_code != 200:
            raise HTTPError(
                f'Got error code. http_status={tag_response.status_code}\n{tag_response.content.decode(encoding="utf-8")}')
        try:
            tag_response = tag_response.json()
            data.extend(tag_response['data']['user_tags'])
        except:
            raise InvalidJSONError('parse response error.')
        return data

    def _query_sdh_users(self) -> Any:
        response = requests.post(
            url=f'{self.cdp_host}/api/v3/horizon/v1/schema/field/list',
            headers=self.http_headers,
            json={'project_id': self.project_id, 'schema_name': 'users', 'page_size': 10_000},
        )
        if response.status_code != 200:
            raise HTTPError(
                f'Got error code. http_status={response.status_code}\n{response.content.decode(encoding="utf-8")}')
        try:
            response = response.json()
            data = response['data']['fields']
            return data
        except:
            raise InvalidJSONError('parse response error.')

    def _query_user_groups(self) -> Any:
        try:
            return self._query_sdh_user_groups()
        except BaseException as e:
            log.warning('query sdh user groups failed. try inf user groups.')
        return self._query_inf_user_groups()

    def _query_inf_user_groups(self) -> Any:
        data = []
        response = requests.get(
            url=f'{self.cdp_host}/api/v3/analytics/v1/property-meta/user-groups/all',
            headers=self.http_headers
        )
        if response.status_code != 200:
            raise HTTPError(
                f'Got error code. http_status={response.status_code}\n{response.content.decode(encoding="utf-8")}')
        try:
            response = response.json()
            data = response['data']['user_groups']
            return data
        except:
            raise InvalidJSONError('parse response error.')

    def _query_sdh_user_groups(self) -> Any:
        response = requests.post(
            url=f'{self.cdp_host}/api/v3/horizon/v1/segment/definition/list',
            headers=self.http_headers,
            json={
                'project_id': self.project_id,
                'entity_name': 'user',
                'show_invisible': False,
                'show_deleted': False,
                'page_size': 10_000
            },
        )
        if response.status_code != 200:
            raise HTTPError(
                f'Got error code. http_status={response.status_code}\n{response.content.decode(encoding="utf-8")}')
        try:
            response = response.json()
            data = response['data']['segment_definitions']
            return [
                {
                    'name': d['name'],
                    'display_name': d['display_name'],
                    'comment': d['comment'],
                    'status': d['status'],
                    'created_by': d['created_by'],
                    'create_type': d['create_type'],
                    'created_type_description': 'CUSTOMIZED_RULE 表示通过页面上规则配置创建；QUERY_RESULT 表示通过页面上的结果保存创建；IMPORT 外部导入数据创建；SQL/EQL 等分别表示通过写 SQL/EQL 计算创建',
                    'segment_rule_ref': d['segment_rule_ref'],
                }
                for d in data
            ]
        except:
            raise InvalidJSONError('parse response error.')


__all__ = ["SensorsMetadataTool"]
