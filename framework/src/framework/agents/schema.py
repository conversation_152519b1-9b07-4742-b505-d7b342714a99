from dataclasses import dataclass
from enum import auto, Enum
from typing import Literal, Dict, Any, List

from PIL.Image import Image
from pydantic import BaseModel, Field


class ViewMessageStatus(BaseModel):
    status: Literal['PROCESSING', 'ERROR', 'WARNING', 'SUCCESS'] = Field(
        default='SUCCESS', description='The status of the message'
    )
    text: str = Field(default='', description='The text of the status message')
    keeping: bool = Field(default=False, description='Whether to keep the message on web pages')


class ViewMessage(BaseModel):
    type: Literal[
        'MARKDOWN', 'THINKING', 'TABLE', 'CHART', 'ENTITY_RELATION',
        'CANVAS_DESIGN', 'CANVAS', 'K<PERSON><PERSON>LEDGE', 'ANALYSE_IDEA', 'RECOMMEND_QUESTION'
    ] = Field(
        default='MARKDOWN', description='The message type'
    )
    content: str | Any = Field(default='', description='The text of the message')


class Role(Enum):
    SYSTEM = auto()
    USER = auto()
    ASSISTANT = auto()
    TOOL = auto()
    INTERPRETER = auto()
    OBSERVATION = auto()

    def __str__(self):
        match self:
            case Role.SYSTEM:
                return "system"
            case Role.USER:
                return "user"
            case Role.ASSISTANT | Role.TOOL | Role.INTERPRETER:
                return "assistant"
            case Role.OBSERVATION:
                return "observation"


@dataclass
class Conversation:
    role: Role
    content: str
    name: str | None = None
    source: str | None = None
    tool: str | None = None
    image: Image | None = None
    documents: List[Dict[str, Any]] | None = None

    def __str__(self) -> str:
        print(self.role, self.content, self.tool)
        match self.role:
            case Role.SYSTEM | Role.USER | Role.ASSISTANT | Role.OBSERVATION:
                return f'{self.role}\n{self.content}'
            case Role.TOOL:
                return f'{self.role}{self.tool}\n{self.content}'
            case Role.INTERPRETER:
                return f'{self.role}interpreter\n{self.content}'
