from typing import List, Sequence, Any

from langchain_community.document_transformers import get_stateful_documents
from langchain_core.documents import Document, BaseDocumentTransformer

from framework.prompt import PromptFactory
from framework.models import BaseLLM
from framework.models.tokenizer import Tokenizer
from common import tools

log = tools.get_logger()

DOCUMENT_COMPRESS_PROMPT = '''
# 职责描述
你是一个智能文档助手，请你尽最大努力，根据用户可能想了解的内容，从文档中提取出有用的文档信息以减少用户的阅读量。
你应该使用更加精炼的语言给出文档内容，并且不丢失用户关心的文档内容信息。
你应尽量把文档内容的字数缩短一半。
你需要仔细理解用户的意图，并依据我给你的所有文档，给出对回复用户有帮助的文档内容，我给你的文档是一些文档碎片，你需要仔细阅读并理解。

# 回复要求
你必须且仅需回复你整理之后的，并且对回复用户有帮助的文档。回复其他的内容都是不允许的。

其中，用户想了解的内容（或者提问）为如下所示：
```
{{user_query}}
```

下面都是给你的文档内容：
***
{{document_contents}}
'''

DOCUMENT_SUMMARY_PROMPT = '''
# 职责描述
你是一个智能文档助手，请你尽最大努力，使用更加精炼的自然语言精简文档信息（不必与原来格式保持一致），将文档的字数缩短一半，并且尽量不丢失文档关键信息。

# 文档内容：
***
{{document_contents}}
'''


def convert_doc_with_state(document: Document, tokenizer: Tokenizer):
    if hasattr(document, 'state'):
        if 'tokens' in document.state:
            return document
        tokens = tokenizer.tokenize_len(document.page_content)
        document.state['tokens'] = tokens
        return document

    document = get_stateful_documents([Document(page_content=document.page_content)])[0]
    tokens = tokenizer.tokenize_len(document.page_content)
    document.state['tokens'] = tokens
    return document


def convert_docs_with_state(documents: Sequence[Document], tokenizer: Tokenizer):
    docs = []
    for doc in documents:
        docs.append(convert_doc_with_state(doc, tokenizer))
    return docs


class LLMCompressChainCompressor(BaseDocumentTransformer):
    """使用大模型压缩文档，递归压缩"""

    def transform_documents(self, documents: Sequence[Document], **kwargs: Any) -> Sequence[Document]:
        pass

    def __init__(
            self,
            model_client: BaseLLM,
            input_prompt_limit: int,
            tokenizer: Tokenizer,
            query: str | None = None,
    ):
        super().__init__()
        self.query = query
        self.llm_model_client = model_client
        self.input_prompt_limit = input_prompt_limit
        self.tokenizer = tokenizer

    async def _simple_chunk_text(self, text: str, max_token_limit: int) -> List[str]:
        result = tools.split_text(
            text=text,
            chunk_size=int(max_token_limit / 2),
            chunk_overlap=int(max_token_limit / 20),
        )
        return result

    async def _summary_as_new_document(
            self,
            documents: Sequence[Document],
    ) -> Document:
        """使用大模型压缩"""
        all_tokens = 0
        docs = []
        for doc in documents:
            doc = convert_doc_with_state(doc, self.tokenizer)
            all_tokens += doc.state['tokens']
            docs.append(doc)
        documents = docs
        if all_tokens <= self.input_prompt_limit / 2:
            log.info(f'total tokens = {all_tokens}, no need to summary.')
            return convert_doc_with_state(
                Document(page_content='\n'.join([x.page_content for x in documents])),
                self.tokenizer
            )

        log.info(f'before summary tokens: {all_tokens}')
        log.debug(f'before summary texts: {[x.page_content for x in documents]}')
        if self.query:
            prompt = PromptFactory.render_prompt(
                DOCUMENT_COMPRESS_PROMPT,
                user_query=self.query,
                document_contents='\n***\n'.join([_.page_content for _ in documents])
            )
        else:
            prompt = PromptFactory.render_prompt(
                DOCUMENT_SUMMARY_PROMPT,
                document_contents='\n***\n'.join([_.page_content for _ in documents])
            )
        content = await self.llm_model_client.acomplete(
            input=[{'role': 'system', 'content': prompt}]
        )
        document = convert_doc_with_state(Document(page_content=content.content), self.tokenizer)
        log.info(f'after summary tokens: {document.state["tokens"]}')
        log.info(f'after summary texts: {document.page_content}')
        return document

    async def _merge_small_documents(self, documents: Sequence[Document]):
        docs = convert_docs_with_state(documents, self.tokenizer)

        # 把小文档先合起来
        total_tokens = 0
        merged_documents = []
        part_docs = []
        for doc in docs:
            total_tokens += doc.state['tokens']
            if total_tokens >= self.input_prompt_limit and part_docs:
                merged_documents.append(Document(page_content='\n***\n'.join([_.page_content for _ in part_docs])))
                part_docs = []
            part_docs.append(doc)
            total_tokens = doc.state['tokens']
        if part_docs:
            merged_documents.append(Document(page_content='\n***\n'.join([_.page_content for _ in part_docs])))

        return merged_documents

    async def _split_large_documents(self, documents: Sequence[Document]) -> Sequence[Document]:
        docs = convert_docs_with_state(documents, self.tokenizer)

        total_tokens = 0
        new_documents = []
        for doc in docs:
            part_docs = []
            if doc.state['tokens'] > self.input_prompt_limit:
                text_units = await self._simple_chunk_text(
                    text=doc.page_content,
                    max_token_limit=self.input_prompt_limit,
                )
                for text_unit in text_units:
                    part_docs.append(convert_doc_with_state(Document(page_content=text_unit), self.tokenizer))
            else:
                part_docs.append(doc)

            new_summary_documents = []
            for part_doc in part_docs:
                if part_doc.state['tokens'] + total_tokens > self.input_prompt_limit and new_summary_documents:
                    # summary and store
                    new_doc = await self._summary_as_new_document(documents=new_summary_documents)
                    new_documents.append(new_doc)
                    total_tokens = 0
                    new_summary_documents = []
                new_summary_documents.append(part_doc)
                total_tokens += part_doc.state['tokens']
            if new_summary_documents:
                new_documents.append(await self._summary_as_new_document(documents=new_summary_documents))
        return new_documents

    async def atransform_documents(
            self,
            documents: Sequence[Document],
            **kwargs: Any,
    ) -> Sequence[Document]:
        """递归压缩，会消耗大量 token"""
        if not documents:
            return documents

        # 最大递归层数
        depth = kwargs.get('depth', 10) - 1
        if depth < 0:
            return documents
        kwargs['depth'] = depth

        docs = convert_docs_with_state(documents, self.tokenizer)
        total_tokens = sum(_.state['tokens'] for _ in docs)
        log.info(f'expected tokens <= {self.input_prompt_limit}, curr_total_tokens = {total_tokens}')
        if self.input_prompt_limit >= total_tokens:
            return docs

        # 先合并小文档
        docs = await self._merge_small_documents(documents=docs)
        # 再分解大文档
        docs = await self._split_large_documents(documents=docs)
        # 递归处理
        return await self.atransform_documents(
            documents=docs,
            **kwargs
        )
