from typing import Sequence, Any
from langchain_core.documents import Document, BaseDocumentTransformer
from framework.models.tokenizer import Tokenizer
from common import tools
from langchain_core.embeddings import Embeddings
import numpy as np
from .llm_chain_compressor import convert_doc_with_state

log = tools.get_logger()


def _similarity(vec1, vec2) -> float:
    # return vec1 @ vec2.T
    return vec1.dot(vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))


class SimilarityChainCompressor(BaseDocumentTransformer):
    """相似度截断文档"""

    def __init__(
            self,
            query: str,
            input_prompt_limit: int,
            tokenizer: Tokenizer,
            embedding: Embeddings,
    ):
        super().__init__()
        self.query = query
        self.input_prompt_limit = input_prompt_limit
        self.tokenizer = tokenizer
        self.embedding = embedding

    def transform_documents(
            self,
            documents: Sequence[Document],
            **kwargs: Any,
    ) -> Sequence[Document]:
        query_embedding = np.array(self.embedding.embed_query(text=self.query))
        documents_tuples = [
            (
                convert_doc_with_state(document, self.tokenizer),
                _similarity(query_embedding, np.array(self.embedding.embed_query(text=document.page_content)))
            ) for document in documents
        ]
        documents_tuples = sorted(documents_tuples, key=lambda x: x[1], reverse=True)

        total_tokens = 0
        documents = []
        for doc in documents_tuples:
            if total_tokens + doc[0].state['tokens'] > self.input_prompt_limit:
                break
            else:
                total_tokens += doc[0].state['tokens']
                documents.append(doc[0])
        return documents
