from typing import Sequence, List, Tuple
from common import tools
from langchain_community.document_transformers import get_stateful_documents
from langchain_core.documents import Document
from langchain_core.messages import HumanMessage, BaseMessage

from framework.models import BaseLLM
from framework.models.embeddings import TextEmbedding, TextEmbeddingFactory
from framework.models.tokenizer import TokenizerFactory

from .llm_chain_compressor import LLMCompressChainCompressor
from .similarity_chain_compressor import SimilarityChainCompressor
from .llm_chain_compressor import convert_doc_with_state

from langchain_core.embeddings import Embeddings

log = tools.get_logger()


class LangChainEmbedding(Embeddings):
    def __init__(self, text_embedder: TextEmbedding):
        self.text_embedder = text_embedder

    def embed_documents(self, texts: list[str]) -> list[list[float]]:
        return self.text_embedder.batch_embed(texts=texts)

    def embed_query(self, text: str) -> list[float]:
        return self.text_embedder.embed(text=text)


DOCUMENT_SPLITTER = 'Retrieved Document:'


def get_all_tokens(documents, tokenizer) -> int:
    result = 0
    for doc in documents:
        doc = convert_doc_with_state(doc, tokenizer)
        result += doc.state['tokens']
    return result


def _convert_documents_to_llm_message(
        documents: Sequence[Document],
        name: str | None = None,
) -> List[BaseMessage]:
    result = []
    if not documents:
        return result

    last_name = None
    last_content = ''
    for doc in documents:
        curr_name = None
        if hasattr(doc, 'state') and doc.state.get('name', None):
            curr_name = doc.state.get('name').strip()
        if curr_name == last_name:
            last_content = last_content + '\n' + DOCUMENT_SPLITTER + '\n' + doc.page_content
        else:
            if last_content:
                result.append(HumanMessage(name=last_name or name, content=last_content))
            last_name = curr_name
            last_content = DOCUMENT_SPLITTER + '\n' + doc.page_content
    if last_content:
        result.append(HumanMessage(name=last_name or name, content=last_content))
    return result


def _convert_history_as_documents(history: List[BaseMessage]):
    tokenizer = TokenizerFactory.get_tokenizer()
    documents = []
    for cov in history:
        docs = cov.content.split(DOCUMENT_SPLITTER)
        for doc in docs:
            doc = doc.strip()
            if doc:
                doc1 = get_stateful_documents([Document(page_content=doc)])[0]
                tokens = tokenizer.tokenize_len(doc)
                doc1.state['tokens'] = tokens
                doc1.state['name'] = cov.name
                documents.append(doc1)
    return documents


async def summary_contextual_compression(
        history: List[BaseMessage],
        input_token_limit: int,
        model_client: BaseLLM,
        compress_rate: float = 0.6,
        name: str | None = None,
        user_query: str | None = None,
        embeddings=LangChainEmbedding(text_embedder=TextEmbeddingFactory.get_text_embedding()),
) -> Tuple[List[BaseMessage], int]:
    """
    上下文过长总结，可能会丢失上下文关键信息
    """
    log.info(f"compress history={history}")
    if not history:
        log.info('history is empty.')
        return history, 0
    if compress_rate >= 1 or compress_rate <= 0:
        compress_rate = 0.6
    compress_to_token_limit = int(input_token_limit * compress_rate)
    log.info(f"compress_to_token_limit={compress_to_token_limit}")

    tokenizer = TokenizerFactory.get_tokenizer()
    documents = _convert_history_as_documents(history)
    all_tokens = get_all_tokens(documents, tokenizer)

    # 长度不够不需要压缩
    log.info(f"all_tokens={all_tokens}")
    if all_tokens <= compress_to_token_limit:
        log.info(f"no need to compress.")
        return history, all_tokens

    # llm 过滤 & 总结压缩
    log.info('start compress documents by llm.')
    llm_compressor = LLMCompressChainCompressor(
        query=user_query,
        model_client=model_client,
        input_prompt_limit=compress_to_token_limit,
        tokenizer=tokenizer,
    )
    documents = await llm_compressor.atransform_documents(documents=documents)
    all_tokens = get_all_tokens(documents, tokenizer)
    if all_tokens <= compress_to_token_limit:
        return _convert_documents_to_llm_message(documents=documents, name=name), all_tokens

    # 还是不行，则直接删除多出来的部分，理论上不会走到这里
    if user_query:
        log.info('start force compress documents.')
        similarity_compressor = SimilarityChainCompressor(
            query=user_query,
            input_prompt_limit=compress_to_token_limit,
            tokenizer=tokenizer,
            embedding=embeddings,
        )
        documents = await similarity_compressor.atransform_documents(documents=documents)
    all_tokens = get_all_tokens(documents, tokenizer)
    return _convert_documents_to_llm_message(documents=documents, name=name), all_tokens


async def contextual_compression(
        user_query: str | None,
        history: List[BaseMessage],
        input_token_limit: int,
        model_client: BaseLLM,
        name: str | None = None,
        compress_rate: float = 0.6,
) -> Tuple[List[BaseMessage], int]:
    """
    上下文过长压缩

    Args:
        model_client
        user_query 用户的查询语句，也包含在 history 中，如果为 None 则需要自行提取
        history 上文内容
        input_token_limit
        compress_rate 压缩到上文限制的比例
    """
    if not history:
        return history, 0
    if compress_rate >= 1 or compress_rate <= 0:
        compress_rate = 0.6
    compress_to_token_limit = int(input_token_limit * compress_rate)

    tokenizer = TokenizerFactory.get_tokenizer()
    documents = _convert_history_as_documents(history)
    all_tokens = get_all_tokens(documents, tokenizer)

    # 长度不够不需要压缩
    if all_tokens <= compress_to_token_limit:
        return history, all_tokens

    # 冗余过滤
    log.info('start compress redundancy filter.')
    embeddings = LangChainEmbedding(text_embedder=TextEmbeddingFactory.get_text_embedding())

    from langchain.document_transformers import EmbeddingsRedundantFilter
    redundant_filter = EmbeddingsRedundantFilter(embeddings=embeddings, similarity_threshold=0.98)
    filter_documents = redundant_filter.transform_documents(documents=documents)
    documents = filter_documents

    all_tokens = get_all_tokens(documents, tokenizer)
    if all_tokens <= compress_to_token_limit:
        return _convert_documents_to_llm_message(documents=documents, name=name), all_tokens

    # llm 过滤 & 总结压缩
    return await summary_contextual_compression(
        history=[],
        input_token_limit=input_token_limit,
        model_client=model_client,
        compress_rate=compress_rate,
        name=name,
        user_query=user_query,
        embeddings=embeddings,
    )
