from typing import List, Iterator, Generator, Any, AsyncGenerator

from jinja2 import Template
from langchain_core.chat_history import BaseChatMessageHistory, InMemoryChatMessageHistory
from langchain_core.messages import HumanMessage, BaseMessage, AIMessage

from framework.agents.schema import Conversation, Role
from framework.models.llm import LanguageModelInput
from .base import BaseAgent


class AssistantAgent(BaseAgent):
    def __init__(self, **kwargs) -> None:
        super().__init__(**kwargs)
        self.memory: BaseChatMessageHistory = InMemoryChatMessageHistory()

    def add_history(self, history: LanguageModelInput):
        if not history:
            return
        if not isinstance(history, list):
            history = [history]
        for msg in history:
            if msg is None:
                continue
            self.memory.add_messages(self._convert_message(message=msg))

    async def async_add_history(self, history: LanguageModelInput):
        if not history:
            return
        if not isinstance(history, list):
            history = [history]
        for msg in history:
            if msg is None:
                continue
            self.memory.add_messages(self._convert_message(message=msg))

    def get_history(self) -> List[BaseMessage]:
        return self.memory.messages.copy()

    def clear_history(self):
        self.memory.clear()

    def chat_and_save(
            self,
            prompt: str | None = None
    ) -> str:
        if prompt:
            self.add_history(HumanMessage(content=prompt))
        message = self.chat(prompts=self.get_history())
        self.add_history(AIMessage(content=message))
        return message

    async def achat_and_save(
            self,
            prompt: str | Template | None = None
    ) -> str:
        if prompt:
            if isinstance(prompt, Template):
                prompt = prompt.render()
            self.add_history(HumanMessage(content=prompt))
        message = await self.achat(prompts=self.get_history())
        self.add_history(AIMessage(content=message))
        return message

    def stream_chat_and_save(
            self,
            prompt: str | None = None
    ) -> Generator[str, Any, Any]:
        if prompt:
            self.add_history(HumanMessage(content=prompt))
        result = []
        for rel in self.stream_chat(prompts=self.get_history()):
            result.append(rel)
            yield rel
        self.add_history(AIMessage(content=''.join(result)))

    async def astream_chat_and_save(
            self,
            prompt: str | None = None
    ) -> AsyncGenerator[str, Any]:
        if prompt:
            self.add_history(HumanMessage(content=prompt))
        result = []
        response = self.astream_chat(prompts=self.get_history())
        async for rel in response:
            result.append(rel)
            yield rel
        self.add_history(AIMessage(content=''.join(result)))

    def _basemessage_to_conversation(self, message: BaseMessage | None) -> Conversation | None:
        if message is None:
            return None
        role = Role.USER
        if message.type == 'ai' or message.type == 'chat' or message.type == 'assistant':
            role = Role.ASSISTANT
        elif message.type == 'system':
            role = Role.SYSTEM
        return Conversation(role=role, content=message.content, name=message.name)
