from langchain_core.messages import HumanMessage, BaseMessage, AIMessage

from common import tools
from framework.models.llm import LanguageModelInput
from framework.models.tokenizer import TokenizerFactory, Tokenizer
from .assistant import AssistantAgent
from .compress import summary_contextual_compression

log = tools.get_logger()


class CompressedAssistantAgent(AssistantAgent):
    """带上下文压缩，防止上下文超过上限"""

    def __init__(
            self,
            max_token_limit: int = 1024 * 8,
            compress_rate: float = 0.6,
            tokenizer: Tokenizer = TokenizerFactory.get_tokenizer(),
            **kwargs
    ) -> None:
        super().__init__(**kwargs)
        self.max_token_limit = max_token_limit
        self.compress_rate = compress_rate
        self.tokenizer = tokenizer
        self.total_history_tokens = 0  # 不计算 system prompt

    async def compress_history(self):
        log.info("Start compress history.")
        compressed_history, all_tokens = await summary_contextual_compression(
            history=self.get_history(),
            input_token_limit=self.max_token_limit,
            model_client=self.llm,
            compress_rate=self.compress_rate,
            name='assistant',
            user_query=None,
        )
        log.info("End compress history.")

        self.clear_history()
        self.memory.add_messages(compressed_history)
        self.total_history_tokens = all_tokens

    async def async_add_history(self, history: LanguageModelInput):
        if not history:
            return
        if isinstance(history, BaseMessage):
            history = [history]
        for msg in history:
            if msg is None:
                continue
            messages = self._convert_message(message=msg)
            self.memory.add_messages(messages)
            for message in messages:
                self.total_history_tokens += self.tokenizer.tokenize_len(message.content)

        # 压缩上下文
        if self.total_history_tokens > self.max_token_limit:
            await self.compress_history()

    def add_history(self, history: LanguageModelInput):
        if not history:
            return
        if isinstance(history, BaseMessage):
            history = [history]
        for msg in history:
            if msg is None:
                continue
            messages = self._convert_message(message=msg)
            self.memory.add_messages(messages)
            for message in messages:
                self.total_history_tokens += self.tokenizer.tokenize_len(message.content)

        # 压缩上下文
        if self.total_history_tokens > self.max_token_limit:
            tools.asyncio_run(lambda: self.compress_history())
