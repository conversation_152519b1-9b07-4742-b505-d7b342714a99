from typing import List

from langchain_core.tools import BaseTool

from framework.models import BaseLLM
from framework.models.llm import LanguageModelInput
from .compressed_participant import CompressedParticipantAgent
from framework.agents.react import ReActAgent


class ReActParticipantAgent(CompressedParticipantAgent, ReActAgent):
    def __init__(
            self,
            tools: List[BaseTool],
            llm: BaseLLM,
            system_prompt: str | None = None,
            max_token_limit: int = 1024 * 8,
            compress_rate: float = 0.6,
            real_query: str | None = None,
            **kwargs,
    ):
        self.results = []
        CompressedParticipantAgent.__init__(
            self,
            tools=tools,
            llm=llm,
            system_prompt=system_prompt,
            max_token_limit=max_token_limit,
            compress_rate=compress_rate,
            real_query=real_query,
            **kwargs
        )
        ReActAgent.__init__(
            self,
            tools=tools,
            llm=llm,
            system_prompt=system_prompt,
            max_token_limit=max_token_limit,
            compress_rate=compress_rate,
            real_query=real_query,
            **kwargs
        )
        self.real_query = real_query

    def chat(
            self,
            prompts: LanguageModelInput | None = None,
    ) -> str:
        result = ReActAgent.chat(self=self, prompts=prompts)
        self.add_history({'role': 'assistant', 'name': self.name, 'content': result})
        return result

    async def achat(
            self,
            prompts: LanguageModelInput | None = None
    ) -> str:
        result = await ReActAgent.achat(self=self, prompts=prompts)
        self.add_history({'role': 'assistant', 'name': self.name, 'content': result})
        await self.compress_history()
        return result
