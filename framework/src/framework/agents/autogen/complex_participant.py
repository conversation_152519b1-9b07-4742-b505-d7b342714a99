from langchain_core.messages import BaseMessage, HumanMessage, AIMessage

from framework.models.llm import LanguageModelInput
from .participant import ParticipantAgent
from .workflow import AutoGenWorkflow
from common import tools


class ComplexParticipantAgent(ParticipantAgent):
    """包装子 manager 类型"""

    def __init__(
            self,
            workflow: AutoGenWorkflow,
            **kwargs,
    ):
        super().__init__(**kwargs)
        self.workflow = workflow

    def receive_from_manager(self, message: LanguageModelInput):
        messages = self._convert_message(message)
        # manager 的消息作为人类需求
        for message in messages:
            self.add_history(HumanMessage(content=message.content))

    def receive_from_other_participant(self, message: LanguageModelInput):
        messages = self._convert_message(message)
        # 其他角色的回复作为 assistant
        for message in messages:
            self.add_history(AIMessage(content=message.content))

    def run(self) -> BaseMessage:
        """运行 workflow 步骤"""
        return tools.asyncio_run(lambda: self.arun())

    async def arun(self) -> BaseMessage:
        """运行 workflow 步骤"""
        history = self.get_history()
        result = await self.workflow.run_with_history(history=history)
        result = result.content
        return self._convert_message(message={'role': 'assistant', 'content': result, 'name': self.name})[0]
