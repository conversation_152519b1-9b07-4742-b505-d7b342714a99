from .participant import ParticipantAgent
from ..compress import contextual_compression
from ..compressed_assistant import CompressedAssistantAgent
from common import tools

log = tools.get_logger()


class CompressedParticipantAgent(CompressedAssistantAgent, ParticipantAgent):
    """带上下文压缩的 agent"""

    def __init__(
            self,
            **kwargs,
    ) -> None:
        CompressedAssistantAgent.__init__(self, **kwargs)
        ParticipantAgent.__init__(self, **kwargs)

    async def compress_history(self):
        log.info("Start compress history.")
        compressed_history, all_tokens = await contextual_compression(
            history=self.get_history(),
            input_token_limit=self.max_token_limit,
            model_client=self.llm,
            compress_rate=self.compress_rate,
            name=self.name,
            user_query=self.real_query,
        )
        log.info("End compress history.")

        self.clear_history()
        self.memory.add_messages(compressed_history)
        self.total_history_tokens = all_tokens
