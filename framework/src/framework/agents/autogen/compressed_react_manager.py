from jinja2 import Template

from framework.prompt import PromptFactory
from .manager import NextParticipantResponse
from .react_manager import ReactManagerAgent

from ..compressed_assistant import CompressedAssistantAgent
from ..compress import contextual_compression
from common import tools

log = tools.get_logger()

MANAGER_PROMPT_REACT_WITH_HISTORY_COMMAND = """
# 职责描述
你正在一个角色扮演游戏中，这个游戏旨在尽最大努力给用户的话题提供参考，参与角色如下：
{{role_and_descriptions}}

# 背景知识及文档内容相关解释
为了能更好的玩这个游戏，你需要以下背景知识：
{{background_knowledge}}

# 输出格式
你需要按照以下格式回复：
```
Thought: 当前问题已经解决了 xx，为了解决 xx 问题，我需要 xx 角色来做 xx 事情
Choose: 角色名称，角色名称只能从 {participants} 中选一个
Do: 角色需要做的事情，比如：请你继续分析用户的意图
```
你必须始终以 `Thought: ` 开头，Thought 中你需要思考当前提供的内容是否足够回答用户问题，以及需要谁做什么等。
不允许使用 markdown 代码块标记回复！如果回复中包含代码信息，则可以使用 markdown 代码块标记。
如果你所选的角色已经回复了你，则他们会加上他们自己的回复，比如：Do Response: 这里是所选择的role的回复内容。
需要注意：只有 reporter 才能与用户沟通，用户无法与其他角色沟通，所以需要最终回复的内容请明确告知 reporter.

# 输出结果的步骤
你需要不断重复上述格式步骤，直至检索到足够的信息为止。需要注意的是，其他角色的发言通常不全（或者会用局部信息欺骗你），为了保证用户能获取足量的信息，你应该尽可能的让他们发言，直到无法得到新的信息为止。
如果其他角色已经无法解决问题，或者出现了很多重复或者无意义的讨论，你应该直接让 reporter 收集结果并回复用户。
注意，reporter 角色只有在需要终止上述思考步骤的时候才需要调用，否则不要指定 reporter 角色。
注意尽量不要让其他角色重复执行他已经执行过的步骤！

已经执行过的步骤包括：
```
{{steps_history}}
```

注意，你不能从对话内容中获取角色名称！

# 用户对话记录与需求
仔细阅读下面的对话记录，并从中识别到用户(user/human)最终的需求，然后按照上述格式解决用户需求。
***
{{chat_history}}

{{query_prompt}}

开始：
"""


class CompressedReactManagerAgent(CompressedAssistantAgent, ReactManagerAgent):
    """带上下文压缩的 react manager，由于压缩过后容易出现重复执行的问题，所以这里需要做特殊处理防止重复执行"""

    def __init__(
            self,
            system_prompt: str | Template = MANAGER_PROMPT_REACT_WITH_HISTORY_COMMAND,
            **kwargs,
    ) -> None:
        CompressedAssistantAgent.__init__(self, **kwargs)
        ReactManagerAgent.__init__(self, system_prompt=system_prompt, **kwargs)
        self._system_prompt = system_prompt
        self._command_history = []

    @property
    def system_prompt(self) -> str:
        query_prompt = ''
        if self.real_query:
            query_prompt = f'# 用户本次实际查询\n下面是用户本次实际的查询内容：\n{self.real_query}'

        system_prompt = PromptFactory.render_prompt(
            self._system_prompt,
            background_knowledge=PromptFactory.render_prompt(self.background_knowledge),
            role_and_descriptions='\n'.join([' - ' + p.name + ': ' + p.description for p in self.participants]),
            participants=[p.name for p in self.participants],
            steps_history='\n'.join(self._command_history),
            chat_history=self.user_chat_history,
            query_prompt=query_prompt,
        )
        return system_prompt

    @system_prompt.setter
    def system_prompt(self, sp: str):
        """ignore"""
        pass

    async def compress_history(self):
        history = self.get_history()
        compressed_history, all_tokens = await contextual_compression(
            history=history,
            input_token_limit=self.max_token_limit,
            model_client=self.llm,
            compress_rate=self.compress_rate,
            name=self.name,
            user_query=self.real_query,
        )
        if not compressed_history:
            log.warning(
                f'before_history_len={history}, compressed_history={compressed_history}, all_tokens={all_tokens}')
        else:
            log.info(f'compressed_history={compressed_history}, all_tokens={all_tokens}')

        self.clear_history()
        self.memory.add_messages(compressed_history)
        self.total_history_tokens = all_tokens

    def extract_next_participant(self, response: str) -> NextParticipantResponse:
        resp = super().extract_next_participant(response=response)
        self._command_history.append(f'{resp.next_participant}: {resp.next_instruction}')
        return resp
