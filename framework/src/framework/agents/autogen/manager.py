import json
import re
from typing import List, Union, Generator, Any, AsyncGenerator

from jinja2 import Template
from langchain_core.chat_history import InMemoryChatMessageHistory
from langchain_core.messages import BaseMessage, HumanMessage

from common import tools
from framework.models.llm import LanguageModelInput
from framework.agents.schema import ViewMessage
from ..assistant import AssistantAgent
from .queue import RotatingQueue
from framework.agents.autogen.participant import ParticipantAgent
from ...prompt import PromptFactory

DEFAULT_ROLE_SELECTOR_PROMPT = """
# 职责描述
你正在一个角色扮演游戏中，这个游戏旨在给用户的话题提供参考，参与角色如下：
{{ role_and_descriptions }}

# 背景知识及文档内容相关解释
为了能更好的玩这个游戏，你需要以下背景知识：
{{ background_knowledge }}

# 回复格式
你需要按照以下格式回复：
```
<role_name>
```
role_name 表示下一个需要发言的角色名称。角色名称只能从 {{ participants }} 中选一个。
你需要将下一个需要发言的角色名称放在 markdown 代码块中。

# 回复要求
  1. 你需要不断重复上述格式步骤，直至各个角色解决了问题为止。
  2. 如果其他角色已经无法解决问题，或者出现了很多重复或者无意义的讨论，你应该直接让 reporter 收集结果并回复用户。
  3. 注意，reporter 角色只有在需要终止上述思考步骤的时候才需要调用，否则不要指定 reporter 角色。
  4. 需要注意：只有 reporter 才能与用户沟通，用户无法与其他角色沟通，所以需要最终回复的内容请明确告知 reporter.
  5. 如果需要角色给出资源，则你必须指定好资源的 id（int 类型的数字），同一个资源的 id 会互相覆盖，如果想最终给用户回复不同的资源，则应该使用不同的资源 id 做区分。

# 用户对话记录与需求
仔细阅读下面的对话记录，并从中识别到用户(user/human)最终的需求，然后按照要求格式解决用户需求。
***
{{ chat_history }}

{{ query_prompt }}
"""

DEFAULT_USER_ROLE_SELECTOR_PROMPT = """# 回复格式
你需要按照以下格式回复：
```
<role_name>
```
role_name 的选择规则如前面的系统级别的要求所示！

好了，请你按照要求的格式给出下一个角色名称：
"""

REPORTER_PROMPT = '''
# 职责
你正在一个角色扮演游戏中，你擅长根据其他角色的讨论内容，将内容进行重新组织后回复用户。
你需要仔细理解其他角色的讨论内容，并根据需求回复用户。

其他的角色可能的回复包括：
  - 1. 普通文本，其他角色的讨论发言内容，或者角色的执行结果
  - 2. 资源，角色可能会给出图表、页面等资源，此时角色会告知资源的 id

# 回复要求
  1. 你需要直接回复用户，不要对用户暴露你正在游戏中。也不要在回复内容中出现发言角色的名字！
  2. 禁止回复敏感内容，禁止回复其他角色未提及的内容。
  3. 你应该直接回复，禁止精简需要回复的内容！
  4. 如果需要展示资源，则你必须使用占位符来替代需要展示的资源（如下所示）！

**资源展示要求：**
如果需要展示资源，则你必须将其他角色给出的资源id放在<|display_resource|>...<|end_resource|>中间，比如：

...
<|display_resource|>
resource_id=123
<|end_resource|>
...

资源展示注意事项：
  1. resource_id 必须在 {{resource_ids }} 范围内，禁止使用不在这个范围内的 resource_id；
  2. 每一个 <|display_resource|> 只能展示一个 resource_id，展示对个 resource_id 请使用多个 <|display_resource|>...<|end_resource|> 展示；
  3. 你不能回复用户“资源”这个概念，用户无法理解“资源”，使用正常的回复语气回复用户即可，在需要的地方展示即可，无需提示“资源”！
  4. <|display_resource|> 和 <|end_resource|> 是两个不可拆分的 token，请回复的时候注意不要遗漏的尖括号！


# 用户对话记录与需求
仔细阅读下面的对话记录，然后回复用户(user/human)。
***
{{ chat_history }}

# 发言的角色和他们的发言内容
***
{{ role_chat_history }}

你的回复：
'''
REPORTER_DESCRIPTION = '''reporter 是这场游戏的信息收集者，他将收集其他人回复结果，并将最终结果整理后回复给用户。记住：reporter 仅在游戏需要结束的时候才会上场，如果其他人回复的内容已经无效或者大量重复讨论，请直接让 reporter 终结这场游戏！'''
REPORTER_NAME = 'reporter'

RESOURCE_START_TOKEN = "<|display_resource|>"
RESOURCE_END_TOKEN = "<|end_resource|>"
RESOURCE_ID_START = "resource_id="

log = tools.get_logger()


class ReporterAgent(ParticipantAgent):
    def __init__(
            self,
            manager_name: str,
            manager: "ManagerAgent",
            description: str | Template | None = None,
            system_prompt: str | Template | None = None,
            **kwargs,
    ):
        self.manager_name = manager_name
        self.manager = manager
        if not description:
            description = REPORTER_DESCRIPTION
        if not system_prompt:
            system_prompt = REPORTER_PROMPT
        self._system_prompt = Template(system_prompt)
        self.participants_memory = InMemoryChatMessageHistory()
        # This role must have name: reporter
        super().__init__(
            name=REPORTER_NAME,
            description=description,
            system_prompt=system_prompt,
            **kwargs,
        )

    @property
    def system_prompt(self) -> str:
        user_chat_history = self.get_history()
        user_chat_history = '\n'.join([f'{h.type}: {h.content}' for h in user_chat_history])

        role_chat_history = self.participants_memory.messages
        role_chat_history = '\n'.join([f'{h.name}: {h.content}' for h in role_chat_history])

        resources = self.manager.collect_resources()
        if resources:
            resource_ids = [str(id) for id in resources]
        else:
            resource_ids = []

        system_prompt = PromptFactory.render_prompt(
            self._system_prompt,
            chat_history=user_chat_history,
            role_chat_history=role_chat_history,
            resource_ids=json.dumps(resource_ids, indent=0),
        )
        return system_prompt

    @system_prompt.setter
    def system_prompt(self, sp: str):
        """ignore"""
        pass

    def receive_from_manager(self, message: LanguageModelInput):
        messages = self._convert_message(message)
        for message in messages:
            self.participants_memory.add_message(
                HumanMessage(content=f"Transferred to {message.name}:\n{message.content}", name=self.manager_name))

    def receive_from_other_participant(self, message: LanguageModelInput):
        messages = self._convert_message(message)
        self.participants_memory.add_messages(messages)

    def run(self) -> BaseMessage:
        """运行 agent 步骤"""
        response = self.chat(prompts=None)
        return self._convert_message(message={'role': 'assistant', 'content': response, 'name': self.name})[0]

    def stream_run(self) -> Generator[BaseMessage, Any, Any]:
        """运行 agent 步骤"""
        response = self.stream_chat(prompts=None)
        for message in response:
            yield self._convert_message(message={'role': 'assistant', 'content': message, 'name': self.name})[0]

    async def arun(self) -> BaseMessage:
        """运行 agent 步骤"""
        response = self.chat(prompts=None)
        return self._convert_message(message={'role': 'assistant', 'content': response, 'name': self.name})[0]

    def add_resource(self, resource_id: int, resource: Any):
        self.resources[resource_id] = resource

    def _check_and_get_resource(self, message: str) -> list | None:
        """检查文本片段中是否有资源引用"""
        if not message:
            return None

        if RESOURCE_START_TOKEN in message and RESOURCE_END_TOKEN in message:
            log.info(f"parse resource from message: {message}")
        else:
            return None

        def _load_resource_as_view_message(resource_str: str):
            try:
                resource_id = int(resource_str.split("=")[1].strip())
                all_resources = self.manager.collect_resources()
                log.info(f"all_resources: {all_resources}")
                resource_detail = all_resources.get(resource_id, None)
                if resource_detail is None:
                    resource_detail = ViewMessage(type='MARKDOWN', content=f'\n```\nNo resource id={resource_id}\n```')
                return resource_detail
            except Exception as e:
                log.error(f'load resource error: {e}')
                return resource_str

        start_escaped = re.escape(RESOURCE_START_TOKEN)
        end_escaped = re.escape(RESOURCE_END_TOKEN)
        pattern = f"{start_escaped}(.*?){end_escaped}"

        text_results = []
        matches = list(re.finditer(pattern, message, re.DOTALL))
        last_end = 0
        for match in matches:
            if match.start() > last_end:
                text_results.append(message[last_end:match.start()])
            text_results.append(match.group(1).strip())
            last_end = match.end()
        if last_end < len(message):
            text_results.append(message[last_end:])

        results = []
        for txt in text_results:
            if txt.strip().startswith(RESOURCE_ID_START):
                results.append(_load_resource_as_view_message(txt.strip()))
            else:
                results.append(txt)

        if len(results) == 0:
            return None
        if len(results) == 1 and isinstance(results[0], str):
            return None
        return results

    async def astream_run(self) -> AsyncGenerator[BaseMessage, Any]:
        """运行 agent 步骤"""
        response = self.astream_chat(prompts=None)

        # 缓存解析资源信息
        cache_queue = RotatingQueue(maxsize=50)

        async for message in response:
            part_content = str(cache_queue)
            log.debug(f'reporter part content cache: {part_content}')
            check_resource_result = self._check_and_get_resource(part_content)
            if check_resource_result is not None:
                await cache_queue.clear()
                for i, r in enumerate(check_resource_result):
                    if isinstance(r, str):
                        if i != len(check_resource_result) - 1:
                            yield self._convert_message(message={
                                'role': 'assistant',
                                'content': r,
                                'name': self.name
                            })[0]
                        else:
                            # 最后一个要放回队列里面，避免出现多个 resource 的错误
                            await cache_queue.put(r)
                    else:
                        yield r
            else:
                token = await cache_queue.put(message)
                if token is not None:
                    yield self._convert_message(message={
                        'role': 'assistant',
                        'content': token,
                        'name': self.name
                    })[0]

        left_content = str(cache_queue)
        if left_content:
            yield self._convert_message(message={
                'role': 'assistant',
                'content': left_content,
                'name': self.name
            })[0]


class NextParticipantResponse(BaseMessage):
    type: str = 'assistant'

    next_participant: str
    """下一个角色名称"""
    next_instruction: BaseMessage | str | None = None
    """下一个角色要做的事情指引"""

    def __init__(
            self,
            content: Union[str, list[Union[str, dict]]],
            next_participant: str,
            next_instruction: str | None = None,
            **kwargs,
    ):
        super().__init__(
            content=content,
            next_participant=next_participant,
            next_instruction=next_instruction,
            **kwargs
        )


_user_chat_history_attr_name = '_user_chat_history'


class ManagerAgent(AssistantAgent):
    def __init__(
            self,
            name: str,
            background_knowledge: str | Template | None = None,
            system_prompt: str | Template | None = None,
            generate_prompt: str | Template | None = None,
            **kwargs,
    ):
        """
        分组 AutoGen 的基类，会自动创建 reporter 角色

        Args:
            name: 本 agent 的名称
            background_knowledge: 预先知道的背景知识
            system_prompt: 系统提示
        """
        self._name = name
        self.participants = []
        self.background_knowledge = background_knowledge or 'No content'
        if isinstance(self.background_knowledge, str):
            self.background_knowledge = Template(self.background_knowledge)

        system_prompt = system_prompt or DEFAULT_ROLE_SELECTOR_PROMPT
        if isinstance(system_prompt, str):
            system_prompt = Template(system_prompt)

        self.generate_prompt = generate_prompt if generate_prompt else DEFAULT_USER_ROLE_SELECTOR_PROMPT
        if isinstance(self.generate_prompt, str):
            self.generate_prompt = Template(self.generate_prompt)

        super().__init__(system_prompt=system_prompt, **kwargs)
        self._system_prompt = system_prompt
        self.participants = []
        self.real_query: str = ''
        self.kwargs = kwargs

    @property
    def name(self):
        return self._name

    def init_participants(self, participants: List[ParticipantAgent]):
        # 自动添加 reporter 角色
        if self.participants:
            log.warning('participants inited, ignore.')
            return

        if not participants:
            participants = []
        if REPORTER_NAME not in set([p.name for p in participants]):
            reporter = ReporterAgent(
                manager_name=self.name,
                manager=self,
                **self.kwargs
            )
            history = super().get_history()
            if history:
                reporter.add_history(history)
            participants.append(reporter)
        self.participants.extend(participants)

    @property
    def system_prompt(self) -> str:
        query_prompt = ''
        if self.real_query:
            query_prompt = f'# 用户本次实际查询\n下面是用户本次实际的查询内容：\n{self.real_query}'

        system_prompt = PromptFactory.render_prompt(
            self._system_prompt,
            background_knowledge=PromptFactory.render_prompt(self.background_knowledge),
            role_and_descriptions='\n'.join([' - ' + p.name + ': ' + p.description for p in self.participants]),
            participants=[p.name for p in self.participants],
            chat_history=self.user_chat_history,
            query_prompt=query_prompt,
        )
        return system_prompt

    @system_prompt.setter
    def system_prompt(self, sp: str):
        """ignore"""
        pass

    @property
    def user_chat_history(self):
        """需要将用户的历史记录和组内讨论记录区分开"""
        attr_name = _user_chat_history_attr_name
        if hasattr(self, attr_name):
            return getattr(self, attr_name)
        history = super().get_history()
        super().clear_history()
        history_contents = ['Chat History And User Requirements:']
        if not history:
            history_contents.append('No content.')
        else:
            for h in history:
                if isinstance(h, HumanMessage):
                    history_contents.append(f'user: {h.content}')
                else:
                    history_contents.append(f'assistant: {h.content}')
        setattr(self, attr_name, '\n'.join(history_contents))
        return getattr(self, attr_name)

    @property
    def terminate_participant(self) -> str:
        """终止的 agent 名称"""
        return REPORTER_NAME

    def collect_resources(self):
        resources = {}
        for participant in self.participants:
            p_resources = participant.resources
            if p_resources:
                resources.update(p_resources)
        return resources

    def collect(self, message: LanguageModelInput):
        messages = self._convert_message(message)
        for message in messages:
            self.add_history(message)

    async def acollect(self, message: LanguageModelInput):
        messages = self._convert_message(message)
        for message in messages:
            await self.async_add_history(message)

    def clear_history(self):
        super().clear_history()
        attr_name = _user_chat_history_attr_name
        if hasattr(self, attr_name):
            delattr(self, attr_name)

    def get_history(self):
        user_chat_history = self.user_chat_history
        log.debug(f'user chat history: {user_chat_history}')
        return self.memory.messages.copy()

    def run(self) -> NextParticipantResponse:
        """运行 agent 步骤"""
        history = self.get_history()
        history.append(HumanMessage(content=PromptFactory.render_prompt(self.generate_prompt)))
        response = self.chat(prompts=history)
        self.log_and_callback(f"manager call history: {history}\nmanager call response: {response}")
        next_participant = self.extract_next_participant(response=response)
        self.add_history(history=[{'role': 'assistant', 'name': self.name, 'content': response}])
        return next_participant

    async def arun(self) -> NextParticipantResponse:
        """协程中运行 agent 步骤"""
        history = self.get_history()
        history.append(HumanMessage(content=PromptFactory.render_prompt(self.generate_prompt)))
        response = await self.achat(prompts=history)
        self.log_and_callback(f"manager call history: {history}\nmanager call response: {response}")
        next_participant = self.extract_next_participant(response=response)
        self.add_history(history=[{'role': 'assistant', 'name': self.name, 'content': response}])
        return next_participant

    def extract_next_participant(self, response: str) -> NextParticipantResponse:
        role_name = tools.extract_code_from_text(response)
        for participant in self.participants:
            if participant.name.strip().lower() == role_name.strip().lower():
                return NextParticipantResponse(
                    next_participant=participant.name,
                    content=response,
                )
        raise RuntimeError(f'Get participant name from response error. [response={response}]')
