from inspect import isawaitable, iscoroutinefunction
from typing import List, Optional, Any, Callable

from langchain_core.messages import BaseMessage, convert_to_messages
from llama_index.core.workflow import (
    StartEvent, StopEvent, step, Event, Context,
)
from llama_index.core.workflow.checkpointer import CheckpointCallback
from llama_index.core.workflow.handler import WorkflowHandler
from llama_index.core.workflow.service import ServiceManager
from llama_index.core.workflow.workflow import dispatcher

from common import tools
from framework.agents.assistant import AssistantAgent
from framework.agents.real_query import RealQueryAgent
from framework.agents.autogen.manager import ManagerAgent
from framework.agents.autogen.participant import ParticipantAgent
from framework.config import DEBUG_MODE
from framework.workflow.base import WorkFlowWithCallback
from framework.models.llm import LanguageModelInput
from framework.agents.schema import ViewMessage, ViewMessageStatus

log = tools.get_logger()


class SetupEvent(Event):
    """初始化标识"""


class ExtractRealQueryEvent(Event):
    """解析实际问的问题"""


class RunManagerEvent(Event):
    """开始运行 manager agent"""
    message: BaseMessage | None = None


class ParticipantResponseEvent(Event):
    """participant 运行返回"""
    message: BaseMessage | None = None


class RunParticipantEvent(Event):
    participant: ParticipantAgent
    instruction: str | None = None


class TerminalMessageEvent(Event):
    message: BaseMessage


class AutoGenWorkflow(WorkFlowWithCallback):
    def __init__(
            self,
            manager_agent: ManagerAgent,
            participant_agents: List[ParticipantAgent],
            need_extract_real_query: bool = False,
            timeout: float | None = 60 * 60,
            disable_validation: bool = False,
            verbose: bool = False,
            service_manager: ServiceManager | None = None,
            num_concurrent_runs: int | None = None,
            max_talk_round: int = 10,
            stream: bool = False,
            think_progress: bool = True,
            output_as_final_answer: bool = True,
            step_callback: Callable[[Any], Any] | None = None,
            callback: Callable[[Any], Any] = None,
            **kwargs,
    ):
        """
        运行 Autogen 的 workflow

        Args:
            step_callback: 中间过程日志回调
            need_extract_real_query: 是否需要解析问题二次提问（rag的时候有用）
            max_talk_round: 最大讨论轮次
            stream: 开启流式返回
            output_as_final_answer: 是否将结果作为最终结果输出给用户
            callback: 流式回复等用来展示的函数
        """
        super().__init__(
            timeout=timeout,
            disable_validation=disable_validation,
            verbose=verbose,
            service_manager=service_manager,
            num_concurrent_runs=num_concurrent_runs,
            callback=step_callback,
        )
        self._inner_callback = callback
        self.step_callback = step_callback
        self.need_extract_real_query = need_extract_real_query
        self.max_talk_round = max_talk_round
        self.manager_agent = manager_agent
        self.participant_agents = participant_agents
        self.original_history = []
        self.talk_round = 0
        self.real_query = ''
        self.stream = stream
        self.think_progress = think_progress
        self.output_as_final_answer = output_as_final_answer

    async def display_callback(self, content: Any):
        if content is None:
            content = ''
        if self._inner_callback:
            try:
                # 判断是否是需要等待的协程
                if isawaitable(content):
                    content = await content
                if iscoroutinefunction(self._inner_callback):
                    await self._inner_callback(content)
                else:
                    self._inner_callback(content)
            except BaseException as e:
                log.warning('display callback error.', e)

    async def display_message_content(self, message: Any, type: str = "MARKDOWN"):
        if message is None:
            return
        if not isinstance(message, ViewMessage):
            message = ViewMessage(
                type=type,
                content=message
            )
        await self.display_callback(message)

    async def display_status(self, status: str, text: str | None = None, keeping: bool = True):
        await self.display_callback(
            ViewMessageStatus(
                status=status,
                text=text or '',
                keeping=keeping
            )
        )

    @dispatcher.span
    async def run_with_history(
            self,
            history: LanguageModelInput,
            ctx: Optional[Context] = None,
            stepwise: bool = False,
            checkpoint_callback: Optional[CheckpointCallback] = None,
            **kwargs: Any,
    ) -> WorkflowHandler:
        """运行 workflow"""
        self._clear_states()
        temp_agent = AssistantAgent()
        temp_agent.add_history(history)
        self.original_history.extend(temp_agent.get_history())

        # 添加聊天记录，如果需要解析出来真实查询语句的话，就不需要添加历史记录
        if not self.need_extract_real_query:
            await self.manager_agent.async_add_history(history=history)
            for participant in self.participant_agents:
                await participant.async_add_history(history=history)

        return await self.run(
            ctx=ctx,
            stepwise=stepwise,
            checkpoint_callback=checkpoint_callback,
            kwargs=kwargs,
        )

    def _clear_states(self):
        """为了防止复用导致的错误，需要清除以前的 history 和状态信息"""
        self.manager_agent.clear_history()
        for participant in self.participant_agents:
            participant.clear_history()

        self.talk_round = 0
        self.real_query = ''

    def get_participant(self, name: str) -> ParticipantAgent | None:
        for participant in self.participant_agents:
            if participant.name == name:
                return participant

    @step
    async def start(self, ctx: Context, event: StartEvent) -> SetupEvent:
        """任务开始"""
        await self._callback('Start run autogen task.')
        return SetupEvent()

    @step
    async def setup(self, ctx: Context, event: SetupEvent) -> ExtractRealQueryEvent:
        await self._callback('Setup autogen task.')
        if self.manager_agent is None:
            raise RuntimeError('manager agent can not be None.')
        self.manager_agent.init_participants(participants=self.participant_agents)

        return ExtractRealQueryEvent()

    @step
    async def extract_real_query(
            self,
            ctx: Context,
            event: ExtractRealQueryEvent
    ) -> RunManagerEvent:
        if self.need_extract_real_query:
            # 解析出来真正的问题
            await self.display_status(status='PROCESSING', text='正在识别意图...', keeping=False)
            real_query_agent = RealQueryAgent(llm=self.manager_agent.llm)
            real_query_agent.add_history(history=self.original_history)
            self.real_query = await real_query_agent.aget_real_query()
            self.manager_agent.real_query = self.real_query
            self.manager_agent.add_history(self.real_query)
            for participant in self.participant_agents:
                if hasattr(participant, 'real_query'):
                    participant.real_query = self.real_query

            await self._callback(f'real query: {self.real_query}')
            if DEBUG_MODE:
                await self.display_message_content(f"\n\n查询重写：{self.real_query}\n\n", type="THINKING")
            await self.display_status(status='SUCCESS', text='意图识别成功', keeping=True)

        if self.output_as_final_answer:
            await self.display_status(status='PROCESSING', text='正在执行多步推理...', keeping=False)
        return RunManagerEvent()

    @step
    async def run_manager(
            self,
            ctx: Context,
            event: RunManagerEvent | ParticipantResponseEvent,
    ) -> RunParticipantEvent:
        """运行 manager 任务"""
        await self._callback(content="Run manager.")

        if isinstance(event, ParticipantResponseEvent):
            if event.message is not None:
                for participant in self.participant_agents:
                    await self._callback(content=f"Broadcast message from participant to {participant.name}.")
                    await participant.areceive_from_other_participant(message=event.message)

        self.talk_round += 1
        if self.talk_round > self.max_talk_round:
            return RunParticipantEvent(
                participant=self.get_participant(name=self.manager_agent.terminate_participant)
            )

        if event.message is not None:
            await self.manager_agent.acollect(event.message)
        response = await self.manager_agent.arun()
        await self._callback({
            'name': self.manager_agent.name,
            'role': 'assistant',
            'content': f'To {response.next_participant}: {response.next_instruction}'
        })
        if DEBUG_MODE:
            await self.display_message_content(
                f"\n\n{self.manager_agent.name} 指定角色任务：\n```json\n{response.model_dump_json(indent=4)}\n```\n\n",
                type="THINKING"
            )

        next_participant = self.get_participant(response.next_participant)
        if next_participant is None:
            raise RuntimeError('Get next participant failed.')
        if response.next_instruction:
            return RunParticipantEvent(participant=next_participant, instruction=response.next_instruction)
        return RunParticipantEvent(participant=next_participant)

    @step
    async def run_participant(
            self,
            ctx: Context,
            event: RunParticipantEvent,
    ) -> TerminalMessageEvent | ParticipantResponseEvent:
        participant = event.participant
        await self._callback(content=f"Run participant: {participant.name}.")

        # 这里也需要广播，让其他 agent 也能记住
        if event.instruction:
            new_message = {
                'role': 'assistant',
                'name': participant.name,
                'content': event.instruction
            }
            for p in self.participant_agents:
                await self._callback(content=f"Broadcast instruction message to {p.name}.")
                if DEBUG_MODE:
                    await self.display_message_content(
                        f"\n```\n消息广播 {self.manager_agent.name} -> {p.name}：\n指令：{event.instruction}\n```",
                        type="THINKING"
                    )
                await p.areceive_from_manager(message=new_message)

        if DEBUG_MODE:
            await self.display_message_content(f"\n\n开始运行角色：{participant.name}\n\n", type="THINKING")

        message_type = "THINKING"
        need_display = False
        if self.think_progress:
            if participant.name != self.manager_agent.terminate_participant:
                need_display = True

        if participant.name == self.manager_agent.terminate_participant and self.output_as_final_answer:
            message_type = "MARKDOWN"
            need_display = True

        try:
            if not self.stream:
                response = await participant.arun()
                if need_display:
                    await self.display_message_content(message=response.content, type=message_type)
            else:
                contents = []
                stream_response = participant.astream_run()
                async for content in stream_response:
                    if isinstance(content, str):
                        contents.append(content)
                        if need_display:
                            await self.display_message_content(message=content, type=message_type)
                    elif isinstance(content, BaseMessage):
                        contents.append(content.content)
                        if need_display:
                            await self.display_message_content(message=content.content, type=message_type)
                    elif isinstance(content, ViewMessage):
                        contents.append(content.content or '')
                        if need_display:
                            await self.display_callback(content)
                    else:
                        if need_display:
                            await self.display_message_content(message=content, type=message_type)

                response = convert_to_messages([{
                    'name': participant.name,
                    'role': 'assistant',
                    'content': ''.join(contents)
                }])[0]
        except Exception as e:
            log.error(f"run participant error. {e}")
            return ParticipantResponseEvent(message=convert_to_messages([{
                'name': participant.name,
                'role': 'assistant',
                'content': f"run participant error. {e}"
            }])[0])

        await self._callback({
            'name': response.name,
            'role': 'assistant',
            'content': response.content
        })
        if self.manager_agent.terminate_participant == participant.name:
            return TerminalMessageEvent(message=response)
        else:
            return ParticipantResponseEvent(message=response)

    @step
    async def terminate(self, ctx: Context, event: TerminalMessageEvent) -> StopEvent:
        """接收最终消息并返回"""
        await self._callback(content="Terminate autogen task and return result.")
        if self.output_as_final_answer:
            await self.display_status(status='SUCCESS', text='推理完成', keeping=True)
        return StopEvent(result=event.message)
