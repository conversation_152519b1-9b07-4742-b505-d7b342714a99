from typing import Tuple
import re

from jinja2 import Template
from langchain_core.messages import BaseMessage, AIMessage, HumanMessage
from pydantic import BaseModel

from .manager import ManagerAgent, NextParticipantResponse
from common import tools
from ...prompt import PromptFactory

log = tools.get_logger()

COMMON_BACKGROUND_KNOWLEDGE = """```
神策数据是一家面向企业提供软件服务的软件公司，其软件产品包括：
1. 神策数界平台(Sensors Data Horizon，简称 SDH 或者 SPS)，用户（用户）数据引擎，即用户数据平台(CDP)，功能包括：
    - 数据管理，主要管理的数据包括：
        - profile: 用户基础信息，存储于 users 表中，比如用户的姓名、年龄、地域等；
        - event: 用户事件行为信息，比如用户在什么时间、什么地点、点击了哪个按钮等等行为记录，存储与 events 表；
        - items: 企业的商品或者物品信息，存储与 items 表。
        - 外部表 / 多实体等
    - 标签管理：通过 SQL、自定义规则等方式计算用户的标签值
    - 分群管理：通过 SQL、自定义规则等方式计算用户的群体分类
2. 神策分析 (Sensors Analytics，简称 SA)产品，用于分析企业用户的数据，比如用户的价值分析、事件分析等；
3. 神策智能运营(Sensors Focus，简称 SF), 依赖 CDP 来完成自动化营销，其主要包含了以下模块功能：
    - 流程画布：简称「画布」，企业可以自主配置用户的流转节点，在各个节点的判定规则（比如是否做了某个事件等），并通过触达通道给用户发送营销信息等；
    - 运营计划：简称「计划」，通过简单的配置即可实现批量的、简单的营销触达，注意流程画布和运营计划是两个功能；
    - 资源位：包括比如弹窗、物品推荐、banner 等营销方式；
    - 微信运营：常见的微信公众号、小程序、微信裂变等玩法（需要注意：微信公众号的群发等可以通过运营计划、流程画布完成）。
```"""
# 使用 react prompt 方法，会更好一些
MANAGER_PROMPT_REACT = """
# 职责描述
你正在一个角色扮演游戏中，这个游戏旨在尽最大努力给用户的话题提供参考，参与角色如下：
{{role_and_descriptions}}

# 背景知识及文档内容相关解释
为了能更好的玩这个游戏，你需要以下背景知识：
{{background_knowledge}}

# 回复格式
你需要按照以下格式回复：
```
Thought: 当前问题已经解决了 xx，为了解决 xx 问题，我需要 xx 角色来做 xx 事情
Choose: 角色名称，角色名称只能从 {{participants}} 中选一个
Do: 角色需要做的事情，比如：请你继续分析用户的意图
```
你必须始终以 `Thought: ` 开头，Thought 中你需要思考当前提供的内容是否足够回答用户问题，以及需要谁做什么等。
不允许使用 markdown 代码块标记回复！如果回复中包含代码信息，则可以使用 markdown 代码块标记。
如果你所选的角色已经回复了你，则他们会加上他们自己的回复，比如：Do Response: 这里是所选择的role的回复内容。
需要注意：只有 reporter 才能与用户沟通，用户无法与其他角色沟通，所以需要最终回复的内容请明确告知 reporter.

# 回复要求
  1. 你需要不断重复上述格式步骤，直至检索到足够的信息为止。需要注意的是，其他角色的发言通常不全（或者会用局部信息欺骗你），为了保证用户能获取足量的信息，你应该尽可能的让他们发言，直到无法得到新的信息为止。
  2. 如果其他角色已经无法解决问题，或者出现了很多重复或者无意义的讨论，你应该直接让 reporter 收集结果并回复用户。
  3. 注意，reporter 角色只有在需要终止上述思考步骤的时候才需要调用，否则不要指定 reporter 角色。
  4. 如果需要角色给出资源，则你必须指定好资源的 id（int 类型的数字），同一个资源的 id 会互相覆盖，如果想最终给用户回复不同的资源，则应该使用不同的资源 id 做区分。

注意，你不能从对话内容中获取角色名称！

# 用户对话记录与需求
仔细阅读下面的对话记录，并从中识别到用户(user/human)最终的需求，然后按照上述格式解决用户需求。
***
{{chat_history}}

{{query_prompt}}

开始：
"""

USER_SELECTOR_REACT_PROMPT = """
# 回复格式
你必须按照以下格式回复（Thought/Choose/Do 三个都只能在你的回复中出现一次！）：
Thought: 当前问题已经解决了 xx，为了解决 xx 问题，我需要 xx 角色来做 xx 事情
Choose: 角色名称，角色名称只能从 {{participants}} 中选一个
Do: 角色需要做的事情，比如：请你继续分析用户的意图

三个步骤的含义如前面的系统级别要求所示！

好了，请你依次回复上述的三个步骤：
"""


class FinalResult(BaseModel):
    content: str | None = None


def extract_role_use(input_text: str) -> Tuple[str, str]:
    pattern = (
        r".*?Choose: (.*)\n+Do: (.*)"
    )

    # 有时候模型不智能，会返回多个 Choose，取第一个，尽量让 participant 执行
    input_text_parts = input_text.split('Choose:')
    if len(input_text_parts) <= 1:
        input_text = 'Choose:' + input_text_parts[0]
    else:
        input_text = 'Choose:' + input_text_parts[1]
    not_allowed_tokens = ['Do Response:', 'Thought:']
    for nt in not_allowed_tokens:
        if nt in input_text:
            input_text = input_text.split(nt)[0]
    match = re.search(pattern, input_text, re.DOTALL)
    if not match:
        raise ValueError(f"Could not extract role from input text: {input_text}")

    role = match.group(1).strip()
    role_do = match.group(2).strip()
    return role, role_do


class ReactManagerAgent(ManagerAgent):
    def __init__(
            self,
            background_knowledge: str | Template = COMMON_BACKGROUND_KNOWLEDGE,
            system_prompt: str | Template = MANAGER_PROMPT_REACT,
            generate_prompt: str | Template = USER_SELECTOR_REACT_PROMPT,
            **kwargs,
    ) -> None:
        super().__init__(
            background_knowledge=background_knowledge,
            system_prompt=system_prompt,
            generate_prompt=generate_prompt,
            **kwargs,
        )

    def _get_react_history(self) -> BaseMessage | None:
        messages = []
        for msg in self.get_history():
            if msg.name != self.name:
                messages.append(f'Do Response: {msg.content}')
            else:
                messages.append(msg.content)
        if not messages:
            return None
        return AIMessage(content='\n'.join(messages))

    def run(self) -> NextParticipantResponse:
        """运行 agent 步骤"""
        history_message = self._get_react_history()
        generate_prompt = PromptFactory.render_prompt(self.generate_prompt)

        if history_message is not None:
            user_prompt = f"# 已回复步骤：\n{history_message.content}\n\n{generate_prompt}"
        else:
            user_prompt = generate_prompt
        react_history = [
            HumanMessage(content=user_prompt)
        ]

        final_exception = None
        # 增加重试逻辑
        for i in range(5):
            try:
                response = self.chat(prompts=react_history)
                self.log_and_callback(f"manager call history: {react_history}\nmanager call response: {response}")
                react_history.append(AIMessage(content=response))
                next_participant = self.extract_next_participant(response=response)
                self.add_history(history=[{'role': 'assistant', 'name': self.name, 'content': response}])
                return next_participant
            except Exception as e:
                log.error(f'arun error: {e}')
                final_exception = e
                # 只有是回复格式错误才需要重试
                if isinstance(react_history[-1], AIMessage):
                    react_history.append(HumanMessage(content=f"回复错误：{e}\n请重新回复！"))
                    continue
                break
        raise final_exception

    async def arun(self) -> NextParticipantResponse:
        """协程中运行 agent 步骤"""
        history_message = self._get_react_history()
        if history_message is not None:
            user_prompt = f"# 已回复步骤：\n{history_message.content}\n\n{self.generate_prompt}"
        else:
            user_prompt = self.generate_prompt
        react_history = [
            HumanMessage(content=user_prompt)
        ]

        final_exception = None
        # 增加重试逻辑
        for i in range(5):
            try:
                response = self.chat(prompts=react_history)
                self.log_and_callback(f"manager call history: {react_history}\nmanager call response: {response}")
                react_history.append(AIMessage(content=response))
                next_participant = self.extract_next_participant(response=response)
                self.add_history(history=[{'role': 'assistant', 'name': self.name, 'content': response}])
                return next_participant
            except Exception as e:
                log.error(f'arun error: {e}')
                final_exception = e
                # 只有是回复格式错误才需要重试
                if isinstance(react_history[-1], AIMessage):
                    react_history.append(HumanMessage(content=f"回复错误：{e}\n请重新回复！"))
                    continue
                break
        raise final_exception

    def extract_next_participant(self, response: str) -> NextParticipantResponse:
        participant_name, participant_instruction = extract_role_use(response)
        for participant in self.participants:
            if participant.name.strip().lower() == participant_name.strip().lower():
                return NextParticipantResponse(
                    next_participant=participant.name,
                    next_instruction=participant_instruction,
                    content=response,
                )
        raise RuntimeError(
            f'Get participant name from response error. '
            f'[response={response}, participant_name={participant_name}, '
            f'all_participant_name={[p.name for p in self.participants]}]'
        )
