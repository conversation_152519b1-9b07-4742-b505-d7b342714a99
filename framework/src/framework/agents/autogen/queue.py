import asyncio


class RotatingQueue(asyncio.Queue):
    def __init__(self, maxsize=0):
        super().__init__(maxsize=maxsize)

    async def put(self, item):
        """
        put element into queue, if queue full, remove and return last element
        """
        result = None
        if self.full():
            result = self.get_nowait()
        await super().put(item)
        return result

    def __str__(self):
        queue = list(self._queue)
        if queue:
            return ''.join([str(item) for item in queue])
        else:
            return ''

    async def clear(self):
        while not self.empty():
            await self.get()
