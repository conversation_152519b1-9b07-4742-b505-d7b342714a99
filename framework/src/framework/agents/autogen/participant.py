"""
基础参与成员 agent 流程定义
"""
import asyncio
import json
from pathlib import Path
from typing import Any, Optional, Dict, Generator, AsyncGenerator

from jinja2 import Template
from langchain_core.messages import BaseMessage, HumanMessage

from framework.models import BaseLLM
from framework import config
from common import tools
from framework.models.llm import LanguageModelInput
from ..assistant import AssistantAgent
from ...prompt import PromptFactory

log = tools.get_logger()


class ParticipantAgent(AssistantAgent):
    """成员 agent 基础类"""

    def __init__(
            self,
            description: str | Template,
            name: str,
            llm: Optional[BaseLLM] = None,
            system_prompt: str | Template | None = None,
            llm_params: dict[str, Any] = None,
            real_query: str | None = None,
            **kwargs,
    ):
        self._description = description if isinstance(description, Template) else Template(description)
        self._name = name
        self.real_query = real_query
        if not llm_params:
            llm_params = config.llm_config
        self._llm_params = llm_params
        super().__init__(
            llm=llm,
            system_prompt=system_prompt,
            **kwargs,
        )

        self._resources = dict()

    @property
    def description(self) -> str:
        return PromptFactory.render_prompt(self._description)

    @property
    def name(self) -> str:
        return self._name

    @property
    def resources(self):
        return self._resources

    def get_original_prompt_and_description(self) -> Dict[str, Any]:
        system_prompt = PromptFactory.render_prompt(self.system_prompt)
        prompt_data = {
            'description': self.description,
            'system_prompt': system_prompt,
        }
        return prompt_data

    def receive_from_manager(self, message: LanguageModelInput):
        messages = self._convert_message(message)
        for message in messages:
            self.add_history(HumanMessage(content=f"Transferred to {message.name}:\n{message.content}", name='system'))

    async def areceive_from_manager(self, message: LanguageModelInput):
        messages = self._convert_message(message)
        for message in messages:
            await self.async_add_history(
                HumanMessage(content=f"Transferred to {message.name}:\n{message.content}", name='system'))

    def receive_from_other_participant(self, message: LanguageModelInput):
        messages = self._convert_message(message)
        for message in messages:
            self.add_history(message)

    async def areceive_from_other_participant(self, message: LanguageModelInput):
        messages = self._convert_message(message)
        for message in messages:
            await self.async_add_history(message)

    def run(self) -> BaseMessage:
        """运行 agent 步骤"""
        history = self.get_history()
        history.append(
            HumanMessage(content=f"You are {self.name}, give the reasonable response immediately.", name='system'))
        response = self.chat(prompts=history)
        return self._convert_message(message={'role': 'assistant', 'content': response, 'name': self.name})[0]

    def stream_run(self) -> Generator[BaseMessage, Any, Any]:
        """运行 agent 步骤"""
        history = self.get_history()
        history.append(
            HumanMessage(content=f"You are {self.name}, give the reasonable response immediately.", name='system'))
        response = self.stream_chat(prompts=history)
        for message in response:
            yield self._convert_message(message={'role': 'assistant', 'content': message, 'name': self.name})[0]

    async def arun(self) -> BaseMessage:
        """运行 agent 步骤"""
        history = self.get_history()
        history.append(
            HumanMessage(content=f"You are {self.name}, give the reasonable response immediately.", name='system'))
        response = await self.achat(prompts=history)
        return self._convert_message(message={'role': 'assistant', 'content': response, 'name': self.name})[0]

    async def astream_run(self) -> AsyncGenerator[BaseMessage, Any]:
        """运行 agent 步骤"""
        history = self.get_history()
        history.append(
            HumanMessage(content=f"You are {self.name}, give the reasonable response immediately.", name='system'))
        response = self.astream_chat(prompts=history)

        if hasattr(response, '__aiter__'):
            async for message in response:
                yield self._convert_message(message={'role': 'assistant', 'content': message, 'name': self.name})[0]
        elif asyncio.iscoroutine(response):
            log.warning('response is coroutine, but not async iterator. please check your code.')
            response = await response
            log.warning(f'response after wait: {response}')
            if isinstance(response, BaseMessage):
                response = response.content
            yield self._convert_message(message={'role': 'assistant', 'content': response, 'name': self.name})[0]
        else:
            raise TypeError(f'response type error. {type(response)}')
