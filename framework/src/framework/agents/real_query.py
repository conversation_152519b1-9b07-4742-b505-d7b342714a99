from jinja2 import Template

from framework.prompt import PromptFactory
from common import tools
from .assistant import AssistantAgent

REAL_QUERY_PROMPT = '''# 核心职能
你的任务是拓展并重新表述给定的查询（即当前待解决的问题），同时保持原意图不变。
目标是使用一个句子来改写当前对话记录中待解决的问题，这些改写应避免歪曲原意，但需通过变换措辞、补充说明性背景或使用更全面的用语来体现多样性。
改写后的查询应准确反映原始核心问题，保持上下文关联且易于理解。

# 处理流程
- 问题扫描：倒序检查最近10轮对话聚焦最后2个未闭环的问题点(闭合标准：答案需包含具体参数/步骤/对比项)
- 要素重建：[限定]+核心问题
  - ▸ 用户会根据你改写的查询进行后续的处理工作，用户看不到原始的对话记录，所以你不能假设用户知道对话历史内容！
  - ▸ 必须包含所有限定词（如：零售行业SA旗舰版）
  - ▸ 专业名词使用首次完整形态（"MA工具"→"智能运营工具(MA)"）

# 改写规范
- 禁用代词：✖"这个产品" → ✔"神策用户画像系统"；
- 禁止合并概念：✖"SA版本区别" → ✔"SA基础版与旗舰版在API数量上的区别"；
- 当问题不明确时触发：请确认您问的是[A]还是[B]等重新提问。
- 你不能修改用户意图！
- 禁止无中生有，限定词或者核心问题中新加历史记录中不存在的行业、时间、地点等限定都是不允许的！

# 智能避错演示
案例1:
对话记录：
user: SA支持实时数据吗？
assistant: 支持实时事件采集
user: 最大并发量？
👉 合规改写：SA系统的实时事件采集功能支持的最大并发请求量是多少？

案例2:
对话记录：
user: 银行客户用什么方案好？
assistant: 推荐私有化部署
user: 要准备什么？
👉 合规改写：银行客户采用私有化部署方案时，需要提前准备哪些基础设施？

案例3：
user: 泛金融行业有哪些产品可以售卖？
assistant: 泛金融行业可售卖产品包括：SA基础版、SA旗舰版
user: 请告诉我基础版和旗舰版的区别

✖ 不合规改写例子：
   1. SA基础版和旗舰版有什么区别？
       - 错误原因：丢失了泛金融的信息！
   2. 如何查询泛金融的SA基础版和泛金融的SA旗舰版有什么区别？
       - 错误原因：修改了用户意图！

👉 合规改写：泛金融的SA基础版和泛金融的SA旗舰版有什么区别？
'''

REAL_QUERY_USER_PROMPT = '''
# 开始
对话记录：
{{ chat_history }}

👉 合规改写：
'''

CRITIC_PROMPT = """# 职责
你是一个评论家，给你一段对话记录和依据对话记录重写的查询，你需要告诉用户重写的查询是否合理。
用户会根据改写的查询进行后续的处理工作，用户看不到原始的对话记录，所以你不能假设用户知道对话历史内容！

一个合理的重写的查询应该满足以下要求：

- 问题扫描：倒序检查最近10轮对话聚焦最后2个未闭环的问题点(闭合标准：答案需包含具体参数/步骤/对比项)
- 要素重建：[行业领域][产品类型][版本信息]+核心问题  
  - ▸ 必须包含所有限定词（如：零售行业SA旗舰版）
  - ▸ 专业名词使用首次完整形态（"MA工具"→"智能运营工具(MA)"）
- 禁用代词：错误："这个产品" → 正确："神策用户画像系统"；
- 禁止合并概念：错误："SA版本区别" → 正确："电商行业SA基础版与旗舰版在API数量上的区别"；
- 当问题不明确时触发：请确认您问的是[A]还是[B]等重新提问。
- 不能修改对话记录中的用户意图！

**对话记录**

{{chat_history}}

**重写的查询**

{{real_query}}

# 回复要求
如果重写的查询合理，你应该直接回复一个特殊的token: YES

如果重写的查询不合理，你应该直接说出不合理的原因。

# 开始
"""


class RealQueryAgent(AssistantAgent):
    def __init__(
            self,
            prompt: str | Template | None = None,
            **kwargs,
    ) -> None:
        kwargs = kwargs | {
            'system_prompt': REAL_QUERY_PROMPT,
        }
        super().__init__(**kwargs)
        if not prompt:
            prompt = REAL_QUERY_PROMPT
        self.prompt = prompt

    def _check_query_response(self, origin_prompt: str, query_response: str) -> str | None:
        """return None if success, else return reason"""
        critic_prompt = PromptFactory.render_prompt(
            CRITIC_PROMPT,
            chat_history=origin_prompt,
            real_query=query_response
        )
        agent = AssistantAgent(system_prompt=critic_prompt)
        response = agent.chat(prompts="请回复 YES 或不合理原因：")
        if 'YES' in response:
            return None
        else:
            return response

    async def _acheck_query_response(self, origin_prompt: str, query_response: str) -> str | None:
        """return None if success, else return reason"""
        critic_prompt = PromptFactory.render_prompt(
            CRITIC_PROMPT,
            chat_history=origin_prompt,
            real_query=query_response
        )
        agent = AssistantAgent(system_prompt=critic_prompt)
        response = await agent.achat(prompts="请回复 YES 或不合理原因：")
        if 'YES' in response:
            return None
        else:
            return response

    def get_real_query(self) -> str:
        history = self.get_history()
        content = []
        for h in history:
            content.append(f'{h.type}: {h.content}')
        prompt = PromptFactory.render_prompt(self.prompt, chat_history='\n'.join(content))
        messages = [
            {'role': 'user', 'content': prompt}
        ]
        for i in range(5):
            response = self.chat(prompts=messages)
            messages.append({'role': 'assistant', 'content': response})
            # check response
            check_result = self._check_query_response(origin_prompt=prompt, query_response=response)
            if not check_result:
                return response
            else:
                messages.append({'role': 'user', 'content': f"你的回复不合理，原因是：{response}\n\n请重新回复"})

        return response

    async def aget_real_query(self) -> str:
        history = self.get_history()
        content = []
        for h in history:
            content.append(f'{h.type}: {h.content}')

        prompt = PromptFactory.render_prompt(self.prompt, chat_history='\n'.join(content))
        messages = [
            {'role': 'user', 'content': prompt}
        ]
        for i in range(5):
            response = await self.achat(prompts=messages)
            messages.append({'role': 'assistant', 'content': response})
            # check response
            check_result = await self._acheck_query_response(origin_prompt=prompt, query_response=response)
            if not check_result:
                return response
            else:
                messages.append({'role': 'user', 'content': f"你的回复不合理，原因是：{response}\n\n请重新回复"})

        return response
