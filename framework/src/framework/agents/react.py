import json
from contextlib import contextmanager
from numbers import Number
from typing import List, Any, Callable, Type, Optional, Dict, Generator, AsyncGenerator

from jinja2 import Template
from langchain.agents import AgentOutputParser
from langchain.agents.react.output_parser import ReActOutputParser as LangchainReActOutputParser
from langchain.agents.mrkl.output_parser import FINAL_ANSWER_ACTION
from langchain.agents.output_parsers import ReActSingleInputOutputParser, ReActJsonSingleInputOutputParser

from framework.tools.invalid import InvalidTool
from langchain_core.agents import AgentAction, AgentFinish
from langchain_core.messages import BaseMessage, SystemMessage, AIMessage, HumanMessage
from langchain_core.tools import BaseTool
from pydantic import BaseModel

from common import tools
from framework.models import BaseLLM
from framework.models.llm import LanguageModelInput
from framework.models.tokenizer import TokenizerFactory, Tokenizer
from .compressed_assistant import CompressedAssistantAgent
from .compress import contextual_compression
from ..output_parser.base import ToolBasedAgentOutputParser, ToolInputParser
from ..prompt import PromptFactory
from ..tools import prompt_tools
from ..utils.parse_utils import ParseUtils

DEFAULT_PROMPT = """{background}
尽你最大努力回答问题。你可以使用以下工具：
```
{{ tools }}
```
我会给你一些历史聊天记录和用户问题，也会包含一些历史步骤，你需要按照格式完成下一步。
格式如下：

Chat History: 历史聊天记录，该步骤由用户给出，你不需要回复此步骤
Question: 你必须回答的输入问题，该步骤由用户给出，你不需要回复此步骤
Thought: 你必须思考该怎么做，你需要从 Thought 步骤开始回复
Action: 要采取的行动，从 {{ tool_names }} 中任选一个工具使用，Action 中给出工具名称
Action Input: 调用工具的输入参数，以 json 字典形式提供的参数信息
Observation: 工具调用的结果
......（这种 Thought/Action/Action Input/Observation 最多可以重复 {{ repeat_times }} 次）
Thought: 我现在知道最终答案了
Final Answer: 原始输入问题的最终答案（需要根据实际问题给出），如果没有合适的工具使用，或者需要更多信息才能回复，则直接回复 Final Answer 即可。

你必须始终以 Thought: 开头，你需要按照 Thought，Action，Action Input 这三步给出下一步的思考、工具和工具输入信息。
不允许使用 markdown 代码块标记回复！如果回复中包含代码信息，则可以使用 markdown 代码块标记。
注意：你只能回复 Thought，Action，Action Input 或者 Final Answer 这四个步骤！禁止回复其他步骤！
如果当前问题已经解决，或者没有合适的工具解决，则按照 Final Answer 的方式给出最终答案或者告知解决问题需要哪些信息，Final Answer 只有在最终回复问题的时候才需要使用。
{{ answer_prompt }}
-------------------
开始！

已知当前时间：
{{ curr_time }}

Chat History: {{ chat_history }}

Question: {{ input }}

Thought:
"""

log = tools.get_logger()
THOUGHT = 'Thought:'
ACTION = 'Action:'
ACTION_INPUT = 'Action Input:'
OBSERVATION = 'Observation:'

langchain_parsers = [
    ReActSingleInputOutputParser(),
    LangchainReActOutputParser(),
    ReActJsonSingleInputOutputParser(),
]


class ReActOutputParser(ToolBasedAgentOutputParser):
    """参考：langchain 的 ReActSingleInputOutputParser，由于有很多国产模型等不能正确返回，这里需要尝试多种方式解析"""

    def _parse0(self, text: str) -> AgentAction | AgentFinish:
        if ACTION in text and ACTION_INPUT in text:
            texts = text.split(ACTION_INPUT)
            if ACTION not in texts[0]:
                raise ValueError('Tool call format error.')
            action_texts = texts[0].split(ACTION)
            if len(action_texts) > 1:
                action = action_texts[1].strip()
            else:
                action = action_texts[0].strip()
            if len(texts) > 1:
                action_input = texts[1]
            else:
                action_input = ''

            if OBSERVATION in action_input:
                action_input = action_input.split(OBSERVATION)[0]
            action_input = action_input.strip('"')
            action_input = action_input.strip()
            action_input = json.loads(ParseUtils.extract_json_from_text(action_input))
            return AgentAction(tool=action, tool_input=action_input, log=text)
        elif FINAL_ANSWER_ACTION in text:
            return AgentFinish(
                return_values={
                    "output": text.split(FINAL_ANSWER_ACTION)[-1].strip()
                },
                log=text
            )
        raise ValueError('Tool call format error.')

    def parse_action(self, text: str) -> AgentAction | AgentFinish:
        """由于 langchain 适配的都是模型能力较强的情况，这里就先用自己的解析器，解析失败了再用 langchain 的"""
        parse_error_messages = []
        try:
            return self._parse0(text)
        except Exception as e:
            parse_error_messages.append(str(e))

        for parser in langchain_parsers:
            try:
                response = parser.parse(text=text)
                if isinstance(response, AgentAction) and response.tool_input:
                    if not isinstance(response.tool_input, dict):
                        response.tool_input = json.loads(response.tool_input)
                    return response
            except Exception as e:
                parse_error_messages.append(str(e))

        error_reasons = [f"try #{i}: {r}" for i, r in enumerate(parse_error_messages)]
        error_reasons = '\n'.join(error_reasons)
        raise ValueError(f"Could not parse values from input text: '{text}'.\n"
                         f"try_fail_reasons:{error_reasons}")


class PydanticReActOutputParser(ReActOutputParser):
    output_cls: Type[BaseModel]

    def parse(self, text: str) -> AgentAction | AgentFinish:
        origin_result = super().parse(text=text)
        if isinstance(origin_result, AgentFinish):
            # 将最终结果解析为目标数据结构
            origin_result.return_values['output'] = (
                ParseUtils.extract_model_from_text(text=origin_result.return_values['output'], model=self.output_cls))
        return origin_result


@contextmanager
def _call_tool_context(
        agent_action: AgentAction,
        call_tools: List[BaseTool],
):
    tool: BaseTool | None = None
    for t in call_tools:
        if t.name.lower() == agent_action.tool.lower():
            tool = t
            break
    if not tool:
        tool = InvalidTool()
        agent_action.tool_input = {
            'requested_tool_name': agent_action.tool,
            'available_tool_names': [t.name for t in call_tools]
        }
    data = {"tool": tool}
    try:
        yield data
    finally:
        tool_result = data.get('result', 'Call Success')
        observer_result = None
        if isinstance(tool_result, str):
            observer_result = tool_result
        elif isinstance(tool_result, Number):
            observer_result = str(tool_result)
        elif isinstance(tool_result, BaseModel):
            observer_result = tool_result.model_dump_json()
        else:
            try:
                observer_result = json.dumps(tool_result, ensure_ascii=False)
            except:
                observer_result = str(tool_result)
        data['result'] = observer_result


def call_tool(
        agent_action: AgentAction,
        call_tools: List[BaseTool],
) -> Any:
    if isinstance(agent_action, AgentFinish):
        return agent_action
    with _call_tool_context(agent_action, call_tools) as data:
        data['result'] = data['tool'].run(tool_input=agent_action.tool_input)
    return data['result']


async def async_call_tool(
        agent_action: AgentAction,
        call_tools: List[BaseTool],
) -> Any:
    if isinstance(agent_action, AgentFinish):
        return agent_action
    with _call_tool_context(agent_action, call_tools) as data:
        data['result'] = await data['tool'].arun(tool_input=agent_action.tool_input)
    return data['result']


def _llm_chat(llm, inputs, stream: bool = False, logs_callback: Callable[[Any], Any] | None = None) -> str:
    if not stream:
        response = llm.complete(input=inputs)
        if logs_callback:
            logs_callback(response.content)
        result = response.content
        log.info(f"llm chat: inputs={inputs}, response={result}")
        return result

    response = llm.stream(input=inputs)
    content = []
    for resp in response:
        if resp.content:
            content.append(resp.content)
            if logs_callback:
                logs_callback(resp)
    result = ''.join(content)
    log.info(f"llm chat: inputs={inputs}, response={result}")
    return result


async def _async_llm_chat(llm, inputs, stream: bool = False, logs_callback: Callable[[Any], Any] | None = None) -> str:
    if not stream:
        response = await llm.acomplete(input=inputs)
        if logs_callback:
            logs_callback(response.content)
        result = response.content
        log.info(f"llm chat: inputs={inputs}, response={result}")
        return result

    response = llm.astream(input=inputs)
    content = []
    async for resp in response:
        if resp.content:
            content.append(resp.content)
            if logs_callback:
                logs_callback(resp)
    result = ''.join(content)
    log.info(f"llm chat: inputs={inputs}, response={result}")
    return result


def _prepare_context_history(llm, max_token_limit, compress_rate, history):
    if not history:
        history = []

    # 长任务做好压缩
    if len(history) > 5:
        last_his = history[-5:]
        history = tools.asyncio_run(lambda: contextual_compression(
            name='assistant',
            model_client=llm,
            user_query=None,
            history=history[:-5],
            input_token_limit=max_token_limit,
            compress_rate=compress_rate,
        ))[0]
        history.extend(last_his)
    return history


async def _async_prepare_context_history(llm, max_token_limit, compress_rate, history):
    if not history:
        history = []

    # 长任务做好压缩
    if len(history) > 5:
        last_his = history[-5:]
        history = (await contextual_compression(
            name='assistant',
            model_client=llm,
            user_query=None,
            history=history[:-5],
            input_token_limit=max_token_limit,
            compress_rate=compress_rate,
        ))[0]
        history.extend(last_his)
    return history


def _prepare_inputs(history, system_prompt, retry_message):
    inputs = [SystemMessage(content=system_prompt)]
    if len(history) > 0:
        for h in history:
            inputs.append(h)
    # retry message 单独作为消息传入, 避免直接放到上下文中, 大模型识别错误
    if retry_message:
        inputs.append(HumanMessage(content=retry_message))
    if not inputs:
        inputs.append(HumanMessage(content="请按照要求的格式回复（以Thought:开头）："))
    last_message = inputs[-1]
    if isinstance(last_message, HumanMessage) or isinstance(last_message, str):
        return inputs
    if isinstance(last_message, dict) and last_message.get("role", "").upper() == "USER":
        return inputs

    inputs.append(HumanMessage(content="请按照要求的格式回复（以Thought:开头）："))
    return inputs


def chat_and_get_result(
        llm: BaseLLM,
        call_tools: List[BaseTool],
        system_prompt: str,
        max_iterations: int,
        format_error_ok: bool = True,
        curr_iteration: int = 1,
        max_token_limit: int = 1024 * 8,
        compress_rate: float = 0.6,
        history: List[BaseMessage] | None = None,
        output_parser: AgentOutputParser = ReActOutputParser(),
        stream: bool = False,
        logs_callback: Callable[[Any], Any] | None = None,
        retry_message: str = None
) -> Any:
    if curr_iteration > max_iterations:
        return 'ReAct failed because of max iterations reached.'
    history = _prepare_context_history(llm, max_token_limit, compress_rate, history)
    inputs = _prepare_inputs(history, system_prompt, retry_message)
    ai_content = _llm_chat(llm, inputs)

    if OBSERVATION in ai_content:
        ai_content = ai_content.split(OBSERVATION)[0]
    history.append(AIMessage(content=ai_content))

    try:
        agent_action = output_parser.parse(text=ai_content)
        if isinstance(agent_action, AgentFinish):
            return agent_action.return_values['output']
    except Exception as e:
        # CASE: 无法解析, retry
        if logs_callback:
            logs_callback(f'parse tool call error. text={ai_content}')
        if format_error_ok and curr_iteration == max_iterations:
            # TODO 这里应该让大模型汇总给出最终结果, 而不是直接返回一个奇怪的 response
            log.debug(
                f"parse tool call error, return error response because of format error ok. [llm_response='{ai_content}']",
                exc_info=e)
            return ai_content
        log.info(f"parse tool call error, try again. [llm_response='{ai_content}']", exc_info=e)
        return chat_and_get_result(
            llm=llm,
            call_tools=call_tools,
            system_prompt=system_prompt,
            max_iterations=max_iterations,
            max_token_limit=max_token_limit,
            curr_iteration=curr_iteration + 1,
            format_error_ok=format_error_ok,
            history=history,
            stream=stream,
            output_parser=output_parser,
            logs_callback=logs_callback,
            retry_message="回复格式错误或无法解析，请按照要求重新回复"
        )
    # 工具调用不进行重试, 工具内部应该进行重试, 重试次数也应该由工具内部控制
    result = call_tool(agent_action=agent_action, call_tools=call_tools)
    curr_msg = f'{OBSERVATION} {result}'
    log.info(curr_msg)
    history.append(HumanMessage(content=curr_msg))
    return chat_and_get_result(
        llm=llm,
        call_tools=call_tools,
        system_prompt=system_prompt,
        max_iterations=max_iterations,
        max_token_limit=max_token_limit,
        curr_iteration=curr_iteration + 1,
        format_error_ok=format_error_ok,
        history=history,
        stream=stream,
        output_parser=output_parser,
        logs_callback=logs_callback,
    )


async def async_chat_and_get_result(
        llm: BaseLLM,
        call_tools: List[BaseTool],
        system_prompt: str,
        max_iterations: int,
        format_error_ok: bool = True,
        curr_iteration: int = 1,
        max_token_limit: int = 1024 * 8,
        compress_rate: float = 0.6,
        history: List[BaseMessage] | None = None,
        output_parser: AgentOutputParser = ReActOutputParser(),
        logs_callback: Callable[[Any], Any] | None = None,
        stream: bool = False,
        retry_message: str = None
) -> Any:
    if curr_iteration > max_iterations:
        return 'ReAct failed because of max iterations reached.'
    history = await _async_prepare_context_history(llm, max_token_limit, compress_rate, history)
    inputs = _prepare_inputs(history, system_prompt, retry_message)

    ai_content = await _async_llm_chat(llm, inputs)
    if OBSERVATION in ai_content:
        ai_content = ai_content.split(OBSERVATION)[0]
    history.append(AIMessage(content=ai_content))

    try:
        agent_action = output_parser.parse(text=ai_content)
        if isinstance(agent_action, AgentFinish):
            return agent_action.return_values['output']
    except Exception as e:
        # CASE: 无法解析, retry
        if logs_callback:
            logs_callback(f'parse tool call error. text={ai_content}')
        if format_error_ok and curr_iteration == max_iterations:
            # TODO 这里应该让大模型汇总给出最终结果, 而不是直接返回一个奇怪的 response
            log.debug(
                f"parse tool call error, return error response because of format error ok. [llm_response='{ai_content}']",
                exc_info=e)
            return ai_content
        log.info(f"parse tool call error, try again. [llm_response='{ai_content}']", exc_info=e)
        return await async_chat_and_get_result(
            llm=llm,
            call_tools=call_tools,
            system_prompt=system_prompt,
            max_iterations=max_iterations,
            max_token_limit=max_token_limit,
            curr_iteration=curr_iteration + 1,
            format_error_ok=format_error_ok,
            history=history,
            output_parser=output_parser,
            stream=stream,
            logs_callback=logs_callback,
            retry_message="回复格式错误或无法解析，请按照要求重新回复"
        )
    # 工具调用不进行重试, 工具内部应该进行重试, 重试次数也应该由工具内部控制
    result = await async_call_tool(agent_action=agent_action, call_tools=call_tools)
    curr_msg = f'{OBSERVATION} {result}'
    log.info(curr_msg)
    history.append(HumanMessage(content=curr_msg))
    return await async_chat_and_get_result(
        llm=llm,
        call_tools=call_tools,
        system_prompt=system_prompt,
        max_iterations=max_iterations,
        max_token_limit=max_token_limit,
        curr_iteration=curr_iteration + 1,
        format_error_ok=format_error_ok,
        history=history,
        stream=stream,
        output_parser=output_parser,
        logs_callback=logs_callback,
    )


class _ReActSync(CompressedAssistantAgent):
    def __init__(
            self,
            tools: List[BaseTool],
            system_prompt_background: str | Template | None = None,
            system_prompt: str | Template | None = None,
            answer_prompt: str | Template | None = None,
            max_token_limit: int = 1024 * 8,
            compress_rate: float = 0.6,
            tokenizer: Tokenizer = TokenizerFactory.get_tokenizer(),
            max_iterations: int = 3,
            format_error_ok: bool = True,  # 格式错误后是否直接当做答案，取值 False 的时候会将错误格式报错
            special_tool_parsers: Optional[Dict[str, ToolInputParser]] = None,
            **kwargs
    ) -> None:
        if not system_prompt:
            system_prompt = DEFAULT_PROMPT
        if not answer_prompt:
            answer_prompt = ''
        self.answer_prompt = answer_prompt
        super().__init__(
            max_token_limit=max_token_limit,
            compress_rate=compress_rate,
            tokenizer=tokenizer,
            system_prompt=system_prompt,
            **kwargs
        )
        if not system_prompt_background:
            system_prompt_background = ''
        self.system_prompt_background = system_prompt_background
        self.tools = tools
        self.max_iterations = max_iterations
        self.format_error_ok = format_error_ok
        self.special_tool_parsers = special_tool_parsers

    def _curr_chat_message(self, prompts: LanguageModelInput | None = None):
        curr_input = ''
        if prompts is None or len(prompts) == 0:
            return {'input': curr_input}
        history = self._convert_message(prompts)
        if not history:
            return {'input': curr_input}
        curr_input = history[-1].content
        history = history[0:-1]
        return {
            'input': curr_input,
            'chat_history': '\n'.join([f'{msg.type}: {msg.content}' for msg in history]),
        }

    def _get_system_prompt(self, prompts: LanguageModelInput | None = None, ) -> str:
        if prompts is None:
            return None
        rendered_tools = prompt_tools.gen_tools_desc(self.tools)
        prompts = self._curr_chat_message(prompts)
        prompts['background'] = PromptFactory.render_prompt(self.system_prompt_background)
        prompts['answer_prompt'] = PromptFactory.render_prompt(self.answer_prompt)
        prompts['tools'] = rendered_tools
        prompts['tool_names'] = ' / '.join([tool.name for tool in self.tools])
        prompts['repeat_times'] = self.max_iterations

        result = PromptFactory.render_prompt(self.system_prompt, **prompts)
        return result

    def chat(
            self,
            prompts: LanguageModelInput | None = None,
    ) -> str:
        if prompts is None:
            return 'How can I help you today?'
        result = chat_and_get_result(
            llm=self.llm,
            call_tools=self.tools,
            system_prompt=self._get_system_prompt(prompts=prompts),
            max_iterations=self.max_iterations,
            max_token_limit=self.max_token_limit,
            curr_iteration=1,
            format_error_ok=self.format_error_ok,
            logs_callback=self.log_and_callback,
            output_parser=ReActOutputParser(
                special_tool_parsers=self.special_tool_parsers)
        )
        if isinstance(result, BaseModel):
            result = result.model_dump_json()
        elif not isinstance(result, str):
            result = str(result)
        log.info(f"ReAct final answer:\n{result}")
        return result

    async def achat(
            self,
            prompts: LanguageModelInput | None = None
    ) -> str:
        raise NotImplementedError()

    def chat_with_pydantic(self, output_cls: Type[BaseModel], prompts: LanguageModelInput) -> BaseModel:
        result = chat_and_get_result(
            llm=self.llm,
            call_tools=self.tools,
            system_prompt=self._get_system_prompt(prompts=prompts),
            max_iterations=self.max_iterations,
            max_token_limit=self.max_token_limit,
            curr_iteration=1,
            # 强制返回正确结构, 否则报错
            format_error_ok=False,
            logs_callback=self.log_and_callback,
            output_parser=PydanticReActOutputParser(special_tool_parsers=self.special_tool_parsers,
                                                    output_cls=output_cls),
        )
        log.info(f"ReAct final answer:\n{result.model_dump_json()}")
        return result

    def stream_chat(
            self,
            prompts: LanguageModelInput | None = None
    ) -> Generator[str, Any, Any]:
        raise NotImplementedError()

    async def astream_chat(
            self,
            prompts: LanguageModelInput | None = None
    ) -> AsyncGenerator[str, Any]:
        raise NotImplementedError()


class _ReActAsync(CompressedAssistantAgent):
    def __init__(
            self,
            tools: List[BaseTool],
            system_prompt_background: str | Template | None = None,
            system_prompt: str | Template | None = None,
            answer_prompt: str | Template | None = None,
            max_token_limit: int = 1024 * 8,
            compress_rate: float = 0.6,
            tokenizer: Tokenizer = TokenizerFactory.get_tokenizer(),
            max_iterations: int = 3,
            format_error_ok: bool = True,  # 格式错误后是否直接当做答案，取值 False 的时候会将错误格式报错
            special_tool_parsers: Optional[Dict[str, ToolInputParser]] = None,
            **kwargs
    ) -> None:
        if not system_prompt:
            system_prompt = DEFAULT_PROMPT
        if not answer_prompt:
            answer_prompt = ''
        self.answer_prompt = answer_prompt
        super().__init__(
            max_token_limit=max_token_limit,
            compress_rate=compress_rate,
            tokenizer=tokenizer,
            system_prompt=system_prompt,
            **kwargs
        )
        if not system_prompt_background:
            system_prompt_background = ''
        self.system_prompt_background = system_prompt_background
        self.tools = tools
        self.max_iterations = max_iterations
        self.format_error_ok = format_error_ok
        self.special_tool_parsers = special_tool_parsers

    def _curr_chat_message(self, prompts: LanguageModelInput | None = None):
        curr_input = ''
        if prompts is None or len(prompts) == 0:
            return {'input': curr_input}
        history = self._convert_message(prompts)
        if not history:
            return {'input': curr_input}
        curr_input = history[-1].content
        history = history[0:-1]
        return {
            'input': curr_input,
            'chat_history': '\n'.join([f'{msg.type}: {msg.content}' for msg in history]),
        }

    def _get_system_prompt(self, prompts: LanguageModelInput | None = None, ) -> str:
        if prompts is None:
            return None
        rendered_tools = prompt_tools.gen_tools_desc(self.tools)
        prompts = self._curr_chat_message(prompts)
        prompts['background'] = PromptFactory.render_prompt(self.system_prompt_background)
        prompts['answer_prompt'] = PromptFactory.render_prompt(self.answer_prompt)
        prompts['tools'] = rendered_tools
        prompts['tool_names'] = ' / '.join([tool.name for tool in self.tools])
        prompts['repeat_times'] = self.max_iterations

        result = PromptFactory.render_prompt(self.system_prompt, **prompts)
        return result

    async def achat(
            self,
            prompts: LanguageModelInput | None = None,
    ) -> str:
        if prompts is None:
            return 'How can I help you today?'
        result = await async_chat_and_get_result(
            llm=self.llm,
            call_tools=self.tools,
            system_prompt=self._get_system_prompt(prompts=prompts),
            max_iterations=self.max_iterations,
            max_token_limit=self.max_token_limit,
            curr_iteration=1,
            format_error_ok=self.format_error_ok,
            logs_callback=self.log_and_callback,
            output_parser=ReActOutputParser(
                special_tool_parsers=self.special_tool_parsers)
        )
        if isinstance(result, BaseModel):
            result = result.model_dump_json()
        elif not isinstance(result, str):
            result = str(result)
        log.info(f"ReAct final answer:\n{result}")
        return result

    def chat(
            self,
            prompts: LanguageModelInput | None = None
    ) -> str:
        raise NotImplementedError()

    async def achat_with_pydantic(self, output_cls: Type[BaseModel], prompts: LanguageModelInput) -> BaseModel:
        result = await async_chat_and_get_result(
            llm=self.llm,
            call_tools=self.tools,
            system_prompt=self._get_system_prompt(prompts=prompts),
            max_iterations=self.max_iterations,
            max_token_limit=self.max_token_limit,
            curr_iteration=1,
            # 强制返回正确结构, 否则报错
            format_error_ok=False,
            logs_callback=self.log_and_callback,
            output_parser=PydanticReActOutputParser(special_tool_parsers=self.special_tool_parsers,
                                                    output_cls=output_cls),
        )
        log.info(f"ReAct final answer:\n{result.model_dump_json()}")
        return result

    def stream_chat(
            self,
            prompts: LanguageModelInput | None = None
    ) -> Generator[str, Any, Any]:
        raise NotImplementedError()

    async def astream_chat(
            self,
            prompts: LanguageModelInput | None = None
    ) -> AsyncGenerator[str, Any]:
        raise NotImplementedError()


class ReActAgent(CompressedAssistantAgent):
    def __init__(
            self,
            tools: List[BaseTool],
            system_prompt_background: str | Template | None = None,
            system_prompt: str | Template | None = None,
            answer_prompt: str | Template | None = None,
            max_token_limit: int = 1024 * 8,
            compress_rate: float = 0.6,
            tokenizer: Tokenizer = TokenizerFactory.get_tokenizer(),
            max_iterations: int = 3,
            format_error_ok: bool = True,  # 格式错误后是否直接当做答案，取值 False 的时候会将错误格式报错
            special_tool_parsers: Optional[Dict[str, ToolInputParser]] = None,
            **kwargs
    ) -> None:
        kwargs = kwargs | {
            "tools": tools,
            "system_prompt_background": system_prompt_background,
            "system_prompt": system_prompt,
            "answer_prompt": answer_prompt,
            "max_token_limit": max_token_limit,
            "compress_rate": compress_rate,
            "tokenizer": tokenizer,
            "max_iterations": max_iterations,
            "format_error_ok": format_error_ok,
            "special_tool_parsers": special_tool_parsers,
        }
        self.agent = _ReActSync(**kwargs)
        self.async_agent = _ReActAsync(**kwargs)

    def chat(
            self,
            prompts: LanguageModelInput | None = None,
    ) -> str:
        return self.agent.chat(prompts=prompts)

    async def achat(
            self,
            prompts: LanguageModelInput | None = None
    ) -> str:
        return await self.async_agent.achat(prompts=prompts)

    def chat_with_pydantic(self, output_cls: Type[BaseModel], prompts: LanguageModelInput) -> BaseModel:
        return self.agent.chat_with_pydantic(output_cls=output_cls, prompts=prompts)

    async def achat_with_pydantic(self, output_cls: Type[BaseModel], prompts: LanguageModelInput) -> BaseModel:
        return await self.async_agent.achat_with_pydantic(output_cls=output_cls, prompts=prompts)

    def stream_chat(
            self,
            prompts: LanguageModelInput | None = None
    ) -> Generator[str, Any, Any]:
        result = self.agent.chat(prompts=prompts)
        yield result

    async def astream_chat(
            self,
            prompts: LanguageModelInput | None = None
    ) -> AsyncGenerator[str, Any]:
        result = await self.async_agent.achat(prompts=prompts)
        yield result
