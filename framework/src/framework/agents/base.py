"""
langchain 的 agent 类型参考：
langchain.agents.agent
"""

from abc import ABC
from typing import Optional, Callable, Any, List, Dict, AsyncGenerator, Generator

from jinja2 import Template

from common import tools
from langchain_core.messages import (
    BaseMessage, convert_to_messages, HumanMessage
)

from framework.models import BaseLLM
from framework.models.llm import LanguageModelInput
from framework.models.models_factory import ModelsFactory
from framework.prompt import PromptFactory

log = tools.get_logger()
DEFAULT_SYSTEM_PROMPT = 'You are a helpful assistant.'


class BaseAgent(ABC):
    """所有 agent 的起源"""

    def __init__(
            self,
            llm: Optional[BaseLLM] = None,
            system_prompt: str | Template | None = None,
            logs_callback: Callable[[Any], Any] | None = None,
            **kwargs,
    ) -> None:
        if not system_prompt:
            system_prompt = DEFAULT_SYSTEM_PROMPT
        if isinstance(system_prompt, str):
            system_prompt = Template(system_prompt)
        if not llm:
            llm = ModelsFactory.get_llm()
        self.llm = llm
        self.system_prompt = system_prompt
        self._logs_callback = logs_callback

    def log_and_callback(self, content: Any):
        try:
            log.info(content)
            if self._logs_callback is not None:
                self._logs_callback(content)
        except:
            pass

    def _concat_system_prompt(
            self,
            prompts: List[BaseMessage] | None = None,
    ) -> LanguageModelInput:
        if not prompts:
            prompts = []
        chat_prompts = [
            {'role': 'system', 'content': PromptFactory.render_prompt(self.system_prompt)},
            *prompts,
        ]
        return chat_prompts

    def chat(
            self,
            prompts: LanguageModelInput | None = None,
    ) -> str:
        prompts = self._convert_message(message=prompts)
        return self.llm.complete(input=self._concat_system_prompt(prompts)).content

    async def achat(
            self,
            prompts: LanguageModelInput | None = None
    ) -> str:
        prompts = self._convert_message(message=prompts)
        result = await self.llm.acomplete(input=self._concat_system_prompt(prompts))
        return result.content

    def stream_chat(
            self,
            prompts: LanguageModelInput | None = None
    ) -> Generator[str, Any, Any]:
        prompts = self._convert_message(message=prompts)
        for rel in self.llm.stream(input=self._concat_system_prompt(prompts)):
            if not rel.content is None:
                yield rel.content

    async def astream_chat(
            self,
            prompts: LanguageModelInput | None = None
    ) -> AsyncGenerator[str, Any]:
        prompts = self._convert_message(message=prompts)
        stream_resp = self.llm.astream(input=self._concat_system_prompt(prompts))
        async for rel in stream_resp:
            if not rel.content is None:
                yield rel.content

    def _convert_message(self, message: LanguageModelInput) -> List[BaseMessage]:
        if isinstance(message, str):
            return [HumanMessage(content=message)]
        if isinstance(message, Dict):
            if 'role' not in message:
                message['role'] = 'user'
            return convert_to_messages([message])
        if isinstance(message, BaseMessage):
            return [message]
        result = []
        if isinstance(message, List):
            for msg in message:
                result.extend(self._convert_message(message=msg))
        return result
