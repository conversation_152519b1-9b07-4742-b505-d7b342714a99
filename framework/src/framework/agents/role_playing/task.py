import inspect
from typing import Type, Literal, Callable, get_type_hints, Optional, Any, Union

from pydantic import BaseModel, Field

from framework.utils.pydantic_utils import gen_pydantic_json_desc


class TaskInput(BaseModel):
    """任务输入, 字段根据任务自行定义"""

    @classmethod
    def for_llm_schema_desc(cls) -> str:
        """针对大模型的输出描述"""
        return gen_pydantic_json_desc(pydantic_model=cls)


class EmptyTaskInput(TaskInput):
    """无需输入参数"""


class TaskResult(BaseModel):
    """任务执行结果, 字段根据任务自行定义"""

    def for_llm_output(self) -> str:
        """针对大模型的输出, 包含了输出内容以及必要的输出解释"""
        return self.model_dump_json(exclude_none=True)


class Task(BaseModel):
    """
    任务的描述
    :arg
        name: 任务名
        description: 任务描述
        input_schema: 任务输入参数
    """
    name: str = Field(..., description='任务名')
    description: str = Field(..., description='任务描述')
    input_schema: Type[TaskInput] = Field(..., description='任务输入参数')
    output_schema: Type[TaskResult] = Field(..., description='任务输出')


class TaskCallbackInfo:
    """任务回调信息"""

    def __init__(self,
                 executor: str,
                 step: Literal['start', 'end'],
                 task: Task,
                 task_input: TaskInput,
                 task_result: TaskResult = None,
                 error: Exception = None):
        """
        :param executor: 任务执行者, 通常为 role/agent 的名称
        :param step: 步骤, start 表示开始, end 表示结束
        :param task: 任务, 任务描述
        :param task_input: 任务输入
        :param task_result: 任务输出
        :param error: 执行出错
        """
        self.executor = executor
        self.step = step
        self.task = task
        self.input = task_input
        self.result = task_result
        self.error = error

    def __str__(self):
        return str(self.__dict__)


class TaskConfig(BaseModel):
    task: Task
    task_arg_name: Optional[str] = None


def task(task_def: Union[Task, str] = None,
         task_desc: Optional[str] = None) -> Callable:
    """Decorator used to mark functions as agent tasks.

    Args:
        task_def (Task | str): Task definition. If a string is provided, it will be used as the task name.
        task_desc (Optional[str]): Task description. Defaults to None.
    Example:
        @task('task_name', 'task_description')
        def task_func(task_input: TaskInput) -> TaskResult:
            ...

        @task(Task(name='task_name', description='task_description', input_schema=TaskInput, output_schema=TaskResult))
        def task_func(task_input: TaskInput) -> TaskResult:
           ...
    """

    def decorator(func: Callable) -> Callable:
        sig = inspect.signature(func)
        # 支持任务无参数
        task_arg_type = EmptyTaskInput
        task_arg_name = None
        for name, t in sig.parameters.items():
            if name in ("self", "cls"):
                continue
            typ = t.annotation
            if issubclass(typ, TaskInput):
                task_arg_type = typ
                task_arg_name = name
                break
        if isinstance(task_def, Task):
            final_task = task_def
        else:
            type_hints = get_type_hints(func)
            return_hint = type_hints.get("return")
            if not issubclass(return_hint, TaskResult):
                raise ValueError(f"Task function {func.__name__} must have a return type of TaskResult")
            task_description = task_desc or func.__doc__
            task_name = str(task_def) if task_def else func.__name__
            final_task = Task(name=task_name, description=task_description, input_schema=task_arg_type,
                              output_schema=return_hint)
        func.__task_config = TaskConfig(task=final_task, task_arg_name=task_arg_name)
        return func

    return decorator
