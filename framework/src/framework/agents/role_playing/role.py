import asyncio
import inspect
import json
from abc import ABC, abstractmethod
from typing import List, Dict, Callable, Optional, Type, Any, Union

from langchain_core.agents import AgentAction, AgentFinish
from langchain_core.messages import HumanMessage
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from common import tools
from framework.agents.react import ReActAgent, ReActOutputParser

from common import tools as utils
from framework.agents.role_playing.task import Task, TaskInput, TaskResult, TaskCallbackInfo, TaskConfig, EmptyTaskInput
from framework.llm_exceptions import LLMResultValidationException
from framework.output_parser.base import ToolInputParser
from framework.utils import pydantic_utils
from framework.utils.parse_utils import ParseUtils
from framework.utils.pydantic_utils import gen_pydantic_json_desc
from framework.models import BaseLLM
from common.tools.text_tools import indent_multi_line

log = utils.get_logger()


class Role(BaseModel):
    """角色"""
    name: str = Field(..., description='角色名称')
    job_desc: str = Field(..., description='职责描述')

    def to_self_prompt(self) -> str:
        return f'你是 {self.name}. 职责: {self.job_desc}'


class BaseRoleTaskAgent(ABC):
    """
    基于 role-task 框架的 agent
    """

    def __init__(self,
                 role: Role,
                 tasks: List[Task] | List[str] = None,
                 system_prompt: str = None,
                 task_callback: Callable[[TaskCallbackInfo], None] = None,
                 llm: Optional[BaseLLM] = None,
                 **kwargs):
        """
        :param role: 角色
        :param tasks: 指定的任务, 应该是角色任务的子集, 如果为空, 则使用角色的任务
        :param system_prompt: 提示词, 若为空则根据角色描述生成
        :param task_callback: 任务回调, 用于记录任务的执行过程
        :param kwargs: 其他参数
        """
        self.role = role
        self.task_callback = task_callback
        self.llm = llm
        if not system_prompt:
            system_prompt = self.role.to_self_prompt()
        self.system_prompt = system_prompt
        self._init_tasks(tasks)

    def _init_tasks(self, tasks: List[Task] | List[str] = None):
        """初始化任务"""
        # 从方法注解中加载任务
        method_tasks = {}
        all_methods = inspect.getmembers(self, predicate=inspect.ismethod)
        for name, method in all_methods:
            if hasattr(method, "__task_config"):
                task_config: TaskConfig = getattr(method, "__task_config")
                method_tasks[task_config.task.name] = (task_config, method)

        if not tasks:
            agent_task_names = set(method_tasks.keys())
            final_tasks = {}
        else:
            agent_task_names = {t.name if isinstance(t, Task) else t for t in tasks}
            final_tasks: Dict[str, Task] = {t.name: t for t in tasks if isinstance(t, Task)}
        task_func: Dict[str, Callable] = {}
        for task_name, (task_config, method) in method_tasks.items():
            if task_name not in agent_task_names:
                continue
            task_func[task_name] = self._gen_task_func(task=task_config.task, task_config=task_config, method=method)
            final_tasks[task_name] = task_config.task
        self.tasks: Dict[str, Task] = final_tasks
        self._task_func: Dict[str, Callable] = task_func

    def _gen_task_func(self, task: Task, task_config: TaskConfig, method: Callable):
        async def _async_wrapper_task_func(task: Task, task_arg: TaskInput):
            kwargs: Dict[str, Any] = {}
            if task_config.task_arg_name:
                kwargs[task_config.task_arg_name] = task_arg
            return await method(**kwargs)

        def _sync_wrapper_task_func(task: Task, task_arg: TaskInput):
            kwargs: Dict[str, Any] = {}
            if task_config.task_arg_name:
                kwargs[task_config.task_arg_name] = task_arg
            return method(**kwargs)

        return _async_wrapper_task_func if asyncio.iscoroutinefunction(method) else _sync_wrapper_task_func

    async def launch_task(self, task: Task | str, task_arg: TaskInput) -> TaskResult:
        """异步执行任务"""
        if isinstance(task, str):
            task = self.tasks.get(task)
            if task is None:
                log.error(f"failed to execute task, invalid task. "
                          f"[role={self.role.name}, task='{task}', arg='{task_arg}, available_tasks={self.tasks.keys()}']")
                raise ValueError(f'任务 {task} 不存在')
        elif task.name not in self.tasks:
            log.error(f"failed to execute task, invalid task. "
                      f"[role={self.role.name}, task='{task.name}', arg='{task_arg}, available_tasks={self.tasks.keys()}']")
            raise ValueError(f'任务 {task.name} 不存在')
        log.info(f"start to execute task. [role={self.role.name}, task='{task.name}', arg='{task_arg}']")
        self._task_callback(TaskCallbackInfo(executor=self.role.name, step='start', task=task, task_input=task_arg))
        try:
            task_func = self._task_func.get(task.name, self._execute_task)
            result: TaskResult = await task_func(task=task, task_arg=task_arg)
        except Exception as e:
            log.warn(
                f"failed to execute task. [role={self.role.name}, task='{task.name}', arg='{task_arg}']", exc_info=e)
            self._task_callback(
                TaskCallbackInfo(executor=self.role.name, step='end', task=task, task_input=task_arg, error=e))
            raise e
        log.info(
            f"task executed successfully. [role={self.role.name}, task='{task.name}', arg='{task_arg}', result='{result}']")
        self._task_callback(
            TaskCallbackInfo(executor=self.role.name, step='end', task=task, task_input=task_arg, task_result=result))
        return result

    async def _execute_task(self, task: Task, task_arg: TaskInput) -> TaskResult:
        """执行任务"""
        pass

    def _task_callback(self, callback_info: TaskCallbackInfo):
        if self.task_callback is not None:
            self.task_callback(callback_info)


class InvalidRoleError(Exception):
    """无效的角色"""

    def __init__(self, invalid_role: str, available_roles: List[str]):
        self.role = invalid_role
        self.available_roles = available_roles


class InvalidTaskError(Exception):
    """无效的任务"""

    def __init__(self, invalid_task: Optional[str], available_tasks: List[str]):
        self.invalid_task = invalid_task
        self.available_tasks = available_tasks


class BaseRoleTaskTool(BaseTool, ABC):
    agents: List[BaseRoleTaskAgent] = Field(..., description="角色代理")


class DelegateWorkArgs(BaseModel):
    task: str = Field(..., description="任务名")
    role: str = Field(..., description="委托的角色")
    task_arg: Optional[Any] = Field(default=None, description="任务参数, JSON 对象, 格式跟具体任务强相关")


class DelegateWorkTool(BaseRoleTaskTool):
    """委托工作给其他角色的工具"""
    name: str = "DelegateWorkTool"
    args_schema: Type[BaseModel] = DelegateWorkArgs
    description: str

    @classmethod
    def from_role_agents(cls, agents: List[BaseRoleTaskAgent]):
        if not agents:
            raise ValueError("miss agents")
        role_names = ",".join([agent.role.name for agent in agents])
        role_tasks_description = "\n\n".join(
            [DelegateWorkTool._gen_role_desc(agent.role, agent.tasks.values()) for agent in agents])
        return cls(agents=agents, description=f"将特定任务委托给以下角色之一: {role_names}\n"
                                              f"角色以及角色能够完成的任务如下:\n{role_tasks_description}"
                                              f"他们对任务具体内容一无所知，因此需要你分享你所知道的一切相关信息, 请不要进行信息引用，给出完整描述"
                                              f"该工具的输入包括委托的角色、你需要他们执行的任务、以及执行任务的参数等"
                   )

    @classmethod
    def _gen_role_desc(cls, role: Role, tasks) -> str:
        task_index = 1
        task_num = len(tasks)
        tasks_desc = ""
        for t in tasks:
            tasks_desc += cls._gen_task_desc(task_index, t)
            if task_index < task_num - 1:
                tasks_desc += "\n"
            task_index += 1
        # 缩进任务描述
        tasks_desc = indent_multi_line(2, tasks_desc)
        return (f"角色名: {role.name}\n"
                f"职责描述: {role.job_desc}\n"
                f"角色能够完成的任务:\n{tasks_desc}")

    @classmethod
    def _gen_delegate_tool_args_desc(cls) -> str:
        return '{"role":"你要委托的角色名", "task": "你指定的任务名", "task_arg": "任务参数, json 字符串, 格式跟具体任务强相关, 请结合角色的任务描述给出"}'

    @classmethod
    def _gen_task_desc(cls, index: int, task: Task) -> str:
        return (f"({index}) {task.name}\n"
                f"任务描述: {task.description}\n"
                f"任务参数: {task.input_schema.for_llm_schema_desc()}")

    def get_tool_input_parser(self) -> ToolInputParser:
        return RoleTaskInputParser(
            task_arg_define={agent.role.name: {t.name: t.input_schema for t in agent.tasks.values()}
                             for agent in self.agents})

    def _run(self, role: str, task: str, task_arg: Any = None, *args: Any, **kwargs: Any) -> Any:
        return tools.asyncio_run(lambda: self._arun(role=role, task=task, task_arg=task_arg, *args, **kwargs))

    async def _arun(self, role: str, task: str, task_arg: Any = None, *args: Any, **kwargs: Any) -> Any:
        """
        调用 agent 执行任务
        参数解析错误(ValidationError, InvalidTaskError, InvalidRoleError)由外部处理, 可进行重试等
        具体执行错误由 agent 内部处理, 具体逻辑由 agent 实现
        """
        for available_agent in self.agents:
            if available_agent.role.name == role:
                target_agent = available_agent
                break
        else:
            raise InvalidRoleError(role, [agent.role.name for agent in self.agents])
        if task is None or task not in target_agent.tasks:
            raise InvalidTaskError(task, [t for t in target_agent.tasks.keys()])
        target_task = target_agent.tasks.get(task)
        if not issubclass(target_task.input_schema, EmptyTaskInput):
            if isinstance(task_arg, target_task.input_schema):
                task_input = task_arg
            elif isinstance(task_arg, str):
                task_input: TaskInput = target_task.input_schema.model_validate_json(task_arg)
            else:
                task_input: TaskInput = target_task.input_schema.model_validate(task_arg)
        else:
            task_input = EmptyTaskInput()
        task_result = await target_agent.launch_task(task=target_task, task_arg=task_input)
        # 返回大模型可理解的结果
        return task_result.for_llm_output()


class RoleTaskInputParser(ToolInputParser):
    task_arg_define: Dict[str, Dict[str, Type[TaskInput]]]

    def parse_tool_input(self, tool_input: Union[str, dict]) -> Union[str, dict]:
        if not tool_input:
            raise LLMResultValidationException("miss tool input")
        if isinstance(tool_input, str):
            tool_input = ParseUtils.extract_model_from_text(tool_input, DelegateWorkArgs)
        elif isinstance(tool_input, dict):
            tool_input = DelegateWorkArgs.model_validate(tool_input)
        elif isinstance(tool_input, DelegateWorkArgs):
            pass
        else:
            raise LLMResultValidationException("invalid tool input")
        role = self.task_arg_define.get(tool_input.role)
        if not role:
            raise LLMResultValidationException(f"invalid role: {tool_input.role}")
        task_input_schema = role.get(tool_input.task)
        if not task_input_schema:
            raise LLMResultValidationException(f"invalid task: {tool_input.task}")
        if issubclass(task_input_schema, EmptyTaskInput):
            # 无需参数
            tool_input.task_arg = None
        else:
            task_arg = tool_input.task_arg
            if not task_arg:
                raise LLMResultValidationException("miss task_arg")
            if isinstance(task_arg, str):
                tool_input.task_arg = task_input_schema.model_validate_json(task_arg)
            else:
                tool_input.task_arg = task_input_schema.model_validate(task_arg)
        return tool_input.model_dump(exclude_none=True)
