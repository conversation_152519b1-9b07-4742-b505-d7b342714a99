import json
from typing import Type, Dict, Any

from pydantic import BaseModel

from common import tools


def _gen_obj_schema_desc(obj_type: str, obj_schema: Dict[str, Any]) -> str:
    # 通过 json 序列化 copy 新的对象, 避免修改原来的对象
    obj_schema = json.loads(json.dumps(obj_schema, ensure_ascii=False))
    # 删掉 title & 所有的自定义描述字段都需要替换掉换行符
    desc_fields = {'description', 'examples'}
    dict_values = [obj_schema]
    while len(dict_values) > 0:
        dict_value = dict_values.pop()
        if 'title' in dict_value and type(dict_value['title']) == str:
            del dict_value['title']
        if '$defs' in dict_value:
            # 删除引用结构, 仅保留对象描述内容
            del dict_value['$defs']
        for k, v in dict_value.items():
            if k in desc_fields and type(v) == str:
                dict_value[k] = v.replace("\n", ' ')
                continue
            if type(v) == dict:
                dict_values.append(v)

    return (f"{obj_type} 类型, JSON SCHEMA 定义如下:\n"
            f"{json.dumps(obj_schema, ensure_ascii=False)}")


def gen_pydantic_json_desc(pydantic_model: Type[BaseModel],
                           model_reference_flag: str = "@"
                           ) -> str:
    """
    生成 pydantic 对象的 json 描述
    :return:
    以下使用 JSON SCHEMA 描述数据结构定义, 注意是 JSON SCHEMA 描述, 不是 JSON 字符串格式
    @User 类型, JSON SCHEMA 定义如下:
    {...}
    依赖的其他数据结构:
      {...}
    """
    model_schema = pydantic_model.model_json_schema(mode='validation', ref_template=model_reference_flag + "{model}")
    if model_schema.get('$defs') is None:
        # CASE: 简单数据结构, 没有依赖其他复杂类型
        root_model_ref = model_reference_flag + pydantic_model.__name__
        root_model = model_schema
        dependent_models_desc = ''
    else:
        # CASE: 复杂数据结构, 有依赖其他复杂类型
        all_models: Dict[str, Dict] = {model_reference_flag + k: v for k, v in model_schema.get('$defs').items()}
        # 如果其他类型依赖了目标数据结构, 则会使用 $ref 进行引用
        root_model_ref: str = model_schema.get('$ref')
        if root_model_ref is None:
            root_model_ref = model_reference_flag + pydantic_model.__name__
            root_model = model_schema
        else:
            root_model = all_models.get(root_model_ref)
        dependent_models_desc = '依赖的其他数据结构:\n'
        for model_name, model in all_models.items():
            if model_name == root_model_ref:
                continue
            dependent_models_desc += tools.text_tools.indent_multi_line(2, _gen_obj_schema_desc(model_name, model))
            dependent_models_desc += '\n'
    root_model_desc = _gen_obj_schema_desc(root_model_ref, root_model)
    return (f"以下使用 JSON SCHEMA 描述数据结构定义, 注意是 JSON SCHEMA 描述, 不是 JSON 字符串格式\n"
            f"{root_model_desc}\n"
            f"{dependent_models_desc}")
