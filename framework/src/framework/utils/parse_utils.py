import re
from re import <PERSON><PERSON>
from typing import Type

from pydantic import BaseModel

from framework.llm_exceptions import LLMResultValidationException


class ParseUtils:
    _json_md_pattern: Pattern = re.compile(r"^.*?`{3}(?:json)?\n?(.*?)`{3}.*?$", re.DOTALL)

    @classmethod
    def extract_json_from_text(cls, text: str) -> str:
        text = text.strip()
        if text.startswith("{") and text.endswith("}"):
            return text
        match = cls._json_md_pattern.search(text)
        if match:
            return match.group(1).strip()
        else:
            raise LLMResultValidationException("Can not parse JSON string")

    @classmethod
    def extract_model_from_text(cls, text: str, model: Type[BaseModel]) -> BaseModel:
        extracted_json = cls.extract_json_from_text(text)
        try:
            return model.model_validate_json(extracted_json)
        except Exception as e:
            raise LLMResultValidationException(f"JSON object content error") from e
