from typing import runtime_checkable, Callable, Any, Literal
from typing_extensions import Protocol
from inspect import isawaitable, iscoroutinefunction

from common import tools
from .agents.schema import (
    Conversation,
    Role,
    ViewMessage,
    ViewMessageStatus
)

log = tools.get_logger()


@runtime_checkable
class Bot(Protocol):
    """机器人定义，使用机器人与人沟通"""

    def __init__(
            self,
            name: str,
            callback: Callable[[Any], Any] = None,
            **kwargs
    ):
        """
        初始化机器人

        Args:
            name: 机器人名称
            callback: 中间过程回调，一般用于打日志或者前端显示
        """
        self.name = name
        self.callback = callback
        self.kwargs = kwargs

    async def display_callback(self, content: Any):
        if self.callback:
            try:
                # 判断是否是需要等待的协程
                if isawaitable(content):
                    content = await content
                if iscoroutinefunction(self.callback):
                    await self.callback(content)
                else:
                    self.callback(content)
            except BaseException as e:
                log.warning('display callback error.', e)
        else:
            log.warning("bot callback is None")

    async def send_message(self, message_type: str, content: str | Any = ''):
        await self.display_callback(ViewMessage(
            type=message_type,
            content=content
        ))

    async def send_status(
            self,
            status_desc: str,
            status: Literal['PROCESSING', 'ERROR', 'WARNING', 'SUCCESS'] = 'PROCESSING',
            keeping: bool = False
    ):
        await self.display_callback(
            ViewMessageStatus(
                keeping=keeping,
                status=status,
                text=status_desc
            )
        )

    def default_response(self):
        return Conversation(role=Role.ASSISTANT, content='有什么可以帮助你的吗？')

    def chat(self, history: list[Conversation]) -> Conversation:
        log.info(f'chat to {self.name}. [history = {str(history)}]')
        return tools.asyncio_run(lambda: self.async_chat(history=history))

    async def async_chat(self, history: list[Conversation]) -> Conversation:
        """异步线程"""
        ...
