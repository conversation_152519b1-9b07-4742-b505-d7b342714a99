import json
import shutil
from abc import ABC, abstractmethod
from pathlib import Path
from typing import List, Union, Literal

from common import tools
from framework.agents.schema import Conversation
from framework.data.document import SensorsDocsMetadata, SensorsDocMetadata
from framework.knowledge import KnowledgeBase

log = tools.get_logger()


class RagClient(ABC):

    def __init__(self, kb: KnowledgeBase, description: str | None = None, **kwargs):
        self.base_path = Path(kb.base_dir)
        self.knowledge_base = kb
        self.description = description
        self.kwargs = kwargs

        if not self.base_path.exists():
            self.base_path.mkdir(parents=True, exist_ok=True)

    @property
    def input_dir(self) -> Path:
        """获取输入数据路径"""
        input_dir = self.base_path.joinpath('input')
        if not input_dir.exists():
            input_dir.mkdir(parents=True, exist_ok=True)
        return input_dir

    @property
    def input_document_files(self) -> List[Path]:
        docs = []
        for file in Path(self.input_dir).glob('**/*'):
            if file.is_file() and file.match('*.txt'):
                docs.append(file)
        return docs

    def fix_input_metadata(self):
        metadata_path = self.input_dir.joinpath('metadata.json')
        data = None
        input_document_files = self.input_document_files
        if not metadata_path.exists():
            # 由于没有元数据文件，就建立新的
            metadata_path.parent.mkdir(parents=True, exist_ok=True)
            data = SensorsDocsMetadata.model_validate({
                'batch_id': 1,
                'documents': [],
            })
            for file in input_document_files:
                doc = SensorsDocMetadata(
                    file_name=file.name,
                    title='',
                    batch_id=1,
                    content_sign=tools.md5(file.read_text(encoding='utf-8'))
                )
                data.upsert_doc(doc)
            metadata_path.write_text(data.model_dump_json(indent=4))
        else:
            data = SensorsDocsMetadata.model_validate(json.loads(metadata_path.read_text(encoding='utf-8')))

        # 校验是否还有没添加进去的文档
        documents = data.documents
        if not documents:
            documents = []
        has_not_include_docs = False
        for file in input_document_files:
            doc_exists = False
            for doc in documents:
                if doc.file_name == file.name:
                    doc_exists = True
                    break
            if not doc_exists:
                has_not_include_docs = True
                doc = SensorsDocMetadata(
                    file_name=file.name,
                    title='',
                    batch_id=data.batch_id + 1,
                    content_sign=tools.md5(file.read_text(encoding='utf-8'))
                )
                data.upsert_doc(doc)

        if has_not_include_docs:
            data.batch_id = data.batch_id + 1
        metadata_path.write_text(data.model_dump_json(indent=4))

    @property
    def additional_dir(self) -> Path:
        """获取输入数据路径"""
        additional_dir = self.base_path.joinpath('additional')
        if not additional_dir.exists():
            additional_dir.mkdir(parents=True, exist_ok=True)
        return additional_dir

    def add_additional_file(self, input_file: Path, new_file_name: str):
        """手动添加其他文件，不放在知识库索引目录内"""
        new_file_path = self.additional_dir.joinpath(new_file_name)
        shutil.move(input_file, new_file_path)
        log.info(f'add additional file success: {new_file_path}')

    def add_input_file(self, input_file: Path):
        """手动添加知识库文件，文件名相同则会校验和替换"""
        self.fix_input_metadata()
        metadata_path = self.input_dir.joinpath('metadata.json')
        metadata_text = metadata_path.read_text(encoding='utf-8')
        log.info(f'before docs metadata: {metadata_text}')
        docs_metadata = SensorsDocsMetadata.model_validate(json.loads(metadata_text))
        this_batch_id = docs_metadata.batch_id + 1
        file_content = input_file.read_text(encoding='utf-8')
        sign = tools.md5(file_content)
        document = None
        for doc in docs_metadata.documents:
            if not doc.file_name == input_file.name:
                continue
            if sign == doc.content_sign:
                log.warning('file exists, skip.')
                return
            else:
                log.warning(f'replace file {input_file.name}.')
                document = doc
            break

        if document is None:
            document = SensorsDocMetadata(
                file_name=input_file.name,
                title='',
                batch_id=this_batch_id,
                content_sign=sign,
            )
        if not document.id:
            document.id = tools.sha256(file_content)
        document.content_sign = sign
        document.batch_id = this_batch_id
        new_path = self.input_dir.joinpath(input_file.name)
        shutil.move(input_file, new_path)
        docs_metadata.batch_id = this_batch_id
        docs_metadata.upsert_doc(document)
        metadata_text = docs_metadata.model_dump_json(indent=4)
        log.info(f'after docs metadata: {metadata_text}')
        metadata_path.write_text(metadata_text)

    def clean_input_files(self):
        log.warning(f'Now CLEAR files of input dir: {self.input_dir.absolute()}')
        shutil.rmtree(self.input_dir)

    def delete_file(self, file_id: str):
        self.fix_input_metadata()
        metadata_path = self.input_dir.joinpath('metadata.json')
        metadata_text = metadata_path.read_text(encoding='utf-8')
        log.info(f'before docs metadata: {metadata_text}')
        docs_metadata = SensorsDocsMetadata.model_validate(json.loads(metadata_text))

        file_info = docs_metadata.get_doc(file_id)
        if not file_info:
            raise RuntimeError(f'文件不存在：{file_id}')
        file_path = self.input_dir.joinpath(file_info.file_name)
        file_path.unlink()

        docs_metadata.delete_doc(file_id)
        metadata_text = docs_metadata.model_dump_json(indent=4)
        log.info(f'after docs metadata: {metadata_text}')
        metadata_path.write_text(metadata_text)

    @abstractmethod
    async def build_index(
            self,
            index_mode: Literal['reindex', 'resume', 'update'] = 'reindex',
            **kwargs
    ):
        """
        首次建立索引
        @param index_mode reindex 表示强制索引，resume 表示继续索引或者更新
        """
        pass

    @abstractmethod
    async def search(self, history: List[Conversation], **kwargs) -> Conversation:
        """async search"""
        pass

    @abstractmethod
    async def simple_search(self, history: List[Conversation], **kwargs) -> Conversation:
        """只通过检索向量数据库实现搜索"""
        pass
