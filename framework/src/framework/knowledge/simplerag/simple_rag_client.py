from pathlib import Path
from typing import Callable, List, Literal, Dict

from langchain_core.tools import BaseTool
from llama_index.core.workflow import Workflow

from framework import config
from common import tools
from framework.agents.schema import Conversation
from framework.knowledge.rag_client import RagClient, KnowledgeBase
from framework.vector_store import LarkVectorStoreFactory
from framework.models import BaseLLM, ModelsFactory
from .index.workflow import SimpleIndexRagWorkflow
from .retrieve.retrieve_engine import RetrieveClient

log = tools.get_logger()


class SimpleRag(RagClient):
    """简单的 rag，如果 llm 上下文很大，则可以考虑使用"""

    def __init__(
            self,
            kb: KnowledgeBase,
            entity_types: Dict[str, str],
            llm: BaseLLM = ModelsFactory.get_llm(),
            pre_knowledge: str | None = None,
            additional_extract_topics: List[Callable[[str, BaseLLM], List[str]]] | None = None,
            max_token_limit: int = int(config.llm_config['input_token_limit'] * 0.8),
            query_limit=20,
            max_talk_round=4,
            score_limit=0.45,
            additional_tools: List[BaseTool] | None = None,
            additional_retrieve_requirement: str | None = None,
            retrieve_documents_only: bool = False,
            callback: Callable[[str], None] = None,
            **kwargs,
    ):
        """
        additional_extract_topics: 额外抽取的主题说明
        additional_retrieve_requirement: 额外的检索要求
        """
        super().__init__(kb, **kwargs)
        self.callback = callback
        self.llm = llm
        self.retrieve_documents_only = retrieve_documents_only
        self.entity_types = entity_types
        self.vector_store = LarkVectorStoreFactory.get_vector_store(collection_name=self._collection_name)
        self.vector_store.connect()
        self.query_limit = query_limit
        self.additional_retrieve_requirement = additional_retrieve_requirement
        self.additional_tools = []
        if additional_tools:
            self.additional_tools.extend([t for t in additional_tools if t is not None])

        self.score_limit = score_limit
        self.max_token_limit = max_token_limit
        self.max_talk_round = max_talk_round
        self.retrieve_client = None

        self.pre_knowledge = pre_knowledge
        self.additional_extract_topics = additional_extract_topics
        if not self.pre_knowledge:
            self.pre_knowledge = ""

    def set_additional_tools(self, additional_tools: List[BaseTool]):
        self.additional_tools.clear()
        if additional_tools:
            self.additional_tools.extend([t for t in additional_tools if t is not None])

    @property
    def retrieve_engine(self):
        if self.retrieve_client is not None:
            return self.retrieve_client
        self.retrieve_client = RetrieveClient(
            llm=self.llm,
            vector_store=self.vector_store,
            score_limit=self.score_limit,
            max_token_limit=self.max_token_limit,
            max_talk_round=self.max_talk_round,
            query_limit=self.query_limit,
            background_knowledge=self.pre_knowledge,
            additional_tools=self.additional_tools,
            additional_retrieve_requirement=self.additional_retrieve_requirement,
            retrieve_documents_only=self.retrieve_documents_only,
            callback=self.callback,
            **self.kwargs
        )
        return self.retrieve_client

    @property
    def _collection_name(self) -> str:
        name = str(self.knowledge_base)
        return f"sensors_collection_{name}"

    def _get_workflow(
            self,
            index_mode: Literal['reindex', 'resume', 'update'] = 'reindex',
    ) -> Workflow:

        _kwargs = {
            'llm': self.llm,
            'input_dir': self.input_dir,
            'output_dir': self.output_dir,
            'vector_store': self.vector_store,
            'entity_types': self.entity_types,
            'pre_knowledge': self.pre_knowledge,
            'additional_extract_topics': self.additional_extract_topics,
            'index_mode': 'reindex' if 'reindex' == index_mode else 'resume',
            'callback': self.callback,
        }

        if 'columns_to_embed' in self.kwargs:
            _kwargs['columns_to_embed'] = self.kwargs['columns_to_embed']
        if 'chunk_size' in self.kwargs:
            _kwargs['chunk_size'] = self.kwargs['chunk_size']

        return SimpleIndexRagWorkflow(**_kwargs)

    @property
    def output_dir(self) -> Path:
        output_dir = self.base_path.joinpath('output_simple')
        if not output_dir.exists():
            output_dir.mkdir(parents=True)
        return output_dir

    async def build_index(
            self,
            index_mode: Literal['reindex', 'resume', 'update'] = 'reindex',
            **kwargs
    ):
        flow = self._get_workflow(index_mode=index_mode)
        result = await flow.run()
        log.info(f'{index_mode} index success. result={result}')

    async def search(self, history: List[Conversation], **kwargs) -> Conversation:
        return await self.retrieve_engine.aretrieve(history=history, retrieve_limit=self.query_limit)

    async def simple_search(self, history: List[Conversation], **kwargs) -> Conversation:
        return await self.retrieve_engine.aretrieve_data(history=history, retrieve_limit=self.query_limit)
