from typing import List, Any, Callable

from langchain_core.tools import BaseTool

from common import tools
from framework.agents.schema import Conversation
from framework.vector_store import LarkVectorStore
from framework.models import BaseLLM
from .base import KnowledgeRetrieve
from .agents.agents_bot import RetrieveAgentBot

log = tools.get_logger()


class RetrieveClient(KnowledgeRetrieve):
    """自定义检索引擎"""

    def __init__(
            self,
            llm: BaseLLM,
            vector_store: LarkVectorStore,
            retrieve_documents_only: bool = True,
            llm_params: dict[str, Any] | None = None,
            score_limit: float = 0.45,
            additional_tools: List[BaseTool] | None = None,
            additional_retrieve_requirement: str | None = None,
            callback: Callable[[str], None] = None,
            **kwargs
    ):
        super().__init__(llm, vector_store, llm_params, callback=callback, **kwargs)

        kwargs = kwargs | {
            'name': tools.uuid4_no_underline(),
            'llm': llm,
            'retrieve_documents_only': retrieve_documents_only,
            'vector_store': vector_store,
            'llm_params': llm_params,
            'score_limit': score_limit,
            'additional_tools': additional_tools,
            'additional_retrieve_requirement': additional_retrieve_requirement,
            'callback': callback,
        }
        self.retrieve_bot = RetrieveAgentBot(**kwargs)

    def retrieve_data(
            self,
            history: List[Conversation] | None = None,
            retrieve_limit: int = 5,
            **kwargs
    ) -> Conversation:
        """检索经过总结的原始数据"""
        return self.retrieve_bot.chat(history=history)

    async def aretrieve_data(
            self,
            history: List[Conversation] | None = None,
            retrieve_limit: int = 5,
            **kwargs
    ) -> Conversation:
        """检索经过总结的原始数据"""
        return await self.retrieve_bot.async_chat(history=history)
