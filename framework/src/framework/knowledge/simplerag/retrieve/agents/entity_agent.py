import json
from typing import List, Dict

from jinja2 import Template
from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage

from framework.prompt import PromptFactory
from framework.agents.autogen import CompressedParticipantAgent
from framework.agents.real_query import RealQueryAgent
from framework.vector_store import LarkVectorStore, VectorDocument
from qdrant_client.http.models import models

from common import tools

log = tools.get_logger()

ENTITY_EXTRACT_PROMPT = '''
# 职责
你是一个严谨的人工智能助手，你需要仔细分析用户需要检索的内容，并识别出来需要检索的实体。
你识别出来的实体中应该包括用户关心的所有实体，不能丢失任何内容信息。

# 识别操作步骤
1. 识别实体，对于每个识别出的实体，输出其实体名称。

比如针对这句话：好的，神策智能运营是有神策数据(sensorsdata)开发的一款基于用户的智能运营工具（MA工具）
应该识别出来的实体名称为：神策，神策智能运营，神策数据，MA工具，sensorsdata

注意：在这一步如果你需要提取的实体超过了10个，那就返回与用户的提问最相关的10条即可。

2. 为了检索的时候，能够更准确的匹配，你提取出来实体以后，需要基于当前对话上下文将实体的名称进行扩展，扩展规则有：
 - 用户想检索的近似的实体名称，比如「神策」和「神策数据」近似，「程序」和「代码」近似等；
 - 扩展名词，从当前词扩展用户可能关心的其他名词，比如：用户问骑车从天府三街到天府五街耗时多长时间？你应该自动把车扩展为「自行车」、「电动车」、「摩托车」等用户可以骑的车；
 - 大小写、缩写扩展，比如 sensorsdata 是两个词 sensors 和 data 的组合，可以扩展为 SensorsData、SENSORSDATA 等，而 MA 是一个缩写，可以扩展为 Marketing Automation、ma 等。

比如针对步骤 1 中的实体，应该扩展为：神策，神策智能运营，智能运营，神策数据，MA工具，MA，ma，Marketing Automation，自动化营销，sensorsdata，SensorsData

3. 扩展完成名称后，你需要按照代码的形式进行输出，输出内容为包括在 markdown 代码块中的 json 字符串，比如：
```json
{
    "entities": ["神策", "神策智能运营", "智能运营", "神策数据", "MA工具"，"MA", "Marketing Automation", "自动化营销", "sensorsdata", "SensorsData"]
}
```

# 识别举例
举例如下：
```
历史对话：
user: 泛金融行业有哪些产品可以售卖？
assistant: 泛金融行业可售卖产品包括：SA基础版、SA旗舰版
user: 那基础版和旗舰版有什么区别？
```
一个好的回复示例如下：

 * 1. 用户是想了解泛金融行业的SA基础版和SA旗舰版的区别，所需要用到的实体包括：SA、基础版、旗舰版、SA基础版、SA旗舰版，而在当前的检索结果中，不包含的实体信息有 SA、SA基础版、xxx
 * 2. 为了更准确的匹配实体信息，需要将实体名称扩展同义词，扩展之后有：SA、SA基础版、神策分析、神策分析基础版、...
 * 3. 输出内容。
```json
{
    "entities": ["SA", "SA基础版", "神策分析", "神策分析基础版"]
}
```

# 回复方式
为了更准确的识别到实体信息，你应该先逐步分析用户的对话，然后再给出你所识别出来的需要检索的内容。
你应该将实体提取、扩展的步骤都详细回复，不能直接回复 json。

如果没有需要提取的内容，则直接返回空即可。
注意不要输出与用户的问题直接和间接推理都无关的内容！

开始：

历史对话：
***
{{history_messages}}
'''

ENTITY_RETRIEVER_NAME = 'entity_retriever'
ENTITY_RETRIEVER_DESCRIPTION = '''实体检索助手，可以检索一些实体信息，实体是从文档中识别出来的实体，需要清晰说明需要检索的实体名称'''


class EntityRetrieve(CompressedParticipantAgent):
    """需要压缩，防止上下文过大"""

    def __init__(
            self,
            vector_store: LarkVectorStore,
            name: str = ENTITY_RETRIEVER_NAME,
            description: str = ENTITY_RETRIEVER_DESCRIPTION,
            max_token_limit: int = 1024 * 8,
            query_limit: int = 20,
            score_limit: float = 0.45,
            **kwargs
    ) -> None:
        super().__init__(name=name, description=description, **kwargs)
        self.max_token_limit = max_token_limit
        self.vector_store = vector_store
        self.query_limit = query_limit
        self.score_limit = score_limit

    def run(self) -> BaseMessage:
        """运行 agent 步骤"""
        results = []
        entities = self._extract_entity_names_from_history()

        # 先精确搜索
        if entities:
            _entities = self._get_entities_by_names(entity_names=entities)
            if _entities:
                results.extend(_entities)

        if len(results) >= self.query_limit:
            results = results[:self.query_limit]
            return self._convert_entities_as_reply(entities=results)

        # 再模糊搜索
        results.extend(self._get_entities_by_similarity())
        if len(results) >= self.query_limit:
            results = results[:self.query_limit]

        return self._convert_entities_as_reply(entities=results)

    def _convert_entities_as_reply(self, entities: List[VectorDocument]) -> BaseMessage:
        content = []
        if not entities:
            content.append('No content')
        else:
            entity_attrs: List[Dict] = [e.attributes for e in entities]
            content.extend([
                'Entities:', '```json', json.dumps(entity_attrs, indent=4, ensure_ascii=False), '```'
            ])
        return self._convert_message(message={'role': 'assistant', 'content': '\n'.join(content), 'name': self.name})[0]

    def _get_entities_by_similarity(self) -> list[VectorDocument]:
        """模糊检索"""
        agent = RealQueryAgent(llm=self.llm)
        for h in self.get_history():
            agent.add_history(h)
        real_query = agent.get_real_query()

        entities = self.vector_store.similarity_search_by_text(
            text=real_query,
            query_filter=models.Filter(
                must=[
                    models.FieldCondition(
                        key="type",
                        match=models.MatchValue(value="entity"),
                    ),
                ]
            ),
            k=self.query_limit,
        )

        result = []
        if entities:
            result = [e.document for e in entities if e.score >= self.score_limit]
        return result

    def _get_entities_by_names(self, entity_names: List[str]) -> list[VectorDocument]:
        """根据名字精确检索"""
        if not entity_names:
            return []

        return self.vector_store.filter(
            query_filter=models.Filter(
                must=[
                    models.FieldCondition(
                        key="type",
                        match=models.MatchValue(value="entity"),
                    ),
                    models.FieldCondition(
                        key="name",
                        match=models.MatchAny(any=entity_names),
                    ),
                ]
            ),
            k=self.query_limit * 10,
        )

    def _extract_entity_names_from_history(self) -> List[str] | None:
        history = self.get_history()
        messages = []
        for h in history:
            if isinstance(h, HumanMessage):
                messages.append(f'user: {h.content}')
            else:
                messages.append(f'assistant: {h.content}')

        entity_extract_prompt = PromptFactory.render_prompt(
            ENTITY_EXTRACT_PROMPT,
            history_messages='\n'.join(messages)
        )
        completion = self.llm.complete(
            input=[
                SystemMessage(content=entity_extract_prompt),
            ]
        )
        content = tools.extract_code_from_text(completion.content)
        if not content:
            return []
        content = json.loads(content)
        entities = [_ for _ in content['entities']]
        return entities

    async def arun(self) -> BaseMessage:
        """运行 agent 步骤"""
        return self.run()
