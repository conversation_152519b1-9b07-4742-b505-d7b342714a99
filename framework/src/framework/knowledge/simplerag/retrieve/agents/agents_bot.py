from typing import Callable, Any, List

from jinja2 import Template
from langchain_core.tools import BaseTool

from framework.prompt import PromptFactory
from common import tools
from framework.bot import Bot
from framework.agents.schema import Conversation, Role
from framework.agents.autogen import AutoGenWorkflow, ReactManagerAgent, ReActParticipantAgent
from framework.vector_store import LarkVectorStore
from framework.models import BaseLLM

from .entity_agent import EntityRetrieve
from .document_agent import DocRetrieve

log = tools.get_logger()

MANAGER_NAME = 'retrieve_manager'
MANAGER_PROMPT = """
# 职责描述
你正在一个角色扮演游戏中，这个游戏旨在尽最大努力给用户的话题提供参考，参与角色如下：
{{role_and_descriptions}}

{{additional_retrieval_requirement}}

# 背景知识及文档内容相关解释
为了能更好的玩这个游戏，你需要以下背景知识：
{{background_knowledge}}

# 输出格式
你需要按照以下格式回复：
```
Thought: 当前问题已经解决了 xx，为了解决 xx 问题，我需要 xx 角色来做 xx 事情
Choose: 角色名称，角色名称只能从 {{participants}} 中选一个
Do: 角色需要做的事情，比如：请你继续分析用户的意图
```
你必须始终以 `Thought: ` 开头，Thought 中你需要思考当前提供的内容是否足够回答用户问题，以及需要谁做什么等。
不允许使用 markdown 代码块标记回复！如果回复中包含代码信息，则可以使用 markdown 代码块标记。
如果你所选的角色已经回复了你，则他们会加上他们自己的回复，比如：Do Response: 这里是所选择的role的回复内容。
需要注意：只有 reporter 才能与用户沟通，用户无法与其他角色沟通，所以需要最终回复的内容请明确告知 reporter.

# 输出结果的步骤
你需要不断重复上述格式步骤，直至检索到足够的信息为止。需要注意的是，其他角色的发言通常不全（或者会用局部信息欺骗你），为了保证用户能获取足量的信息，你应该尽可能的让他们发言，直到无法得到新的信息为止。
如果其他角色已经无法解决问题，或者出现了很多重复或者无意义的讨论，你应该直接让 reporter 收集结果并回复用户。
注意，reporter 角色只有在需要终止上述思考步骤的时候才需要调用，否则不要指定 reporter 角色。

注意，你不能从对话内容中获取角色名称！

# 用户对话记录与需求
仔细阅读下面的对话记录，并从中识别到用户(user/human)最终的需求，然后按照上述格式解决用户需求。
***
{{chat_history}}

{{query_prompt}}

开始：
"""


class RetrieveAgentBot(Bot):
    def __init__(
            self,
            name: str,
            llm: BaseLLM,
            vector_store: LarkVectorStore,
            retrieve_documents_only: bool,
            max_token_limit: int = 1024 * 8,
            query_limit: int = 20,
            max_talk_round: int = 10,
            score_limit: float = 0.45,
            additional_tools: List[BaseTool] | None = None,
            additional_retrieve_requirement: str | None = None,
            callback: Callable[[Any], None] | None = None,
            **kwargs
    ):
        super().__init__(name, callback=callback)
        self.llm = llm
        self.vector_store = vector_store
        self.max_token_limit = max_token_limit
        self.query_limit = query_limit
        self.max_talk_round = max_talk_round
        self.score_limit = score_limit
        self.additional_retrieve_requirement = additional_retrieve_requirement
        self.additional_tools = additional_tools
        self.retrieve_documents_only = retrieve_documents_only
        self.kwargs = kwargs
        self.workflow = None
        self.doc_retrieve = None

    async def async_chat(self, history: list[Conversation]) -> Conversation:
        messages = []
        for message in history:
            if message.role == Role.ASSISTANT:
                messages.append({'role': 'assistant', 'content': message.content})
            else:
                messages.append({'role': 'user', 'content': message.content})

        if self.retrieve_documents_only and (not self.additional_tools):
            self._init_document_retrieve()
            self.doc_retrieve.add_history(messages)
            result = await self.doc_retrieve.arun()
        else:
            await self._init_workflow_graph()
            result = await self.workflow.run_with_history(history=messages)
        return Conversation(role=Role.ASSISTANT, content=result.content)

    def _init_document_retrieve(self):
        if self.doc_retrieve is not None:
            return
        kwargs = self.kwargs | {
            'llm': self.llm,
            'vector_store': self.vector_store,
            'max_token_limit': self.max_token_limit,
            'query_limit': self.query_limit,
            'score_limit': self.score_limit,
            'logs_callback': self.callback,
        }
        self.doc_retrieve = DocRetrieve(**kwargs)

    async def _init_workflow_graph(self):
        """初始化检索 autogen 图"""
        if self.workflow is not None:
            return
        self._init_document_retrieve()
        kwargs = self.kwargs | {
            'llm': self.llm,
            'vector_store': self.vector_store,
            'max_token_limit': self.max_token_limit,
            'query_limit': self.query_limit,
            'score_limit': self.score_limit,
            'logs_callback': self.callback,
        }
        agents = [
            EntityRetrieve(**kwargs),
            self.doc_retrieve,
        ]

        if self.additional_tools:
            for tool in self.additional_tools:
                # 由于有些模型的工具选择能力很弱，这里只使用单个 tool，结合多 agent 做推理
                kwargs = self.kwargs | {
                    'name': f'role{tool.name}',
                    'description': tool.description,
                    'tools': [tool],
                    'llm': self.llm,
                    'answer_prompt': 'Final Answer 的内容请直接复制 Observation 步骤的回复内容，格式和内容均不能变化！！！',
                    'max_token_limit': self.max_token_limit,
                    'logs_callback': self.callback,
                }
                agents.append(ReActParticipantAgent(**kwargs))

        additional_retrieve_requirement = self.additional_retrieve_requirement
        if additional_retrieve_requirement:
            additional_retrieve_requirement = f'\n用户对解决问题的一些其他要求：\n{additional_retrieve_requirement}'
        manager_prompt = PromptFactory.render_prompt(
            MANAGER_PROMPT,
            additional_retrieval_requirement=additional_retrieve_requirement
        )
        manager = ReactManagerAgent(
            name=MANAGER_NAME,
            llm=self.llm,
            system_prompt=manager_prompt,
            **self.kwargs,
        )

        self.workflow = AutoGenWorkflow(
            manager_agent=manager,
            participant_agents=agents,
            need_extract_real_query=True,
            max_talk_round=self.max_talk_round,
            step_callback=self.callback,
        )
