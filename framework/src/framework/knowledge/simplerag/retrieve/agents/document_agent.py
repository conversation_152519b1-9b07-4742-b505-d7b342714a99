import json
import time
from typing import List

from jinja2 import Template
from langchain_core.messages import BaseMessage

from framework.prompt import PromptFactory
from framework.agents.assistant import AssistantAgent
from framework.agents.autogen import CompressedParticipantAgent
from framework.agents.compress import summary_contextual_compression
from framework.agents.real_query import RealQueryAgent
from framework.vector_store import LarkVectorStore, VectorDocument
from qdrant_client.http.models import models

from common import tools

log = tools.get_logger()

DOC_RETRIEVER_NAME = 'doc_retriever'
DOC_RETRIEVER_DESCRIPTION = '''文档（即 text_unit）检索助手，可以被指定文档ID进行检索，也可以不指定文档ID，给出检索需求来检索'''

TEXT_UNIT_IDS_PROMPT = """
请按照 json 格式给出需要你检索的文档 id（或者 text_unit 的 id）。
如果没有指定需要你检索的文档或者 text_unit 的 id，直接给出空列表。

你需要将回复放在 markdown 代码块中，比如：
```json
["00eae7a4d7744047bc3e56df6244b950", "f63487f267f4404d83bdf7d82e7f9dff"]
```

如果没有给你指定 id，直接回复空列表：
```json
[]
```

除了回复 json 代码块以外，其他的任何多余回复都是禁止的！
"""

async def get_document_agent_text_unit_ids_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="DOCUMENT_AGENT_PROMPT_TEXT_UNIT_IDS_PROMPT",
        prompt_name="文档检索机器人 - 系统提示词",
        prompt_desc="文档检索机器人 - 系统提示词",
        prompt_content=TEXT_UNIT_IDS_PROMPT,
    )
    return template

class DocRetrieve(CompressedParticipantAgent):
    """需要压缩，防止上下文过大"""

    def __init__(
            self,
            vector_store: LarkVectorStore,
            name: str = DOC_RETRIEVER_NAME,
            description: str = DOC_RETRIEVER_DESCRIPTION,
            max_token_limit: int = 1024 * 8,
            query_limit: int = 20,
            score_limit: float = 0.45,
            **kwargs
    ) -> None:
        super().__init__(name=name, description=description, **kwargs)
        self.max_token_limit = max_token_limit
        self.vector_store = vector_store
        self.query_limit = query_limit
        self.score_limit = score_limit

    def run(self) -> BaseMessage:
        """运行 agent 步骤"""
        response = tools.asyncio_run(self.arun)
        return self._convert_message(message={'role': 'assistant', 'content': response, 'name': self.name})[0]

    def _convert_docs_as_reply(self, docs: List[VectorDocument]) -> List[BaseMessage]:
        content = []
        if not docs:
            content.append('No content')
        else:
            content.append('Documents:\n***')
            texts: List[str] = []
            for e in docs:
                txt = []
                text = e.attributes.get('text', None)
                descriptions = e.attributes.get('descriptions', None)
                topic = e.attributes.get('topics', None)
                entities = e.attributes.get('entities', None)
                if text:
                    txt.append(text)
                if descriptions:
                    txt.append(f'descriptions:\n{descriptions}')
                if topic:
                    txt.append(f'topics:\n{topic}')
                if entities:
                    txt.append(f'entities:\n{entities}')
                texts.append('\n'.join(txt))

            count = 0
            for text in texts:
                count += 1
                content.append(f'\n\nParagraph {count}:')
                content.append(text)
        messages = []
        for msg in content:
            message = self._convert_message(message={'role': 'assistant', 'content': msg, 'name': self.name})[0]
            messages.append(message)
        return messages

    def _get_documents_by_similarity(self, user_query: str) -> list[VectorDocument]:
        """模糊检索"""
        self.log_and_callback(f'get documents with query: {user_query}')
        documents = self.vector_store.similarity_search_by_text(
            text=user_query,
            query_filter=models.Filter(
                must=[
                    models.FieldCondition(
                        key="type",
                        match=models.MatchValue(value="text_unit"),
                    ),
                ]
            ),
            k=self.query_limit,
        )

        result = []
        if documents:
            self.log_and_callback(f'get documents result: {documents}')
            result = [e.document for e in documents if e.score >= self.score_limit]
            self.log_and_callback(f'filtered documents count: {len(documents)}')
        else:
            self.log_and_callback('no documents match.')
        return result

    def _get_documents_by_vids(self, text_unit_vids: List[str]) -> list[VectorDocument]:
        """根据 text_unit id 精确检索"""
        if not text_unit_vids:
            return []

        return self.vector_store.filter(
            query_filter=models.Filter(
                must=[
                    models.FieldCondition(
                        key="type",
                        match=models.MatchValue(value="text_unit"),
                    ),
                    models.FieldCondition(
                        key="vid",
                        match=models.MatchAny(any=text_unit_vids),
                    ),
                ]
            ),
            k=self.query_limit,
        )

    def _extract_text_unit_ids_from_history(self) -> List[str] | None:
        agent = AssistantAgent(llm=self.llm, system_prompt=tools.asyncio_run(get_document_agent_text_unit_ids_prompt).render())
        agent.add_history(self.get_history())
        content = agent.chat_and_save(prompt=f'已知你是{self.name}，请按照要求格式回复：')
        content = tools.extract_code_from_text(content)
        if not content:
            return []
        try:
            return json.loads(content)
        except BaseException as e:
            log.warning('get text_unit ids error', e)
            return []

    async def arun(self) -> BaseMessage:
        """运行 agent 步骤"""
        text_unit_ids = self._extract_text_unit_ids_from_history()

        # 有精确搜索就不进行模糊搜索
        current_user_query = None
        if text_unit_ids:
            results = self._get_documents_by_vids(text_unit_vids=text_unit_ids)
        else:
            agent = RealQueryAgent(llm=self.llm)
            for h in self.get_history():
                agent.add_history(h)
            current_user_query = agent.get_real_query()
            results = self._get_documents_by_similarity(user_query=current_user_query)

        if results and len(results) > self.query_limit:
            results = results[:self.query_limit]

        # 压缩
        start_time = time.time()
        self.log_and_callback('已检索到内容，正在压缩 ...')
        response = self._convert_docs_as_reply(docs=results)
        response, all_tokens = await summary_contextual_compression(
            history=response,
            user_query=current_user_query,
            input_token_limit=self.max_token_limit,
            model_client=self.llm,
            compress_rate=self.compress_rate,
            name=self.name,
        )
        self.log_and_callback(f'压缩完成，压缩耗时 {time.time() - start_time}s')
        response = '\n'.join([r.content for r in response])
        self.log_and_callback(f'压缩结果：{response}')
        return self._convert_message(message={'role': 'assistant', 'content': response, 'name': self.name})[0]
