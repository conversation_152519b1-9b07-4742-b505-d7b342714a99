import os
from pathlib import Path
from typing import Callable, Any, List
import pandas as pd
import numpy as np
from tenacity import retry, stop_after_attempt, retry_if_exception_type, wait_fixed
from concurrent.futures.thread import Thread<PERSON>oolExecutor
from concurrent.futures import wait, ALL_COMPLETED

from common import tools
from framework.models import BaseLLM
from framework.prompt import PromptFactory
from .prompts import *

log = tools.get_logger()


def _callback(callback: Callable[[Any], Any] | None, content: Any):
    try:
        log.info(content)
        if not callback:
            return
        callback(content)
    except BaseException as e:
        log.warning('callback error.', e)


@retry(
    stop=stop_after_attempt(5),
    retry=retry_if_exception_type(BaseException),
    wait=wait_fixed(60),
    reraise=True,
)
def _call_llm(llm: BaseLLM, system_prompt: str) -> str:
    return llm.complete(input={'role': 'system', 'content': system_prompt}).content


async def create_text_unit_topics(
        data_path: Path,
        llm: BaseLLM,
        pre_knowledge: str | None = None,
        additional_extract_topics: List[Callable[[str, BaseLLM], List[str]]] | None = None,
        callback: Callable[[Any], Any] | None = None
) -> pd.DataFrame:
    middle_text_units = pd.read_parquet(data_path)
    middle_text_units.reset_index(drop=True, inplace=True)
    _callback(callback, f'start create topics total_size={middle_text_units.shape[0]}')

    # 多线程，充分利用 CPU 计算，至少留 2 个 CPU，不然反复切换进程会导致系统吞吐量降低
    results = []
    use_cpu_count = os.cpu_count() or 1
    if use_cpu_count > 2:
        use_cpu_count -= 2
    with ThreadPoolExecutor(thread_name_prefix='create_text_unit_topics_', max_workers=use_cpu_count) as t:
        features = []
        for batch in tools.batch_rows(data=middle_text_units, batch_count=use_cpu_count):
            _callback(callback, f'start create topics batch_size={batch.shape[0]}')
            feature = t.submit(
                _do_create_topics,
                input=batch,
                llm=llm,
                pre_knowledge=pre_knowledge,
                additional_extract_topics=additional_extract_topics,
                callback=callback,
            )
            features.append(feature)
        wait(features, return_when=ALL_COMPLETED)
        for f in features:
            results.append(f.result())
    return pd.concat(results)


def _do_create_topics(
        input: pd.DataFrame,
        llm: BaseLLM,
        pre_knowledge: str | None = None,
        additional_extract_topics: List[Callable[[str, BaseLLM], List[str]]] | None = None,
        callback: Callable[[Any], Any] | None = None
) -> pd.DataFrame:
    if not additional_extract_topics:
        additional_extract_topics = []

    all_topics = []
    total_count = input.shape[0]
    count = 0

    def _default_create_topic(input_text: str, call_llm: BaseLLM, ) -> List[str]:
        return _standard_create_text_unit_topic(text=input_text, pre_knowledge=pre_knowledge, llm=call_llm)

    additional_extract_topics.append(_default_create_topic)

    for idx, text_unit in input.iterrows():
        count += 1
        topics = text_unit['topics']
        if isinstance(topics, np.ndarray) and topics.any():
            topics = topics.tolist()
            _callback(callback=callback, content=f'{count}/{total_count} topics exist, skip.')
            all_topics.append(topics)
            continue
        text = text_unit["text"]
        log.info(f'{count}/{total_count} 提取文档主题')
        log.debug(f'提取文档主题：\n***\n{text}')
        topics = []
        for topic_function in additional_extract_topics:
            try:
                _topics = topic_function(text, llm)
                if _topics:
                    topics.extend(_topics)
            except BaseException as e:
                log.error('can not get topics from text', e)
                _callback(callback=callback, content=f'can not get topics from text. {e}')

        topics = list(set([topic for topic in topics if len(topic.strip()) > 0]))
        _callback(
            callback=callback,
            content=f'{count}/{total_count}: index={idx}, in batch extracted topics: {topics}'
        )
        all_topics.append(topics)
    input['topics'] = all_topics
    return input


def _standard_create_text_unit_topic(
        text: str,
        pre_knowledge: str,
        llm: BaseLLM,
) -> List[str]:
    seperator = '<|TOPIC|>'
    response = _call_llm(
        llm=llm,
        system_prompt=PromptFactory.render_prompt(
            TOPIC_PROMPT,
            end_seperator=seperator,
            background_knowledge=pre_knowledge,
            document_content=text
        )
    )
    response = tools.extract_code_from_text(response)
    topics = []
    if len(response) > 0:
        topics = list(set([t.strip() for t in response.split(seperator) if t.strip()]))
    return topics
