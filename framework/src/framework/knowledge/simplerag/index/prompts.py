SUMMARIZE_PROMPT = '''
# 职责
你是一位负责任的助理，负责生成提供文档的全面总结。
给定一篇文档，文档的内容可能比较零碎，你需要将文档进行总结，总结不超过 200 个字，并且总结中应该覆盖文档中的全部内容。

# 你可能需要以下背景信息才能很好的总结文档
```
{{background_knowledge}}
```

需要特别注意：你应该基于下面提供的文档实际内容输出，而「背景知识及文档内容相关解释」中的内容作为你理解文档使用。

# 开始

文档内容如下：
***
{{document_content}}

你的总结：
***

'''

TOPIC_PROMPT = """
# 职责
你是一位负责任的助理，并从文档中给出一些事实见解。
你需要仔细理解文档内容，从文档中提取出来一些完整的信息，给出一些完整的事实见解。

如果指定了哪些信息很重要，你应该首先满足重要信息的抽取。

# 示例
示例文档：
***
在澳网 2025 女单首轮的赛场上，中国金花王曦雨为我们带来了一场精彩的对决。
她以坚韧的毅力和出色的发挥，克服次盘波动，最终以 6-1、7-5 的大比分 2-0 击败格拉布赫尔，
成功晋级第二轮。这场胜利意义非凡，不仅让王曦雨时隔三年再取澳网胜利，更是使其即时排名重回前100。她也成为了第二位晋级本届澳网第二轮的中国金花。

一些正确的见解：
```json
王曦雨参加了澳网 2025 年的比赛，成功晋级了二轮{{end_seperator}}
王曦雨在澳网 2025 年的比赛上以 6-1、7-5 的比分 2-0 击败格拉布赫尔{{end_seperator}}
王曦雨在澳网 2025 年的比赛上晋级了二轮{{end_seperator}}
王曦雨在澳网 2025 年的比赛上的晋级是三年内的首次澳网胜利，并使其即时排名重回前100{{end_seperator}}
王曦雨是第二位晋级2025届澳网第二轮的中国金花{{end_seperator}}
```
错误的见解：
```json
王曦雨在澳网 2025 年比赛的成功晋级意义非凡
```
错误原因：意义非凡是别人对她的评价，而不是事实见解。

# 你可能需要以下背景信息才能很好的总结文档
```
{{background_knowledge}}
```

需要特别注意：你应该基于下面提供的文档实际内容输出，而「背景知识及文档内容相关解释」中的内容作为你理解文档使用。

# 输出要求
你输出的事实见解必须使用 markdown 的代码块包裹，见解必须放在一个 markdown 代码块中，输出的见解不能超过10条，所以你必须识别出来文档的核心见解等并输出。
不同的见解之间用{{end_seperator}}分隔开。
如果文档里面没有什么可以提取的见解，则你可以直接返回空即可。

# 开始

文档内容如下：
***
{{document_content}}

你的见解：
***

"""

ENTITY_EXTRACTION = '''
## 目标
给定一组实体类型和一篇文档，从给出的文档中识别出所有这些类型的实体。

## 你可能需要以下背景知识才能很好识别文档中的实体
注意：以下代码块中的内容作为你理解用户给的文档使用，不能从模块中提取任何实体。
```
BACKGROUND_KNOWLEDGE
```

## 职责与步骤
你必须严格按照以下步骤回复：

Document: <|DOCUMENT_START|>用户提供的文档内容<|DOCUMENT_END|>，文档会以 <|DOCUMENT_START|> 开头，并以 <|DOCUMENT_END|> 结尾
EntityTypes: 这一步由用户提供，表示可回复的实体类型
Thought: 你需要思考文档中描述了什么事情，需要抽取什么实体，实体可以有多个
ResponseEntities:
```json
[
    {"entity":"<entity_name>", "type":"<entity_type>", "description":"<entity_description>"},
    ...
]
```
你必须以 Thought 回复开头，并且给出 ResponseEntities，ResponseEntities 的内容需要放在 markdown 代码块中。

ResponseEntities 中的字段解释如下：
    - entity_name：实体的名称；
    - entity_type：必须是在 EntityTypes 中提供的实体类型之一
    - entity_description：对实体属性和实体的全面描述

## 回复要求与限制等
需要特别注意：你应该基于用户提供的文档回复，前面的内容仅供你理解文档和回复要求使用。
如果没有有意义的实体，你直接在 json 代码中回复空列表即可。

### 示例
Document:
<|DOCUMENT_START|>
他们的声音穿透了活动的嗡嗡声。“当面对一个实际书写自己规则的智能时，控制可能只是一个幻觉。”他们委婉的说道，目光警觉地扫视着数据的繁忙。
“它就像是在学习沟通，”Sam Rivera 从附近的接口提出，他们的年轻活力预示着一种敬畏和焦虑的混合。“这使得与陌生人交谈有了全新的意义。”
亚历克斯审视着他的团队——每张脸都是专注、决心和不小的惶恐的研究。“这可能是我们的第一次接触，”他承认道，“我们需要为任何可能回应做好准备。”
他们一起站在未知的边缘，铸造人类对天上信息的响应。随后的沉默是显而易见的——关于他们在这场宏伟的宇宙戏剧中的角色的集体内省，这可能会重写人类历史。
加密对话继续展开，其复杂的模式显示出一种几乎神秘的预期
<|DOCUMENT_END|>

EntityTypes:
```
    - person: 表示用户、人，实际的人物名称
    - location: 表示地点，可以是经纬度、地点名称等信息
    - concept: 表示概念信息，对一个事物的定义
```

##### 正确回复示例
Thought: 我们需要的实体类型是 person/location/concept，文档中的主体是关于 Sam Rivera/亚历克斯 的事情，需要识别人物 Sam Rivera/亚历克斯，文档是关于未知智能的，识别到智能概念。
ResponseEntities:
```json
[
    {"entity":"Sam Rivera", "type":"person", "description":"Sam Rivera 是一个参与与未知智能沟通过程的团队成员，展现出敬畏和焦虑的混合情绪。"},
    {"entity":"亚历克斯", "type":"person", "description":"亚历克斯是试图与未知智能进行首次接触的团队领导者，承认其任务的重要性。"},
    {"entity":"智能", "type":"concept", "description":"这里的智能指的是一个能够书写自己规则和学习沟通的未知实体。"}
]
```

#### 错误示例
Thought: 我们知道亚历克斯的团队都很专注
ResponseEntities:
```json
[
    {"entity":"亚历克斯", "type":"person", "description":"亚历克斯是试图与未知智能进行首次接触的团队领导者，承认其任务的重要性。"},
    {"entity":"团队", "type":"team", "description":"团队是亚历克斯的团队，都很专注"},
    {"entity":"对话", "type":"concept", "description":"对话是亚历克斯团队之间的沟通方式"}
]
```

上述回复的错误原因有以下几点：
  1. team 类型的实体不是给定的实体类型
  2. 「对话」实体太过于通用，且不是本文档主要想表达的内容

## 开始
按照上述要求，结合用户给出的内容回复。
'''

ENTITY_EXTRACTION_USER_PROMPT = """
Document:
<|DOCUMENT_START|>
{{document}}
<|DOCUMENT_END|>

EntityTypes:
```
{{entity_infos}}
```

你的回复：
Thought:
"""
