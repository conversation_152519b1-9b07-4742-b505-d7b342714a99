import time
from pathlib import Path

from llama_index.core.workflow import Event


class SavedFiles:
    def __init__(self, output_path: Path):
        # 'file_name', 'id', 'url', 'title', 'raw_content', 'content_sign', 'batch_id', 'type'
        self.final_documents = output_path.joinpath('create_final_documents.parquet')
        # same as final_text_units
        self.middle_text_units = output_path.joinpath('create_middle_text_units.parquet')
        # id, type, text_unit_id, name, descriptions
        self.final_entities = output_path.joinpath('create_final_entities.parquet')
        # id, type, document_id, document_batch_id, text, summary, topics, entities
        self.final_text_units = output_path.joinpath('create_final_text_units.parquet')


class BaseEvent(Event):
    def __init__(self, start_time: int = time.time(), **kwargs):
        super().__init__(start_time=start_time, **kwargs)


class DocumentStepEvent(BaseEvent):
    """start to load documents"""

    def __init__(self, start_time: int = time.time(), **kwargs):
        super().__init__(**kwargs)


class CreateBasicTextUnitStepEvent(BaseEvent):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class SummaryTextUnitStepEvent(BaseEvent):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class CreateEntitiesStepEvent(BaseEvent):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class GetTopicsStepEvent(BaseEvent):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class StoreStepEvent(BaseEvent):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
