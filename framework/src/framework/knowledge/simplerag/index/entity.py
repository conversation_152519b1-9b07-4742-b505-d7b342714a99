import json
import os
from pathlib import Path
from typing import Callable, Any, Dict, <PERSON><PERSON>, List
import pandas as pd
from tenacity import retry, stop_after_attempt, retry_if_exception_type, wait_fixed
from concurrent.futures.thread import Thread<PERSON>oolExecutor
from concurrent.futures import wait, ALL_COMPLETED

from common import tools
from framework.models import BaseLLM
from framework.prompt import PromptFactory
from .prompts import *

log = tools.get_logger()


def _callback(callback: Callable[[Any], Any] | None, content: Any):
    try:
        log.info(content)
        if not callback:
            return
        callback(content)
    except BaseException as e:
        log.warning('callback error.', e)


@retry(
    stop=stop_after_attempt(5),
    retry=retry_if_exception_type(BaseException),
    wait=wait_fixed(60),
    reraise=True,
)
def _call_llm(llm: BaseLLM, prompts: List[Dict[str, Any]]) -> str:
    return llm.complete(input=prompts).content


async def extract_entities(
        text_data_path: Path,
        llm: BaseLLM,
        pre_knowledge: str,
        entity_types: Dict[str, str],
        max_gleaning: int = 2,
        callback: Callable[[Any], Any] | None = None
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    middle_text_units = pd.read_parquet(text_data_path)
    middle_text_units.reset_index(drop=True, inplace=True)

    # for test
    # middle_text_units = middle_text_units.loc[:10, :]

    _callback(callback, f'start get entities total_size={middle_text_units.shape[0]}')

    # 优化，由于 entities 解析比较耗时，这里加文件缓存
    entities_cache_df = None
    temp_entities_path = text_data_path.parent.joinpath('temp_cache_text_units_with_entities.parquet')
    if temp_entities_path.exists():
        entities_cache_df = pd.read_parquet(temp_entities_path)

    results = []
    use_cpu_count = os.cpu_count() or 1
    if use_cpu_count > 2:
        use_cpu_count -= 2
    with ThreadPoolExecutor(thread_name_prefix='create_text_unit_entities_', max_workers=use_cpu_count) as t:
        features = []
        for batch in tools.batch_rows(data=middle_text_units, batch_count=use_cpu_count):
            _callback(callback, f'start get entities batch_len={batch.shape[0]}')
            feature = t.submit(
                _do_extract,
                input=batch,
                entities_cache=entities_cache_df,
                llm=llm,
                pre_knowledge=pre_knowledge,
                entity_types=entity_types,
                max_gleaning=max_gleaning,
                callback=callback,
            )
            features.append(feature)
        wait(features, return_when=ALL_COMPLETED)
        for f in features:
            results.append(f.result())
        results = pd.concat(results)
    results.reset_index(drop=True, inplace=True)
    results.to_parquet(path=temp_entities_path)
    merged_entities = _get_merged_entities(results)
    return results, merged_entities


def _get_merged_entities(data: pd.DataFrame) -> pd.DataFrame:
    # 合并 entities
    entities_data = []
    for idx, row in data.iterrows():
        entities = None
        try:
            entities = row['entities']
            if not entities:
                continue
            entities = json.loads(entities)
        except BaseException as e:
            log.warning('get entities error.', e)
        if (entities is None) or (not isinstance(entities, list)):
            continue
        for entity in entities:
            entity['id'] = tools.sha256(entity['entity'])
            entity['text_unit_ids'] = row['id']
            entity['name'] = entity['entity']
            entity['entity_type'] = entity['type']
            entity['type'] = 'entity'
            entity['descriptions'] = entity['description']
            entities_data.append(entity)

    df = pd.DataFrame(entities_data)
    df = df.groupby(by='name', group_keys=False).agg(
        name=pd.NamedAgg(column="name", aggfunc=lambda x: [_ for _ in x][0]),
        id=pd.NamedAgg(column="id", aggfunc=lambda x: [_ for _ in x][0]),
        text_unit_ids=pd.NamedAgg(column="text_unit_ids", aggfunc=lambda x: [_ for _ in x]),
        descriptions=pd.NamedAgg(column="descriptions", aggfunc=lambda x: [_ for _ in x]),
    ).reset_index(drop=True)
    return df


def _validate_entities(entities: str) -> bool:
    try:
        if not entities:
            return False
        entities = json.loads(entities)
        for e in entities:
            if not e['entity'] or not e['type'] or not e['description']:
                return False
        return True
    except:
        return False


def _find_entities_in_cache_df(
        entities_cache: pd.DataFrame | None,
        text_unit_id: str,
) -> str | None:
    if entities_cache is None or text_unit_id is None:
        return None
    columns = set(entities_cache.columns)
    for idx, row in entities_cache.iterrows():
        if 'entities' in columns and _validate_entities(row['entities']):
            return row['entities']
    return None


def _do_extract(
        input: pd.DataFrame,
        entities_cache: pd.DataFrame | None,
        llm: BaseLLM,
        pre_knowledge: str,
        entity_types: Dict[str, str],
        max_gleaning: int = 2,
        callback: Callable[[Any], Any] | None = None
) -> pd.DataFrame:
    total_count = input.shape[0]
    count = 0
    columns = set(input.columns)
    for idx, text_unit in input.iterrows():
        count += 1
        if 'entities' in columns and _validate_entities(text_unit['entities']):
            _callback(callback=callback, content=f'{count}/{total_count} entity exists, skip.')
            continue
        text_unit_id = text_unit['id']
        entities = _find_entities_in_cache_df(entities_cache=entities_cache, text_unit_id=text_unit_id)
        if not entities:
            text = text_unit['text']
            summary = text_unit['summary']

            entities = _do_extract_entities0(
                text=text,
                summary=summary,
                llm=llm,
                pre_knowledge=pre_knowledge,
                entity_types=entity_types,
                max_gleaning=max_gleaning
            )
            entities = json.dumps(entities, ensure_ascii=False)
        if entities:
            input.at[idx, 'entities'] = entities
        _callback(
            callback=callback,
            content=f'{count}/{total_count}: index={idx}, in batch get entities. entities={entities}'
        )
    return input


def _call_and_get_response(
        llm: BaseLLM,
        history: List[Dict],
) -> List[Dict[str, Any]]:
    history = history.copy()
    fix_prompt = '你的回复格式错误，请重新回复：'
    for i in range(10):
        try:
            response = _call_llm(llm=llm, prompts=history)
            if response:
                code = tools.extract_code_from_text(response)
                if not _validate_entities(code):
                    raise RuntimeError(f'llm give entities error. response={response}')
                return json.loads(code)
        except BaseException as e:
            log.warning(f'llm give entities error.', e)
            history.append({'role': 'user', 'content': fix_prompt})
            continue
        raise RuntimeError('Got response error.')


def _do_extract_entities0(
        text: str,
        summary: str,
        llm: BaseLLM,
        pre_knowledge: str,
        entity_types: Dict[str, str],
        max_gleaning: int = 2,
) -> List[Dict[str, Any]]:
    doc = f'# 文档摘要\n{summary}\n\n# 文档内容\n{text}'
    history = [
        {'role': 'system', 'content': PromptFactory.render_prompt(ENTITY_EXTRACTION)},
        {'role': 'user', 'content': PromptFactory.render_prompt(
            ENTITY_EXTRACTION_USER_PROMPT,
            document=doc,
            entity_infos='\n'.join(['  - ' + k + ': ' + v for k, v in entity_types.items()])
        )}
    ]
    total_response = []
    for i in range(max_gleaning):
        try:
            response = _call_and_get_response(llm=llm, history=history)
            if response:
                total_response.extend(response)
            else:
                break
        except BaseException as e:
            log.warning('get entities from text error.', e)
    return total_response
