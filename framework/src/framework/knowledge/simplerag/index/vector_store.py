import json
import os
from concurrent.futures import wait, ALL_COMPLETED
from concurrent.futures.thread import Thread<PERSON>oolExecutor
from pathlib import Path
from typing import List, Dict, Any
from qdrant_client import models

import pandas as pd
from tenacity import retry, stop_after_attempt, retry_if_exception_type, wait_fixed

from common import tools
from framework.vector_store import LarkVectorStore

log = tools.get_logger()


def _do_callback(callback=None, msg: str = None):
    if callback and msg:
        try:
            callback(msg)
        except:
            log.warning('do callback error.')


async def store_to_vector_db(
        vector_store: LarkVectorStore,
        final_text_units: Path,
        clear_all: bool = False,
        callback=None,
        columns_to_embed: List[str] = None
) -> None:
    if not columns_to_embed:
        columns_to_embed = ['text', 'summary', 'topics']
    _do_callback(callback, '正在加载数据文件...')

    data = pd.read_parquet(final_text_units)
    data.reset_index(drop=True, inplace=True)

    log.info("Start store data.")
    if clear_all:
        _do_callback(callback, '正在清除旧数据...')
        vector_store.clear_all_data()

    _do_callback(callback, '开始添加新数据')

    await _store_data(
        vector_store=vector_store,
        data=data,
        columns_to_embed=columns_to_embed,
        callback=callback,
    )
    _do_callback(callback, '数据添加完成')


async def _store_data_single_thread(
        vector_store: LarkVectorStore,
        data: pd.DataFrame,
        columns_to_embed: str | List[str],
        callback=None,
        **kwargs
):
    # 单线程测试
    _do_callback(callback, f'开始向量化并插入 total_size={str(data.shape[0])}')
    _store_data_batch(
        vector_store=vector_store,
        data=data,
        columns_to_embed=columns_to_embed,
        **kwargs,
    )
    log.info('All embedding and store finished.')


async def _store_data(
        vector_store: LarkVectorStore,
        data: pd.DataFrame,
        columns_to_embed: str | List[str],
        callback=None,
        **kwargs
):
    # 分批导入
    _do_callback(callback, f'开始向量化并插入 total_size={str(data.shape[0])}')

    # 多线程，充分利用 CPU 计算，至少留 2 个 CPU，不然反复切换进程会导致系统吞吐量降低
    use_cpu_count = os.cpu_count() or 1
    if use_cpu_count > 2:
        use_cpu_count -= 2
    with ThreadPoolExecutor(thread_name_prefix='embedding_and_store_data_', max_workers=use_cpu_count) as t:
        features = []
        for batch in tools.batch_rows(data=data, batch_count=use_cpu_count):
            _do_callback(callback, f'start insert batch_size=={batch.shape[0]}')
            features.append(t.submit(
                _store_data_batch,
                vector_store=vector_store,
                data=batch,
                columns_to_embed=columns_to_embed,
                **kwargs,
            ))
        wait(features, return_when=ALL_COMPLETED)
    log.info('All embedding and store finished.')


@retry(
    stop=stop_after_attempt(10),
    retry=retry_if_exception_type(BaseException),
    wait=wait_fixed(10),
    reraise=False,
)
def _store_data_batch(
        vector_store: LarkVectorStore,
        data: pd.DataFrame,
        columns_to_embed: str | List[str],
        **kwargs
):
    columns = list(data.columns)
    if 'id' in columns:
        columns.remove('id')
        columns.insert(0, 'vid')
        data = data.rename(columns={'id': 'vid'})
    else:
        data['vid'] = ''

    data.reset_index(drop=True, inplace=True)

    # 由于 embedding 太过于消耗 cpu，这里为了减少资源消耗，需要去掉已经在数据库中的数据
    delete_index = []
    for idx, row in data.iterrows():
        try:
            if _compare_record_exists(vector_store=vector_store, data=row.to_dict(), columns_to_store=set(columns)):
                delete_index.append(idx)
        except BaseException as e:
            log.warning('compare record exists error.', e)

    if delete_index:
        data.drop(index=delete_index, inplace=True)
        data.reset_index(drop=True, inplace=True)

    if data.shape[0] == 0:
        return

    if isinstance(columns_to_embed, str):
        columns_to_embed = [columns_to_embed]

    payloads = json.loads(data.loc[:, columns].to_json(orient='records'))
    document_ids = [vid or tools.uuid4_no_underline() for vid in data['vid'].tolist()]
    try:
        vector_store.batch_load_text(document_ids=document_ids, payloads=payloads, embed_keys=set(columns_to_embed))
    except BaseException as e:
        log.error('batch load text to vector db error.', e)
        raise e
    log.info('batch embedding and store finished.')


def _compare_record_exists(
        vector_store: LarkVectorStore,
        data: Dict[str, Any],
        columns_to_store: set[str],
        **kwargs
) -> bool:
    if 'vid' not in data:
        return False
    queried_documents = vector_store.filter(
        query_filter=models.Filter(
            must=models.FieldCondition(
                key='vid',
                match=models.MatchValue(value=data['vid'])
            )
        ),
        k=100,
    )
    if not queried_documents:
        return False
    elif len(queried_documents) > 1:
        # 可能是以前导错了，需要删除
        vector_store.delete_by_ids(ids=[doc.id for doc in queried_documents])
        return False

    # 需要对比属性是否相等，更新属性
    queried_document = queried_documents[0]
    attrs = queried_document.attributes | {}
    queried_columns = set(attrs.keys())
    if bool(columns_to_store.difference(queried_columns) or queried_columns.difference(columns_to_store)):
        return False
    for key in columns_to_store:
        if not type(data[key]) != type(attrs[key]):
            return False
        if isinstance(data[key], list):
            d1 = set(data[key])
            d2 = set(attrs[key])
            if bool(d1.difference(d2) or d2.difference(d1)):
                return False
        elif data[key] != attrs[key]:
            return False
    return True
