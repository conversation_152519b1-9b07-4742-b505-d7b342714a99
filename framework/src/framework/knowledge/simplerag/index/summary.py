import os
from pathlib import Path
from typing import Callable, Any
import pandas as pd
from tenacity import retry, stop_after_attempt, retry_if_exception_type, wait_fixed
from concurrent.futures.thread import Thread<PERSON>oolExecutor
from concurrent.futures import wait, ALL_COMPLETED

from common import tools
from framework.models import BaseLL<PERSON>
from framework.prompt import PromptFactory
from .prompts import *

log = tools.get_logger()


def _callback(callback: Callable[[Any], Any] | None, content: Any):
    try:
        log.info(content)
        if not callback:
            return
        callback(content)
    except BaseException as e:
        log.warning('callback error.', e)


@retry(
    stop=stop_after_attempt(5),
    retry=retry_if_exception_type(BaseException),
    wait=wait_fixed(60),
    reraise=True,
)
def _call_llm(llm: BaseLLM, system_prompt: str) -> str:
    return llm.complete(input={'role': 'system', 'content': system_prompt}).content


async def summary_text_units(
        data_path: Path,
        llm: BaseLLM,
        pre_knowledge: str | None = None,
        callback: Callable[[Any], Any] | None = None
) -> pd.DataFrame:
    middle_text_units = pd.read_parquet(data_path)
    total = middle_text_units.shape[0]
    if total == 0:
        return pd.DataFrame()

    middle_text_units.reset_index(drop=True, inplace=True)
    _callback(callback, f'start summary total_size={total}')

    # 多线程，充分利用 CPU 计算，至少留 2 个 CPU，不然反复切换进程会导致系统吞吐量降低
    results = []
    use_cpu_count = os.cpu_count() or 1
    if use_cpu_count > 2:
        use_cpu_count -= 2
    with ThreadPoolExecutor(thread_name_prefix='create_text_unit_summaries_', max_workers=use_cpu_count) as t:
        features = []
        for batch in tools.batch_rows(data=middle_text_units, batch_count=use_cpu_count):
            _callback(callback, f'start summary batch_len={batch.shape[0]}')
            feature = t.submit(
                _do_summary,
                input=batch,
                llm=llm,
                pre_knowledge=pre_knowledge,
                callback=callback,
            )
            features.append(feature)
        wait(features, return_when=ALL_COMPLETED)
        for f in features:
            results.append(f.result())
    return pd.concat(results)


def _do_summary(
        input: pd.DataFrame,
        llm: BaseLLM,
        pre_knowledge: str | None = None,
        callback: Callable[[Any], Any] | None = None
) -> pd.DataFrame:
    total_count = input.shape[0]
    count = 0
    for idx, text_unit in input.iterrows():
        count += 1
        summary = text_unit['summary']
        if not (summary is None or pd.isna(summary) or len(summary) == 0):
            _callback(callback=callback, content=f'{count}/{total_count} document summary exists, skip.')
            continue
        try:
            text = text_unit["text"]
            log.info(f'{count}/{total_count} 总结文档内容：\n***\n{text}')
            response = _call_llm(
                llm=llm,
                system_prompt=PromptFactory.render_prompt(
                    SUMMARIZE_PROMPT,
                    background_knowledge=pre_knowledge,
                    document_content=text
                )
            )
            if response:
                _callback(
                    callback=callback,
                    content=f'{count}/{total_count}: index={idx}, in batch summary document. summary={response}'
                )
                input.at[idx, 'summary'] = response
        except BaseException as e:
            log.error('failed to summary text_unit', e)
            _callback(callback=callback, content=f'failed to summary text_unit, skip. {e}')

    return input
