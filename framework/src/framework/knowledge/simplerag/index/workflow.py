"""
简单 rag 的 index flow
只用到向量数据库存储
"""
import json
from pathlib import Path
from typing import Optional, List, Callable, Literal, Dict

import pandas as pd

from llama_index.core.workflow import Workflow, step, StartEvent, StopEvent
from llama_index.core.workflow.service import ServiceManager
from tenacity import retry, stop_after_attempt, retry_if_exception_type, wait_fixed

from common import tools
from framework.config import knowledge_config
from framework.vector_store import LarkVectorStore
from framework.data.document import SensorsDocMetadata, SensorsDocsMetadata
from framework.models import BaseLLM
from .summary import summary_text_units
from .topics import create_text_unit_topics
from .entity import extract_entities

from .schemas import (
    DocumentStepEvent, CreateBasicTextUnitStepEvent, SummaryTextUnitStepEvent,
    GetTopicsStepEvent, StoreStepEvent, SavedFiles, CreateEntitiesStepEvent,
)
from .vector_store import store_to_vector_db

log = tools.get_logger()


@retry(
    stop=stop_after_attempt(5),
    retry=retry_if_exception_type(BaseException),
    wait=wait_fixed(10),
    reraise=True,
)
def _call_llm(llm: BaseLLM, system_prompt: str) -> str:
    return llm.complete(input={'role': 'system', 'content': system_prompt}).content


class SimpleIndexRagWorkflow(Workflow):
    """
    知识库 index workflow
    对于需要首次处理的，清空 output 目录，并重新索引
    对于需要 resume 的，增量增加 document 和 text_unit，后面流程相同
    """

    def __init__(
            self,
            llm: BaseLLM,
            input_dir: Path,
            output_dir: Path,
            vector_store: LarkVectorStore,
            pre_knowledge: str,
            entity_types: Dict[str, str],
            additional_extract_topics: List[Callable[[str, BaseLLM], List[str]]] | None = None,
            columns_to_embed: List[str] = None,
            chunk_size: int = knowledge_config['sensors_knowledge_config']['graphrag_envs']['chunk_size'],
            chunk_overlap: int = knowledge_config['sensors_knowledge_config']['graphrag_envs']['chunk_overlap'],
            index_mode: Literal['reindex', 'resume'] = 'reindex',
            timeout: Optional[float] = 24 * 60 * 60,
            disable_validation: bool = False,
            verbose: bool = False,
            service_manager: Optional[ServiceManager] = None,
            num_concurrent_runs: Optional[int] = None,
            callback: Callable[[str], None] | None = None,
            **kwargs,
    ):
        """
        模式 index_mode 说明：reindex 表示全量重新索引；resume 表示增量索引，也会继续上一次的索引
        additional_extract_topics: 额外抽取的主题说明

        chunk_size <=0 表示不需要分块
        """
        super().__init__(
            timeout=timeout, disable_validation=disable_validation,
            verbose=verbose, service_manager=service_manager,
            num_concurrent_runs=num_concurrent_runs
        )
        self.entity_types = entity_types
        self.llm = llm
        self.input_path = input_dir
        self.output_dir = output_dir
        self.vector_store = vector_store
        self.columns_to_embed = columns_to_embed
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.pre_knowledge = pre_knowledge
        self.additional_extract_topics = additional_extract_topics
        self.callback = callback
        self.index_mode = index_mode
        self.saved_files = SavedFiles(output_path=self.output_dir)

    def _callback(self, content: str):
        try:
            log.info(content)
            if not self.callback:
                return
            self.callback(content)
        except BaseException as e:
            log.warning('callback error.', e)

    @property
    def reindex(self) -> bool:
        return self.index_mode == 'reindex'

    @property
    def resume(self) -> bool:
        return self.index_mode == 'resume'

    @property
    def metadata_path(self) -> Path:
        path = Path(self.input_path, 'metadata.json')
        if not path.exists():
            path.parent.mkdir(parents=True, exist_ok=True)
            metadata = SensorsDocsMetadata(batch_id=0, indexed_batch_id=0, documents=[])
            path.write_text(metadata.model_dump_json(indent=4))
        return path

    @property
    def input_document_files(self) -> List[Path]:
        docs = []
        for file in Path(self.input_path).glob('**/*'):
            if file.is_file() and file.match('*.txt'):
                docs.append(file)
        return docs

    def _check_and_fix_metadata(self):
        metadata_path = self.metadata_path
        data = None
        input_document_files = self.input_document_files
        if not metadata_path.exists():
            # 由于没有元数据文件，当做没有索引过，这里就必须重新索引
            self.index_mode = 'reindex'
            metadata_path.parent.mkdir(parents=True, exist_ok=True)
            data = SensorsDocsMetadata.model_validate({
                'batch_id': 1,
                'documents': [],
            })
            for file in input_document_files:
                doc = SensorsDocMetadata(
                    file_name=file.name,
                    title='',
                    batch_id=1,
                    content_sign=tools.md5(file.read_text(encoding='utf-8'))
                )
                data.upsert_doc(doc)
            metadata_path.write_text(data.model_dump_json(indent=4))
        else:
            data = self.data_meta

        # 校验是否还有没添加进去的文档
        documents = data.documents
        if not documents:
            documents = []
        has_not_include_docs = False
        for file in input_document_files:
            doc_exists = False
            for doc in documents:
                if doc.file_name == file.name:
                    doc_exists = True
                    break
            if not doc_exists:
                has_not_include_docs = True
                doc = SensorsDocMetadata(
                    file_name=file.name,
                    title='',
                    batch_id=data.batch_id + 1,
                    content_sign=tools.md5(file.read_text(encoding='utf-8'))
                )
                data.upsert_doc(doc)

        if has_not_include_docs:
            data.batch_id = data.batch_id + 1
        if self.index_mode == 'reindex':
            data.indexed_batch_id = 0
        self.metadata_path.write_text(data.model_dump_json(indent=4))
        self.max_batch_id = data.batch_id

    @property
    def data_meta(self) -> SensorsDocsMetadata:
        return SensorsDocsMetadata.model_validate(json.loads(self.metadata_path.read_text(encoding='utf-8')))

    @step
    async def first_start_build_work(self, event: StartEvent) -> DocumentStepEvent:
        """开始构建"""
        log.info(f'start workflow to build index. {event.dict()}')
        self._callback('开始索引文档')
        self._check_and_fix_metadata()

        # 将原来的输出数据备份
        if self.reindex and self.output_dir.exists():
            origin_path = str(self.output_dir)
            self.output_dir.rename(origin_path + '_backup_' + tools.uuid4_no_underline())
            self.output_dir = Path(origin_path)
            self.output_dir.mkdir()

        return DocumentStepEvent()

    @step
    async def create_document_tables(self, event: DocumentStepEvent) -> CreateBasicTextUnitStepEvent:
        self._callback('正在加载文档')

        data_meta = self.data_meta
        documents = data_meta.documents
        documents_dict = dict([(d.file_name, d) for d in documents])
        metadatas = []

        # load all documents
        for file in self.input_document_files:
            file_name = file.name
            file_meta = documents_dict.get(file_name, None)
            if file_meta is None:
                self._callback(f'skip file because of no metadata: {file_name}')
                continue

            metadata = file_meta.model_dump()
            content = file.read_text(encoding='utf-8')
            metadata['id'] = metadata['url']
            metadata['type'] = 'document'
            if not metadata['url']:
                # sha256 更长一些，冲突概率小一些
                metadata['id'] = tools.sha256(metadata['title'] if metadata['title'] else '' + content)
            metadata['raw_content'] = content
            metadatas.append(metadata)

        self._callback(f'共加载 {str(len(metadatas))} 篇文档')

        # for test, need delete
        # metadatas = metadatas[:10]

        input_docs = pd.DataFrame(metadatas)
        input_docs.reset_index(drop=True, inplace=True)
        input_docs.to_parquet(path=self.saved_files.final_documents)
        return CreateBasicTextUnitStepEvent(
            **(event.dict()),
        )

    @step
    async def create_basic_text_units(self, event: CreateBasicTextUnitStepEvent) -> SummaryTextUnitStepEvent:
        """使用 langchain 的 TextSplitter 来分割，效果更好一些"""
        log.info(f'start create basic text units. {event.dict()}')

        self._callback('正在进行文档分割')
        documents: pd.DataFrame = pd.read_parquet(self.saved_files.final_documents)
        documents.reset_index(drop=True, inplace=True)
        middle_text_units = pd.DataFrame(
            columns=['id', 'type', 'document_id', 'document_batch_id', 'text', 'summary', 'topics'],
            data=[]
        )
        if self.saved_files.final_text_units.exists():
            middle_text_units = pd.read_parquet(self.saved_files.final_text_units)

        new_text_units = []
        for idx, document in documents.iterrows():
            document_id = str(document['id'])
            batch_id = int(document['batch_id'])
            indexes = middle_text_units[(middle_text_units['document_id'] == document_id)].index.to_list()
            need_reindex = True
            if indexes:
                tmp_units = middle_text_units.loc[indexes]
                deleted_indexes = tmp_units[(tmp_units['document_batch_id'] < batch_id)].index.to_list()
                if not deleted_indexes:
                    need_reindex = False
                else:
                    # 删除该文档关联的所有 text_units，然后再重新索引
                    middle_text_units.drop(index=indexes)
            if not need_reindex:
                continue
            content = document['raw_content']
            doc_text_units = tools.split_text(
                text=content,
                chunk_size=self.chunk_size,
                chunk_overlap=self.chunk_overlap,
            )
            for unit in doc_text_units:
                unit_id = tools.uuid4_no_underline()
                unit = f"来源文档: {document['title']}\n来源链接：{document['url']}\n***\n{unit}"
                new_text_units.append({
                    'id': unit_id,
                    'type': 'text_unit',
                    'text': unit,
                    'document_id': document_id,
                    'document_batch_id': batch_id,
                })
        new_df = pd.DataFrame(new_text_units)
        middle_text_units = pd.concat([middle_text_units, new_df])
        middle_text_units.reset_index(drop=True, inplace=True)
        middle_text_units.to_parquet(path=self.saved_files.middle_text_units)

        return SummaryTextUnitStepEvent(
            **(event.dict()),
        )

    @step
    async def summary_text_units(self, event: SummaryTextUnitStepEvent) -> GetTopicsStepEvent:
        """文档摘要，对于失败了的 text_unit，需要重试"""
        self._callback('正在总结文档内容')
        log.info(f'start summary text units. {event.dict()}')
        middle_text_units = await summary_text_units(
            data_path=self.saved_files.middle_text_units,
            llm=self.llm,
            pre_knowledge=self.pre_knowledge,
            callback=self.callback
        )

        middle_text_units.reset_index(drop=True, inplace=True)
        middle_text_units.to_parquet(path=self.saved_files.middle_text_units)
        del middle_text_units
        return GetTopicsStepEvent(
            **(event.dict()),
        )

    @step
    async def create_final_topics(self, event: GetTopicsStepEvent) -> CreateEntitiesStepEvent:
        self._callback('开始解析文档主题')
        log.info(f'start create final topics. {event.dict()}')
        middle_text_units = await create_text_unit_topics(
            data_path=self.saved_files.middle_text_units,
            llm=self.llm,
            pre_knowledge=self.pre_knowledge,
            additional_extract_topics=self.additional_extract_topics,
            callback=self.callback
        )
        middle_text_units.reset_index(drop=True, inplace=True)
        middle_text_units.to_parquet(path=self.saved_files.middle_text_units)
        return CreateEntitiesStepEvent(
            **(event.dict()),
        )

    @step
    async def create_entities(self, event: CreateEntitiesStepEvent) -> StoreStepEvent:
        self._callback('开始抽取文档实体')
        log.info(f'start create final entities. {event.dict()}')
        middle_text_units, entities = await extract_entities(
            text_data_path=self.saved_files.middle_text_units,
            llm=self.llm,
            pre_knowledge=self.pre_knowledge,
            entity_types=self.entity_types,
            max_gleaning=2,
            callback=self.callback
        )

        # 保存成 final text unit
        entities.reset_index(drop=True, inplace=True)
        middle_text_units.reset_index(drop=True, inplace=True)
        entities.to_parquet(path=self.saved_files.final_entities)
        middle_text_units.to_parquet(path=self.saved_files.final_text_units)
        return StoreStepEvent(
            **(event.dict()),
        )

    @step
    async def store(self, event: StoreStepEvent) -> StopEvent:
        """save to vector store and graph database"""
        await self.store_vector_db(event)
        log.info('Full index created successfully')
        self._callback('索引完成.')

        if hasattr(self, 'max_batch_id'):
            self.data_meta.indexed_batch_id = self.max_batch_id
            self.metadata_path.write_text(self.data_meta.model_dump_json(indent=4), encoding='utf-8')

        return StopEvent()

    async def store_vector_db(self, event: StoreStepEvent) -> None:
        self._callback('正在导入 text_units 到数据库...')
        await store_to_vector_db(
            self.vector_store,
            final_text_units=self.saved_files.final_text_units,
            clear_all=not self.resume,
            columns_to_embed=self.columns_to_embed,
            callback=self._callback,
        )
        self._callback('正在导入 entities 到数据库...')
        await store_to_vector_db(
            self.vector_store,
            final_text_units=self.saved_files.final_entities,
            clear_all=False,
            callback=self._callback,
            columns_to_embed=['name', 'descriptions']
        )
        self._callback('导入完成')
