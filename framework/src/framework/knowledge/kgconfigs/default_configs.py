DEFAULT_ENTITY_TYPES = {
    'product': '产品，比如神策分析、神策智能运营等属于产品，产品可以有别名、英文名等其他称呼，都归为产品类别，其他非产品类别的实体请勿归到此类别',
    'function': '功能，表示产品拥有的功能或者功能拥有的子功能，比如神策分析中有功能事件分析，事件分析中有其他的子功能等。比如「上传素材」是一个功能，一个功能有可能会有一些缩写等，也都归为此类别',
    'service': '服务，表示公司对外提供的服务等，服务可能会有一些英文名、缩写等，都归为此类别',
    # 'customer': '客户，表示神策这家公司的客户，客户有时会会有简称等，客户及其简称都归为此类别',
    # 'organization': '组织，即企业、团体等实体组织，组织也会有简称等，也都归为此类别',
    # 'event': '表示点击页面、用户操作等动作。比如「点击」是一个动作',
    # 'sample': '代码示例、功能举例等等，一些示例类型的内容必须归属于 sample，示例需要尽量完整。',
    'schema': '表示设计模式，比如数据结构、设计规范等，也表示一些预定义的数据格式，比如预置的事件、属性等等',
    'others': '其他的不好归类的实体类型则归类到本类型中',
}
DEFAULT_RELATION_TYPES = {
    'has_function': '表示产品具有什么功能，或者功能具有什么子功能。',
    'has_schema': '表示某个功能具有什么样的数据结构、事件、属性等。',
    'has_service': '表示某个功能具有什么样的服务，或者某个产品有什么样的服务等。',
    'alias': '表示实体的别名，实体的别名是相互的，即 A alias B，则也需要一个 B alias A',
}

ENTITY_EXTRACTION_USER_PROMPT = """
Document:
<|DOCUMENT_START|>
__DOCUMENT__
<|DOCUMENT_END|>

EntityTypes:
```
ENTITY_TYPES_AND_DESCRIPTIONS
```

RelationTypes:
```
RELATION_TYPES_AND_DESCRIPTIONS
```

Thought:
"""
