# Copyright (c) 2024 Microsoft Corporation.
# Licensed under the MIT License

"""A module containing create_community_reports and load_strategy methods definition."""
import json

import pandas as pd
from openai import RateLimitError
from tenacity import retry, stop_after_attempt, retry_if_exception_type, wait_fixed

from common import tools
from framework.models import BaseLLM
from . import schemas
from .prep_community_report_context import prep_community_report_context
from .typing import (
    CommunityReport,
    CommunityReportsStrategy,
    StrategyConfig,
)
from .utils import get_levels
from common.tools import extract_code_from_text

log = tools.get_logger()


async def summarize_communities(
        local_contexts,
        nodes,
        community_hierarchy,
        llm: BaseLLM,
        prompt: str,
        strategy: dict,
        num_threads: int = 4,
        max_input_length: int = 8000,
):
    """Generate community summaries."""
    levels = get_levels(nodes)
    log.info(f'community levels: {levels}')
    reports: list[CommunityReport | None] = []
    runner = _run_graph_intelligence

    for level in levels:
        level_contexts = prep_community_report_context(
            pd.DataFrame(reports),
            local_context_df=local_contexts,
            community_hierarchy_df=community_hierarchy,
            level=level,
            max_tokens=strategy.get(
                "max_input_tokens", max_input_length
            ),
        )

        async def run_generate(record):
            result = await _generate_report(
                runner=runner,
                llm=llm,
                prompt=prompt,
                strategy=strategy,
                community_id=record[schemas.NODE_COMMUNITY],
                community_level=record[schemas.COMMUNITY_LEVEL],
                community_context=record[schemas.CONTEXT_STRING],
            )
            return result

        log.info(f'community level = {level}, communities count = {level_contexts.shape[0]}')
        for index, row in level_contexts.iterrows():
            if row['community'] >= 0:
                reports.append(await run_generate(row))

    return pd.DataFrame(reports)


async def _generate_report(
        runner: CommunityReportsStrategy,
        llm: BaseLLM,
        prompt: str,
        strategy: dict,
        community_id: int,
        community_level: int,
        community_context: str,
) -> CommunityReport | None:
    """Generate a report for a single community."""
    return await runner(
        community_id, community_context, community_level, llm, prompt, strategy
    )


@retry(
    stop=stop_after_attempt(10),
    retry=retry_if_exception_type(RateLimitError),
    wait=wait_fixed(60),
    reraise=True,
)
async def _run_graph_intelligence(
        community: str | int,
        input: str,
        level: int,
        llm: BaseLLM,
        prompt: str,
        args: StrategyConfig,
) -> CommunityReport | None:
    prompt = prompt.replace('THIS_IS_TEXT_INPUT', input)
    history = [
        {'role': 'system', 'content': prompt}
    ]
    response = await llm.acomplete(
        input=history,
        stream=False,
    )
    response = response.content
    report = extract_code_from_text(response)
    try:
        report = json.loads(report)
    except BaseException as e:
        log.error(f'get community report error. prompt={prompt}, content={response}', e)
        return None

    final_report: CommunityReport = CommunityReport(
        community=community,
        level=level,
        rank=_parse_rank(report),
        rank_explanation=report['rating_explanation'],
        full_content=_get_text_output(report),
        full_content_json=json.dumps(report, ensure_ascii=False),
        **report,
    )
    return final_report


def _get_text_output(parsed_output: dict) -> str:
    title = parsed_output.get("title", "Report")
    summary = parsed_output.get("summary", "")
    findings = parsed_output.get("findings", [])

    def finding_summary(finding: dict):
        if isinstance(finding, str):
            return finding
        return finding.get("summary")

    def finding_explanation(finding: dict):
        if isinstance(finding, str):
            return ""
        return finding.get("explanation")

    report_sections = "\n\n".join(
        f"## {finding_summary(f)}\n\n{finding_explanation(f)}" for f in findings
    )
    return f"# {title}\n\n{summary}\n\n{report_sections}"


def _parse_rank(report: dict) -> float:
    rank = report.get("rating", -1)
    try:
        return float(rank)
    except ValueError:
        log.exception("Error parsing rank: %s defaulting to -1", rank)
        return -1
