# Copyright (c) 2024 Microsoft Corporation.
# Licensed under the MIT License

"""A module containing embeddings values."""

entity_embedding = "entity"
relationship_description_embedding = "relationship.description"
document_raw_content_embedding = "document.raw_content"
community_embedding = "community"
text_unit_text_embedding = "text_unit"

all_embeddings: set[str] = {
    entity_embedding,
    relationship_description_embedding,
    document_raw_content_embedding,
    community_embedding,
    text_unit_text_embedding,
}

# 需要存到 vector db 的包括：实体及描述、社区及报告、文本单元信息
required_embeddings: set[str] = {
    entity_embedding,
    community_embedding,
    text_unit_text_embedding,
}

embedding_column_suffix = '_embedding'
embedding_store_column_suffix = '_embedding_store'


