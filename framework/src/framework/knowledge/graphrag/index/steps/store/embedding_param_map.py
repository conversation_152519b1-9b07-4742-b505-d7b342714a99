import json
from pathlib import Path
from typing import Dict, List

import pandas as pd
from framework.knowledge.graphrag.index.steps.store.store_consts import (
    community_embedding,
    document_raw_content_embedding,
    entity_embedding,
    relationship_description_embedding,
    text_unit_text_embedding,
)


def embedding_param_map(
        data_frames: Dict[str, pd.DataFrame],
        data_paths: Dict[str, Path],
) -> Dict:
    final_documents = data_paths.get('final_documents', None)
    final_relationships = data_paths.get('final_relationships', None)
    final_text_units = data_paths.get('final_text_units', None)
    final_entities = data_paths.get('final_entities', None)
    final_nodes = data_paths.get('final_nodes', None)
    final_communities = data_paths.get('final_communities', None)
    final_community_reports = data_paths.get('final_community_reports', None)

    community_and_reports = None
    if final_community_reports is not None and final_communities is not None:
        community_and_reports = pd.merge(
            data_frames['final_communities'][
                ['id', 'community', 'level', 'human_readable_id', 'entity_ids', 'relationship_ids', 'text_unit_ids',
                 'period']],
            data_frames['final_community_reports'][
                ['community', 'title', 'summary', 'full_content', 'rank', 'rank_explanation', 'findings', 'size']],
            how='inner',
            on='community'
        )

    entities = None
    if final_entities is not None and final_nodes is not None:
        entities = pd.merge(
            data_frames['final_nodes'][['id', 'title', 'community', 'type', 'description', 'level']],
            data_frames['final_entities'][['id', 'text_unit_ids']],
            how='inner',
            on='id'
        )
        entities = entities.rename(columns={'type': 'entity_type', 'title': 'entity_name'})

    result = {
        document_raw_content_embedding: {
            "data": data_frames['final_documents'].loc[:, ["id", "url", "raw_content"]]
            if final_documents is not None
            else None,
            "columns_to_embed": ["raw_content"],
            "data_file": final_documents
        },
        relationship_description_embedding: {
            "data": data_frames['final_relationships'].loc[:, ["id", "description"]]
            if final_relationships is not None
            else None,
            "columns_to_embed": ["description"],
            "data_file": final_relationships
        },
        text_unit_text_embedding: {
            "data": data_frames['final_text_units'].loc[:, ["id", "text", "document_ids"]]
            if final_text_units is not None
            else None,
            "columns_to_embed": ["text", "document_ids"],
            "data_file": final_text_units
        },
        entity_embedding: {
            "data": entities
            if entities is not None
            else None,
            "columns_to_embed": ["entity_name", "description"],
            "data_file": final_entities
        },
        # community 也一并加入关键问题 findings
        community_embedding: {
            "data": community_and_reports[[
                "id", "full_content", "summary", "title", "community", "level", "rank", "rank_explanation", "findings",
                "entity_ids", "relationship_ids", "text_unit_ids"
            ]].assign(
                findings=lambda x: _get_findings_summaries(x)
            )
            if community_and_reports is not None
            else None,
            "columns_to_embed": ["title", "summary", "full_content", "findings"],
            "data_file": final_community_reports
        },
    }

    return result


def _get_findings_summaries(data: pd.DataFrame) -> List[str | List[str]]:
    result = []
    for idx, row in data.iterrows():
        findings = row['findings']
        if isinstance(findings, str):
            findings = json.loads(findings)
        result.append([f['summary'] for f in findings])
    return result
