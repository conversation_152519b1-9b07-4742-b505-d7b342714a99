from pathlib import Path
from typing import List, Dict
from common import tools as my_tools
import pandas as pd
from tenacity import retry, stop_after_attempt, retry_if_exception_type, wait_fixed

from framework.graph_store import LarkGraphStore, GraphData, Vertex, Edge, GraphLabelValue

log = my_tools.get_logger()


def _fix_special_chars(text: str) -> str:
    """去除字段前后的双引号、单引号，替换转义字符等"""
    if not text:
        return text
    text = text.replace('\\', '\\\\')
    text = text.replace('"', '\\"')
    return text


def _do_callback(callback=None, msg: str = None):
    if callback and msg:
        try:
            callback(msg)
        except:
            log.warning('do callback error.')


@retry(
    stop=stop_after_attempt(3),
    retry=retry_if_exception_type(BaseException),
    wait=wait_fixed(10),
    reraise=True,
)
async def store_to_graph_db(
        graph_store: LarkGraphStore,
        final_documents: Path | None,
        final_relationships: Path | None,
        final_text_units: Path | None,
        final_entities: Path | None,
        final_nodes: Path | None,
        final_community_reports: Path | None,
        final_communities: Path | None,
        entity_types: Dict[str, str],
        clear_all: bool = False,
        callback=None,
) -> None:
    _do_callback(callback, '正在加载数据文件...')
    data_frames = {
        'final_documents': pd.read_parquet(final_documents) if final_documents else None,
        'final_relationships': pd.read_parquet(final_relationships) if final_relationships else None,
        'final_text_units': pd.read_parquet(final_text_units) if final_text_units else None,
        'final_entities': pd.read_parquet(final_entities) if final_entities else None,
        'final_nodes': pd.read_parquet(final_nodes) if final_nodes else None,
        'final_community_reports': pd.read_parquet(final_community_reports) if final_community_reports else None,
        'final_communities': pd.read_parquet(final_communities) if final_communities else None,
    }
    if clear_all:
        _do_callback(callback, '正在清除旧数据...')
        graph_store.clear_all_data()

    _do_callback(callback, '正在加载 Vertexes & Relations...')
    graph_data = GraphData(
        vertexes=_get_vertex(entity_types=entity_types, **data_frames),
        edges=_get_edges(graph_store=graph_store, **data_frames),
    )

    _do_callback(callback, f'开始插入新数据。节点数：{str(len(graph_data.vertexes))}，关系数：{str(len(graph_data.edges))}')
    graph_store.upsert_graph_data(data=graph_data)
    _do_callback(callback, '数据插入完成')


def _get_vertex(
        entity_types: Dict[str, str],
        final_documents: pd.DataFrame | None,
        final_text_units: pd.DataFrame | None,
        final_nodes: pd.DataFrame | None,
        final_community_reports: pd.DataFrame | None,
        final_communities: pd.DataFrame | None,
        **kwargs,
) -> List[Vertex]:
    entity_label_names = set(entity_types.keys())
    all_vertex = []
    if final_documents is not None:
        for idx, row in final_documents.iterrows():
            v = Vertex(
                id=_fix_special_chars(row['id']),
                values=[GraphLabelValue(label_name='document', properties={
                    'url': _fix_special_chars(row['url']),
                    'name': _fix_special_chars(row['title']),
                })]
            )
            all_vertex.append(v)

    if final_text_units is not None:
        for idx, row in final_text_units.iterrows():
            v = Vertex(
                id=row['id'],
                values=[GraphLabelValue(label_name='text_unit', properties={
                    'n_tokens': row['n_tokens'],
                    'human_readable_id': row['human_readable_id'],
                    'content': _fix_special_chars(row['text']),
                })]
            )
            all_vertex.append(v)

    if final_community_reports is not None and final_communities is not None:
        community_and_reports = pd.merge(
            final_communities[['community', 'level', 'human_readable_id']],
            final_community_reports[[
                'community', 'title', 'summary', 'full_content', 'rank',
                'rank_explanation'
            ]],
            how='inner',
            on='community'
        )
        for idx, row in community_and_reports.iterrows():
            community_id = 'community_' + str(row['community'])
            v = Vertex(
                id=community_id,
                values=[GraphLabelValue(label_name='community', properties={
                    'human_readable_id': row['human_readable_id'],
                    'community': row['community'],
                    'level': row['level'],
                    'name': community_id if pd.isna(row['title']) else _fix_special_chars(row['title']),
                    'summary': 'None' if pd.isna(row['summary']) else _fix_special_chars(row['summary']),
                    'rank': 0 if pd.isna(row['rank']) else row['rank'],
                    'rank_explanation': 'None' if pd.isna(row['rank_explanation']) else _fix_special_chars(
                        row['rank_explanation']),
                    'full_content': 'None' if pd.isna(row['full_content']) else _fix_special_chars(
                        row['full_content']),
                })]
            )
            all_vertex.append(v)

    if final_nodes is not None:
        for idx, row in final_nodes.iterrows():
            label_name = row['type'].lower()
            if not label_name or label_name not in entity_label_names:
                label_name = 'others'

            name = _fix_special_chars(row['title'])
            v = Vertex(
                id=row['id'],
                values=[GraphLabelValue(label_name=label_name, properties={
                    'human_readable_id': row['human_readable_id'],
                    'name': name,
                    'description': _fix_special_chars(row['description']),
                    'level': row['level'],
                    'degree': row['degree'],
                })]
            )
            all_vertex.append(v)

    return all_vertex


def _get_edges(
        graph_store: LarkGraphStore,
        final_relationships: pd.DataFrame | None,
        final_text_units: pd.DataFrame | None,
        final_entities: pd.DataFrame | None,
        final_community_reports: pd.DataFrame | None,
        final_communities: pd.DataFrame | None,
        **kwargs,
) -> List[Edge]:
    edge_schemas = graph_store.get_schema().edge_labels
    label_names = {_.label_name for _ in edge_schemas}
    all_edges = []

    # text_unit 是从哪个 document 分割的，实体是从哪个 text_unit 抽取的
    if final_text_units is not None:
        for idx, row in final_text_units.iterrows():
            text_unit_id = row['id']

            document_ids = row['document_ids']
            if document_ids is not None:
                for document_id in document_ids:
                    e = Edge(
                        rank=0,
                        src_vid=text_unit_id,
                        dst_vid=document_id,
                        values=[GraphLabelValue(label_name='chunk_from', properties={})]
                    )
                    all_edges.append(e)

            entity_ids = row['entity_ids']
            if entity_ids is not None:
                for entity_id in entity_ids:
                    e = Edge(
                        rank=0,
                        src_vid=entity_id,
                        dst_vid=text_unit_id,
                        values=[GraphLabelValue(label_name='extracted_from', properties={})]
                    )
                    all_edges.append(e)

    # 不同实体之间的关系
    if final_relationships is not None and final_entities is not None:
        entities_and_relations = pd.merge(
            final_relationships,
            final_entities.loc[:, ['title', 'id']].rename(columns={'title': 'source', 'id': 'source_id'}),
            how='left',
            on='source'
        )
        entities_and_relations = pd.merge(
            entities_and_relations,
            final_entities.loc[:, ['title', 'id']].rename(columns={
                'title': 'target',
                'id': 'target_id',
            }),
            how='left',
            on='target'
        )
        for idx, row in entities_and_relations.iterrows():
            if pd.isna(row['source_id']) or pd.isna(row['target_id']):
                continue
            inner_label_name = row['relation_type'].lower()
            if inner_label_name not in label_names:
                inner_label_name = 'relation_to'
            e = Edge(
                rank=row['weight'],
                src_vid=row['source_id'],
                dst_vid=row['target_id'],
                values=[GraphLabelValue(
                    label_name=inner_label_name,
                    properties={
                        'human_readable_id': row['human_readable_id'],
                        'description': 'None' if pd.isna(row['description']) else _fix_special_chars(
                            row['description']),
                        'weight': row['weight'],
                        'combined_degree': row['combined_degree'],
                    })]
            )
            all_edges.append(e)

    # community 链接到哪些 text_unit
    if final_community_reports is not None and final_communities is not None:
        community_and_reports = pd.merge(final_communities, final_community_reports, how='inner', on='community')
        for idx, row in community_and_reports.iterrows():
            community_id = 'community_' + str(row['community'])
            text_unit_ids = row['text_unit_ids']
            if text_unit_ids is None:
                continue
            flat_text_unit_ids = set()
            for text_unit_id in text_unit_ids:
                split_ids = text_unit_id.split(',')
                flat_text_unit_ids.update(_ for _ in split_ids)
            for text_unit_id in flat_text_unit_ids:
                e = Edge(
                    rank=0,
                    src_vid=community_id,
                    dst_vid=text_unit_id.strip(),
                    values=[GraphLabelValue(label_name='link_text_unit_to', properties={})]
                )
                all_edges.append(e)

    # community 包含哪些 entity
    if final_communities is not None and final_entities is not None:
        entities = final_entities.loc[:, ['id', 'type']].rename(columns={'id': 'entity_id', 'type': 'entity_type'})
        communities = final_communities.loc[:, ['community', 'entity_ids']].rename(
            columns={'community': 'community_id', 'entity_ids': 'entity_id'}
        ).explode("entity_id")
        entities = pd.merge(entities, communities, how='inner', on='entity_id')
        for idx, row in entities.iterrows():
            entity_type = row['entity_type'].lower()
            if not entity_type:
                entity_type = 'others'
            community_id = 'community_' + str(row['community_id'])
            inner_label_name = 'has_entity_' + entity_type
            if inner_label_name not in label_names:
                inner_label_name = 'has_entity'
            e = Edge(
                rank=0,
                src_vid=community_id,
                dst_vid=row['entity_id'],
                values=[GraphLabelValue(
                    label_name=inner_label_name,
                    properties={})]
            )
            all_edges.append(e)

    return all_edges
