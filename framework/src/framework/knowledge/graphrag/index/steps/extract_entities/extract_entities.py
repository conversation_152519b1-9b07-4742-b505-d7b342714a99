import html, re
import json
import os
from concurrent.futures import ThreadPoolExecutor, wait, ALL_COMPLETED
from typing import List, Any, Dict, Tuple
from collections.abc import Mapping

import networkx as nx
import pandas as pd
from tenacity import retry, stop_after_attempt, retry_if_exception_type, wait_fixed

from common import tools
from framework.knowledge.kgconfigs import ENTITY_EXTRACTION_USER_PROMPT
from framework.models import BaseLLM

log = tools.get_logger()


def _do_callback(callback=None, msg: str = None):
    try:
        log.info(msg)
        if callback:
            callback(msg)
    except:
        log.warning('do callback error.')


async def extract_entities(
        input: pd.DataFrame,  # text_units
        llm: BaseLLM,
        text_column: str,
        prompt: str,
        entity_types: Dict[str, str],
        relation_types: Dict[str, str],
        max_gleaning: int = 2,
        callback=None,
) -> tuple[pd.DataFrame, list[nx.Graph]]:
    log.info('start extract entities.')
    input.reset_index(drop=True, inplace=True)

    #  for test need delete
    # input = input.iloc[0:10, :]

    # 多线程，充分利用 CPU 计算，至少留 2 个 CPU，不然反复切换进程会导致系统吞吐量降低
    _do_callback(callback, f'start extract entities and relations total_size={input.shape[0]}')
    results = []
    use_cpu_count = os.cpu_count() or 1
    if use_cpu_count > 2:
        use_cpu_count -= 2
    with ThreadPoolExecutor(thread_name_prefix='create_text_unit_summaries_', max_workers=use_cpu_count) as t:
        features = []
        for batch in tools.batch_rows(data=input, batch_count=use_cpu_count):
            _do_callback(callback, f'start extract entities and relations batch_len={batch.shape[0]}')
            feature = t.submit(
                _do_extract_entities,
                all_data_input=input,
                input=batch,
                llm=llm,
                text_column=text_column,
                prompt=prompt,
                entity_types=entity_types,
                relation_types=relation_types,
                max_gleaning=max_gleaning,
                callback=callback,
            )
            features.append(feature)
        wait(features, return_when=ALL_COMPLETED)
        for f in features:
            results.append(f.result())
        graphs = []
        for result in results:
            graphs.extend(result[1])
        inputs = pd.concat([_[0] for _ in results])
        inputs.reset_index(drop=True, inplace=True)
        return inputs, graphs


def _do_extract_entities(
        all_data_input: pd.DataFrame,
        input: pd.DataFrame,
        llm: BaseLLM,
        text_column: str,
        prompt: str,
        entity_types: Dict[str, str],
        relation_types: Dict[str, str],
        max_gleaning: int = 2,
        callback=None,
) -> tuple[pd.DataFrame, list[nx.Graph]]:
    input = input.copy()
    graphs = []
    entities = []
    llm_entities = []
    total_size = all_data_input.shape[0]
    columns = set(input.columns)

    for row_idx, row in input.iterrows():
        llm_entity, graph, _entity = None, None, None
        if not isinstance(row_idx, int):
            row_idx = int(row_idx)
        try:
            # 已经抽取过了，不再抽取
            if 'llm_entity' in columns and not pd.isna(row['llm_entity']):
                _do_callback(callback=callback, msg=f'entities and relations exists, skip.')
                llm_entity, graph, _entity = _process_results_from_records(
                    all_data_input=all_data_input,
                    source_doc_id=row_idx,
                    extracted_records=json.loads(row['llm_entity']),
                )
            else:
                _do_callback(
                    callback=callback,
                    msg=f'extract entities of text units: {str(row_idx)}/{str(total_size)}'
                )
                llm_entity, graph, _entity = _do_extract_entity(
                    all_data_input=all_data_input,
                    row=row,
                    index=row_idx,
                    llm=llm,
                    text_column=text_column,
                    prompt=prompt,
                    entity_types=entity_types,
                    relation_types=relation_types,
                    max_gleaning=max_gleaning,
                    callback=callback,
                )
        except BaseException as e:
            log.error(f'extract entity error. [index={str(row_idx)}]', e)

        llm_entity = json.dumps(llm_entity, ensure_ascii=False) if llm_entity else None
        llm_entities.append(llm_entity)
        entities.append(_entity)
        graphs.append(graph)

    input['llm_entity'] = llm_entities
    input['entities'] = entities
    return input, graphs


@retry(
    stop=stop_after_attempt(10),
    retry=retry_if_exception_type(BaseException),
    wait=wait_fixed(60),
    reraise=True,
)
def _do_extract_entity(
        all_data_input: pd.DataFrame,
        row: pd.Series,
        index: int,
        llm: BaseLLM,
        text_column: str,
        prompt: str,
        entity_types: Dict[str, str],
        relation_types: Dict[str, str],
        max_gleaning: int = 2,
        callback=None,
):
    text = row[text_column]
    response: Tuple[List[Dict], List[Dict]] | None = _do_extract_text_entities(
        input=text,
        llm=llm,
        prompt=prompt,
        entity_types=entity_types,
        relation_types=relation_types,
        max_gleaning=max_gleaning,
        callback=callback,
    )
    if not response or not response[0]:
        return None, None, None
    records = {
        'entities': response[0],
        'relations': response[1] if response[1] else [],
    }
    return _process_results_from_records(
        all_data_input=all_data_input,
        source_doc_id=index,
        extracted_records=records,
    )


def _process_results_from_records(
        all_data_input: pd.DataFrame,
        source_doc_id: int,
        extracted_records: Dict[str, List],
):
    graph = _process_results(
        source_doc_id=source_doc_id,
        extracted_data=extracted_records,
        join_descriptions=True,
    )

    # Map the "source_id" back to the "id" field
    for _, node in graph.nodes(data=True):  # type: ignore
        if node is not None:
            node["source_id"] = ",".join(
                all_data_input.loc[int(id), 'id'] for id in node["source_id"].split(",")
            )

    for _, _, edge in graph.edges(data=True):  # type: ignore
        if edge is not None:
            edge["source_id"] = ",".join(
                all_data_input.loc[int(id), 'id'] for id in edge["source_id"].split(",")
            )

    return extracted_records, graph, [
        ({"name": item[0], **(item[1] or {})})
        for item in graph.nodes(data=True)
        if item is not None
    ]


def _get_field_without_quote(text: str) -> str:
    """去除字段前后的双引号、单引号"""
    if not text:
        return text
    if text.startswith('"') and text.endswith('"'):
        return text[1:-1]
    if text.startswith("'") and text.endswith("'"):
        return text[1:-1]
    return text


def _clean_str(input: Any) -> str:
    """Clean an input string by removing HTML escapes, control characters, and other unwanted characters."""
    # If we get non-string input, just give it back
    if not isinstance(input, str):
        return input

    result = html.unescape(input.strip())
    # https://stackoverflow.com/questions/4324790/removing-control-characters-from-a-string-in-python
    return _get_field_without_quote(re.sub(r"[\x00-\x1f\x7f-\x9f]", "", result))


def _unpack_descriptions(data: Mapping) -> list[str]:
    value = data.get("description", None)
    return [] if value is None else value.split("\n")


def _unpack_source_ids(data: Mapping) -> list[str]:
    value = data.get("source_id", None)
    return [] if value is None else value.split(", ")


def _process_results(
        source_doc_id: int,
        extracted_data: Dict[str, List],
        join_descriptions: bool = True,
) -> nx.Graph:
    graph = nx.Graph()
    entities = extracted_data['entities']
    relations = extracted_data['relations']
    for entity in entities:
        graph.add_node(
            entity['entity'],
            type=entity.get('type', 'others'),
            description=entity.get('description', 'None'),
            source_id=str(source_doc_id),
        )
    for relation in relations:
        source_entity = relation['source_entity']
        target_entity = relation['target_entity']
        if source_entity not in graph.nodes() or target_entity not in graph.nodes():
            log.warning(f'edge {source_entity} -> {target_entity} skip because of no such entity.')
            continue
        relation_type = relation.get('relation_type', None)
        description = relation.get('relation_description', '')
        weight = relation.get('relation_strength', 1)
        if graph.has_edge(source_entity, target_entity):
            edge_data = graph.get_edge_data(source_entity, target_entity)
            if edge_data is not None:
                weight += edge_data["weight"]
                if join_descriptions:
                    description = "\n".join(
                        list({
                            *_unpack_descriptions(edge_data),
                            description,
                        })
                    )
            if not relation_type:
                relation_type = edge_data.get('relation_type', None)
        graph.add_edge(
            source_entity,
            target_entity,
            relation_type=relation_type,
            description=description,
            weight=weight,
            source_id=str(source_doc_id),
        )
    return graph


def _extract_entity_and_relations(response: str) -> Tuple[List[Dict], List[Dict]] | None:
    """return entities / relations"""
    response_entities_token = 'ResponseEntities:'
    response_relations_token = 'ResponseRelations:'
    if response_entities_token not in response:
        raise RuntimeError(f'No {response_entities_token} in reply.')
    entities_text = response.split(response_entities_token)[1]
    relations_text = ''
    if response_relations_token in entities_text:
        text_split = entities_text.split(response_relations_token)
        entities_text = text_split[0]
        relations_text = text_split[1]
    entities_text = tools.extract_code_from_text(entities_text)
    relations_text = tools.extract_code_from_text(relations_text)
    if not entities_text.strip():
        return [], []
    entities = json.loads(entities_text)

    # check entity
    for e in entities:
        if not e['entity'] or not e['type'] or not e['description']:
            raise RuntimeError('Entity format error.')

    relations = []
    if relations_text.strip():
        relations = json.loads(relations_text)
    for r in relations:
        if not r['source_entity'] or not r['target_entity'] or not r['relation_type'] or not r['relation_description']:
            raise RuntimeError('Relation format error.')

    return entities, relations


def _do_extract_text_entities(
        input: str,
        llm: BaseLLM,
        prompt: str,
        entity_types: Dict[str, str],
        relation_types: Dict[str, str],
        max_gleaning: int = 2,
        callback=None,
) -> Tuple[List[Dict], List[Dict]] | None:
    loop_prompt = '可能还有一些实体和关系被遗漏了，请按照要求的格式回复被遗留的实体和关系，如果没有实体和关系被遗漏，则将它们回复空列表即可：\nThought:'

    user_prompt = ENTITY_EXTRACTION_USER_PROMPT.replace(
        '__DOCUMENT__', input
    ).replace(
        'ENTITY_TYPES_AND_DESCRIPTIONS', '\n'.join(['  - ' + k + ': ' + v for k, v in entity_types.items()])
    ).replace(
        'RELATION_TYPES_AND_DESCRIPTIONS', '\n'.join(['  - ' + k + ': ' + v for k, v in relation_types.items()])
    )
    history = [
        {'role': 'system', 'content': prompt},
        {'role': 'user', 'content': user_prompt}
    ]
    result = ([], [])
    for i in range(max_gleaning):
        cur_result, extracted_result = _do_extract_text_entities0(llm=llm, history=history)
        log.info(f'before entity_response:\n{cur_result}')
        if extracted_result is None:
            raise RuntimeError(f'llm got error response. response={cur_result}')
        log.info(f'after entity_response:\n{extracted_result[0]}\n{extracted_result[1]}')
        if not extracted_result or not extracted_result[0]:
            break
        result[0].extend(extracted_result[0])
        if extracted_result[1]:
            result[1].extend(extracted_result[1])

        if i == max_gleaning - 1:
            break
        history.append({'role': 'assistant', 'content': cur_result})
        history.append({'role': 'user', 'content': loop_prompt})

    # 修复部分
    if result[0]:
        result = _fix_unreasonable_relations(llm=llm, entities=result[0], relations=result[1], history=history)

    _do_callback(
        callback=callback,
        msg=f'entities and relations extracted. entities_count={len(result[0])}, relations_count={len(result[1])}'
    )
    return result


def _fix_unreasonable_relations(
        llm: BaseLLM,
        entities: List[Dict[str, Any]],
        relations: List[Dict[str, Any]],
        history: List[Dict[str, Any]],
) -> Tuple[List[Dict], List[Dict]] | None:
    fix_prompt = '已收到你的回复，请按照格式补全 Entity：\nThought: 当前缺少 entity: {entities} 的 entity 信息，我需要从文档中提取和补充'
    entity_names = [entity['entity'] for entity in entities]
    relation_entities = []
    if relations:
        relation_entities.extend([e['source_entity'] for e in relations])
        relation_entities.extend([e['target_entity'] for e in relations])
    else:
        relations = []
    fixed_entities = []
    for e in relation_entities:
        if not e in entity_names:
            fixed_entities.append(e)

    if not fixed_entities:
        return entities, relations

    fix_prompt = fix_prompt.format(entities=fixed_entities)
    history.append({'role': 'user', 'content': fix_prompt})
    cur_result, extracted_result = _do_extract_text_entities0(llm=llm, history=history)
    if not extracted_result:
        return _remove_unreasonable_relations(entities, relations)
    if extracted_result[0]:
        entities.extend(extracted_result[0])
    if extracted_result[1]:
        relations.extend(extracted_result[1])

    # 还是不行，就把对应的关系去掉
    return _remove_unreasonable_relations(entities, relations)


def _remove_unreasonable_relations(
        entities: List[Dict[str, Any]],
        relations: List[Dict[str, Any]]
) -> Tuple[List[Dict], List[Dict]]:
    if not relations:
        return entities, relations
    entity_names = [entity['entity'] for entity in entities]
    remove_relations = []
    for relation in relations:
        if relation['source_entity'] not in entity_names or relation['target_entity'] not in entity_names:
            remove_relations.append(relation)
    for e in remove_relations:
        relations.remove(e)
    return entities, relations


def _do_extract_text_entities0(
        llm: BaseLLM,
        history: List[Dict[str, Any]],
):
    error_prompt = '你回复的内容存在错误：{error}\n请修复后重新回复：\nThought:'
    history = history.copy()
    # 回复错误重试
    for i in range(10):
        cur_result = _llm_completion(llm, history)
        log.info(f'before entity_response:\n{cur_result}')
        try:
            extracted_result = _extract_entity_and_relations(cur_result)
            return cur_result, extracted_result
        except BaseException as e:
            log.warning(f'response error. response={cur_result}')
            history.append({'role': 'user', 'content': error_prompt.format(error=str(e))})


def _llm_completion(
        llm: BaseLLM,
        messages: List
) -> str:
    response = llm.complete(input=messages)
    result = response.content
    # log.info(f'llm generate: history={messages}, result={result}')
    if not isinstance(result, str):
        raise RuntimeError('llm generate result is not string')
    return result
