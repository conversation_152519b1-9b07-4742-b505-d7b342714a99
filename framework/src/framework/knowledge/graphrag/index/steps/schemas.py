import time
from pathlib import Path
from typing import Dict

import pandas as pd
from llama_index.core.workflow import Event

from framework.graph_store.base import LABEL_ID_PROPERTY_METADATA, GraphDataType


class SavedFiles:
    def __init__(self, output_path: Path):
        self.final_documents = output_path.joinpath('create_final_documents.parquet')
        self.final_text_units = output_path.joinpath('create_final_text_units.parquet')
        self.final_entities = output_path.joinpath('create_final_entities.parquet')
        self.final_nodes = output_path.joinpath('create_final_nodes.parquet')
        self.final_relationships = output_path.joinpath('create_final_relationships.parquet')
        self.final_communities = output_path.joinpath('create_final_communities.parquet')
        self.final_community_reports = output_path.joinpath('final_community_reports.parquet')

        self.entities_graph = output_path.joinpath('entities_graph.xml')
        self.entities_summarized_graph = output_path.joinpath('entities_summarized_graph.xml')


class BaseEvent(Event):
    def __init__(self, start_time: int = time.time(), **kwargs):
        super().__init__(start_time=start_time, **kwargs)


class DocumentStepEvent(BaseEvent):
    """start to load documents"""

    def __init__(self, start_time: int = time.time(), **kwargs):
        super().__init__(**kwargs)


class CreateBasicTextUnitStepEvent(BaseEvent):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class CreateBaseEntityGraphStepEvent(BaseEvent):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class CreateFinalEntitiesStepEvent(BaseEvent):
    def __init__(
            self,
            base_entities: pd.DataFrame,
            **kwargs
    ):
        super().__init__(
            base_entities=base_entities,
            **kwargs
        )


class CreateFinalNodesStepEvent(BaseEvent):
    def __init__(
            self,
            base_entities: pd.DataFrame,
            **kwargs
    ):
        super().__init__(
            base_entities=base_entities,
            **kwargs
        )


class CreateFinalCommunitiesStepEvent(BaseEvent):
    def __init__(
            self,
            base_entities: pd.DataFrame,
            **kwargs
    ):
        super().__init__(
            base_entities=base_entities,
            **kwargs
        )


class CreateFinalRelationshipsStepEvent(BaseEvent):
    def __init__(
            self,
            base_entities: pd.DataFrame,
            **kwargs
    ):
        super().__init__(
            base_entities=base_entities,
            **kwargs
        )


class CreateFinalTextUnitStepEvent(BaseEvent):
    def __init__(
            self,
            base_entities: pd.DataFrame,
            **kwargs
    ):
        super().__init__(
            base_entities=base_entities,
            **kwargs
        )


class CreateFinalCommunityReportsStepEvent(BaseEvent):
    def __init__(
            self,
            base_entities: pd.DataFrame,
            **kwargs
    ):
        super().__init__(
            base_entities=base_entities,
            **kwargs
        )


class EmbeddingStepEvent(BaseEvent):
    def __init__(
            self,
            base_entities: pd.DataFrame,
            **kwargs
    ):
        super().__init__(
            base_entities=base_entities,
            **kwargs
        )


class StoreStepEvent(BaseEvent):
    def __init__(
            self,
            base_entities: pd.DataFrame,
            **kwargs
    ):
        super().__init__(
            base_entities=base_entities,
            **kwargs
        )


def graph_schema_for_knowledge(
        entity_types: Dict[str, str],
        relation_types: Dict[str, str],
) -> Dict:
    """
    需要存储的信息包括：
        实体：id / 名称 / 解释 / 实体标签 ...
        实体关系
        社区：id / 名称 / 解释 / 报告 ...
        原始文档：文档信息和标签
        文本单元：信息和标签
    """
    return {
        "vertex_labels": [
            {
                "label_name": "document",
                "properties": [
                    LABEL_ID_PROPERTY_METADATA.model_dump(mode='json'),
                    {
                        "name": "url",
                        "data_type": GraphDataType.STRING,
                        "nullable": True,
                        "comment": "Document url."
                    },
                    {
                        "name": "name",
                        "data_type": GraphDataType.STRING,
                        "nullable": True,
                        "comment": "Document name."
                    },
                ],
                "comment": "原始文档信息，包含文档 url，不包含详细内容"
            },
            {
                "label_name": "text_unit",
                "properties": [
                    LABEL_ID_PROPERTY_METADATA.model_dump(mode='json'),
                    {
                        "name": "n_tokens",
                        "data_type": GraphDataType.INT32,
                        "nullable": False,
                        "comment": "Text unit token count for llm."
                    },
                    {
                        "name": "human_readable_id",
                        "data_type": GraphDataType.INT32,
                        "nullable": False,
                        "comment": "Text unit human readable id."
                    },
                    {
                        "name": "content",
                        "data_type": GraphDataType.STRING,
                        "nullable": False,
                        "comment": "Text unit content."
                    },
                ],
                "comment": "由原始文档分割来的文本块，包含文档块详细内容"
            },
            {
                "label_name": "community",
                "properties": [
                    LABEL_ID_PROPERTY_METADATA.model_dump(mode='json'),
                    {
                        "name": "human_readable_id",
                        "data_type": GraphDataType.INT32,
                        "nullable": False,
                        "comment": "Community human readable id."
                    },
                    {
                        "name": "community",
                        "data_type": GraphDataType.INT32,
                        "nullable": True,
                        "comment": "Community index."
                    },
                    {
                        "name": "level",
                        "data_type": GraphDataType.INT32,
                        "nullable": True,
                        "comment": "Community level."
                    },
                    {
                        "name": "name",
                        "data_type": GraphDataType.STRING,
                        "nullable": False,
                        "comment": "Community name."
                    },
                    {
                        "name": "summary",
                        "data_type": GraphDataType.STRING,
                        "nullable": False,
                        "comment": "Community summary."
                    },
                    {
                        "name": "rank",
                        "data_type": GraphDataType.FLOAT,
                        "nullable": False,
                        "comment": "Community rank."
                    },
                    {
                        "name": "rank_explanation",
                        "data_type": GraphDataType.STRING,
                        "nullable": False,
                        "comment": "Community rank explanation."
                    },
                    {
                        "name": "full_content",
                        "data_type": GraphDataType.STRING,
                        "nullable": False,
                        "comment": "Community full content."
                    },
                ],
                "comment": "不同实体的社区，一个社区有很多篇文档和实体等，社区包含社区的描述信息、社区的名称、总结等信息"
            },
            *[
                {
                    "label_name": _inner_label_name,
                    "properties": [
                        LABEL_ID_PROPERTY_METADATA.model_dump(mode='json'),
                        {
                            "name": "human_readable_id",
                            "data_type": GraphDataType.INT32,
                            "nullable": False,
                            "comment": "Entity human readable id."
                        },
                        {
                            "name": "name",
                            "data_type": GraphDataType.STRING,
                            "nullable": True,
                            "comment": "Entity name."
                        },
                        {
                            "name": "description",
                            "data_type": GraphDataType.STRING,
                            "nullable": True,
                            "comment": "Entity description."
                        },
                        {
                            "name": "level",
                            "data_type": GraphDataType.INT16,
                            "nullable": False,
                            "comment": "Entity entity community level."
                        },
                        {
                            "name": "degree",
                            "data_type": GraphDataType.INT16,
                            "nullable": True,
                            "comment": "Entity entity degree."
                        },
                    ],
                    "comment": f"{_inner_desc}，实体从 text_unit 中提取，被包含在社区中。"
                }
                for _inner_label_name, _inner_desc in entity_types.items()
            ],
        ],
        "edge_labels": [
            {
                "label_name": "chunk_from",
                "properties": [
                    LABEL_ID_PROPERTY_METADATA.model_dump(mode='json'),
                ],
                "comment": "表示 text_units 是从哪个 document 分割而来的，方向：text_unit-[chunk_from]->document"
            },
            {
                "label_name": "extracted_from",
                "properties": [
                    LABEL_ID_PROPERTY_METADATA.model_dump(mode='json'),
                ],
                "comment": "表示实体是从哪个 text_unit 抽取的，方向：实体-[extracted_from]->text_unit"
            },
            *[
                {
                    "label_name": _inner_relation_name,
                    "properties": [
                        LABEL_ID_PROPERTY_METADATA.model_dump(mode='json'),
                        {
                            "name": "human_readable_id",
                            "data_type": GraphDataType.INT32,
                            "nullable": False,
                            "comment": "Text unit human readable id."
                        },
                        {
                            "name": "description",
                            "data_type": GraphDataType.STRING,
                            "nullable": False,
                            "comment": "entities relation description."
                        },
                        {
                            "name": "weight",
                            "data_type": GraphDataType.INT16,
                            "nullable": False,
                            "comment": "entities relation weight."
                        },
                        {
                            "name": "combined_degree",
                            "data_type": GraphDataType.INT16,
                            "nullable": False,
                            "comment": "entities relation combined_degree."
                        },
                    ],
                    "comment": _inner_relation_desc
                }
                for _inner_relation_name, _inner_relation_desc in relation_types.items()
            ],
            {
                "label_name": 'relation_to',
                "properties": [
                    LABEL_ID_PROPERTY_METADATA.model_dump(mode='json'),
                    {
                        "name": "human_readable_id",
                        "data_type": GraphDataType.INT32,
                        "nullable": False,
                        "comment": "Text unit human readable id."
                    },
                    {
                        "name": "description",
                        "data_type": GraphDataType.STRING,
                        "nullable": False,
                        "comment": "entities relation description."
                    },
                    {
                        "name": "weight",
                        "data_type": GraphDataType.INT16,
                        "nullable": False,
                        "comment": "entities relation weight."
                    },
                    {
                        "name": "combined_degree",
                        "data_type": GraphDataType.INT16,
                        "nullable": False,
                        "comment": "entities relation combined_degree."
                    },
                ],
                "comment": '当前实体到其他实体的其他关系'
            },
            {
                "label_name": "link_text_unit_to",
                "properties": [
                    LABEL_ID_PROPERTY_METADATA.model_dump(mode='json'),
                ],
                "comment": "community 链接到哪些 text_unit，方向：community-[link_text_unit_to]->text_unit"
            },
            *[
                {
                    "label_name": "has_entity_" + _inner_label_name,
                    "properties": [
                        LABEL_ID_PROPERTY_METADATA.model_dump(mode='json'),
                    ],
                    "comment": f"community 包含实体，方向：community-[has_entity_{_inner_label_name}]->{_inner_label_name}"
                }
                for _inner_label_name in entity_types.keys()
            ],
            {
                "label_name": "has_entity",
                "properties": [
                    LABEL_ID_PROPERTY_METADATA.model_dump(mode='json'),
                ],
                "comment": "community 包含的其他实体，方向：community-[has_entity]->其他同社区的实体"
            },
        ],
    }
