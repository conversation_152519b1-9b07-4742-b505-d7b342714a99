"""Defines the interface for verb callbacks."""
from typing import Protocol

from collections.abc import Callable
from dataclasses import dataclass


@dataclass
class Progress:
    """A class representing the progress of a task."""

    percent: float | None = None
    """0 - 1 progress"""

    description: str | None = None
    """Description of the progress"""

    total_items: int | None = None
    """Total number of items"""

    completed_items: int | None = None
    """Number of items completed""" ""


ProgressHandler = Callable[[Progress], None]
"""A function to handle progress reports."""


class VerbCallbacks(Protocol):
    """Provides a way to report status updates from the pipeline."""

    def progress(self, progress: Progress) -> None:
        """Report a progress update from the verb execution"."""
        ...

    def error(
            self,
            message: str,
            cause: BaseException | None = None,
            stack: str | None = None,
            details: dict | None = None,
    ) -> None:
        """Report a error from the verb execution."""
        ...

    def warning(self, message: str, details: dict | None = None) -> None:
        """Report a warning from verb execution."""
        ...

    def log(self, message: str, details: dict | None = None) -> None:
        """Report an informational message from the verb execution."""
        ...

    def measure(self, name: str, value: float) -> None:
        """Report a telemetry measurement from the verb execution."""
        ...
