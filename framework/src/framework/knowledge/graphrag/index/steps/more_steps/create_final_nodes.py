# Copyright (c) 2024 Microsoft Corporation.
# Licensed under the MIT License

"""All the steps to transform final nodes."""

from typing import Any, cast
from .unpack_graph import unpack_graph

import pandas as pd

from ..layout_graph import layout_graph


async def create_final_nodes(
        entity_graph: pd.DataFrame,
        layout_strategy: dict[str, Any],
) -> pd.DataFrame:
    """All the steps to transform final nodes."""
    laid_out_entity_graph = cast(
        pd.DataFrame,
        layout_graph(
            entity_graph,
            layout_strategy,
            embeddings_column="embeddings",
            graph_column="clustered_graph",
            to="node_positions",
            graph_to="positioned_graph",
        ),
    )

    nodes = cast(
        pd.DataFrame,
        unpack_graph(
            laid_out_entity_graph, column="positioned_graph", type="nodes"
        ),
    )

    nodes_without_positions = nodes.drop(columns=["x", "y"])
    nodes = cast(pd.DataFrame, nodes[["id", "x", "y"]])
    joined = nodes_without_positions.merge(
        nodes,
        on="id",
        how="inner",
    )
    joined.rename(columns={"label": "title", "cluster": "community"}, inplace=True)
    joined["community"] = joined["community"].fillna(-1).astype(int)

    # drop anything that isn't graph-related or needing to be preserved
    # the rest can be looked up on the canonical entities table
    '''
    joined.drop(
        columns=["source_id", "type", "description", "size", "graph_embedding"],
        inplace=True,
    )
    '''

    deduped = joined.drop_duplicates(subset=["title", "community"])
    return deduped
