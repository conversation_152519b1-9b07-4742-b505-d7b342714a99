# Copyright (c) 2024 Microsoft Corporation.
# Licensed under the MIT License

"""All the steps to transform final relationships."""

from typing import cast

import pandas as pd

from framework.knowledge.graphrag.index.steps.more_steps.unpack_graph import unpack_graph


def compute_edge_combined_degree(
        edge_df: pd.DataFrame,
        node_degree_df: pd.DataFrame,
        node_name_column: str,
        node_degree_column: str,
        edge_source_column: str,
        edge_target_column: str,
) -> pd.Series:
    """Compute the combined degree for each edge in a graph."""

    def join_to_degree(df: pd.DataFrame, column: str) -> pd.DataFrame:
        degree_column = _degree_colname(column)
        result = df.merge(
            node_degree_df.rename(
                columns={node_name_column: column, node_degree_column: degree_column}
            ),
            on=column,
            how="left",
        )
        result[degree_column] = result[degree_column].fillna(0)
        return result

    output_df = join_to_degree(edge_df, edge_source_column)
    output_df = join_to_degree(output_df, edge_target_column)
    output_df["combined_degree"] = (
            output_df[_degree_colname(edge_source_column)]
            + output_df[_degree_colname(edge_target_column)]
    )
    return cast(pd.Series, output_df["combined_degree"])


def _degree_colname(column: str) -> str:
    return f"{column}_degree"


def create_final_relationships(
        entity_graph: pd.DataFrame,
        nodes: pd.DataFrame,
) -> pd.DataFrame:
    """All the steps to transform final relationships."""
    graph_edges = unpack_graph(entity_graph, "clustered_graph", "edges")

    graph_edges.rename(columns={"source_id": "text_unit_ids"}, inplace=True)

    filtered = cast(
        pd.DataFrame, graph_edges
    )

    pruned_edges = filtered.drop(columns=["level"])
    filtered_nodes = cast(pd.DataFrame, nodes[["title", "degree"]])

    pruned_edges["combined_degree"] = compute_edge_combined_degree(
        pruned_edges,
        filtered_nodes,
        node_name_column="title",
        node_degree_column="degree",
        edge_source_column="source",
        edge_target_column="target",
    )

    pruned_edges["text_unit_ids"] = pruned_edges["text_unit_ids"].str.split(",")

    # TODO: Find duplication source
    deduped = pruned_edges.drop_duplicates(subset=["source", "target"])
    return deduped.loc[
           :,
           [
               "id",
               "human_readable_id",
               "relation_type",
               "source",
               "target",
               "description",
               "weight",
               "combined_degree",
               "text_unit_ids",
           ],
           ]
