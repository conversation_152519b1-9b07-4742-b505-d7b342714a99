# Copyright (c) 2024 Microsoft Corporation.
# Licensed under the MIT License

"""All the steps to transform final entities."""

import pandas as pd

from .unpack_graph import unpack_graph


def create_final_entities(
        entity_graph: pd.DataFrame,
) -> pd.DataFrame:
    """All the steps to transform final entities."""
    # Process nodes
    nodes_unpack = unpack_graph(entity_graph, "clustered_graph", "nodes")
    nodes_unpack = nodes_unpack.rename(columns={"label": "title"})
    loc_columns = [
        "id",
        "title",
        "type",
        "description",
        "human_readable_id",
        "source_id",
    ]
    nodes = (
        nodes_unpack.loc[:, loc_columns].drop_duplicates(subset="id")
    )

    nodes = nodes.loc[nodes["title"].notna()]

    nodes["text_unit_ids"] = nodes["source_id"].str.split(",")

    return nodes.loc[
           :,
           [
               "id",
               "human_readable_id",
               "title",
               "type",
               "description",
               "text_unit_ids",
           ],
           ]
