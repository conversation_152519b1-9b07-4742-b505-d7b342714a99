from typing import Any, <PERSON>, Tuple, Dict
import networkx as nx
from common import tools
from framework.models import BaseLLM

log = tools.get_logger()


def _do_callback(callback=None, msg: str = None):
    try:
        log.info(msg)
        if callback:
            callback(msg)
    except:
        log.warning('do callback error.')


description_attr = 'description'
old_description_attr = 'old_description'


async def summarize_descriptions(
        llm: BaseLLM,
        input: nx.Graph,
        old_summarized_graph: nx.Graph,
        prompt: str,
        num_threads: int = 16,
        callback=None,
) -> nx.Graph:
    # node,  attrs
    result_list = []
    all_list: List[Tuple[str | tuple[str, str], Dict[str, Any]]] = []
    for node, attrs in input.nodes(data=True):
        if old_summarized_graph.has_node(node):
            exist_node_attrs = old_summarized_graph.nodes[node] | {}
            if _compare_summary_equals(exist_node_attrs.get(old_description_attr, ''), attrs.get(description_attr, '')):
                result_list.append((node, exist_node_attrs))
                _do_callback(callback=callback, msg=f'node {node} already summarized, skip.')
                continue
        all_list.append((node, attrs))
    for edge_start, edge_end, attrs in input.edges(data=True):
        if old_summarized_graph.has_edge(edge_start, edge_end):
            exist_edge_attrs = old_summarized_graph.edges[edge_start, edge_end] | {}
            if _compare_summary_equals(exist_edge_attrs.get(old_description_attr, ''), attrs.get(description_attr, '')):
                result_list.append(((edge_start, edge_end), exist_edge_attrs))
                _do_callback(callback=callback, msg=f'edge {edge_start}->{edge_end} already summarized, skip.')
                continue
        all_list.append(((edge_start, edge_end), attrs))

    _do_callback(callback=callback, msg=f'total {len(all_list)} nodes & edges need to be summarized.')
    batches = tools.split_as_batch(data=all_list, batch_count=num_threads)
    for batch in batches:
        result_list.extend(await _do_summaries(
            llm=llm,
            prompt=prompt,
            entity_and_attrs=batch,
            callback=callback
        ))

    graph = nx.Graph()
    for rel in result_list:
        if rel is None:
            continue
        if isinstance(rel[0], str):
            graph.add_node(rel[0], **rel[1])
        else:
            graph.add_edge(rel[0][0], rel[0][1], **rel[1])
    return graph


def _compare_summary_equals(summary1: str, summary2: str) -> bool:
    if summary1 is None and summary2 is not None:
        return False
    if summary1 is not None and summary2 is None:
        return False
    if summary1 is None and summary2 is None:
        return True
    if summary1 == '' or summary2 == '':
        return False
    summary1 = set([_.strip() for _ in summary1.strip().split('\n')])
    summary2 = set([_.strip() for _ in summary2.strip().split('\n')])
    if bool(summary1.difference(summary2) or summary2.difference(summary1)):
        return False
    return True


async def _do_summaries(
        llm: BaseLLM,
        prompt: str,
        entity_and_attrs: List[Tuple[str | tuple[str, str], Dict[str, Any]]],
        callback=None,
) -> List[Tuple[str | tuple[str, str], Dict[str, Any]]]:
    for e in entity_and_attrs:
        names = e[0]
        if isinstance(names, str):
            names = [names]
        old_descriptions = e[1].get(description_attr, '')
        descriptions = old_descriptions.split('\n')
        new_description = None
        if len(descriptions) <= 1:
            new_description = old_descriptions
        else:
            new_description = await _do_single_summary(
                llm=llm, prompt=prompt, entity_names=names, descriptions=descriptions, callback=callback
            )
        e[1][old_description_attr] = old_descriptions
        if new_description is None:
            new_description = ''
        e[1][description_attr] = new_description
    return entity_and_attrs


async def _do_single_summary(
        llm: BaseLLM,
        prompt: str,
        entity_names: List[str],
        descriptions: List[str],
        callback=None,
) -> str:
    prompt = prompt.format(entity_name=entity_names, description_list=descriptions)
    history = [{'role': 'system', 'content': prompt}]
    for i in range(10):
        try:
            response = await _llm_completion(llm, history)
            if response:
                _do_callback(
                    callback,
                    f'summary of {entity_names} and descriptions: {descriptions}. response={response}'
                )
                return response
        except:
            history.append({'role': 'user', 'content': '请回复：'})


async def _llm_completion(
        llm: BaseLLM,
        messages: List
) -> str:
    response = await llm.acomplete(input=messages)
    result = response.content
    # log.info(f'llm generate: history={messages}, result={result}')
    if not isinstance(result, str):
        raise RuntimeError('llm generate result is not string')
    return result
