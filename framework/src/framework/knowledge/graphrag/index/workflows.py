"""
由于 graphrag 与 steps 的依赖会出现冲突，而且 graphrag 过差，
所以这里不直接使用 graphrag，而是使用 steps 的 workflow 直接
重写一个 graphrag.
"""
import json
from pathlib import Path
from typing import Optional, List, Dict, Callable, Literal, Any

import pandas as pd
import networkx as nx

from llama_index.core.workflow import Workflow, step, StartEvent, StopEvent
from llama_index.core.workflow.service import ServiceManager

from common import tools
from framework.config import knowledge_config
from framework.knowledge.kgconfigs import DEFAULT_ENTITY_TYPES, DEFAULT_RELATION_TYPES
from framework.vector_store import LarkVectorStore
from framework.graph_store import LarkGraphStore
from framework.data.document import SensorsDocMetadata, SensorsDocsMetadata
from framework.models import BaseLLM
from framework.models.tokenizer import TokenizerFactory

from .steps.schemas import (
    DocumentStepEvent, CreateBasicTextUnitStepEvent, CreateBaseEntityGraphStepEvent,
    CreateFinalEntitiesStepEvent, CreateFinalNodesStepEvent, CreateFinalCommunitiesStepEvent,
    CreateFinalRelationshipsStepEvent, CreateFinalTextUnitStepEvent, CreateFinalCommunityReportsStepEvent,
    StoreStepEvent, SavedFiles,
)
from .steps.extract_entities import extract_entities
from .steps.merge_graphs import merge_graphs
from .steps.more_steps.cluster_graph import cluster_graph
from .steps.more_steps.create_final_entities import create_final_entities
from .steps.more_steps.create_final_nodes import create_final_nodes
from .steps.more_steps.create_final_communities import create_final_communities
from .steps.more_steps.create_final_relationships import create_final_relationships
from .steps.more_steps.create_final_text_units import create_final_text_units
from .steps.community_reports import create_final_community_reports
from .steps.summarize_descriptions import summarize_descriptions
from .steps.store.vector_store import store_to_vector_db
from .steps.store.graph_store import store_to_graph_db

log = tools.get_logger()


class LlamaIndexRagWorkflow(Workflow):
    """
    知识库 index workflow
    对于需要首次处理的，清空 output 目录，并重新索引
    对于需要 resume 的，增量增加 document 和 text_unit，后面流程相同
    """

    def __init__(
            self,
            llm: BaseLLM,
            input_dir: Path,
            output_dir: Path,
            vector_store: LarkVectorStore,
            graph_store: LarkGraphStore,
            prompts: Dict[str, str],
            entity_types: Dict[str, str] = None,
            relation_types: Dict[str, str] = None,
            chunk_size: int = knowledge_config['sensors_knowledge_config']['graphrag_envs']['chunk_size'],
            chunk_overlap: int = knowledge_config['sensors_knowledge_config']['graphrag_envs']['chunk_overlap'],
            index_mode: Literal['reindex', 'resume'] = 'reindex',
            timeout: Optional[float] = 24 * 60 * 60,
            disable_validation: bool = False,
            verbose: bool = False,
            service_manager: Optional[ServiceManager] = None,
            num_concurrent_runs: Optional[int] = None,
            callback: Callable[[str], None] | None = None,
            **kwargs,
    ):
        """
        模式 index_mode 说明：reindex 表示全量重新索引；resume 表示增量索引，也会继续上一次的索引
        """
        super(LlamaIndexRagWorkflow, self).__init__(
            timeout=timeout, disable_validation=disable_validation,
            verbose=verbose, service_manager=service_manager,
            num_concurrent_runs=num_concurrent_runs
        )
        if entity_types is None:
            entity_types = DEFAULT_ENTITY_TYPES
        if relation_types is None:
            relation_types = DEFAULT_RELATION_TYPES
        self.llm = llm
        self.input_path = input_dir
        self.output_dir = output_dir
        self.vector_store = vector_store
        self.graph_store = graph_store
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.entity_types = entity_types
        self.relation_types = relation_types
        self.prompts = prompts
        self.callback = callback
        self.index_mode = index_mode
        self.saved_files = SavedFiles(output_path=self.output_dir)

        # 成功和失败的文档记录, id-reason
        self.success_documents = {}
        self.failed_documents = {}

    def _callback(self, content: Any):
        try:
            log.info(content)
            if self.callback:
                self.callback(content)
        except BaseException as e:
            log.warning('callback error.', e)

    @property
    def reindex(self) -> bool:
        return self.index_mode == 'reindex'

    @property
    def resume(self) -> bool:
        return self.index_mode == 'resume'

    @property
    def metadata_path(self) -> Path:
        return Path(self.input_path, 'metadata.json')

    @property
    def input_document_files(self) -> List[Path]:
        docs = []
        for file in Path(self.input_path).glob('**/*'):
            if file.is_file() and file.match('*.txt'):
                docs.append(file)
        return docs

    def _check_and_fix_metadata(self):
        metadata_path = self.metadata_path
        data = None
        input_document_files = self.input_document_files
        if not metadata_path.exists():
            # 由于没有元数据文件，当做没有索引过，这里就必须重新索引
            self.index_mode = 'reindex'
            metadata_path.parent.mkdir(parents=True, exist_ok=True)
            data = SensorsDocsMetadata.model_validate({
                'batch_id': 1,
                'documents': [],
            })
            for file in input_document_files:
                doc = SensorsDocMetadata(
                    file_name=file.name,
                    title='',
                    batch_id=1,
                    content_sign=tools.md5(file.read_text(encoding='utf-8'))
                )
                data.upsert_doc(doc)
            metadata_path.write_text(data.model_dump_json(indent=4))
        else:
            data = self.data_meta

        # 校验是否还有没添加进去的文档
        documents = data.documents
        if not documents:
            documents = []
        has_not_include_docs = False
        for file in input_document_files:
            doc_exists = False
            for doc in documents:
                if doc.file_name == file.name:
                    doc_exists = True
                    break
            if not doc_exists:
                has_not_include_docs = True
                doc = SensorsDocMetadata(
                    file_name=file.name,
                    title='',
                    batch_id=data.batch_id + 1,
                    content_sign=tools.md5(file.read_text(encoding='utf-8'))
                )
                data.upsert_doc(doc)

        if has_not_include_docs:
            data.batch_id = data.batch_id + 1
        if self.index_mode == 'reindex':
            data.indexed_batch_id = -1
        self.metadata_path.write_text(data.model_dump_json(indent=4))
        self.max_batch_id = data.batch_id

    @property
    def data_meta(self) -> SensorsDocsMetadata:
        return SensorsDocsMetadata.model_validate(json.loads(self.metadata_path.read_text(encoding='utf-8')))

    @step
    async def first_start_build_work(self, event: StartEvent) -> DocumentStepEvent:
        """开始构建"""
        log.info(f'start workflow to build index. {event.dict()}')
        self._callback(f'开始索引文档, reindex={self.reindex}, resume={self.resume}')
        self._check_and_fix_metadata()

        # 将原来的输出数据备份
        if self.reindex and self.output_dir.exists():
            origin_path = str(self.output_dir)
            self.output_dir.rename(origin_path + '_backup_' + tools.uuid4_no_underline())
            self.output_dir = Path(origin_path)
            self.output_dir.mkdir()

        return DocumentStepEvent()

    @step
    async def create_document_tables(self, event: DocumentStepEvent) -> CreateBasicTextUnitStepEvent:
        # url, title, file_name
        columns = ['file_name', 'id', 'url', 'title', 'raw_content', 'content_sign', 'batch_id', 'text_unit_ids']
        self._callback('正在加载文档')
        if self.saved_files.final_documents.exists():
            input_docs = pd.read_parquet(self.saved_files.final_documents)
        else:
            input_docs = pd.DataFrame(columns=columns)

        data_meta = self.data_meta
        documents = data_meta.documents
        documents_dict = dict([(d.file_name, d) for d in documents])
        metadatas = []

        # load all documents
        for file in self.input_document_files:
            file_name = file.name
            file_meta = documents_dict.get(file_name, None)
            if file_meta is None or file_meta.batch_id <= data_meta.indexed_batch_id:
                self._callback(f'skip file: {file_name}')
                continue

            metadata = file_meta.model_dump()
            content = file.read_text(encoding='utf-8')
            metadata['id'] = metadata['url']
            if not metadata['url']:
                # sha256 更长一些，冲突概率小一些
                metadata['id'] = tools.sha256(metadata['title'] if metadata['title'] else '' + content)
            metadata['raw_content'] = content
            metadatas.append(metadata)

        self._callback(f'共加载 {str(len(metadatas))} 篇文档')
        if metadatas:
            new_df = pd.DataFrame(metadatas)
            input_docs = pd.concat([input_docs, new_df], ignore_index=True)

        input_docs = input_docs.drop_duplicates(subset=['id'], keep='last')
        input_docs.reset_index(drop=True, inplace=True)
        return CreateBasicTextUnitStepEvent(
            documents=input_docs,
            **(event.dict()),
        )

    @step
    async def create_basic_text_units(self, event: CreateBasicTextUnitStepEvent) -> CreateBaseEntityGraphStepEvent:
        """使用 langchain 的 TextSplitter 来分割，效果更好一些"""
        log.info(f'start create basic text units. {event.dict()}')

        self._callback('正在进行文档分割')
        documents: pd.DataFrame = event['documents']
        text_units = []
        text_unit_ids = []
        tokenizer = TokenizerFactory.get_tokenizer()
        if 'text_unit_ids' not in list(documents.columns):
            documents['text_unit_ids'] = None

        doc_ids = set()

        for index, row in documents.iterrows():
            need_split = False
            if 'text_unit_ids' in row:
                tmp_text_unit_ids = row['text_unit_ids']
                try:
                    if tmp_text_unit_ids is None or tmp_text_unit_ids.size <= 0:
                        need_split = True
                except:
                    log.warning('test text unit ids error. will re-split now.')
                    need_split = True

            if not need_split:
                text_unit_ids.append(row['text_unit_ids'])
                continue

            self._callback(f'split text: {row["id"]}')
            doc_ids.add(row['id'])
            doc = row['raw_content']
            doc_text_units = tools.split_text(
                text=doc,
                chunk_size=self.chunk_size,
                chunk_overlap=self.chunk_overlap,
            )
            unit_ids = []
            for unit in doc_text_units:
                unit_id = tools.uuid4_no_underline()
                unit = f"来源文档: {row['title']}\n来源链接：{row['url']}\n***\n{unit}"
                text_units.append({
                    'id': unit_id,
                    'text': unit,
                    'document_ids': [row['id']],
                    'n_tokens': tokenizer.tokenize_len(unit),
                })
                unit_ids.append(unit_id)
            text_unit_ids.append(unit_ids)

        # save final documents
        documents['text_unit_ids'] = text_unit_ids
        documents.reset_index(drop=True, inplace=True)
        documents.to_parquet(path=self.saved_files.final_documents)
        del documents

        self._callback(f'共分割成 {str(len(text_units))} 个子文档')

        # save text units
        text_units_frame = None
        if self.saved_files.final_text_units.exists():
            text_units_frame = pd.read_parquet(self.saved_files.final_text_units)
            text_units_frame.reset_index(drop=True, inplace=True)
            # 需要将原来的旧数据做去重删除
            old_indexes = []
            for idx, row in text_units_frame.iterrows():
                line_doc_ids = list(row['document_ids'])
                for ldi in line_doc_ids:
                    if ldi in doc_ids:
                        old_indexes.append(idx)
            text_units_frame = text_units_frame.drop(index=old_indexes)
        else:
            text_units_frame = pd.DataFrame()

        if text_units:
            text_units_frame = pd.concat([text_units_frame, pd.DataFrame(text_units)], ignore_index=True)
        text_units_frame.reset_index(drop=True, inplace=True)
        text_units_frame.to_parquet(path=self.saved_files.final_text_units)
        del text_units

        return CreateBaseEntityGraphStepEvent(
            **(event.dict()),
        )

    @step
    async def create_base_entity_graph(self, event: CreateBaseEntityGraphStepEvent) -> CreateFinalEntitiesStepEvent:
        """抽取实体和图信息，对于失败了的 text_unit，需要重试"""
        self._callback('正在抽取实体')
        log.info(f'start create basic entity graph. {event.dict()}')
        final_text_units = pd.read_parquet(self.saved_files.final_text_units)
        final_text_units.reset_index(drop=True, inplace=True)

        # 后面都是增量索引了，这里先保存一下元数据
        if hasattr(self, 'max_batch_id'):
            self.data_meta.indexed_batch_id = self.max_batch_id
            self.metadata_path.write_text(self.data_meta.model_dump_json(indent=4), encoding='utf-8')

        # 对于已经抽取过实体的 text_units，只需要解析图、关系等，不需要 llm 再次抽取
        text_units, graphs = await extract_entities(
            input=final_text_units,
            llm=self.llm,
            text_column='text',
            prompt=self.prompts['entity_extraction'],
            entity_types=self.entity_types,
            relation_types=self.relation_types,
            max_gleaning=2,  # 最大提取次数
            callback=self.callback
        )
        text_units.reset_index(drop=True, inplace=True)
        text_units.to_parquet(path=self.saved_files.final_text_units)

        self._callback('正在合并子图')
        merged_graph = merge_graphs(
            graphs,
            node_operations={
                'source_id': {'operation': 'concat', 'delimiter': ', ', 'distinct': True},
                'description': {'operation': 'concat', 'separator': '\n', 'distinct': False},
            },
            edge_operations={
                'source_id': {'operation': 'concat', 'delimiter': ', ', 'distinct': True},
                'description': {'operation': 'concat', 'separator': '\n', 'distinct': False},
                'weight': 'sum'
            },
        )

        # 需要做图关系简化，比如两个关系 A->B->C 和 A->C，则只需要保留 A->B->C ? 语义是否相等？
        # TODO 扩展别名？

        # 这一步也过于耗时，也需要增量更新
        old_summarized_graph = nx.Graph()
        if self.saved_files.entities_summarized_graph.exists():
            old_summarized_graph = nx.read_graphml(str(self.saved_files.entities_summarized_graph.absolute()))

        nx.write_graphml_xml(merged_graph, self.saved_files.entities_graph)
        self._callback('正在生成实体描述')
        summarized = await summarize_descriptions(
            llm=self.llm,
            input=merged_graph,
            old_summarized_graph=old_summarized_graph,
            prompt=self.prompts['summarize_descriptions'],
            callback=self.callback
        )
        self._callback(f'summarized graph: {summarized}')
        nx.write_graphml_xml(summarized, self.saved_files.entities_summarized_graph)

        log.info('开始社区检测')
        self._callback('开始社区检测')
        clustered = cluster_graph(
            summarized,
            column="entity_graph",
            strategy={'type': "leiden", 'max_cluster_size': 100},  # 每个社区最大节点数量
            to="clustered_graph",
            level_to="level",
        )
        if clustered.shape[0] == 0:
            self._callback('No clusters found in data.')

        final_columns = ["level", "clustered_graph"]
        base_entities = clustered[final_columns]

        result = CreateFinalEntitiesStepEvent(
            base_entities=base_entities,
            **(event.dict()),
        )
        result['text_units'] = text_units
        return result

    @step
    async def create_final_entities(self, event: CreateFinalEntitiesStepEvent) -> CreateFinalNodesStepEvent:
        self._callback('开始生成最终实体文件')
        log.info(f'start create final entities. {event.dict()}')
        data = create_final_entities(entity_graph=event['base_entities'])
        data.reset_index(drop=True, inplace=True)
        data.to_parquet(path=self.saved_files.final_entities)
        del data

        return CreateFinalNodesStepEvent(
            **(event.dict()),
        )

    @step
    async def create_final_nodes(self, event: CreateFinalNodesStepEvent) -> CreateFinalCommunitiesStepEvent:
        self._callback('开始生成实体节点文件')
        log.info(f'start create final nodes. {event.dict()}')
        final_nodes = await create_final_nodes(
            entity_graph=event['base_entities'],
            layout_strategy={'type': 'zero'},
        )
        final_nodes.reset_index(drop=True, inplace=True)
        final_nodes.to_parquet(path=self.saved_files.final_nodes)
        del final_nodes

        return CreateFinalCommunitiesStepEvent(
            **(event.dict()),
        )

    @step
    async def create_final_communities(
            self,
            event: CreateFinalCommunitiesStepEvent
    ) -> CreateFinalRelationshipsStepEvent:
        self._callback('开始创建社区文件')
        log.info(f'start create final communities. {event.dict()}')
        final_communities = create_final_communities(entity_graph=event['base_entities'])
        final_communities.reset_index(drop=True, inplace=True)
        final_communities.to_parquet(path=self.saved_files.final_communities)
        del final_communities

        return CreateFinalRelationshipsStepEvent(
            **(event.dict()),
        )

    @step
    async def create_final_relationships(
            self,
            event: CreateFinalRelationshipsStepEvent,
    ) -> CreateFinalTextUnitStepEvent:
        self._callback('开始创建实体关系文件')
        log.info(f'start create final relationships. {event.dict()}')
        final_relationships = create_final_relationships(
            entity_graph=event['base_entities'],
            nodes=pd.read_parquet(path=self.saved_files.final_nodes),
        )
        final_relationships.reset_index(drop=True, inplace=True)
        final_relationships.to_parquet(path=self.saved_files.final_relationships)
        del final_relationships

        return CreateFinalTextUnitStepEvent(
            **(event.dict()),
        )

    @step
    async def create_final_text_units(
            self,
            event: CreateFinalTextUnitStepEvent
    ) -> CreateFinalCommunityReportsStepEvent:
        self._callback('开始创建文本单元文件')
        log.info(f'start create final text units. {event.dict()}')
        final_text_units = create_final_text_units(
            text_units=pd.read_parquet(path=self.saved_files.final_text_units),
            final_entities=pd.read_parquet(path=self.saved_files.final_entities),
            final_relationships=pd.read_parquet(path=self.saved_files.final_relationships),
        )
        final_text_units.reset_index(drop=True, inplace=True)
        final_text_units.to_parquet(path=self.saved_files.final_text_units)

        return CreateFinalCommunityReportsStepEvent(
            **(event.dict()),
        )

    @step
    async def create_final_community_reports(
            self,
            event: CreateFinalCommunityReportsStepEvent
    ) -> StoreStepEvent:
        self._callback('正在生成社区报告')
        # TODO 这个耗时较长，先检测是否已有
        log.info(f'start create final community reports. {event.dict()}')
        final_community_reports = await create_final_community_reports(
            nodes_input=pd.read_parquet(path=self.saved_files.final_nodes),
            edges_input=pd.read_parquet(path=self.saved_files.final_relationships),
            entities=pd.read_parquet(path=self.saved_files.final_entities),
            communities=pd.read_parquet(path=self.saved_files.final_communities),
            llm=self.llm,
            prompt=self.prompts['community_report'],
            summarization_strategy={'max_report_length': 2000, 'max_input_length': 8000, 'stagger': 0.3},
        )
        final_community_reports.reset_index(drop=True, inplace=True)
        final_community_reports.to_parquet(path=self.saved_files.final_community_reports)
        del final_community_reports

        return StoreStepEvent(
            **(event.dict()),
        )

    @step
    async def store(self, event: StoreStepEvent) -> StopEvent:
        """save to vector store and graph database"""
        await self.store_vector_db(event)
        await self.store_graph_db(event)
        log.info('Full index created successfully')
        self._callback('索引完成.')

        if hasattr(self, 'max_batch_id'):
            self.data_meta.indexed_batch_id = self.max_batch_id
            self.metadata_path.write_text(self.data_meta.model_dump_json(indent=4), encoding='utf-8')

        return StopEvent()

    async def store_vector_db(self, event: StoreStepEvent) -> None:
        self._callback('正在导入向量数据库...')
        log.info(f'start store all data to vector db. event={event.dict()}')
        await store_to_vector_db(
            self.vector_store,
            final_documents=self.saved_files.final_documents,
            final_relationships=self.saved_files.final_relationships,
            final_text_units=self.saved_files.final_text_units,
            final_entities=self.saved_files.final_entities,
            final_nodes=self.saved_files.final_nodes,
            final_communities=self.saved_files.final_communities,
            final_community_reports=self.saved_files.final_community_reports,
            clear_all=True,
            callback=self._callback,
        )

    async def store_graph_db(self, event: StoreStepEvent) -> None:
        self._callback('正在导入图数据库...')
        log.info(f'start store all data to graph db. event={event.dict()}')
        await store_to_graph_db(
            self.graph_store,
            final_documents=self.saved_files.final_documents,
            final_relationships=self.saved_files.final_relationships,
            final_text_units=self.saved_files.final_text_units,
            final_entities=self.saved_files.final_entities,
            final_nodes=self.saved_files.final_nodes,
            final_communities=self.saved_files.final_communities,
            final_community_reports=self.saved_files.final_community_reports,
            entity_types=self.entity_types,
            clear_all=True,
            callback=self._callback,
        )
