# 自定义多阶段启发式搜索引擎
from abc import ABC
from typing import Any, List, Callable

from common import tools
from framework.agents.schema import Conversation, Role
from framework.agents.assistant import AssistantAgent
from framework.graph_store import LarkGraphStore
from framework.vector_store import LarkVectorStore
from framework.models import BaseLLM

log = tools.get_logger()


class KnowledgeRetrieve(ABC):
    """
    由于 graphrag 的 local search 需要加载所有文件，消耗内存巨大，这里考虑自定义多步骤启发式检索方式。
    """

    def __init__(
            self,
            llm: BaseLLM,
            vector_store: LarkVectorStore,
            graph_store: LarkGraphStore,
            llm_params: dict[str, Any],
            callback: Callable[[str], None] = None,
            **kwargs
    ):
        self.llm = llm
        self.vector_store = vector_store
        self.graph_store = graph_store
        self.token_encoder = vector_store.embedder
        self.llm_params = llm_params or {}
        self.callback = callback

    def _callback(self, msg: str):
        if self.callback:
            try:
                log.info(msg)
                self.callback(msg)
            except:
                pass

    def retrieve(
            self,
            history: List[Conversation] | None = None,
            retrieve_limit: int = 20,
            **kwargs
    ) -> Conversation:
        """检索并返回结果"""
        if not history:
            return Conversation(role=Role.ASSISTANT, content='What can I help you?')
        data = self.retrieve_data(history=history, retrieve_limit=retrieve_limit, **kwargs)
        system_prompt = "请参考以下文档内容回答用户问题：\n" + data.content
        assistant = AssistantAgent(llm=self.llm, system_prompt=system_prompt)
        for h in history:
            assistant.add_history({'role': str(h.role), 'content': h.content})
        answer = assistant.chat_and_save(prompt='请回答我的问题：')
        return Conversation(role=Role.ASSISTANT, content=answer)

    def retrieve_data(
            self,
            history: List[Conversation] | None = None,
            retrieve_limit: int = 20,
            **kwargs
    ) -> Conversation:
        """仅检索向量数据库和图数据库"""
        ...

    async def aretrieve(
            self,
            history: List[Conversation] | None = None,
            retrieve_limit: int = 20,
            **kwargs
    ) -> Conversation:
        return self.retrieve(history=history, retrieve_limit=retrieve_limit, **kwargs)

    async def aretrieve_data(
            self,
            history: List[Conversation] | None = None,
            retrieve_limit: int = 20,
            **kwargs
    ) -> Conversation:
        """仅检索向量数据库"""
        return self.retrieve_data(history=history, retrieve_limit=retrieve_limit, **kwargs)
