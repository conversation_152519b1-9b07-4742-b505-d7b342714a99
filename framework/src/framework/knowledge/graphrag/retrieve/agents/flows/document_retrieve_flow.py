import json
from collections import Counter
from numbers import Number
from typing import Optional, Callable, List

from langchain_core.messages import BaseMessage, AIMessage
from llama_index.core.workflow import step, StartEvent, StopEvent, Event
from llama_index.core.workflow.retry_policy import ConstantDelayRetryPolicy
from llama_index.core.workflow.service import ServiceManager
from qdrant_client.http.models import models

from common import tools
from framework.agents.assistant import AssistantAgent
from framework.agents.real_query import RealQueryAgent
from framework.vector_store import LarkVectorStore, VectorSearchResult, VectorDocument
from framework.models import BaseLLM
from .base_retrieve_flow import RetrieveFlowWithHistory

include_columns_in_text = {
    'entity': ['entity_type', 'entity_name', 'description'],
    'community': ['title', 'full_content'],
    'text_unit': ['text']
}

log = tools.get_logger()


def _get_document_as_text(
        search_result: VectorSearchResult | VectorDocument,
        document_keyword: str | None = 'Document:'
) -> str:
    if search_result is None:
        return ''
    if isinstance(search_result, VectorSearchResult):
        search_result = search_result.document
    document = search_result
    if document is None:
        return ''
    content = []
    if document.attributes is not None and document.attributes.get('type', None) in include_columns_in_text:
        doc_type = document.attributes['type']
        valid_columns = include_columns_in_text[doc_type]
        if document_keyword:
            content.append(document_keyword)
        for k, v in document.attributes.items():
            if k not in valid_columns:
                continue
            if v is None:
                continue
            if isinstance(v, str):
                content.append(str(k) + ': ' + v)
            elif isinstance(v, Number):
                content.append(str(k) + ': ' + str(v))
            else:
                content.append(str(k) + ': ' + json.dumps(v, ensure_ascii=False))
    return '\n'.join(content)


class BaseFlowEvent(Event):
    query_times: int = 0


class EntitiesEvent(BaseFlowEvent):
    entities: List[VectorSearchResult] | None


class CommunitiesEvent(BaseFlowEvent):
    entities: List[VectorSearchResult] | None


class CommunityWithoutEntityEvent(BaseFlowEvent):
    """没有实体的时候检索"""


class FinalResultEvent(BaseFlowEvent):
    """表示需要开始构建最终结果"""


class DocumentRetrieveFlow(RetrieveFlowWithHistory):
    def __init__(
            self,
            llm: BaseLLM,
            vector_store: LarkVectorStore,
            chat_history: List[BaseMessage],
            query_score_limit: float = 0.45,
            max_token_limit: int = 1024 * 8,
            query_limit: int = 20,
            timeout: Optional[float] = 24 * 60 * 60,
            disable_validation: bool = False,
            verbose: bool = False,
            compress_results: bool = False,
            service_manager: Optional[ServiceManager] = None,
            num_concurrent_runs: Optional[int] = None,
            callback: Callable[[str], None] | None = None,
    ):
        super(DocumentRetrieveFlow, self).__init__(
            llm=llm,
            chat_history=chat_history,
            max_token_limit=max_token_limit,
            query_limit=query_limit,
            timeout=timeout,
            disable_validation=disable_validation,
            verbose=verbose,
            service_manager=service_manager,
            num_concurrent_runs=num_concurrent_runs,
            callback=callback,
        )
        self.query_score_limit = query_score_limit
        self.vector_store = vector_store
        self.base_entity_names = []
        self.compress_results = compress_results
        self.user_real_query = None

    def _add_history(self, documents: List[VectorSearchResult | VectorDocument | str]):
        if not documents:
            return
        for document in documents:
            doc = document
            if isinstance(doc, VectorSearchResult) or isinstance(doc, VectorDocument):
                doc = _get_document_as_text(search_result=doc)
            if not doc:
                continue
            self.add_chat_history(AIMessage(content=doc))

    @step(retry_policy=ConstantDelayRetryPolicy())
    async def start(self, event: StartEvent) -> BaseFlowEvent:
        agent = AssistantAgent(
            llm=self.llm,
            system_prompt=("请根据对话内容给出当前可能要检索的实体名称，你需要逐行回复需要检索的实体名称，并且把最相关的实体名称放在最前面\n"
                           "需要注意，你只能回复需要检索的实体名称，不能回复其他任何内容，不要将实体名称放在 markdown 代码块中！")
        )
        for h in self.get_chat_history():
            agent.add_history(h)
        base_entity_names = agent.chat_and_save().split('\n')
        base_entity_names = [b.strip() for b in base_entity_names if b.strip()]
        self.base_entity_names.extend(base_entity_names)

        # extract real query
        if self.compress_results:
            real_query_agent = RealQueryAgent(llm=self.llm)
            self.user_real_query = real_query_agent.get_real_query()
        return BaseFlowEvent()

    @step(retry_policy=ConstantDelayRetryPolicy())
    async def get_entities(self, event: BaseFlowEvent) -> EntitiesEvent | StopEvent:
        """提取实体"""
        entities_result = []
        base_entity_names_len = len(self.base_entity_names)
        for i in range(base_entity_names_len):
            be_result = self.vector_store.similarity_search_by_text(
                text=f'请检索：{self.base_entity_names[i]}',
                query_filter=models.Filter(
                    must=[
                        models.FieldCondition(
                            key="type",
                            match=models.MatchValue(value="entity"),
                        ),
                    ]
                ),
                k=self.query_limit,
            )
            if be_result:
                for r in be_result:
                    r.score = r.score * 0.9 + (base_entity_names_len - i) / base_entity_names_len * 0.1
                entities_result.extend(be_result)

        # 分数过滤，去掉无关的
        entities_result = [_ for _ in entities_result if _.score >= self.query_score_limit]

        # 去重，重复 entity 找到 level 最大的
        entity_dict = {}
        for entity in entities_result:
            entity_name = entity.document.attributes['entity_name'].strip()
            curr_entity = entity_dict.get(entity_name, None)
            if curr_entity is not None:
                if int(curr_entity.document.attributes['level']) < int(entity.document.attributes['level']):
                    curr_entity = entity
            else:
                curr_entity = entity
            entity_dict[entity_name] = curr_entity

        entities_result = list(entity_dict.values())
        entities_result = sorted(entities_result, key=lambda x: x.score, reverse=True)
        if len(entities_result) > self.query_limit:
            entities_result = entities_result[:self.query_limit]

        self._add_history(documents=entities_result)

        log.info(f"entities:{json.dumps([e.document.attributes for e in entities_result], ensure_ascii=False)}")

        return EntitiesEvent(
            query_times=1,
            entities=entities_result,
        )

    @step(retry_policy=ConstantDelayRetryPolicy())
    async def get_communities(self, event: EntitiesEvent) -> CommunitiesEvent | CommunityWithoutEntityEvent | StopEvent:
        entities = event.entities
        if not entities:
            return CommunityWithoutEntityEvent(query_times=event.query_times)

        entity_names = [e.document.attributes.get('entity_name', 'None') for e in entities]
        log.info(f'get communities of entity: {entity_names}')

        # 社区排序，好像意义不大？
        community_rank = {}
        for entity in entities:
            community_id = entity.document.attributes['community']
            weight = community_rank.get(community_id, 0)
            community_rank[community_id] = weight + entity.score
        sorted_communities = [
            (community_id, community_weight)
            for community_id, community_weight in community_rank.items()
        ]
        sorted_communities = sorted(sorted_communities, key=lambda x: x[0], reverse=True)

        # 检索社区
        communities = self.vector_store.filter(
            query_filter=models.Filter(
                must=[
                    models.FieldCondition(
                        key="type",
                        match=models.MatchValue(value="community"),
                    ),
                    models.FieldCondition(
                        key="community",
                        match=models.MatchAny(any=[community_id for community_id, _ in sorted_communities]),
                    ),
                ]
            ),
            k=self.query_limit,
        )
        if not communities:
            communities = []

        community_names = [c.attributes.get('title', 'None') for c in communities]
        log.info(f'get community documents of: {community_names}')

        def _get_community_document(community) -> str:
            contents = [
                f'## Community: {community.attributes.get("title", "None")}',
                f'Summary: {community.attributes.get("summary", "None")}',
                f'Findings: {community.attributes.get("findings", "None")}'
            ]
            return '\n'.join(contents)

        community_docs = [_get_community_document(community) for community in communities]
        self._add_history(documents=community_docs)

        return CommunitiesEvent(
            query_times=event.query_times + 1,
            entities=event.entities,
        )

    @step(retry_policy=ConstantDelayRetryPolicy())
    async def get_textunits_contents(self, event: CommunitiesEvent) -> FinalResultEvent:
        """检索包含实体的文本块"""
        entities = event.entities

        # 统计包含 entity 的文档，并排序
        text_unit_counter = Counter()
        for entity in entities:
            curr_tu_ids = entity.document.attributes.get('text_unit_ids', [])
            if not isinstance(curr_tu_ids, list):
                log.warning(f'Invalid text unit ids format. [entity={entity}]')
                continue
            text_unit_counter.update(curr_tu_ids)
        sorted_text_unit_ids = text_unit_counter.most_common(self.query_limit)
        sorted_text_unit_ids = [ids[0] for ids in sorted_text_unit_ids]
        log.info(f'get text_units from ids: {sorted_text_unit_ids}')

        doc_content = []
        if sorted_text_unit_ids:
            text_units = self.vector_store.filter(
                query_filter=models.Filter(
                    must=[
                        models.FieldCondition(
                            key="vid",
                            match=models.MatchAny(any=sorted_text_unit_ids),
                        ),
                    ]
                ),
                k=self.query_limit,
            )
            if text_units:
                doc_content.extend([
                    '## Documents in community',
                    *[_get_document_as_text(_tu, document_keyword='---') for _tu in text_units]
                ])
        for doc in doc_content:
            self._add_history(doc)

        return FinalResultEvent(
            query_times=event.query_times + 1,
        )

    @step(retry_policy=ConstantDelayRetryPolicy())
    async def get_result_without_entity(self, event: CommunityWithoutEntityEvent) -> FinalResultEvent | StopEvent:
        """直接检索所有内容"""
        if not self.user_real_query:
            real_query_agent = RealQueryAgent(llm=self.llm)
            self.user_real_query = real_query_agent.get_real_query()

        log.info(f'get result without entity: {self.user_real_query}')
        common_result = self.vector_store.similarity_search_by_text(
            text=self.user_real_query,
            k=self.query_limit,
        )
        if not common_result:
            common_result = []
        common_result = [_ for _ in common_result if _.score >= self.query_score_limit]
        if not common_result:
            return StopEvent(result='No text found.')
        self._add_history(documents=common_result)
        return FinalResultEvent(
            query_times=event.query_times + 1,
        )

    @step(retry_policy=ConstantDelayRetryPolicy())
    async def get_final_result(self, event: FinalResultEvent) -> StopEvent:
        final_result = await self._get_final_result(
            with_compress=self.compress_results,
            real_query=self.user_real_query,
        )
        log.info(f'Document retrieve flow finished. [result={final_result}]')
        return StopEvent(result=final_result)
