"""
知识图谱查找，查找逻辑：
1. 使用 LLM 提取实体，并查找实体
2. 询问 LLM 当前是否解决问题
3. 询问 LLM 下一步需要查找的方向
4. 根据 3 的方向查找
5. 循环 2
"""
import json
from typing import Optional, Callable, List, Dict, Any

from jinja2 import Template
from langchain_core.messages import BaseMessage, AIMessage, SystemMessage, HumanMessage
from llama_index.core.workflow import step, StartEvent, StopEvent, Event, WorkflowRuntimeError
from llama_index.core.workflow.retry_policy import ConstantDelayRetryPolicy
from llama_index.core.workflow.service import ServiceManager

from framework.prompt import PromptFactory
from common import tools
from framework.graph_store import LarkGraphStore, Edge, GraphLabelValue, Vertex
from framework.models import BaseLLM
from .base_retrieve_flow import RetrieveFlowWithHistory

log = tools.get_logger()

GRAPH_STRUCTURE_PROMPT = '''
知识图谱中包含的节点有：
  > * 实体节点：包括多种节点类型，用来表示实体，即具体的事物，比如某个功能是个实体，某个人是个实体等。实体是从文本块中抽取而来的，更多实体信息可以参考文本块；
  > * 社区节点：由多个同类或者有一定关联的实体组合而成的社区。社区可以关联有多个实体和多个文本块；
  > * 文本块：由完整的文档分割而来的文本段落，实体是从文本块中抽取出来的；
  > * 文档：原始文档的信息，文本块是由文档通过一定方式分割而来的。

更详细的不同节点类型(node_type)和描述如下：
__VERTEX_TYPE_AND_DESCS__

知识图谱中包含的关系类型(edge_type)和信息有：
__EDGE_TYPE_AND_DESCS__
'''

async def get_graph_structure_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="GRAPH_STRUCTURE_PROMPT",
        prompt_name="知识图谱检索机器人 - 系统提示词",
        prompt_desc="知识图谱检索机器人 - 系统提示词",
        prompt_content=GRAPH_STRUCTURE_PROMPT,
    )
    return template

NEXT_QUERY_PROMPT = '''
# 职责描述
你是一个智能知识图谱助手，你正在帮助用户解决问题。
由于前面的知识图谱检索没有检索到足够的信息回复用户，所以需要你帮助决策下一步需要怎么检索知识图谱。

# 知识图谱的一些基本信息
GRAPH_STRUCTURE_PROMPT

# 已经检索到的知识信息
__KNOWLEDGE_HISTORY__

# 用户的提问
__USER_QUERY__

# 回复要求
你的回复必须遵循以下格式：
***
Thought: 当前问题已经解决了 xx，为了解决 xx 问题，我需要在知识图谱中做 xx 和 xx 操作以查询 xx 信息
Step: 我需要通过 xx 节点遍历 xx，并通过 xx 实体名称找到实体节点...
Do: 
```json
[
    {"type":"walk", "src_node_id":<source_node_id>, "dst_node_types":<dst_node_types>, "edge_types":<filter_edge_types>, "walk_step":<walk_step>},
    {"type":"walk", "src_node_id":<source_node_id>, "dst_node_types":<dst_node_types>, "edge_types":<filter_edge_types>, "walk_step":<walk_step>},
    {"type":"find", "node_names":<node_names>},
]
```

回复格式说明：
 * 在 Thought 中，你需要先按步骤分析用户的问题和已检索到的知识信息，并给出尚待检索的信息。
 * 在 Step 中，然后你需要结合知识图谱信息，逐步给出需要怎么检索。
 * 在 Do 中，需要你另起一行按照也定格式给出检索步骤。

最终 Do 中的检索步骤是一个包含在 markdown 的代码块中的 json 字符串。
其中：
    * <type> 表示节点的类型，取值可以为 "walk" 或者 "find"。walk 表示从某个节点开始遍历，需要指定 <source_node_id>；find 表示寻找名称为 <node_names> 的节点信息（注意节点信息如果已存在，不要重复寻找）。
    * <source_node_id> 表示遍历的开始节点 id；
    * <dst_node_types> 表示需要遍历的目标节点类型，是个字符串列表，列表可以为空；
    * <filter_edge_types> 表示遍历时需要筛选的边类型，是个字符串列表，列表可以为空；
    * <walk_step> 表示遍历的步数，指的是从 0 到 <walk_step> 步（即知识图谱中的 <walk_step> 跳遍历）；
    * <node_name> 只有在 type=find 的时候才需要设置，表示需要寻找的节点名称列表，节点名称是你自己从上下文中判读出来还需要检索的节点。
你必须始终以 `Thought: ` 开头，Thought 中你需要思考当前提供的内容是否足够回答用户问题，以及需要谁做什么等。最后的 Do 下面的步骤必须另起一行使用 markdown 的代码块包裹 json。
在你的回复中必须有且仅有一个 markdown 代码块！

一个示例的回复 json：
```json
[
    {"type":"walk", "src_node_id":"kiuhay78sxiabsuxajsx", "dst_node_types":["community","text_unit"], "edge_types":[], "walk_step":3},
    {"type":"find", "node_names":["流程画布", "运营计划"]}
]
```

注意：你回复的 json 中的步骤不能超过10个，如果必须超过10个，则回复最急需被检索的10个。
'''

async def get_next_query_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="GRAPH_RETRIEVE_NEXT_QUERY_PROMPT",
        prompt_name="知识图谱检索机器人 - 下一步查询提示词",
        prompt_desc="知识图谱检索机器人 - 下一步查询提示词",
        prompt_content=NEXT_QUERY_PROMPT,
    )
    return template

ASK_PROBLEM_SOLVED_PROMPT = '''
# 职责描述
你是一个智能助手，仔细阅读下面的对话历史，判断 assistant 给的内容中是否需要。

{{chat_history}}

# 回复要求
如果当前文档中的内容已经能完全满足对话历史中的需求，则你需要回复且仅需要回复 YES，否则仅需要回复 NO

# 文档内容列表
{{documents}}
'''

async def get_ask_problem_solved_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="GRAPH_RETRIEVE_ASK_PROBLEM_SOLVED_PROMPT",
        prompt_name="知识图谱检索机器人 - 判断问题是否解决提示词",
        prompt_desc="知识图谱检索机器人 - 判断问题是否解决提示词",
        prompt_content=ASK_PROBLEM_SOLVED_PROMPT,
    )
    return template

ENTITY_EXTRACT_PROMPT = '''
# 职责
你是一个严谨的人工智能助手，你需要仔细分析用户需要检索的内容，并识别出来需要检索的实体。
你识别出来的实体中应该包括用户关心的所有实体，不能丢失任何内容信息。

# 识别操作步骤
1. 识别实体，对于每个识别出的实体，输出其实体名称。

比如针对这句话：好的，神策智能运营是有神策数据(sensorsdata)开发的一款基于用户的智能运营工具（MA工具）
应该识别出来的实体名称为：神策，神策智能运营，神策数据，MA工具，sensorsdata

注意：在这一步如果你需要提取的实体超过了10个，那就返回与用户的提问最相关的10条即可。

2. 为了检索的时候，能够更准确的匹配，你提取出来实体以后，需要基于当前对话上下文将实体的名称进行扩展，扩展规则有：
 - 用户想检索的近似的实体名称，比如「神策」和「神策数据」近似，「程序」和「代码」近似等；
 - 扩展名词，从当前词扩展用户可能关心的其他名词，比如：用户问骑车从天府三街到天府五街耗时多长时间？你应该自动把车扩展为「自行车」、「电动车」、「摩托车」等用户可以骑的车；
 - 大小写、缩写扩展，比如 sensorsdata 是两个词 sensors 和 data 的组合，可以扩展为 SensorsData、SENSORSDATA 等，而 MA 是一个缩写，可以扩展为 Marketing Automation、ma 等。

比如针对步骤 1 中的实体，应该扩展为：神策，神策智能运营，智能运营，神策数据，MA工具，MA，ma，Marketing Automation，自动化营销，sensorsdata，SensorsData

3. 扩展完成名称后，你需要按照代码的形式进行输出，输出内容为包括在 markdown 代码块中的 json 字符串，比如：
```json
{
    "entities": ["神策", "神策智能运营", "智能运营", "神策数据", "MA工具"，"MA", "Marketing Automation", "自动化营销", "sensorsdata", "SensorsData"]
}
```

# 识别举例
举例如下：
```
历史对话：
user: 泛金融行业有哪些产品可以售卖？
assistant: 泛金融行业可售卖产品包括：SA基础版、SA旗舰版
user: 那基础版和旗舰版有什么区别？
```
一个好的回复示例如下：

 * 1. 用户是想了解泛金融行业的SA基础版和SA旗舰版的区别，所需要用到的实体包括：SA、基础版、旗舰版、SA基础版、SA旗舰版，而在当前的检索结果中，不包含的实体信息有 SA、SA基础版、xxx
 * 2. 为了更准确的匹配实体信息，需要将实体名称扩展同义词，扩展之后有：SA、SA基础版、神策分析、神策分析基础版、...
 * 3. 输出内容。
```json
{
    "entities": ["SA", "SA基础版", "神策分析", "神策分析基础版"]
}
```

# 回复方式
为了更准确的识别到实体信息，你应该先逐步分析用户的对话，然后再给出你所识别出来的需要检索的内容。
你应该将实体提取、扩展的步骤都详细回复，不能直接回复 json。

如果没有需要提取的内容，则直接返回空即可。
注意不要输出与用户的问题直接和间接推理都无关的内容！

开始：

历史对话：
***

'''

async def get_entity_extract_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="GRAPH_RETRIEVE_ENTITY_EXTRACT_PROMPT",
        prompt_name="知识图谱检索机器人 - 实体抽取提示词",
        prompt_desc="知识图谱检索机器人 - 实体抽取提示词",
        prompt_content=ENTITY_EXTRACT_PROMPT,
    )
    return template

def _get_graph_edge_content_as_doc(edge: Edge) -> str:
    """将内容转换为字符串"""
    edge_type = edge.values[0] if edge.values else None
    content = [
        f"边: {edge.src_vid} -- [{edge_type.label_name if edge_type is not None else '?'}] -> {edge.dst_vid}",
        f'\t边rank值：{str(edge.rank)}'
    ]
    if edge_type is not None and edge_type.properties:
        content.append(f'\t\t边属性：{edge_type.properties}')
    return '\n'.join(content)


def _get_graph_vertex_content_as_doc(vertex: Vertex) -> str:
    """将内容转换为字符串"""
    content = [f'实体节点ID: {vertex.id}']
    entity_name = None
    if vertex.values:
        for value in vertex.values:
            content.append(f'\t实体标签：{value.label_name}')
            if value.properties:
                entity_name = value.properties.get('name', None)
                content.append(f'\t\t实体标签属性：{value.properties}')
    if entity_name:
        content.insert(0, f'实体名称：{entity_name}')
    return '\n'.join(content)


class BaseFlowEvent(Event):
    query_times: int = 0


class EntitiesEvent(BaseFlowEvent):
    entities: List[str] | None


class QueryFinishedEvent(BaseFlowEvent):
    """表示查询结束，需要判断"""
    query_times: int = 0


class ContinuePrecessEvent(BaseFlowEvent):
    reason: str | None = None


class NextQueryEvent(BaseFlowEvent):
    steps: List[Dict[str, Any]]


class FinalResultEvent(BaseFlowEvent):
    """表示需要开始构建最终结果"""


class GraphRetrieveFlow(RetrieveFlowWithHistory):
    def __init__(
            self,
            llm: BaseLLM,
            graph_store: LarkGraphStore,
            user_real_query: str,
            chat_history: List[BaseMessage],
            max_token_limit: int = 1024 * 8,
            query_limit: int = 50,
            timeout: Optional[float] = 24 * 60 * 60,
            disable_validation: bool = False,
            verbose: bool = False,
            service_manager: Optional[ServiceManager] = None,
            num_concurrent_runs: Optional[int] = None,
            callback: Callable[[Any], None] | None = None,
            max_chat_round_limit: int = 5,
    ):
        super(GraphRetrieveFlow, self).__init__(
            llm=llm,
            user_real_query=user_real_query,
            chat_history=chat_history,
            max_token_limit=max_token_limit,
            query_limit=query_limit,
            timeout=timeout,
            disable_validation=disable_validation,
            verbose=verbose,
            service_manager=service_manager,
            num_concurrent_runs=num_concurrent_runs,
            callback=callback,
        )
        self.graph_store = graph_store

        # 最大多次查询次数
        self.max_chat_round_limit = max_chat_round_limit

        # 已存在的实体、边等缓存
        self.exist_entities = set()
        self.exist_edges = set()

    def _get_edge_id(self, edge: Edge) -> str:
        """生成 获取边 id"""
        return edge.src_vid + ' -> [' + str(edge.rank) + '] -> ' + edge.dst_vid

    def _add_history(
            self,
            vertexes: List[Vertex] | None = None,
            edges: List[Edge] | None = None,
    ):
        if not vertexes and not edges:
            return
        if vertexes:
            all_vertexes = [_get_graph_vertex_content_as_doc(_) for _ in vertexes]
            all_vertexes = '\n'.join(all_vertexes)
            self.add_chat_history(AIMessage(content=f'nodes or vertexes:\n```{all_vertexes}\n```'))
        if edges:
            all_edges = [_get_graph_edge_content_as_doc(_) for _ in edges]
            all_edges = '\n'.join(all_edges)
            self.add_chat_history(AIMessage(content=f'edges or relationships:\n```{all_edges}\n```'))

    @step(retry_policy=ConstantDelayRetryPolicy())
    async def get_entities(self, event: StartEvent) -> EntitiesEvent | StopEvent:
        """将历史对话解析出来，并提取"""
        messages = []
        history = self.get_chat_history()
        for h in history:
            if isinstance(h, HumanMessage):
                messages.append(f'user: {h.content}')
            else:
                messages.append(f'assistant: {h.content}')

        entity_extract_prompt = await get_entity_extract_prompt()
        completion = await self.llm.acomplete(
            input=[
                SystemMessage(content=entity_extract_prompt.render() + '\n'.join(messages)),
            ]
        )
        content = tools.extract_code_from_text(completion.content)
        if not content:
            return StopEvent(result='No entities found.')
        content = json.loads(content)
        entities = [_ for _ in content['entities']]
        if not entities:
            return StopEvent(result='No entities found.')
        return EntitiesEvent(query_times=0, entities=entities)

    @step(retry_policy=ConstantDelayRetryPolicy())
    async def query_entities(self, event: EntitiesEvent) -> QueryFinishedEvent | StopEvent:
        vertex_names = event.entities
        vertexes = []
        vertex_names = set(vertex_names)  # 去重
        for name in vertex_names:
            vertex_graph_data = self.graph_store.filter_vertexes(
                label_filter=GraphLabelValue(properties={'name': name}),
                limit=self.query_limit,
            )
            if vertex_graph_data.vertexes:
                vertexes.extend(vertex_graph_data.vertexes)

        if not vertexes:
            return StopEvent(result='No entities found from graph.')

        for vertex in vertexes:
            self.exist_entities.add(vertex.id)

        self._add_history(vertexes=vertexes)
        return QueryFinishedEvent(query_times=event.query_times)

    @step(retry_policy=ConstantDelayRetryPolicy())
    async def ask_problem_solved(self, event: QueryFinishedEvent) -> ContinuePrecessEvent | FinalResultEvent:
        if event.query_times >= self.max_chat_round_limit:
            return FinalResultEvent(query_times=event.query_times)
        messages = []
        history = self.get_chat_history(with_retrieved=True)
        for h in history:
            if isinstance(h, HumanMessage):
                messages.append(f'user: {h.content}')
            else:
                messages.append(f'assistant: {h.content}')

        ask_problem_solved_prompt = await get_ask_problem_solved_prompt()
        # todo 变量是如何替换的？
        completion = await self.model_client.create(
            messages=[
                SystemMessage(content=ASK_PROBLEM_SOLVED_PROMPT.replace('QAR_DATA', '\n***\n'.join(messages))),
            ]
        )
        content = completion.content
        if 'YES' == content.upper().strip():
            return FinalResultEvent(query_times=event.query_times)
        else:
            return ContinuePrecessEvent(query_times=event.query_times + 1, reason='Not resolved problem.')

    @step(retry_policy=ConstantDelayRetryPolicy())
    async def get_final_result(self, event: FinalResultEvent) -> StopEvent:
        final_result = await self._get_final_result()
        return StopEvent(result=final_result)

    @step(retry_policy=ConstantDelayRetryPolicy())
    async def get_next_step(self, event: ContinuePrecessEvent) -> NextQueryEvent | FinalResultEvent:
        """获取下一步的操作步骤，由于有些模型还不支持 function call，这里就先不用 function call"""
        schema = self.graph_store.get_schema()
        vertex_desc = []
        if schema.vertex_labels:
            for label in schema.vertex_labels:
                vertex_desc.append(f'  > * {label.label_name}:{label.comment}')
        edge_desc = []
        if schema.edge_labels:
            for label in schema.edge_labels:
                edge_desc.append(f'  > * {label.label_name}:{label.comment}')
        next_query_prompt = await get_next_query_prompt()
        graph_structure_prompt = await get_graph_structure_prompt()
        prompt = next_query_prompt.render()
        prompt = prompt.replace('GRAPH_STRUCTURE_PROMPT', graph_structure_prompt.render())
        prompt = prompt.replace('__KNOWLEDGE_HISTORY__', '\n***\n'.join(h.content for h in self.chat_history))
        prompt = prompt.replace('__USER_QUERY__', self.user_real_query)
        prompt = prompt.replace('__VERTEX_TYPE_AND_DESCS__', '\n'.join(vertex_desc))
        prompt = prompt.replace('__EDGE_TYPE_AND_DESCS__', '\n'.join(edge_desc))

        completion = await self.model_client.create(
            messages=[
                SystemMessage(content=prompt),
            ]
        )
        content = tools.extract_code_from_text(completion.content)
        if not content:
            return FinalResultEvent(query_times=event.query_times)
        content = json.loads(content)
        return NextQueryEvent(query_times=event.query_times, steps=content)

    @step(retry_policy=ConstantDelayRetryPolicy())
    async def run_next_step(self, event: NextQueryEvent) -> QueryFinishedEvent | FinalResultEvent:
        """
        step 格式样例：
        [
            {"type":"walk", "src_node_id":"kiuhay78sxiabsuxajsx", "dst_node_types":["community","text_unit"], "edge_types":[], "walk_step":3}
            {"type":"find", "node_names":["流程画布", "运营计划"]}
        ]
        """
        steps = event.steps
        if not steps:
            return FinalResultEvent(query_times=event.query_times)

        all_vertexes = []
        all_edges = []
        for query_step in steps:
            try:
                query_type = query_step.get('type')
                if "walk" == query_type:
                    dst_node_types = query_step.get('dst_node_types', [])
                    edge_types = query_step.get('edge_types', [])
                    vertex_labels = [GraphLabelValue(label_name=label_name) for label_name in dst_node_types]
                    edge_labels = [GraphLabelValue(label_name=edge_type) for edge_type in edge_types]

                    sub_graph = self.graph_store.walk(
                        src_vid=query_step['src_node_id'],
                        dst_vertex_label_filters=vertex_labels,
                        edge_label_filters=edge_labels,
                        max_step=int(query_step['walk_step']),
                        limit=self.query_limit * 5,
                    )
                    all_vertexes.extend(sub_graph.vertexes if sub_graph.vertexes else [])
                    all_edges.extend(sub_graph.edges if sub_graph.edges else [])
                else:
                    node_names = query_step.get('node_names', None)
                    if not node_names:
                        raise WorkflowRuntimeError('No node names found.')
                    vertex_names = set(node_names)

                    for name in vertex_names:
                        if name in self.exist_entities:
                            continue
                        vertex_graph_data = self.graph_store.filter_vertexes(
                            label_filter=GraphLabelValue(properties={'name': name}),
                            limit=self.query_limit,
                        )
                        if vertex_graph_data.vertexes:
                            all_vertexes.extend(vertex_graph_data.vertexes)
            except BaseException as e:
                log.warning(f'run next step for graph failed. [step={query_step}]', e)

        # 全局去重过滤
        all_vertexes = [v for v in all_vertexes if v.id not in self.exist_entities]
        all_edges = [edge for edge in all_edges if self._get_edge_id(edge) not in self.exist_edges]
        self.exist_entities.update([v.id for v in all_vertexes])
        self.exist_edges.update([self._get_edge_id(edge) for edge in all_edges])

        self._add_history(vertexes=all_vertexes, edges=all_edges)
        return QueryFinishedEvent(query_times=event.query_times)
