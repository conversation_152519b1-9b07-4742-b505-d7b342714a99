from typing import Optional, Callable, List, Any

from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from llama_index.core.workflow.service import ServiceManager

from common import tools
from framework.agents.assistant import AssistantAgent
from framework.agents.compress import contextual_compression
from framework.agents.compressed_assistant import CompressedAssistantAgent
from framework.workflow.base import WorkFlowWithCallback
from framework.models import BaseLLM

log = tools.get_logger()

FINAL_RESULT_PROMPT = '''
# 职责描述
仔细阅读下列对话历史和参考资料，从中摘取出对回答用户问题有用的内容：
对话历史：
***
{chat_history}


参考资料：
***
{documents}

# 待回复的用户问题
***
{real_query}

# 回复要求
***
1. 你应该从给你的上下文中摘取内容，而不是自己总结内容。
2. 上下文的对话内容中，如果是用户的问句，或者是系统提示，则直接剔除，否则可以从其中摘取对用户查询有帮助的内容回复。
3. 直接回复内容本身，不要重复问题。
4. 你应该尽量保留对回复用户的查询问题有用的内容原文。

# 开始：
你摘取的内容：
'''


class RetrieveFlowWithHistory(WorkFlowWithCallback):
    def __init__(
            self,
            llm: BaseLLM,
            chat_history: List[BaseMessage],
            max_token_limit: int = 1024 * 8,
            query_limit: int = 5,
            timeout: Optional[float] = 24 * 60 * 60,
            disable_validation: bool = False,
            verbose: bool = False,
            service_manager: Optional[ServiceManager] = None,
            num_concurrent_runs: Optional[int] = None,
            callback: Callable[[Any], None] | None = None,
    ):
        super(RetrieveFlowWithHistory, self).__init__(
            callback=callback,
            timeout=timeout, disable_validation=disable_validation,
            verbose=verbose, service_manager=service_manager,
            num_concurrent_runs=num_concurrent_runs,
        )
        self.llm = llm
        self.query_limit = query_limit
        if not chat_history:
            chat_history = []
        self._chat_history = chat_history
        self.max_token_limit = max_token_limit
        """
        self.assistant = CompressedAssistantAgent(
            llm=self.llm,
            max_token_limit=max_token_limit,
            compress_rate=0.6,
        )
        """
        self.query_results = []

    def add_chat_history(self, message: BaseMessage):
        if message is not None:
            self.query_results.append(message)

    def get_chat_history(self, with_retrieved: bool = False):
        history = self._chat_history.copy()
        if with_retrieved:
            history.extend(self.query_results)
        return history

    async def _get_final_result(
            self,
            with_compress: bool = False,
            real_query: str | None = None,
    ) -> str:
        documents = '\n'.join([h.content for h in self.query_results])
        if not with_compress:
            return documents

        history = self.get_chat_history()
        history_messages = [f'{h.type}: {h.content}' for h in history]
        # compress documents
        message, tokens = await contextual_compression(
            user_query=real_query,
            history=[AIMessage(content=documents)],
            input_token_limit=self.max_token_limit,
            model_client=self.llm,
            name='retriever',
            compress_rate=0.6,
        )
        message = '\n'.join([msg.content for msg in message])
        log.info(f'after compress tokens: {tokens}, message={message}')

        """
        system_prompt = FINAL_RESULT_PROMPT.format(
            chat_history='\n'.join(history_messages),
            documents=message,
            real_query=real_query,
        )
        agent = AssistantAgent(llm=self.llm, system_prompt=system_prompt)
        return await agent.achat()
        """
        return message
