from typing import List

from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage


def extract_history_with_qar_context(
        chat_history: List[BaseMessage],
        real_query: str | None = None,
) -> List[str]:
    """将历史对话组合成 Q-A-R 格式"""
    chat_history = chat_history.copy()
    messages = []
    for msg in chat_history:
        if isinstance(msg, SystemMessage) or (isinstance(msg, HumanMessage) and msg.source == 'system'):
            continue
        messages.insert(0, msg)

    prompt_messages = []
    in_retrieve = False
    retrieve_context = ''
    for idx, msg in enumerate(messages):
        if idx == 0 and msg.source != 'user':
            in_retrieve = True
        elif msg.source == 'user':
            in_retrieve = False
        if in_retrieve:
            retrieve_context = f'## 新段落：\n{msg.content}\n{retrieve_context}'
        else:
            prompt_messages.append(("Q: " if msg.source == 'user' else "A: ") + msg.content)
    if real_query:
        prompt_messages.append('Q: ' + real_query)
    prompt_messages.append('R:\n' + retrieve_context)
    return prompt_messages
