from typing import Callable, Any

from jinja2 import Template

from framework.prompt import PromptFactory
from common import tools
from framework.bot import Bo<PERSON>
from framework.agents.schema import Conversation, Role
from framework.agents.autogen import AutoGenWorkflow, ReactManagerAgent
from framework.graph_store import LarkGraphStore
from framework.vector_store import LarkVectorStore
from framework.models import BaseLLM
from .documents_retrieve import DocumentsRetrieve
from .graph_retrieve import GraphRetrieve
from .subgraph.sub_graph_retrieve import FindEntityAgent, GraphWalkerAgent

log = tools.get_logger()

MANAGER_NAME = 'retrieve_manager'
MANAGER_PROMPT = """
# 职责描述
你正在一个角色扮演游戏中，这个游戏旨在尽最大努力给用户的话题提供参考，参与角色如下：
{{role_and_descriptions}}

需要注意，knowledge_retriever 的检索会比较全面，但是可能缺少详细信息（来源原始文档等），而 document_retriever 可以提供文档内容，但是由于是根据相似度匹配检索，所以可能会出现检索不全面。

# 背景知识及文档内容相关解释
为了能更好的玩这个游戏，你需要以下背景知识：
{{background_knowledge}}

# 输出格式
你需要按照以下格式回复：
```
Thought: 当前问题已经解决了 xx，为了解决 xx 问题，我需要 xx 角色来做 xx 事情
Choose: 角色名称，角色名称只能从 {{participants}} 中选一个
Do: 角色需要做的事情，比如：请你继续分析用户的意图
```
你必须始终以 `Thought: ` 开头，Thought 中你需要思考当前提供的内容是否足够回答用户问题，以及需要谁做什么等。
不允许使用 markdown 代码块标记回复！如果回复中包含代码信息，则可以使用 markdown 代码块标记。
如果你所选的角色已经回复了你，则他们会加上他们自己的回复，比如：Do Response: 这里是所选择的role的回复内容。
需要注意：只有 reporter 才能与用户沟通，用户无法与其他角色沟通，所以需要最终回复的内容请明确告知 reporter.

# 输出结果的步骤
你需要不断重复上述格式步骤，直至检索到足够的信息为止。需要注意的是，其他角色的发言通常不全（或者会用局部信息欺骗你），为了保证用户能获取足量的信息，你应该尽可能的让他们发言，直到无法得到新的信息为止。
如果其他角色已经无法解决问题，或者出现了很多重复或者无意义的讨论，你应该直接让 reporter 收集结果并回复用户。
注意，reporter 角色只有在需要终止上述思考步骤的时候才需要调用，否则不要指定 reporter 角色。

注意，你不能从对话内容中获取角色名称！

# 用户对话记录与需求
仔细阅读下面的对话记录，并从中识别到用户(user/human)最终的需求，然后按照上述格式解决用户需求。
***
{{chat_history}}

{{query_prompt}}

开始：
"""

async def get_agents_bot_manager_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="AGENTS_BOT_MANAGER_PROMPT",
        prompt_name="多角色检索机器人 - 管理者提示词",
        prompt_desc="多角色检索机器人 - 管理者提示词",
        prompt_content=MANAGER_PROMPT,
    )
    return template


class RetrieveAgentBot(Bot):
    def __init__(
            self,
            name: str,
            llm: BaseLLM,
            vector_store: LarkVectorStore,
            graph_store: LarkGraphStore,
            max_token_limit: int = 1024 * 8,
            query_limit: int = 20,
            max_talk_round: int = 10,
            score_limit: float = 0.45,
            graph_only: bool = False,
            documents_only: bool = True,
            callback: Callable[[Any], None] | None = None,
            **kwargs
    ):
        super().__init__(name, callback=callback)
        self.name = name
        self.llm = llm
        self.vector_store = vector_store
        self.graph_store = graph_store
        self.max_token_limit = max_token_limit
        self.query_limit = query_limit
        self.max_talk_round = max_talk_round
        self.score_limit = score_limit
        self.graph_only = graph_only
        self.documents_only = documents_only
        if self.graph_only and self.documents_only:
            raise RuntimeError("graph_only & documents_only can not be both True!")
        self.kwargs = kwargs
        self.workflow = None

    async def async_chat(self, history: list[Conversation]) -> Conversation:
        self._init_workflow_graph()

        messages = []
        for message in history:
            if message.role == Role.ASSISTANT:
                messages.append({'role': 'assistant', 'content': message.content})
            else:
                messages.append({'role': 'user', 'content': message.content})
        result = await self.workflow.run_with_history(history=messages)
        return Conversation(role=Role.ASSISTANT, content=result.content)

    def _init_workflow_graph(self):
        """初始化检索 autogen 图"""
        if self.workflow is not None:
            return
        log.info(f'document_only = {self.documents_only}, graph_only={self.graph_only}')
        agents = []

        """
        agents.append(GraphRetrieve(
            llm=self.llm,
            graph_store=self.graph_store,
            max_token_limit=self.max_token_limit,
            query_limit=self.query_limit,
            max_talk_round=self.max_talk_round,
            logs_callback=self.callback,
            **self.kwargs,
        ))"""
        if not self.documents_only:
            agents.append(FindEntityAgent(
                llm=self.llm,
                graph_store=self.graph_store,
                max_token_limit=self.max_token_limit,
                query_limit=self.query_limit,
                logs_callback=self.callback,
                **self.kwargs,
            ))
            agents.append(GraphWalkerAgent(
                llm=self.llm,
                graph_store=self.graph_store,
                max_token_limit=self.max_token_limit,
                query_limit=self.query_limit,
                logs_callback=self.callback,
                **self.kwargs,
            ))
        if not self.graph_only:
            agents.append(
                DocumentsRetrieve(
                    llm=self.llm,
                    vector_store=self.vector_store,
                    max_token_limit=self.max_token_limit,
                    query_limit=self.query_limit,
                    score_limit=self.score_limit,
                    **self.kwargs,
                )
            )

        manager = ReactManagerAgent(
            name=MANAGER_NAME,
            llm=self.llm,
            system_prompt=tools.asyncio_run(get_agents_bot_manager_prompt),
            **self.kwargs,
        )

        self.workflow = AutoGenWorkflow(
            manager_agent=manager,
            participant_agents=agents,
            need_extract_real_query=True,  # 需要重新提取问题
            max_talk_round=self.max_talk_round,
            step_callback=self.callback,
        )
