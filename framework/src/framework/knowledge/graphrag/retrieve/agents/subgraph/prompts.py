from jinja2 import Template

from framework.prompt import PromptFactory

GRAPH_STRUCTURE_PROMPT = '''
图数据库选型：{{graph_type}}

知识图谱中包含的节点有：
  > * 实体节点：包括多种节点类型，用来表示实体，即具体的事物，比如某个功能是个实体，某个人是个实体等。实体是从文本块中抽取而来的，更多实体信息可以参考文本块；
  > * 社区节点：由多个同类或者有一定关联的实体组合而成的社区。社区可以关联有多个实体和多个文本块；
  > * 文本块：由完整的文档分割而来的文本段落，实体是从文本块中抽取出来的；
  > * 文档：原始文档的信息，文本块是由文档通过一定方式分割而来的。

更详细的不同节点类型和描述如下：
node_types:
```
{{node_types}}
```

知识图谱中包含的关系类型和信息有：
edge_types:
```
{{edge_types}}
```
'''

async def get_graph_structure_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="GRAPH_STRUCTURE_PROMPT",
        prompt_name="知识图谱检索机器人 - 系统提示词",
        prompt_desc="知识图谱检索机器人 - 系统提示词",
        prompt_content=GRAPH_STRUCTURE_PROMPT,
    )
    return template

MANAGER_PROMPT_REACT = """
# 职责描述
你正在一个角色扮演游戏中，这个游戏旨在尽最大努力通过检索图数据库来满足用户需求，参与角色如下：
{{role_and_descriptions}}

# 背景知识及文档内容相关解释
为了能更好的玩这个游戏，你需要以下背景知识：
{{background_knowledge}}

# 输出格式
你需要按照以下格式回复：
```
Thought: 当前问题已经解决了 xx，为了解决 xx 问题，我需要 xx 角色来做 xx 事情
Choose: 角色名称，角色名称只能从 {participants} 中选一个
Do: 角色需要做的事情，比如：请你继续分析用户的意图
```
你必须始终以 `Thought: ` 开头，Thought 中你需要思考当前提供的内容是否足够回答用户问题，以及需要谁做什么等。
不允许使用 markdown 代码块标记回复！如果回复中包含代码信息，则可以使用 markdown 代码块标记。
如果你所选的角色已经回复了你，则他们会加上他们自己的回复，比如：Do Response: 这里是所选择的role的回复内容。
需要注意：只有 reporter 才能与用户沟通，用户无法与其他角色沟通，所以需要最终回复的内容请明确告知 reporter.

# 输出结果的步骤
你需要不断重复上述格式步骤，直至检索到足够的信息为止。需要注意的是，其他角色的发言通常不全（或者会用局部信息欺骗你），为了保证用户能获取足量的信息，你应该尽可能的让他们发言，直到无法得到新的信息为止。
如果其他角色已经无法解决问题，或者出现了很多重复或者无意义的讨论，你应该直接让 reporter 收集结果并回复用户。
注意，reporter 角色只有在需要终止上述思考步骤的时候才需要调用，否则不要指定 reporter 角色。
注意尽量不要让其他角色重复执行他已经执行过的步骤！

已经执行过的步骤包括：
```
{{steps_history}}
```

注意，你不能从对话内容中获取角色名称！

# 图数据库的 schema 说明
GRAPH_STRUCTURE_PROMPT

# 用户对话记录与需求
仔细阅读下面的对话记录，并从中识别到用户(user/human)最终的需求，然后按照上述格式解决用户需求。
***
{{chat_history}}

{{query_prompt}}

开始：
"""

async def get_graph_retrieve_manager_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="GRAPH_RETRIEVE_MANAGER_PROMPT",
        prompt_name="知识图谱检索机器人 - 管理者提示词",
        prompt_desc="知识图谱检索机器人 - 管理者提示词",
        prompt_content=MANAGER_PROMPT_REACT,
    )
    return template

WALK_PROMPT = """
需要你严格使用以下 json 格式告诉用户需要在图中遍历的步骤，遍历步骤示例：
```json
{"src_node_id":<source_node_id>, "dst_node_types":<dst_node_types>, "edge_types":<filter_edge_types>, "walk_step":<walk_step>, "offset": <offset>}
```
其中：
    * <source_node_id> 表示遍历的开始节点 id；
    * <dst_node_types> 表示需要遍历的目标节点类型，必须是包含在 schema 的 node_types 中的类型，是个字符串列表，列表可以为空；
    * <filter_edge_types> 表示遍历时需要筛选的边类型，必须是包含在 schema 的 edge_types 中的类型，是个字符串列表，列表可以为空；
    * <walk_step> 表示遍历的步数，指的是从 0 到 <walk_step> 步（即知识图谱中的 <walk_step> 跳遍历）；
    * <offset> 表示从第几条结果开始返回，如果想指定从第几条开始返回的话，需要设置 offset 值（不设置默认为 0）。

你必须将遍历步骤包含在 markdown 代码块中。
你只能回复一条遍历步骤数据！

# 图数据库的 schema 说明
GRAPH_STRUCTURE_PROMPT
"""

async def get_graph_walker_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="GRAPH_WALKER_PROMPT",
        prompt_name="知识图谱检索机器人 - 遍历提示词",
        prompt_desc="知识图谱检索机器人 - 遍历提示词",
        prompt_content=WALK_PROMPT,
    )
    return template

ENTITY_NAMES_SEP = '<SEP>'
QUERY_ENTITY_NAMES_PROMPT = """
# 职责
你是一个严谨的人工智能助手，你需要根据上下文分析出来当前需要检索的实体名称。

可以被检索的实体类型如下（类型描述中可能会提到实体名称的规则）：
```
ENTITY_SCHEMA_PROMPT
```
你需要给出的是实体名称，上述所给出的是实体类型，区别于实体名称，实体名称指的是节点的名称，一个节点可以属于一个或者多个实体类型！

# 回复要求
你应该严格按照以下三步回复：
Thought: 当前需要检索的实体是：<实体名称1>、<实体名称2>、...，在该步骤中需要思考当前需要检索的是哪些实体
Expand: 根据实体名称近义词扩展和大小写扩展为：<实体名称1>、<实体名称>、...，在该步骤中需要按照实体名称的近似、名称拆解（比如：流程画布配置可以拆解为画布、画布配置、流程画布配置）、组合、大小写转换等扩展实体名称
Final: 最终需要检索的实体名称为：
```
<实体名称1><SEP><实体名称2><SEP><实体名称3>...
```

最终的 Final 中，需要将实体名称包含在 markdown 代码块中，不同的实体名称需要使用<SEP>分开。

禁止回复除了上述要求的三步步骤以外的任何内容！
"""

async def get_query_entity_names_prompt() -> Template:
    template = await PromptFactory.async_create_or_get_prompt(
        business="focus",
        business_cname="下一代产品 Demo",
        prompt_code="GRAPH_QUERY_ENTITY_NAMES_PROMPT",
        prompt_name="知识图谱检索机器人 - 实体名称查询提示词",
        prompt_desc="知识图谱检索机器人 - 实体名称查询提示词",
        prompt_content=QUERY_ENTITY_NAMES_PROMPT,
    )
    return template
