import json
import time
from typing import Callable, Any, List

from langchain_core.messages import BaseMessage, HumanMessage, AIMessage

from common import tools
from framework.agents.autogen import AutoGenWorkflow, CompressedParticipantAgent, CompressedReactManagerAgent
from framework.graph_store import LarkGraphStore, GraphLabelValue, Edge, Vertex, GraphData
from framework.models import BaseLLM
from common import tools as my_tools
from framework.agents.compress import summary_contextual_compression
import networkx as nx

from .prompts import *

log = my_tools.get_logger()


def _get_sub_graph_as_doc(
        src_node_id: str,
        graph_data: GraphData,
        max_token_limit: int,
) -> List[str]:
    """将子图解析成文档，需要防止做上下文压缩的时候，边和节点被过分分割开，导致检索不准确，但是可能会导致消耗的 token 增多"""
    graph = graph_data.as_graph()
    if graph is None:
        return []

    nodes_dict = dict([(v.id, v) for v in graph_data.vertexes])

    edges_dict = {}
    if graph_data.edges:
        edges_dict = dict([(f'{e.src_vid}->{e.dst_vid}', e) for e in graph_data.edges])

    # 使用 bfs 遍历图，这样可以返回局部图特征
    relations = []
    vertex_ids: List[str] = [src_node_id]
    v_index = 0
    while v_index < len(vertex_ids):
        vid = vertex_ids[v_index]
        v_content = _get_graph_vertex_content_as_doc(nodes_dict.get(vid, None))
        v_index += 1
        for next_vid in nx.descendants_at_distance(graph, source=vid, distance=1):
            # 有环的，回路再遍历一次描述
            if next_vid not in vertex_ids:
                vertex_ids.append(next_vid)
            next_v_content = _get_graph_vertex_content_as_doc(nodes_dict.get(next_vid, None))
            edge = edges_dict.get(f'{vid}->{next_vid}', None)
            edge_content = _get_graph_edge_content_as_doc(edge)
            relations.append(f'{v_content}\n{next_v_content}\n{edge_content}')
    documents = tools.split_text(
        text='\n'.join(relations),
        chunk_size=int(max_token_limit * 0.65),
        chunk_overlap=200,
    )
    log.info(f'extract graph and chunked texts:\n{documents}')
    return documents


def _get_graph_edge_content_as_doc(edge: Edge) -> str:
    """将内容转换为字符串"""
    if edge is None:
        return ''
    edge_type = edge.values[0] if edge.values else None
    content = [
        f"relation: {edge.src_vid}-[{edge_type.label_name if edge_type is not None else '?'}]->{edge.dst_vid}",
        f'\trelation rank: {str(edge.rank)}'
    ]
    if edge_type is not None and edge_type.properties:
        content.append(f'\t\trelation_property：{edge_type.properties}')
    return '\n'.join(content)


def _get_graph_vertex_content_as_doc(vertex: Vertex) -> str:
    """将内容转换为字符串"""
    if vertex is None:
        return ''
    content = [f'id: {vertex.id}']
    entity_name = None
    if vertex.values:
        for value in vertex.values:
            content.append(f'\tlabel: {value.label_name}')
            if value.properties:
                entity_name = value.properties.get('name', None)
                content.append(f'\t\tlabel_property: {value.properties}')
    if entity_name:
        content.insert(0, f'name: {entity_name}')
    return '\n'.join(content)


def _get_graph_vertex_desc(graph_store: LarkGraphStore) -> str:
    schema = graph_store.get_schema()
    vertex_desc = []
    if schema.vertex_labels:
        for label in schema.vertex_labels:
            vertex_desc.append(f'{label.label_name}: {label.comment}')
    return '\n'.join(vertex_desc)


def _get_graph_desc(graph_store: LarkGraphStore) -> str:
    schema = graph_store.get_schema()
    edge_desc = []
    if schema.edge_labels:
        for label in schema.edge_labels:
            edge_desc.append(f'{label.label_name}: {label.comment}')
    graph_info_prompt = tools.asyncio_run(get_graph_structure_prompt)
    graph_info_prompt = graph_info_prompt.render(
        graph_type=graph_store.type(),
        node_types=_get_graph_vertex_desc(graph_store),
        edge_types='\n'.join(edge_desc),
    )
    return graph_info_prompt


class FindEntityAgent(CompressedParticipantAgent):
    """从节点名称开始找节点，为了保证程序稳健，不使用 ReAct 调用工具"""

    def __init__(
            self,
            graph_store: LarkGraphStore,
            query_limit: int,
            name='entity_finder',
            description='仅能使用实体名称检索图数据库中的实体(entity)',
            **kwargs
    ):
        self.entity_sep = ENTITY_NAMES_SEP
        self._system_prompt = tools.asyncio_run(get_query_entity_names_prompt).render().replace(
            'ENTITY_SCHEMA_PROMPT',
            _get_graph_vertex_desc(graph_store)
        )
        super().__init__(name=name, description=description, system_prompt=self._system_prompt, **kwargs)
        self.query_limit = query_limit
        self.graph_store = graph_store

    def run(self) -> BaseMessage:
        """运行 agent 步骤"""
        return tools.asyncio_run(self.arun)

    async def arun(self) -> BaseMessage:
        """运行 agent 步骤"""
        history = self.get_history()
        history.append(HumanMessage(content=f"你的回复：", name='system'))
        response = self.chat(prompts=history)
        self.log_and_callback(response)
        entity_names = my_tools.extract_code_from_text(response).split(self.entity_sep)
        entity_names = [e.strip() for e in entity_names if e.strip()]
        if not entity_names:
            log.warning('no entity names found')
            return self._convert_message(message={
                'role': 'assistant',
                'content': '没有成功识别需要查询的实体，请清晰给出需要查询的实体名称',
                'name': self.name
            })[0]

        all_vertexes = []
        for name in entity_names:
            vertex_graph_data = self.graph_store.filter_vertexes(
                label_filter=GraphLabelValue(properties={'name': name}),
                limit=self.query_limit,
            )
            if vertex_graph_data.vertexes:
                all_vertexes.extend(vertex_graph_data.vertexes)
        if not all_vertexes:
            return self._convert_message(message={
                'role': 'assistant',
                'content': '图谱中没有对应实体',
                'name': self.name
            })[0]
        all_vertexes = [_get_graph_vertex_content_as_doc(_) for _ in all_vertexes]

        # 需要预先压缩一下再返回
        start_time = time.time()
        self.log_and_callback('开始内容压缩 ...')
        vertex_history = [AIMessage(content=v) for v in all_vertexes]
        vertex_history, all_tokens = await summary_contextual_compression(
            history=vertex_history,
            input_token_limit=self.max_token_limit,
            model_client=self.llm,
            compress_rate=self.compress_rate,
            name=self.name,
            user_query=f'查找列表中的实体信息：{entity_names}'
        )
        self.log_and_callback(f'完成内容压缩，压缩耗时 {time.time() - start_time}s')

        return self._convert_message(message={
            'role': 'assistant',
            'content': '\n'.join([v.content for v in vertex_history]),
            'name': self.name
        })[0]


class GraphWalkerAgent(CompressedParticipantAgent):
    """从某个节点开始，walk 到其他节点"""

    def __init__(
            self,
            graph_store: LarkGraphStore,
            query_limit: int,
            name='graph_walker',
            description=('可以从某个 node_id（节点id）开始遍历图数据库。'
                         '需要指定： source_node_id 即开始节点 id，一次只能指定一个！！！'
                         'dst_node_types 即目标节点类型（可以不指定）；'
                         'edge_types 即遍历过程中只走这些类型的边；'
                         'walk_step 即从 source_node_id 开始遍历的步数；'
                         '由于graph_walker的内部限制，一次查询可能不会返回所有的数据，所以可以指定 offset 即从第几条开始返回，用于继续查询使用'),
            **kwargs
    ):
        self._system_prompt = tools.asyncio_run(get_graph_walker_prompt).render().replace('GRAPH_STRUCTURE_PROMPT', _get_graph_desc(graph_store))
        super().__init__(name=name, description=description, system_prompt=self._system_prompt, **kwargs)
        self.query_limit = query_limit
        self.graph_store = graph_store

    def _run_step(self, query_step, limit: int) -> List[str]:
        dst_node_types = query_step.get('dst_node_types', [])
        edge_types = query_step.get('edge_types', [])
        vertex_labels = [GraphLabelValue(label_name=label_name) for label_name in dst_node_types]
        edge_labels = [GraphLabelValue(label_name=edge_type) for edge_type in edge_types]
        offset = query_step.get('offset', 0)

        sub_graph = self.graph_store.walk(
            src_vid=query_step['src_node_id'],
            dst_vertex_label_filters=vertex_labels,
            edge_label_filters=edge_labels,
            max_step=int(query_step['walk_step']),
            offset=offset,
            limit=limit,
        )
        # 节点和边关联整合成文档
        documents = _get_sub_graph_as_doc(
            src_node_id=query_step['src_node_id'],
            graph_data=sub_graph,
            max_token_limit=self.max_token_limit
        )
        return documents

    def run(self) -> BaseMessage:
        """运行 agent 步骤"""
        history = self.get_history()
        history.append(HumanMessage(content=f"请给出遍历步骤：", name='system'))
        response = self.chat(prompts=history)
        response = my_tools.extract_code_from_text(response)
        try:
            query_step = json.loads(response)
            if not query_step:
                raise RuntimeError('No steps')
        except:
            log.warning(f'get walk steps error. response={response}')
            return self._convert_message(message={
                'role': 'assistant',
                'content': '没有成功识别遍历步骤，请提供充足信息后重试',
                'name': self.name
            })[0]

        documents = []
        limit = self.query_limit * 5
        try:
            docs = self._run_step(query_step=query_step, limit=limit)
        except BaseException as e:
            docs = [f'遍历失败，失败原因：{str(e)}']
        documents.extend(docs)

        if not documents:
            return self._convert_message(message={
                'role': 'assistant',
                'content': '无数据',
                'name': self.name
            })[0]

        # 需要预先压缩一下再返回
        start_time = time.time()
        self.log_and_callback('开始内容压缩 ...')
        documents = [AIMessage(content=doc) for doc in documents]
        documents, all_tokens = my_tools.asyncio_run(
            lambda: summary_contextual_compression(
                history=documents,
                input_token_limit=self.max_token_limit,
                model_client=self.llm,
                compress_rate=self.compress_rate,
                name=self.name,
            ))
        self.log_and_callback(f'完成内容压缩，压缩耗时 {time.time() - start_time}s')

        final_content = '\n'.join([doc.content for doc in documents])
        offset = query_step.get('offset', 0)
        final_content += f'\n\n已遍历范围：from={offset}, to={offset + limit}'
        return self._convert_message(message={
            'role': 'assistant',
            'content': final_content,
            'name': self.name
        })[0]

    async def arun(self) -> BaseMessage:
        """运行 agent 步骤"""
        return self.run()


class SubGraphManagerAgent(CompressedReactManagerAgent):
    """
    由于可能会存在上下文很大的情况，这里需要压缩上下文
    """

    def __init__(
            self,
            max_token_limit: int,
            max_talk_round: int,
            graph_store: LarkGraphStore,
            system_prompt: str | Template = tools.asyncio_run(get_graph_retrieve_manager_prompt),
            **kwargs,
    ) -> None:
        if isinstance(system_prompt, Template):
            system_prompt = system_prompt.render()
        system_prompt = system_prompt.replace('GRAPH_STRUCTURE_PROMPT', _get_graph_desc(graph_store))
        super().__init__(system_prompt=system_prompt, **kwargs)
        self.max_token_limit = max_token_limit
        self.max_talk_round = max_talk_round


def get_workflow(
        llm: BaseLLM,
        graph_store: LarkGraphStore,
        max_token_limit: int = 1024 * 8,
        query_limit: int = 5,
        max_talk_round: int = 2,
        logs_callback: Callable[[Any], Any] | None = None,
) -> AutoGenWorkflow:
    agents = [
        FindEntityAgent(
            llm=llm,
            graph_store=graph_store,
            max_token_limit=max_token_limit,
            query_limit=query_limit,
            logs_callback=logs_callback,
        ),
        GraphWalkerAgent(
            llm=llm,
            graph_store=graph_store,
            max_token_limit=max_token_limit,
            query_limit=query_limit,
            logs_callback=logs_callback,
        ),
    ]

    manager = SubGraphManagerAgent(
        name='graph_sub_manager',
        llm=llm,
        graph_store=graph_store,
        max_token_limit=max_token_limit,
        max_talk_round=max_talk_round,
    )

    return AutoGenWorkflow(
        manager_agent=manager,
        participant_agents=agents,
        need_extract_real_query=True,  # 需要重新提取问题
        step_callback=logs_callback
    )
