import time
from typing import List
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage

from framework.agents.autogen import CompressedParticipantAgent
from framework.agents.compress import summary_contextual_compression
from framework.vector_store import LarkVectorStore

from common import tools
from .flows.document_retrieve_flow import DocumentRetrieveFlow

log = tools.get_logger()

DOC_RETRIEVER_NAME = 'document_retriever'
DOC_RETRIEVER_DESCRIPTION = f'''文档检索助手，通过相似程度和相关程度匹配文档内容'''


class DocumentsRetrieve(CompressedParticipantAgent):
    """需要压缩，防止上下文过大"""

    def __init__(
            self,
            vector_store: LarkVectorStore,
            name: str = DOC_RETRIEVER_NAME,
            description: str = DOC_RETRIEVER_DESCRIPTION,
            max_token_limit: int = 1024 * 8,
            query_limit: int = 20,
            score_limit: float = 0.45,
            **kwargs
    ) -> None:
        super().__init__(name=name, description=description, **kwargs)
        self.max_token_limit = max_token_limit
        self.vector_store = vector_store
        self.query_limit = query_limit
        self.score_limit = score_limit

    def run(self) -> BaseMessage:
        """运行 agent 步骤"""
        history = self.get_history()
        history.append(HumanMessage(content=f"请根据要求检索：", name='system'))
        workflow = self._get_workflow(history=history)
        response = tools.asyncio_run(lambda: workflow.run())

        return self._convert_message(message={'role': 'assistant', 'content': response, 'name': self.name})[0]

    async def arun(self) -> BaseMessage:
        """运行 agent 步骤"""
        history = self.get_history()
        history.append(HumanMessage(content=f"请根据要求检索：", name='system'))
        workflow = self._get_workflow(history=history)
        response = await workflow.run()

        return self._convert_message(message={'role': 'assistant', 'content': response, 'name': self.name})[0]

    def _get_workflow(self, history: List[BaseMessage]):
        return DocumentRetrieveFlow(
            llm=self.llm,
            vector_store=self.vector_store,
            chat_history=history,
            query_score_limit=0.45,
            max_token_limit=self.max_token_limit,
            query_limit=self.query_limit,
            callback=self._logs_callback,
        )
