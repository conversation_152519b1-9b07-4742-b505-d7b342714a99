from typing import Callable, Any

from framework.agents.autogen.complex_participant import ComplexParticipantAgent
from framework.graph_store import LarkGraphStore

from common import tools
from .subgraph.sub_graph_retrieve import get_workflow
from framework.models import BaseLLM

log = tools.get_logger()

GRAPH_RETRIEVER_NAME = 'knowledge_retriever'
GRAPH_RETRIEVER_DESCRIPTION = '''实体知识检索助手，可以检索比如 {entities} 类型的实体和知识，检索的内容实体覆盖可能全面，但是没有详细信息。'''


class GraphRetrieve(ComplexParticipantAgent):
    def __init__(
            self,
            llm: BaseLLM,
            graph_store: LarkGraphStore,
            max_token_limit: int = 1024 * 8,
            query_limit: int = 5,
            max_talk_round: int = 2,
            logs_callback: Callable[[Any], Any] | None = None,
            **kwargs,
    ) -> None:
        super().__init__(
            llm=llm,
            name=GRAPH_RETRIEVER_NAME,
            description=GRAPH_RETRIEVER_DESCRIPTION.format(entities=",".join(
                [
                    label.label_name
                    for label in graph_store.get_schema().vertex_labels
                ]
            )),
            workflow=get_workflow(
                llm=llm,
                graph_store=graph_store,
                max_token_limit=max_token_limit,
                query_limit=query_limit,
                max_talk_round=max_talk_round,
                logs_callback=logs_callback,
            ),
            logs_callback=logs_callback,
            **kwargs,
        )
