from typing import List, Any, Callable
from common import tools
from framework.agents.schema import Conversation
from framework.graph_store import LarkGraphStore
from framework.vector_store import LarkVectorStore
from framework.models import BaseLLM
from .agents.agents_bot import RetrieveAgentBot
from .base import KnowledgeRetrieve

log = tools.get_logger()


class RetrieveClient(KnowledgeRetrieve):
    """自定义检索引擎"""

    def __init__(
            self,
            llm: BaseLLM,
            vector_store: LarkVectorStore,
            graph_store: LarkGraphStore,
            llm_params: dict[str, Any],
            graph_only: bool = False,
            documents_only: bool = True,
            callback: Callable[[str], None] = None,
            **kwargs
    ):
        super().__init__(llm, vector_store, graph_store, llm_params, callback=callback, **kwargs)
        self.retrieve_bot = RetrieveAgentBot(
            name=tools.uuid4_no_underline(),
            llm=llm,
            vector_store=vector_store,
            graph_store=graph_store,
            llm_params=llm_params,
            graph_only=graph_only,
            documents_only=documents_only,
            callback=callback,
            **kwargs
        )

    def retrieve_data(
            self,
            history: List[Conversation] | None = None,
            retrieve_limit: int = 10,
            **kwargs
    ) -> Conversation:
        """检索经过总结的原始数据"""
        return self.retrieve_bot.chat(history=history)
