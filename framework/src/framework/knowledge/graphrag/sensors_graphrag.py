from pathlib import Path
from typing import List, Dict, Callable, Literal

from llama_index.core.workflow import Workflow

from framework import config
from common import tools
from framework.agents.schema import Conversation, Role
from framework.vector_store import LarkVectorStoreFactory
from framework.graph_store import LarkGraphStoreFactory, GraphSchema
from framework.models import BaseLLM
from framework.models.models_factory import ModelsFactory
from framework.knowledge.graphrag.index.steps.schemas import graph_schema_for_knowledge
from framework.knowledge.graphrag.index.workflows import LlamaIndexRagWorkflow
from framework.knowledge.rag_client import RagClient, KnowledgeBase
from framework.knowledge.graphrag.retrieve.retrieve_engine import RetrieveClient

log = tools.get_logger()


class SensorsGraphRag(RagClient):
    """
    graphrag implements from llama-index.
    doc: https://docs.llamaindex.ai/en/stable/examples/cookbooks/GraphRAG_v2/#graphrag-implementation-with-llamaindex-v2
    代码没有按照上述来，代码模仿微软的 graphrag 实现。
    """

    def __init__(
            self,
            kb: KnowledgeBase,
            index_prompts: Dict[str, str],
            entity_types: Dict[str, str],
            relation_types: Dict[str, str],
            llm: BaseLLM = ModelsFactory.get_llm(),
            max_token_limit: int = int(config.llm_config['input_token_limit'] * 0.8),
            query_limit=5,
            max_talk_round=2,
            score_limit=0.45,
            callback: Callable[[str], None] = None,
            **kwargs
    ) -> None:
        super().__init__(kb, **kwargs)
        self.llm = llm
        self.callback = callback

        self.index_prompts = index_prompts
        self.entity_types = entity_types
        self.relation_types = relation_types

        if not self.entity_types or not self.relation_types:
            raise ValueError('No entity_types / relation_types found.')

        self.vector_store = LarkVectorStoreFactory.get_vector_store(
            collection_name=self._collection_name
        )
        self.vector_store.connect()
        self.graph_store = LarkGraphStoreFactory.get_graph_store(
            space=self._space_name,
            schema=self._graph_schema
        )
        self.graph_store.connect()
        self.query_limit = query_limit

        self.retrieve_client = RetrieveClient(
            llm=self.llm,
            vector_store=self.vector_store,
            graph_store=self.graph_store,
            llm_params=config.llm_config,
            max_token_limit=max_token_limit,
            query_limit=query_limit,
            max_talk_round=max_talk_round,
            score_limit=score_limit,
            callback=self.callback,
            **self.kwargs,
        )

    @property
    def output_dir(self) -> Path:
        output_dir = self.base_path.joinpath('output_with_graph')
        if not output_dir.exists():
            output_dir.mkdir(parents=True)
        return output_dir

    @property
    def _collection_name(self) -> str:
        name = str(self.knowledge_base)
        return f"sensors_collection_{name}"

    @property
    def _space_name(self) -> str:
        return self._collection_name.replace('-', '_')

    @property
    def _graph_schema(self) -> GraphSchema:
        schema = graph_schema_for_knowledge(
            entity_types=self.entity_types,
            relation_types=self.relation_types,
        )
        return GraphSchema.model_validate(schema)

    def _get_workflow(
            self,
            index_mode: Literal['reindex', 'resume', 'update'] = 'reindex',
    ) -> Workflow:
        return LlamaIndexRagWorkflow(
            llm=self.llm,
            input_dir=self.input_dir,
            output_dir=self.output_dir,
            vector_store=self.vector_store,
            graph_store=self.graph_store,
            prompts=self.index_prompts,
            entity_types=self.entity_types,
            relation_types=self.relation_types,
            index_mode='reindex' if 'reindex' == index_mode else 'resume',
            callback=self.callback,
        )

    async def build_index(
            self,
            index_mode: Literal['reindex', 'resume', 'update'] = 'reindex',
            **kwargs
    ):
        flow = self._get_workflow(index_mode=index_mode)
        result = await flow.run()
        log.info(f'first build index success. result={result}')

    async def search(self, history: List[Conversation], **kwargs) -> Conversation:
        return await self.simple_search(history=history)

    async def simple_search(self, history: List[Conversation], **kwargs) -> Conversation:
        if not history:
            return Conversation(role=Role.ASSISTANT, content='你想问什么呢？')
        return await self.retrieve_client.aretrieve_data(
            history=history,
            retrieve_limit=self.query_limit,
        )
