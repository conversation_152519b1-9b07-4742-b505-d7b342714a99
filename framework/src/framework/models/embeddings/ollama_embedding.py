from typing import Any
import requests
from framework.models.embeddings.base import TextEmbedding

DEFAULT_HOST = "http://**********:11434"


class OllamaEmbedding(TextEmbedding):
    def __init__(
            self,
            model_name: str = 'ollama:bge-m3',
            **model_kwargs
    ):
        if model_name.startswith('ollama:'):
            model_name = model_name[7:]
        self.model_name = model_name
        self.host = model_kwargs.get('host', DEFAULT_HOST)

    def embed(self, text: str, **kwargs: Any) -> list[float]:
        result = self.batch_embed([text])
        return result[0]

    async def aembed(self, text: str, **kwargs: Any) -> list[float]:
        return self.embed(text, **kwargs)

    def batch_embed(self, texts: list[str], **kwargs: Any) -> list[list[float]]:
        response = requests.post(
            f'{self.host}/api/embed',
            json={
                "model": self.model_name,
                "input": texts,
            },
        )
        response = response.json()
        return response['embeddings']

    async def batch_aembed(self, texts: list[str], **kwargs: Any) -> list[list[float]]:
        return self.batch_embed(texts, **kwargs)

    def suggested_batch_size(self) -> int:
        return 100
