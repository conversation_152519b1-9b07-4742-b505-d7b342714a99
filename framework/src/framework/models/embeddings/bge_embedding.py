from typing import Any

from framework.models.embeddings.base import TextEmbedding


class BgeEmbedding(TextEmbedding):
    def __init__(
            self,
            model_name: str = 'BAAI/bge-m3',
            encode_kwargs=None,
            **model_kwargs
    ):
        if encode_kwargs is None:
            encode_kwargs = {}
        if 'normalize_embeddings' not in encode_kwargs:
            encode_kwargs['normalize_embeddings'] = True  # enable cosine similarity
        self.encode_kwargs = encode_kwargs
        self.model_name = model_name
        self.model_kwargs = model_kwargs
        self._model = None

    @property
    def model(self):
        # 懒加载，避免每次测试太慢
        if self._model is None:
            from langchain_community.embeddings import HuggingFaceBgeEmbeddings
            self._model = HuggingFaceBgeEmbeddings(
                model_name=self.model_name,
                model_kwargs=self.model_kwargs,  # 可以使用 GPU 加速
                encode_kwargs=self.encode_kwargs,
                embed_instruction='',
                query_instruction='',
            )
        return self._model

    def embed(self, text: str, **kwargs: Any) -> list[float]:
        return self.model.embed_documents(texts=[text])[0]

    async def aembed(self, text: str, **kwargs: Any) -> list[float]:
        vectors = await self.model.aembed_documents(texts=[text])
        return vectors[0]

    def batch_embed(self, texts: list[str], **kwargs: Any) -> list[list[float]]:
        return self.model.embed_documents(texts=texts)

    async def batch_aembed(self, texts: list[str], **kwargs: Any) -> list[list[float]]:
        return await self.model.aembed_documents(texts=texts)

    def suggested_batch_size(self) -> int:
        return 100  # 拍脑袋拍的
