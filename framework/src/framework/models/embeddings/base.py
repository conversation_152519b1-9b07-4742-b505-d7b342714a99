from abc import abstractmethod
from typing import Any, Dict

from framework.config import vector_storage_config

from fastembed.text.text_embedding_base import TextEmbeddingBase
from fastembed import TextEmbedding as te


class TextEmbedding:
    """The text embedding interface."""

    @abstractmethod
    def embed(self, text: str, **kwargs: Any) -> list[float]:
        """Embed a text string."""

    @abstractmethod
    async def aembed(self, text: str, **kwargs: Any) -> list[float]:
        """Embed a text string asynchronously."""

    @abstractmethod
    def batch_embed(self, texts: list[str], **kwargs: Any) -> list[list[float]]:
        """Embed a text string batch."""

    @abstractmethod
    async def batch_aembed(self, texts: list[str], **kwargs: Any) -> list[list[float]]:
        """Embed a text string batch asynchronously."""

    @abstractmethod
    def suggested_batch_size(self) -> int:
        """Give suggedted batch size"""


# wrap proxy for fastembed models
class WrappedTextEmbedding(TextEmbedding):
    def __init__(self, text_embedder: TextEmbeddingBase):
        self.text_embedder = text_embedder

    def embed(self, text: str, **kwargs: Any) -> list[float]:
        for item in self.text_embedder.embed(documents=text, **kwargs):
            return item.tolist()

    async def aembed(self, text: str, **kwargs: Any) -> list[float]:
        return self.embed(text=text, **kwargs)

    def batch_embed(self, texts: list[str], **kwargs: Any) -> list[list[float]]:
        ans = []
        for item in self.text_embedder.embed(documents=texts, **kwargs):
            ans.append(item)
        return ans

    async def batch_aembed(self, texts: list[str], **kwargs: Any) -> list[list[float]]:
        return self.batch_embed(text=texts, **kwargs)

    def suggested_batch_size(self) -> int:
        return 256


_models_cache: Dict[str, TextEmbedding] = {}


class TextEmbeddingFactory:

    @classmethod
    def get_text_embedding(cls, model_name: str = vector_storage_config['model'], **kwargs) -> TextEmbedding | None:
        if not model_name:
            raise RuntimeError('model name is empty.')

        if model_name in _models_cache:
            return _models_cache[model_name]

        if model_name.startswith('ollama:'):
            from .ollama_embedding import OllamaEmbedding
            embedding = OllamaEmbedding(model_name=model_name, **kwargs)
            _models_cache[model_name] = embedding
            return embedding

        # 先看 fastembed 支持列表
        fastembed_supported_list = te.list_supported_models()
        for model in fastembed_supported_list:
            if model['model'] == model_name:
                embedding = WrappedTextEmbedding(text_embedder=te(model_name=model_name, **kwargs))
                _models_cache[model_name] = embedding
                return embedding

        # 自定义的 embedder
        if model_name.startswith('BAAI/bge-'):
            from .bge_embedding import BgeEmbedding
            embedding = BgeEmbedding(model_name=model_name, **kwargs)
            _models_cache[model_name] = embedding
            return embedding
