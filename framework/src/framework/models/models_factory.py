# 从此处获取 model
from enum import StrEnum

from framework import config
from framework.config import model_config
from .llm import BaseLLM


class ModelType(StrEnum):
    OPENAI = 'openai'
    AZURE_OPENAI = 'azure_openai'
    OLLAMA = 'ollama'
    GROQ = 'groq'


class ModelsFactory:
    @classmethod
    def _get_model_type(cls, llm_config: dict) -> ModelType:
        if 'type' in llm_config and llm_config['type'] == 'openai_chat':
            return ModelType.OPENAI
        if 'type' in llm_config and llm_config['type'] == 'azure_openai_chat':
            return ModelType.AZURE_OPENAI
        if 'type' in llm_config and llm_config['type'] == 'ollama':
            return ModelType.OLLAMA
        if 'type' in llm_config and llm_config['type'] == 'groq':
            return ModelType.GROQ
        return ModelType.OLLAMA

    @classmethod
    def get_llm(cls, scenario: str = 'default') -> BaseLLM:
        llm_config = model_config[config.scenario_reference_model.get(scenario, 0)]
        match ModelsFactory._get_model_type(llm_config):
            case ModelType.OPENAI:
                from .llm.openai_chat import OpenAI
                return OpenAI(**llm_config)
            case ModelType.AZURE_OPENAI:
                from .llm.azure_openai import AzureOpenAI
                return AzureOpenAI(**llm_config)
            case ModelType.OLLAMA:
                from .llm.ollama import OllamaModel
                return OllamaModel(**llm_config)
            case ModelType.GROQ:
                from .llm.groq import GroqModel
                return GroqModel(**llm_config)
            case _:
                raise RuntimeError('model not supported.')
