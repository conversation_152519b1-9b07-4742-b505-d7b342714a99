from abc import ABC
from typing import List, Dict

from common import tools
from framework.config import knowledge_config

log = tools.get_logger()


class Tokenizer(ABC):

    def tokenize(self, text: str) -> List[str | int]:
        """分词器"""
        ...

    def tokenize_len(self, text: str) -> int:
        """分词结果长度"""
        ...


_cache: Dict[str, Tokenizer] = {}


class TokenizerFactory:

    @classmethod
    def get_tokenizer(self, encoding_name: str = knowledge_config['tokenizer_config']['encoding_name']) -> Tokenizer:
        if encoding_name in _cache:
            return _cache[encoding_name]

        try:
            from .tiktoken_tokenizer import TiktokenTokenizer
            encoding = TiktokenTokenizer(encoding_name=encoding_name)
            _cache[encoding_name] = encoding
            return encoding
        except BaseException as e:
            log.info(f'not load any encoding models from tiktoken. encoding_name={encoding_name}')
            raise e
