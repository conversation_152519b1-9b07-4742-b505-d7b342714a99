from typing import List, Any

from tiktoken._educational import *

from .base import Tokenizer


class TiktokenTokenizer(Tokenizer):

    def __init__(self, encoding_name: str, **data: Any):
        if encoding_name not in tiktoken.list_encoding_names():
            raise RuntimeError(f'not supported encoding name {encoding_name}')
        self.encoding = tiktoken.get_encoding(encoding_name)

    def tokenize(self, text: str) -> List[str | int]:
        return self.encoding.encode(text=text)

    def tokenize_len(self, text: str) -> int:
        return len(self.tokenize(text=text))
