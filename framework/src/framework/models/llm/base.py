from typing import (
    Protocol,
    Union,
    Optional,
    runtime_checkable,
    Dict,
    Any,
    List,
    TypeAlias,
    Generator,
    AsyncGenerator
)

from langchain_core.messages import BaseMessage, BaseMessageChunk
from langchain_core.runnables import RunnableConfig

LanguageModelInput: TypeAlias = Union[
    str,
    Dict[str, Any],
    List[str],
    List[Dict[str, Any]],
    BaseMessage,
    List[BaseMessage]
]


@runtime_checkable
class BaseLLM(Protocol):
    """模型标准，所有的大模型适配都必须以此为标准"""

    def complete(
            self,
            input: LanguageModelInput,
            config: Optional[RunnableConfig] = None,
            stop: Optional[list[str]] = None,
            **kwargs
    ) -> BaseMessage:
        ...

    async def acomplete(
            self,
            input: LanguageModelInput,
            config: Optional[RunnableConfig] = None,
            stop: Optional[list[str]] = None,
            **kwargs
    ) -> BaseMessage:
        ...

    def stream(
            self,
            input: LanguageModelInput,
            config: Optional[RunnableConfig] = None,
            stop: Optional[list[str]] = None,
            **kwargs
    ) -> Generator[BaseMessageChunk, Any, Any]:
        ...

    async def astream(
            self,
            input: LanguageModelInput,
            config: Optional[RunnableConfig] = None,
            stop: Optional[list[str]] = None,
            **kwargs
    ) -> AsyncGenerator[BaseMessageChunk, Any]:
        ...
