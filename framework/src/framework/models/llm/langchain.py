from typing import Optional, Any, Iterator, AsyncIterator

from langchain_core.language_models import BaseChatModel
from langchain_core.messages import BaseMessage, BaseMessageChunk
from langchain_core.runnables import RunnableConfig

from .base import LanguageModelInput


class LangChainModel:
    """适配 langchain"""

    def __init__(
            self,
            llm: BaseChatModel,
            **kwargs: Any
    ):
        self.llm = llm
        self.kwargs = kwargs

    def complete(
            self,
            input: LanguageModelInput,
            config: Optional[RunnableConfig] = None,
            stop: Optional[list[str]] = None,
            **kwargs
    ) -> BaseMessage:
        return self.llm.invoke(input=input, config=config, stop=stop, **kwargs)

    async def acomplete(
            self,
            input: LanguageModelInput,
            config: Optional[RunnableConfig] = None,
            stop: Optional[list[str]] = None,
            **kwargs
    ) -> BaseMessage:
        return await self.llm.ainvoke(input=input, config=config, stop=stop, **kwargs)

    def stream(
            self,
            input: LanguageModelInput,
            config: Optional[RunnableConfig] = None,
            stop: Optional[list[str]] = None,
            **kwargs
    ) -> Iterator[BaseMessageChunk]:
        return self.llm.stream(input=input, config=config, stop=stop, **kwargs)

    async def astream(
            self,
            input: LanguageModelInput,
            config: Optional[RunnableConfig] = None,
            stop: Optional[list[str]] = None,
            **kwargs
    ) -> AsyncIterator[BaseMessageChunk]:
        return self.llm.astream(input=input, config=config, stop=stop, **kwargs)
