import time
from typing import Optional, List, Dict, Any, Callable, Generator, AsyncGenerator
from langchain_core.messages import BaseMessageChunk, BaseMessage, AIMessage, AIMessageChunk
from langchain_core.runnables import RunnableConfig
from openai.types.chat import Cha<PERSON><PERSON><PERSON>ple<PERSON>, ChatCompletionChunk
from random import Random

from common import tools
from tenacity import retry, stop_after_attempt, retry_if_exception_type, wait_fixed

from .base import LanguageModelInput
import openai

log = tools.get_logger()

_supported_role_types = set(['system', 'assistant', 'user', 'function', 'tool'])
filter_response = "I'm sorry, I can't assist with that request."


def _convert_openai_message(completion: ChatCompletion) -> BaseMessage:
    choice = completion.choices[0]
    result = AIMessage(content=choice.message.content)
    result.type = choice.message.role
    result.id = completion.id
    result.response_metadata['logprobs'] = choice.logprobs
    result.response_metadata['usage'] = completion.usage
    if result.content == filter_response:
        log.warning(f'response filtered by azure safety strategy. response={completion.model_dump_json(indent=4)}')
        result.content = ''
    if choice.message.function_call is not None:
        result.additional_kwargs['tool_calls'] = choice.message.function_call
    if choice.message.tool_calls is not None:
        result.additional_kwargs['tool_calls'] = choice.message.tool_calls
    return result


def _convert_openai_message_chunk(chunk: ChatCompletionChunk) -> BaseMessageChunk:
    choice = chunk.choices[0]
    content = choice.delta.content
    if content is None:
        content = ''
    result = AIMessageChunk(content=str(content))
    result.id = chunk.id
    result.response_metadata['logprobs'] = choice.logprobs
    result.response_metadata['usage'] = chunk.usage
    if choice.delta.tool_calls is not None:
        result.additional_kwargs['tool_calls'] = choice.delta.tool_calls
    return result


# 由于 azure_openai 经常不稳定，这里加重试
@retry(
    stop=stop_after_attempt(3),
    retry=retry_if_exception_type(BaseException),
    wait=wait_fixed(5),
    reraise=True,
)
def _complete(
        client: openai.OpenAI,
        model: str,
        temperature: float | None,
        messages: List[Dict[str, Any]],
        stop: Optional[list[str]] = None,
        seed: int = 0,
        stream: bool = False,
):
    return client.chat.completions.create(
        model=model,
        temperature=temperature,
        messages=messages,
        stop=stop,
        seed=seed,
        stream=stream,
    )


@retry(
    stop=stop_after_attempt(3),
    retry=retry_if_exception_type(BaseException),
    wait=wait_fixed(5),
    reraise=True,
)
async def _acomplete(
        client: openai.AsyncOpenAI,
        model: str,
        temperature: float | None,
        messages: List[Dict[str, Any]],
        stop: Optional[list[str]] = None,
        seed: int = 0,
        stream: bool = False,
):
    return await client.chat.completions.create(
        model=model,
        temperature=temperature,
        messages=messages,
        stop=stop,
        seed=seed,
        stream=stream,
    )


def _split_thinking(response: BaseMessage) -> BaseMessage:
    # 去掉里面的 <think>...</think> 中间的部分
    content = response.content
    if content is not None:
        content = content.strip()
        if content.startswith('<think>'):
            index = content.find('</think>')
            if index > 0:
                index = index + len('</think>')
                think = content[:index]
                content = content[index:]
                response.content = content.strip()
                response.additional_kwargs['think'] = think
    return response


class OpenAI:
    def __init__(
            self,
            model: str,
            api_key: str,
            base_url: str,
            display_callback: Callable[[BaseMessage], Any] | None = None,
            **kwargs
    ):
        """
        ARgs:
            display_callback: 用于回显的函数
        """
        self.random = Random()
        self.model = model
        self.display_callback = display_callback
        self._client = openai.OpenAI(
            base_url=base_url,
            api_key=api_key
        )
        self._async_client = openai.AsyncOpenAI(
            base_url=base_url,
            api_key=api_key,
        )
        if kwargs:
            self.kwargs = kwargs
        else:
            self.kwargs = {}
        self.temperature = 0
        if 'temperature' in self.kwargs:
            self.temperature = self.kwargs['temperature']

    def _convert_role_name(self, role: str):
        if role in _supported_role_types:
            return role
        if role == 'human':
            return 'user'
        if role == 'ai' or role == 'chat':
            return 'assistant'
        raise f'Unknown role type: {role}'

    def _convert_input(self, input: LanguageModelInput) -> List[Dict[str, Any]]:
        if isinstance(input, str):
            return [{'role': 'user', 'content': input}]
        if isinstance(input, Dict):
            if 'role' not in input:
                input['role'] = 'user'
            input['role'] = self._convert_role_name(input['role'])
            return [input]
        if isinstance(input, BaseMessage):
            return [{'role': self._convert_role_name(input.type), 'content': input.content, 'name': input.name}]
        result = []
        if isinstance(input, List):
            for i in input:
                result.extend(self._convert_input(input=i))
        return result

    def complete(
            self,
            input: LanguageModelInput,
            config: Optional[RunnableConfig] = None,
            stop: Optional[list[str]] = None,
            **kwargs
    ) -> BaseMessage:
        start_time = time.time()
        response = _complete(
            client=self._client,
            model=self.model,
            temperature=self.temperature,
            messages=self._convert_input(input),
            stop=stop,
            seed=self.random.randint(1, 100_000),
            stream=False,
        )

        end_time = time.time()
        log.info(f'generate cost: {end_time - start_time}s')
        response = _convert_openai_message(response)
        response = _split_thinking(response)
        if self.display_callback:
            self.display_callback(response)
        return response

    async def acomplete(
            self,
            input: LanguageModelInput,
            config: Optional[RunnableConfig] = None,
            stop: Optional[list[str]] = None,
            **kwargs
    ) -> BaseMessage:
        start_time = time.time()
        response = await _acomplete(
            client=self._async_client,
            model=self.model,
            temperature=self.temperature,
            messages=self._convert_input(input),
            stop=stop,
            seed=self.random.randint(1, 100_000),
            stream=False,
        )
        end_time = time.time()
        log.info(f'generate cost: {end_time - start_time}s')
        response = _convert_openai_message(response)
        response = _split_thinking(response)
        if self.display_callback:
            self.display_callback(response)
        return response

    def stream(
            self,
            input: LanguageModelInput,
            config: Optional[RunnableConfig] = None,
            stop: Optional[list[str]] = None,
            **kwargs
    ) -> Generator[BaseMessageChunk, Any, Any]:
        """stream complete"""
        start_time = time.time()
        response = _complete(
            client=self._client,
            model=self.model,
            temperature=self.temperature,
            messages=self._convert_input(input),
            stop=stop,
            seed=self.random.randint(1, 100_000),
            stream=True,
        )
        for chunk in response:
            if self.display_callback:
                self.display_callback(chunk)
            yield _convert_openai_message_chunk(chunk)

        end_time = time.time()
        log.info(f'generate cost: {end_time - start_time}s')

    async def astream(
            self,
            input: LanguageModelInput,
            config: Optional[RunnableConfig] = None,
            stop: Optional[list[str]] = None,
            **kwargs
    ) -> AsyncGenerator[BaseMessageChunk, Any]:
        """stream complete"""
        start_time = time.time()
        response = await _acomplete(
            client=self._async_client,
            model=self.model,
            temperature=self.temperature,
            messages=self._convert_input(input),
            stop=stop,
            seed=self.random.randint(1, 100_000),
            stream=True,
        )
        async for chunk in response:
            if self.display_callback:
                self.display_callback(chunk)
            yield _convert_openai_message_chunk(chunk)

        end_time = time.time()
        log.info(f'generate cost: {end_time - start_time}s')
