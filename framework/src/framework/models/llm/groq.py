from typing import List, Dict, Any

from langchain_core.messages import BaseMessage

from . import LanguageModelInput
from .azure_openai import AzureOpenAI


class GroqModel(AzureOpenAI):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def _convert_input(self, input: LanguageModelInput) -> List[Dict[str, Any]]:
        if isinstance(input, str):
            return [{'role': 'user', 'content': input, 'name': 'user'}]
        if isinstance(input, Dict):
            if 'role' not in input:
                input['role'] = 'user'
            input['role'] = self._convert_role_name(input['role'])
            if 'name' not in input:
                input['name'] = 'user'
            return [input]
        if isinstance(input, BaseMessage):
            role_name = self._convert_role_name(input.type)
            return [{'role': role_name, 'content': input.content, 'name': input.name or role_name}]
        result = []
        if isinstance(input, List):
            for i in input:
                result.extend(self._convert_input(input=i))
        return result
