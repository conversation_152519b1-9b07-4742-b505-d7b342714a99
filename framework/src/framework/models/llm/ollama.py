from typing import Optional, Any, Callable, Generator, AsyncGenerator

from langchain_ollama import ChatOllama
from langchain_core.messages import BaseMessage, BaseMessageChunk
from langchain_core.runnables import RunnableConfig

from .base import LanguageModelInput
from .langchain import LangChainModel


class OllamaModel:
    """使用 langchain 适配 ollama"""

    def __init__(
            self,
            display_callback: Callable[[BaseMessage], Any] | None = None,
            **kwargs: Any
    ):
        self.kwargs = kwargs
        self.display_callback = display_callback
        self.client = LangChainModel(
            llm=ChatOllama(**kwargs),
            **kwargs
        )

    def complete(
            self,
            input: LanguageModelInput,
            config: Optional[RunnableConfig] = None,
            stop: Optional[list[str]] = None,
            **kwargs
    ) -> BaseMessage:
        response = self.client.complete(
            input=input,
            config=config,
            stop=stop,
            stream=False,
            **kwargs,
        )
        if self.display_callback:
            self.display_callback(response)
        return response

    async def acomplete(
            self,
            input: LanguageModelInput,
            config: Optional[RunnableConfig] = None,
            stop: Optional[list[str]] = None,
            **kwargs
    ) -> BaseMessage:
        response = await self.client.acomplete(
            input=input,
            config=config,
            stop=stop,
            stream=False,
            **kwargs,
        )
        if self.display_callback:
            self.display_callback(response)
        return response

    def stream(
            self,
            input: LanguageModelInput,
            config: Optional[RunnableConfig] = None,
            stop: Optional[list[str]] = None,
            **kwargs
    ) -> Generator[BaseMessageChunk, Any, Any]:
        response = self.client.stream(
            input=input,
            config=config,
            stop=stop,
            stream=True,
            **kwargs,
        )
        for chunk in response:
            if self.display_callback:
                self.display_callback(chunk)
            yield chunk

    async def astream(
            self,
            input: LanguageModelInput,
            config: Optional[RunnableConfig] = None,
            stop: Optional[list[str]] = None,
            **kwargs
    ) -> AsyncGenerator[BaseMessageChunk, Any]:
        response = await self.client.astream(
            input=input,
            config=config,
            stop=stop,
            stream=True,
            **kwargs,
        )
        async for chunk in response:
            if self.display_callback:
                self.display_callback(chunk)
            yield chunk
