import contextlib
import time
from numbers import Number
from typing import List, Tuple, Union, Dict, Any, Literal
import pandas as pd
from nebula3.data.ResultSet import ResultSet
from nebula3.gclient.net import ConnectionPool, Session, ExecuteError
from nebula3.Config import Config

from framework import config
from common import tools
from . import GraphLabelValue, Edge, Vertex
from .base import (
    LarkGraphStore, GraphData, GraphSchema, GraphDataType, GraphLabelMetadata, ID,
    LABEL_ID_PROPERTY_METADATA, LABEL_ALL,
)

log = tools.get_logger()
_heart_beat_interval = 10


def _get_data_type_name(data_type: GraphDataType) -> str:
    return data_type.name.lower()


def _is_number_data_type(data_type: GraphDataType) -> bool:
    match data_type:
        case GraphDataType.INT64, GraphDataType.INT32, GraphDataType.INT16, GraphDataType.INT8, GraphDataType.FLOAT, \
             GraphDataType.DOUBLE:
            return True
        case _:
            return False


def _get_vertex_value_str(vertex: Vertex) -> <PERSON><PERSON>[str, str]:
    """获取顶点的字段名称和值"""
    value_result = f'"{vertex.id}":('
    label_meta = []
    vertex_values = vertex.values if vertex.values else []
    for idx, label_value in enumerate(vertex_values):
        if idx != 0:
            value_result += ','
        value_result += f'"{label_value.label_id}"'
        props_meta = label_value.label_name + "(" + LABEL_ID_PROPERTY_METADATA.name
        for k, v in label_value.properties.items():
            value_result += ',' + (f'"{v}"' if not isinstance(v, Number) else str(v)).replace('\n', '  ')
            props_meta += "," + k
        props_meta += ")"
        label_meta.append(props_meta)
    value_result += ')'
    return ','.join(label_meta), value_result


def _get_edge_value_str(edge: Edge) -> Tuple[str, str]:
    """获取边的字段名称和值，在 NebulaGraph 中，边没有 ID 的概念，所以忽略 ID"""
    value_result = f'"{edge.src_vid}" -> "{edge.dst_vid}"@{str(edge.rank if edge.rank > 0 else 0)}:('
    label_meta = []
    edge_values = edge.values if edge.values else []
    for idx, label_value in enumerate(edge_values):
        if idx != 0:
            value_result += ','
        value_result += f'"{label_value.label_id}"'
        props_meta = label_value.label_name + "(" + LABEL_ID_PROPERTY_METADATA.name
        for k, v in label_value.properties.items():
            value_result += ',' + (f'"{v}"' if not isinstance(v, Number) else str(v)).replace('\n', '  ')
            props_meta += "," + k
        props_meta += ")"
        label_meta.append(props_meta)
        break
    value_result += ')'
    return ','.join(label_meta), value_result


def _covert_value_to_query_str(label_value: GraphLabelValue) -> str:
    result = label_value.label_name + ""


def _get_batches(objs: List, batch_size: int) -> List:
    if not objs:
        return []
    for i in range(0, len(objs), batch_size):
        yield objs[i:i + batch_size]


def _get_config_str(config_dict: Dict[str, Any]) -> List[str]:
    config_strs = []
    for key, value in config_dict:
        if isinstance(value, Union[int, float, bool, Number]):
            config_strs.append(f'{key}={str(value)}')
        else:
            config_strs.append(f'{key}="{value}"')
    return config_strs


def _get_label_props_str(label: GraphLabelMetadata) -> List[str]:
    label_properties_str = []
    label_properties = label.properties if label.properties else []
    has_id_prop = len([_ for _ in label_properties if _.name == LABEL_ID_PROPERTY_METADATA.name]) > 0
    if not has_id_prop:
        label_properties.insert(0, LABEL_ID_PROPERTY_METADATA)

    for prop in label_properties:
        prop_ngql = f"{prop.name} {_get_data_type_name(prop.data_type)}"
        if prop.data_type_config:
            prop_ngql += f"({prop.data_type_config})"
        if not prop.nullable:
            prop_ngql += ' NOT NULL'
        if prop.default_value and _is_number_data_type(prop.data_type):
            prop_ngql += f' DEFAULT {prop.default_value}'
        elif prop.default_value:
            prop_ngql += f' DEFAULT "{prop.default_value}"'
        label_properties_str.append(prop_ngql)
    return label_properties_str


def _create_edge_type(session: Session, etype: GraphLabelMetadata) -> int:
    try:
        tag_info = session.execute_py(f'DESC EDGE {etype.label_name};')
        if tag_info and tag_info.row_size() >= 1:
            return 0
    except ExecuteError as e:
        log.warning(f'get edge type {etype.label_name} failed, will try to create edge type.')
        log.info('DO NOT TERMINATE THIS PROGRAM WHILE CREATING!')

    if not etype.configs:
        etype.configs = {}
    config_strs = _get_config_str(etype.configs)

    type_properties = _get_label_props_str(label=etype)

    session.execute_py(f"""
    CREATE EDGE IF NOT EXISTS {etype.label_name}
    (
        {', '.join(type_properties)}
    ) {', '.join(config_strs)};
    """)

    # 由于 nebula graph 还有 bug，如果不建索引就没法检索，所以这里需要每个字段都建索引
    return 1


def _create_edge_index(session: Session, etype: GraphLabelMetadata) -> None:
    for prop in etype.properties:
        index_name = f'ie_{etype.label_name}_{prop.name}'
        if prop.data_type == GraphDataType.STRING:
            # 变长字符串的索引长度必须指定，索引长度固定为 20
            session.execute_py(f'CREATE EDGE INDEX IF NOT EXISTS {index_name} ON {etype.label_name}({prop.name}(20))')
        else:
            session.execute_py(f'CREATE EDGE INDEX IF NOT EXISTS {index_name} ON {etype.label_name}({prop.name})')


def _create_vertex_label(session: Session, label: GraphLabelMetadata) -> int:
    try:
        tag_info = session.execute_py(f'DESC TAG {label.label_name};')
        if tag_info and tag_info.row_size() >= 1:
            return 0
    except ExecuteError as e:
        log.warning(f'get vertex tag {label.label_name} failed, will try to create tag.')
        log.info('DO NOT TERMINATE THIS PROGRAM WHILE CREATING!')

    if not label.configs:
        label.configs = {}
    config_strs = _get_config_str(label.configs)

    label_properties = _get_label_props_str(label=label)

    session.execute_py(f"""
    CREATE TAG IF NOT EXISTS {label.label_name}
    (
        {', '.join(label_properties)}
    ) {', '.join(config_strs)};
    """)

    # 由于 nebula graph 还有 bug，如果不建索引就没法检索，所以这里需要每个字段都建索引
    return 1


def _create_vertex_index(session: Session, label: GraphLabelMetadata) -> None:
    for prop in label.properties:
        index_name = f'iv_{label.label_name}_{prop.name}'
        if prop.data_type == GraphDataType.STRING:
            # 变长字符串的索引长度必须指定，索引长度固定为 20
            session.execute_py(f'CREATE TAG INDEX IF NOT EXISTS {index_name} ON {label.label_name}({prop.name}(20))')
        else:
            session.execute_py(f'CREATE TAG INDEX IF NOT EXISTS {index_name} ON {label.label_name}({prop.name})')


def _standard_extract_graph_result(
        data: ResultSet,
        vertex_column_names: set[str] | None = None,
        edge_column_names: set[str] | None = None,
) -> GraphData | None:
    if not data:
        return None
    data: List[dict[str, Any]] = data.as_primitive()
    if not vertex_column_names:
        vertex_column_names = {'v', 'v0', 'v1', 'v2', 'v3', 'v4', 'v5', 'v6', 'v7', 'v8', 'v9'}
    if not edge_column_names:
        edge_column_names = {'e', 'e0', 'e1', 'e2', 'e3', 'e4', 'e5', 'e6', 'e7', 'e8', 'e9'}

    graph_datas = []
    for item in data:
        items = [item] if isinstance(item, dict) else item
        for item_i in items:
            row_graph_data = _standard_extract_graph_result_item(
                item=item_i,
                vertex_column_names=vertex_column_names,
                edge_column_names=edge_column_names
            )
            graph_datas.append(row_graph_data)
    return _merge_graph_data(graph_datas)


def _standard_extract_graph_result_item(
        item: Dict[str, Any],
        vertex_column_names: set[str],
        edge_column_names: set[str],
) -> GraphData:
    vertex_list = []
    edges_list = []
    for key, value in item.items():
        if not value:
            continue
        value = [value] if isinstance(value, dict) else value
        if key in vertex_column_names:
            for vertex_dict in value:
                vid = vertex_dict.get('vid')
                tags_dict = vertex_dict.get('tags', {})
                labels = []
                for tag_name, tag_props in tags_dict.items():
                    labels.append(GraphLabelValue(
                        label_name=tag_name,
                        label_id=tag_props.get('label_id', None),
                        properties=tag_props,
                    ))
                vertex_list.append(Vertex(id=vid, values=labels))
        elif key in edge_column_names:
            for edge_dict in value:
                props: dict = edge_dict.get('props', {})
                edges_list.append(
                    Edge(src_vid=edge_dict['src'], dst_vid=edge_dict['dst'], rank=edge_dict.get('rank', 0), values=[
                        GraphLabelValue(
                            label_name=edge_dict.get('type'),
                            label_id=props.pop('label_id'),
                            properties=props,
                        )
                    ]))

    return GraphData(vertexes=vertex_list, edges=edges_list)


def _merge_graph_data(data: List[GraphData]) -> GraphData:
    if not data:
        return GraphData()
    vertex_list = []
    edges_list = []
    for d in data:
        if d.vertexes:
            vertex_list.extend(d.vertexes)
        if d.edges:
            edges_list.extend(d.edges)
    return GraphData(vertexes=vertex_list, edges=edges_list)


class NebulaGraphStore(LarkGraphStore):
    """
    Graph database store
    reference: https://docs.nebula-graph.com.cn/3.8.0/nebula-studio/deploy-connect/st-ug-connect/
    nGQL 指南：https://docs.nebula-graph.com.cn/3.8.0/3.ngql-guide/1.nGQL-overview/1.overview/
    """

    def __init__(
            self,
            space: str,
            schema: GraphSchema,
            hosts: List[Tuple] = config.graph_storage_config['hosts'],
            max_connection_pool_size: int = config.graph_storage_config['max_connection_pool_size'],
            user: str = config.graph_storage_config['user'],
            password: str = config.graph_storage_config['password'],
            **kwargs,
    ):
        super().__init__(space=space, schema=schema, **kwargs)
        self._connection_pool = None
        self.hosts = hosts
        self.max_connection_pool_size = max_connection_pool_size
        self.user = user
        self.password = password
        self.partition_num = kwargs.get('partition_num', 10)

    def type(self) -> str:
        return 'NebulaGraph'

    def connect(self):
        self._connection_pool = ConnectionPool()
        pool_config = Config()
        pool_config.max_connection_pool_size = self.max_connection_pool_size
        self._connection_pool.init(self.hosts, pool_config)

        with self._open_session(use_space=False) as session:
            space_info: ResultSet = None
            try:
                space_info = session.execute_py(f"DESC SPACE {self.space}")
            except ExecuteError as e:
                log.warning(f'get space:{self.space} info failed, will try to create space.')
                log.info('DO NOT TERMINATE THIS PROGRAM WHILE CREATING!')
            if not space_info or space_info.row_size() <= 0:
                log.warning(f'now create space {self.space}')
                session.execute_py(
                    f"CREATE SPACE IF NOT EXISTS {self.space}(partition_num = {self.partition_num}, vid_type=FIXED_STRING(100));")
                time.sleep(_heart_beat_interval * 2)  # 刚创建完 space 需要休眠一下，否则容易报错

        with self._open_session() as session:
            # 初始化 schema，schema 必须要有 id 属性
            new_vertex_labels = []
            new_edge_labels = []
            if self.schema.vertex_labels:
                for label in self.schema.vertex_labels:
                    rel = _create_vertex_label(session=session, label=label)
                    if rel != 0:
                        new_vertex_labels.append(label)
            if self.schema.edge_labels:
                for label in self.schema.edge_labels:
                    rel = _create_edge_type(session=session, etype=label)
                    if rel != 0:
                        new_edge_labels.append(label)
            if new_vertex_labels or new_vertex_labels:
                time.sleep(_heart_beat_interval * 2)

            # 由于 nebula graph 还有 bug，如果不建索引就没法检索，所以这里需要每个字段都建索引
            # 索引会导致插入变慢，但是查询会变快
            for label in new_vertex_labels:
                _create_vertex_index(session=session, label=label)
            for label in new_edge_labels:
                _create_edge_index(session=session, etype=label)
            if new_vertex_labels or new_vertex_labels:
                time.sleep(_heart_beat_interval * 2)

    def clear_all_data(self):
        log.warning(f'clean all data of space: {self.space}')
        with self._open_session() as session:
            session.execute_py(f"CLEAR SPACE IF EXISTS {self.space};")

    def execute_df(self, query: str, **params) -> pd.DataFrame:
        query_result = self._execute(query=query, **params)
        return query_result.as_data_frame(primitive=True)

    def execute(self, query: str, **params) -> GraphData:
        result = self._execute(query=query, **params)
        return _standard_extract_graph_result(data=result)

    def _execute(self, query: str, **params) -> ResultSet:
        log.info(f'run query: {query}, params={params}')
        with self._open_session() as session:
            query_result: ResultSet = session.execute_py(query, params=params)
            if not query_result.is_succeeded():
                raise RuntimeError(
                    f'query nebula graph failed. error_code={str(query_result.error_code())}, error_message={query_result.error_msg()}')
            return query_result

    @contextlib.contextmanager
    def _open_session(self, use_space=True):
        session = None
        try:
            session = self._connection_pool.get_session(self.user, self.password)
            if use_space:
                session.execute_py(f'USE {self.space};')
            yield session
        except Exception:
            raise
        finally:
            if session:
                session.release()

    def upsert_graph_data(self, data: GraphData, batch_size: int = 20) -> None:
        """
        UPSERT 操作，对应的顶点和边如果存在则更新，否则插入
        对于 VERTEX 的 UPSERT，由于原生的支持不是很好，这里使用先 DELETE 后 INSERT 的方式来插入
        """
        if not data:
            return

        # 先插入顶点，再插入边
        vertexes = data.vertexes if data.vertexes else []
        # 先删除后插入，一起删除节点关联的边
        self.delete_vertex(vid=[_.id for _ in vertexes], with_edge=True, batch_size=batch_size)

        self.insert_vertex(vertex=vertexes, batch_size=batch_size)

        edges = data.edges if data.edges else []
        self.insert_edge(edge=edges, batch_size=batch_size)

        log.info("upsert graph data done.")

    def insert_vertex(
            self,
            vertex: Union[List[Vertex], Vertex],
            batch_size: int = 20
    ) -> None:
        if not vertex:
            return

        vertexes = [vertex] if isinstance(vertex, Vertex) else vertex

        # fix ids
        for ver in vertexes:
            if not ver.id:
                ver.id = tools.uuid4_no_underline()

            values = [] if not ver.values else ver.values
            for val in values:
                if not val.label_id:
                    val.label_id = tools.uuid4_no_underline()

        with self._open_session() as session:
            for part_vertexes in _get_batches(vertexes, batch_size):
                # 先单条插入，多条后续可以优化
                for ver in part_vertexes:
                    prop_names, prop_values = _get_vertex_value_str(ver)
                    session.execute_py(f'INSERT VERTEX IF NOT EXISTS {prop_names} VALUES {prop_values}')

    def insert_edge(
            self,
            edge: Union[List[Edge], Edge],
            batch_size: int = 20
    ) -> None:
        if not edge:
            return

        edges = [edge] if isinstance(edge, Edge) else edge

        # fix ids
        for e in edges:
            values = [] if not e.values else e.values
            for val in values:
                if not val.label_id:
                    val.label_id = tools.uuid4_no_underline()

        with self._open_session() as session:
            for part_edges in _get_batches(edges, batch_size):
                # 先单条插入，多条后续可以优化
                for e in part_edges:
                    prop_names, prop_values = _get_edge_value_str(e)
                    session.execute_py(f'INSERT EDGE IF NOT EXISTS {prop_names} VALUES {prop_values}')

    def delete_vertex(
            self,
            vid: Union[List[ID], ID],
            with_edge: bool = False,
            batch_size: int = 20
    ) -> None:
        all_converted_vids = [vid] if isinstance(vid, ID) else vid
        with self._open_session() as session:
            for converted_vids in _get_batches(all_converted_vids, batch_size):
                converted_vids = [f'"{_}"' for _ in converted_vids]
                if not with_edge:
                    session.execute_py(f'DELETE VERTEX {",".join(converted_vids)}')
                else:
                    session.execute_py(f'DELETE VERTEX {",".join(converted_vids)} WITH EDGE')

    def query_vertexes(
            self,
            vid: List[ID] | ID | None = None,
            limit: int = 10
    ) -> GraphData:
        return self._query_vertexes_by_ids(vid=vid, limit=limit)

    def _query_vertexes_by_ids(self, vid: List[ID] | ID | None = None, limit: int = 10) -> GraphData:
        gql = f'MATCH (v) RETURN (v) LIMIT {str(limit)}'
        if not vid:
            return self.execute(query=gql)
        vids = []
        if isinstance(vid, ID):
            vids.append(vid)
        else:
            vids.extend(vid)
        all_vids = ', '.join('"' + v + '"' for v in vids)
        gql = f'MATCH (v) WHERE id(v) IN _ALL_VIDS_ RETURN v LIMIT {str(limit)};'
        gql = gql.replace('_ALL_VIDS_', '{' + all_vids + '}')
        return self.execute(gql)

    def filter_vertexes(
            self,
            vid: ID | None = None,
            label_filter: GraphLabelValue | None = None,
            limit: int = 10,
    ) -> GraphData:
        pre_data = None
        if vid:
            pre_data = self._query_vertexes_by_ids(vid=vid, limit=limit)
        if not label_filter:
            return pre_data
        if pre_data is not None:
            # 直接手动筛选
            vertexes = [self._vertex_filter_label(v, label_filter) for v in pre_data.vertexes]
            vertexes = [_ for _ in vertexes if _ is not None]
            return GraphData(vertexes=vertexes)
        else:
            return self._query_vertexes_by_filter(label_filter=label_filter, limit=limit)

    def _query_vertexes_by_filter(self, label_filter: GraphLabelValue | None = None, limit: int = 10, ) -> GraphData:
        if label_filter.label_name == LABEL_ALL and (not label_filter.properties):
            return self._query_vertexes_by_ids(limit=limit)
        label_values_gql = []
        labeled_where_clause = []
        non_labeled_where_clause = []
        for k, v in label_filter.properties.items():
            collection_name = f'{label_filter.label_name}_{k}'
            label_values_gql.append(f'''['{str(v)}'] AS {collection_name}''')
            if label_filter.label_name == LABEL_ALL:
                non_labeled_where_clause.append(f'''props['{k}'] in {collection_name}''')
            else:
                labeled_where_clause.append(f'''v.{label_filter.label_name}.{k} IN {collection_name}''')

        gql = None
        if label_filter.label_name != LABEL_ALL and label_filter.properties:
            gql = f"""
            MATCH (v) 
            WITH {','.join(label_values_gql)} 
            WHERE {' AND '.join(labeled_where_clause)} 
            RETURN v LIMIT {limit};
            """
        elif label_filter.label_name == LABEL_ALL and label_filter.properties:
            gql = f"""
            MATCH (v) 
            WITH v, properties(v) as props, {','.join(label_values_gql)} 
            WHERE {' AND '.join(non_labeled_where_clause)}
            return v LIMIT {limit}
            """
        elif label_filter.label_name != LABEL_ALL and (not label_filter.properties):
            gql = f"""
            MATCH (v:{label_filter.label_name}) 
            return v LIMIT {limit}
            """
        return self.execute(gql)

    def _vertex_filter_label(self, vertex: Vertex, label_filter: GraphLabelValue) -> Vertex | None:
        vertex_labels = vertex.values
        if label_filter.label_name and label_filter.label_name != LABEL_ALL:
            vertex_labels = [_ for _ in vertex_labels if _.label_name == label_filter.label_name]
        if label_filter.label_id:
            vertex_labels = [_ for _ in vertex_labels if _.label_id == label_filter.label_id]
        if label_filter.properties:
            for k, v in label_filter.properties.items():
                vertex_labels = [_ for _ in vertex_labels if _.properties.get(k, None) == v]
        if not vertex_labels:
            return None
        vertex.values = vertex_labels
        return vertex

    def walk(
            self,
            src_vid: ID,
            dst_vertex_label_filters: List[GraphLabelValue] | None = None,
            edge_label_filters: List[GraphLabelValue] | None = None,
            direction: Literal['DIRECT', 'REVERSELY', 'BIDIRECT'] | None = None,
            min_step: int = 1,
            max_step: int = 1,
            offset: int = 0,
            limit: int = 10,
    ) -> GraphData:
        gql = f"""GO {min_step} TO {max_step} STEPS FROM "{src_vid}" OVER """

        if edge_label_filters:
            gql = f"""{gql} {','.join(f.label_name for f in edge_label_filters)}"""
        else:
            gql = f"""{gql} *"""
        if direction == 'DIRECT':
            direction = None
        if direction:
            gql = f"""{gql} {direction}"""
        filter_conditions = []
        if edge_label_filters:
            all_filters = []
            for label_value in edge_label_filters:
                if not label_value.properties:
                    continue
                for k, v in label_value.properties.items():
                    v = str(v) if isinstance(v, Number) else '"v"'
                    all_filters.append(f"""properties(edge).{k} == {v}""")
            if all_filters:
                filter_conditions.append(' AND '.join(all_filters))
        if dst_vertex_label_filters:
            all_filters = []
            for label_value in dst_vertex_label_filters:
                if not label_value.properties:
                    continue
                if label_value.label_name == LABEL_ALL:
                    for k, v in label_value.properties.items():
                        v = str(v) if isinstance(v, Number) else '"v"'
                        all_filters.append(f"""properties($$).{k} == {v}""")
                else:
                    for k, v in label_value.properties.items():
                        v = str(v) if isinstance(v, Number) else '"v"'
                        all_filters.append(f"""$$.{label_value.label_name}.{k} == {v}""")
            if all_filters:
                filter_conditions.append(' AND '.join(all_filters))
        if filter_conditions:
            gql = f"""{gql} WHERE {' AND '.join(filter_conditions)}"""
        gql = f"""{gql} YIELD $$ AS v, edge AS e | OFFSET {offset} LIMIT {limit}"""

        result = self.execute(query=gql)
        # 由于引用的是目的点，需要手动加上起始点
        g = self.filter_vertexes(vid=src_vid, limit=1)
        result = _merge_graph_data([result, g])
        return result

    def find_path(
            self,
            src_vids: List[ID],
            dst_vids: List[ID],
            edge_label_filters: List[GraphLabelValue] | None = None,
            strategy: Literal['SHORTEST', 'SINGLE SHORTEST', 'ALL', 'NOLOOP'] = 'SINGLE SHORTEST',
            direction: str | None = None,
            with_labels: bool = True,
            max_step: int = 0,
            limit: int = 10
    ) -> GraphData:
        return super().find_path(src_vids, dst_vids, edge_label_filters, strategy, direction, with_labels, max_step,
                                 limit)
