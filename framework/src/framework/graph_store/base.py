from abc import ABC
from enum import Enum
from typing import Any, Dict, List, Union, Literal

import networkx as nx
from pydantic import BaseModel
import pandas as pd

ID = str
TAG = str
LABEL_ALL = 'ALL'


class GraphDataType(Enum):
    INT64 = 1
    INT32 = 2
    INT16 = 3
    INT8 = 4
    FLOAT = 5
    DOUBLE = 6
    BOOLEAN = 7
    FIXED_STRING = 8  # 定长字符串
    STRING = 9  # 变长字符串
    DATE = 10
    TIME = 11
    DATETIME = 12
    TIMESTAMP = 13
    DURATION = 14
    LIST = 15  # 列表
    SET = 16  # 集合
    MAP = 17  # 映射
    GEOGRAPHY = 18  # 地理位置 https://docs.nebula-graph.com.cn/3.1.0/3.ngql-guide/3.data-types/10.geography/
    NULL = 100  # 空值兜底


class GraphPropertyMetadata(BaseModel):
    name: str
    data_type: GraphDataType
    data_type_config: str | None = None  # 数据类型其他配置，比如定长字符串的长度
    nullable: bool = True
    default_value: Any | None = None
    comment: str | None = None


# 每个 Label 都必须带 id ！这样方便后续管理
LABEL_ID_PROPERTY_METADATA = GraphPropertyMetadata.model_validate({
    "name": "label_id",
    "data_type": GraphDataType.STRING,
    "nullable": False,
    "comment": "identity for label."
})


class GraphLabelMetadata(BaseModel):
    label_name: str
    properties: List[GraphPropertyMetadata]
    configs: Dict[str, Any] | None = None
    comment: str | None = None


class GraphSchema(BaseModel):
    vertex_labels: List[GraphLabelMetadata]
    edge_labels: List[GraphLabelMetadata]


class GraphLabelValue(BaseModel):
    label_name: str = LABEL_ALL
    label_id: str | None = None
    properties: Dict[str, Any] | None = None


class Vertex(BaseModel):
    id: ID
    metadata: List[GraphLabelMetadata] | None = None
    values: List[GraphLabelValue] | None = None


class Edge(BaseModel):
    id: ID | None = None
    rank: int = 0
    src_vid: ID
    dst_vid: ID
    metadata: List[GraphLabelMetadata] | None = None
    values: List[GraphLabelValue] | None = None


class GraphPath(BaseModel):
    """图中的路径"""
    ...


class GraphData(BaseModel):
    """查询结果数据结构，结构可以优化，这里是按列存储，按行存储更合理些"""
    vertexes: List[Vertex] | None = None
    edges: List[Edge] | None = None
    paths: List[GraphPath] | None = None

    def as_graph(self) -> nx.Graph | None:
        if not self.vertexes:
            return None

        # 有向图
        graph = nx.DiGraph()
        for v in self.vertexes:
            if v is None:
                continue
            attr = {}
            if v.values:
                for value in v.values:
                    attr[value.label_name] = value.properties
            graph.add_node(v.id, attr=attr)
        if not self.edges:
            return graph

        for e in self.edges:
            if e is None:
                continue
            attr = {'weight': e.rank}
            if e.values:
                for value in e.values:
                    attr[value.label_name] = value.properties
            graph.add_edge(e.src_vid, e.dst_vid, attr=attr)
        return graph


class LarkGraphStore(ABC):
    """图数据库存储"""

    def __init__(self, space: str, schema: GraphSchema, **kwargs):
        self.space = space
        self.schema = schema
        self.kwargs = kwargs

    def type(self) -> str:
        """get graph db type"""
        ...

    def connect(self):
        """connect the db"""
        ...

    def clear_all_data(self):
        """clean all data of the space"""
        ...

    def execute_df(self, query: str, **params) -> pd.DataFrame:
        """run query language, return as  pd.DataFrame"""
        ...

    def execute(self, query: str, **params) -> GraphData:
        """run query language, return as GraphData"""
        ...

    def get_schema(self) -> GraphSchema:
        """get graph schema"""
        return self.schema

    def upsert_graph_data(self, data: GraphData, batch_size: int = 200) -> None:
        """upsert data"""
        ...

    def insert_vertex(self, vertex: Union[List[Vertex], Vertex], batch_size: int = 200) -> None:
        """插入顶点"""
        ...

    def insert_edge(self, edge: Union[List[Edge], Edge], batch_size: int = 200) -> None:
        """插入边"""
        ...

    def delete_vertex(self, vid: Union[List[ID], ID], with_edge: bool = False, batch_size: int = 200) -> None:
        """
        删除顶点
        @param vid: 顶点 id
        @param with_edge: 是否顺便删除与该点关联的出边和入边
        """
        ...

    def query_vertexes(
            self,
            vid: List[ID] | ID | None = None,
            limit: int = 10,
    ) -> GraphData:
        """
        query only vertex.
        @param vid: 节点 ID
        @param limit: 返回条数限制
        """
        ...

    def filter_vertexes(
            self,
            vid: ID | None = None,
            label_filter: GraphLabelValue | None = None,
            limit: int = 10,
    ) -> GraphData:
        """
        query only vertex.
        @param vid: 节点 ID
        @param label_filter: 节点限制条件
        @param limit: 返回条数限制
        """
        ...

    def walk(
            self,
            src_vid: ID,
            dst_vertex_label_filters: List[GraphLabelValue] | None = None,
            edge_label_filters: List[GraphLabelValue] | None = None,
            direction: Literal['DIRECT', 'REVERSELY', 'BIDIRECT'] | None = None,
            min_step: int = 1,
            max_step: int = 1,
            offset: int = 0,
            limit: int = 10,
    ) -> GraphData:
        """
        从某个节点开始遍历图

        Args:
            src_vid：开始的节点 ID 列表
            dst_vertex_label_filters: 结束节点筛选器
            edge_label_filters: 遍历的边筛选器
            direction: 图下钻方向，默认为 DIRECT 正向下钻，其他取值有："REVERSELY" | "BIDIRECT"，分别表示反向下钻、双向下钻
            min_step & max_step: 遍历 min_step~max_step 跳的边，min_step 取值 0 和 1 语义相同，如果仅指定 max_step，则表示从节点开始下钻 max_step 步
            offset: 开始的结果 offset
            limit: 返回条数限制
        """
        ...

    def find_path(
            self,
            src_vids: List[ID],
            dst_vids: List[ID],
            edge_label_filters: List[GraphLabelValue] | None = None,
            strategy: Literal['SHORTEST', 'SINGLE SHORTEST', 'ALL', 'NOLOOP'] = 'SINGLE SHORTEST',
            direction: str | None = None,
            with_labels: bool = True,
            max_step: int = 0,
            limit: int = 10,
    ) -> GraphData:
        """
        获取点之间的路径

        Params:
            strategy：查找的策略，取值：
                SHORTEST: 查找所有最短路径
                SINGLE SHORTEST: 查找所有最短路径，随机返回其中一条
                ALL: 查找所有路径
                NOLOOP: 查找非循环路径
            with_labels: 是否查询涉及的点和边的 label
            edge_label_filters: 遍历的边筛选器
            direction: 图下钻方向，默认为正向下钻，其他取值有："REVERSELY" | "BIDIRECT"，分别表示反向下钻、双向下钻
            max_step: 路径的最大跳数
            limit: 返回条数限制
        """
        ...


class LarkGraphStoreFactory:

    @classmethod
    def get_graph_store(
            cls,
            space: str,
            schema: GraphSchema,
            store_config: Dict[str, Any] | None = None,
            **kwargs
    ) -> LarkGraphStore:
        if not space:
            raise RuntimeError('Graph store space null!')
        if not schema:
            raise RuntimeError('Graph store schema null!')

        if not store_config:
            store_config = config.graph_storage_config
        from .nebula_graph_store import NebulaGraphStore
        return NebulaGraphStore(space=space, schema=schema, **store_config, **kwargs)
