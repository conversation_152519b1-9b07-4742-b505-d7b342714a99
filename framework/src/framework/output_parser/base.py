from abc import ABC, abstractmethod
from typing import Union, Any, Dict, Optional

from langchain.agents import AgentOutputParser
from langchain_core.agents import AgentAction, AgentFinish
from pydantic import BaseModel


class ToolInputParser(BaseModel, ABC):
    @abstractmethod
    def parse_tool_input(self, origin_tool_input: Union[str, dict]) -> Union[str, dict]:
        """检查 agent action 是否符合预期
        :raise LLMResultValidationException"""


class ToolBasedAgentOutputParser(AgentOutputParser, ABC):
    special_tool_parsers: Optional[Dict[str, ToolInputParser]] = None

    def parse(self, text: str) -> Union[AgentAction, AgentFinish]:
        action = self.parse_action(text=text)
        if isinstance(action, AgentAction):
            if self.special_tool_parsers is not None:
                checker = self.special_tool_parsers.get(action.tool)
                if checker is not None:
                    action.tool_input = checker.parse_tool_input(action.tool_input)
        return action

    @abstractmethod
    def parse_action(self, text: str) -> Union[AgentAction, AgentFinish]:
        """解析 action"""
