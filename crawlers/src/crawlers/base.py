from selenium import webdriver
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.firefox.webdriver import WebDriver
from selenium.webdriver.firefox.options import Options
from crawlers.drivers.firefox import SensorsGeckoDriverManager


def get_web_driver() -> WebDriver:
    opt = Options()
    opt.add_argument("--headless")  # 无头
    opt.add_argument("--disbale-gpu")  # 无gpu图形化界面，为了调试能看见，可以把这行注释掉

    # may download firefox driver
    driver = webdriver.Firefox(
        service=FirefoxService(SensorsGeckoDriverManager().install()),
        options=opt,
    )
    return driver
