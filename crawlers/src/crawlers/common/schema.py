from enum import Enum
import re
from pydantic import BaseModel


class PageSelector(BaseModel):
    """页面内容选择器"""
    by: str  # 参考：selenium.webdriver.common.by.By
    value: str


class UrlFilterBy(Enum):
    url_prefix = 'url_prefix'
    url_suffix = 'url_suffix'
    url_regx = 'url_regx'  # url 正则表达式匹配


class UrlFilter(BaseModel):
    """爬取页面筛选器"""
    by: UrlFilterBy
    value: str

    def filter(self, url: str) -> bool:
        if not url or not url.strip():
            return False
        match self.by:
            case UrlFilterBy.url_prefix:
                return url.strip().startswith(self.value)
            case UrlFilterBy.url_suffix:
                return url.strip().endswith(self.value)
            case UrlFilterBy.url_regx:
                return re.match(self.value, url) is not None
            case _:
                raise RuntimeError(f'Unsupported filter by: {self.by}')
