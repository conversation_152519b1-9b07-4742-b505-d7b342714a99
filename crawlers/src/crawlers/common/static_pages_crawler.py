import time
from typing import List, Tuple
from selenium.webdriver.common.by import By
import requests
from fake_useragent import UserAgent
from bs4 import BeautifulSoup
from random import Random

from common import tools
from .schema import PageSelector
from .crawler_with_llm import BasicCrawlerWithLLM

log = tools.get_logger()


class StaticPagesCrawler(BasicCrawlerWithLLM):
    def __init__(
            self,
            wait_time_range: Tuple[int, int] | None = None,
            speed_limit_page_keyword: str = None,
            **kwargs
    ):
        """
        静态网页爬虫

        Args:
            wait_time_range: 每次爬取的时间间隔区间，从区间中取随机值
            speed_limit_page_keyword: 爬虫被限流了的过滤关键字
            kwargs: 兼容其他参数不报错
        """
        super().__init__(**kwargs)
        self.wait_time_range = wait_time_range
        if self.wait_time_range is None:
            self.wait_time_range = (0, 10)
        self.speed_limit_page_keyword = speed_limit_page_keyword

    async def start(self, max_page_limit: int = 10_000):
        await self._start_with_requests(max_page_limit=max_page_limit)

    async def _start_with_requests(self, max_page_limit: int = 10_000):
        """爬取静态页面"""
        random = Random(int(time.time()))
        page_count = 0
        for i in range(max_page_limit):
            url = self._next_url()
            if not url:
                break
            page_count += 1
            try:
                self.set_been_crawled(url)
                page_result = await self._get_static_page(url=url)
                self._process_page_result(url=url, page_result=page_result)
                time.sleep(random.randint(self.wait_time_range[0], self.wait_time_range[1]))
            except BaseException as e:
                log.warning('crawler error.', e)
                self._callback(f'crawler for page {url} occurs error: {e}')

    async def _get_static_page(self, url) -> Tuple[str | None, str | None, List[str] | None]:
        log.info(f'start get page: {url}')
        ua = UserAgent()
        headers = {
            'User-Agent': ua.random,
        }
        resp = requests.get(url, headers=headers)
        html = resp.text
        title = await self.parse_title(html)
        soup = BeautifulSoup(html, 'html5lib')

        is_content_page = self._passed_content_url_filter(url)
        links_html_elements = []
        if self.links_selector:
            for selector in self.content_selector:
                element = self._find_static_page_element(soup, selector)
                if element:
                    links_html_elements.append(element)

        # 如果链接元素已经提取到，并且该页面不在内容过滤器页面中，则不再解析页面内容
        need_parse_content = True
        if links_html_elements and (not is_content_page):
            need_parse_content = False

        content_html_elements = []
        if need_parse_content:
            if is_content_page and self.content_selector:
                for selector in self.content_selector:
                    element = self._find_static_page_element(soup, selector)
                    if element:
                        content_html_elements.append(element)

        if need_parse_content and (not content_html_elements):
            body = self._find_static_page_element(soup, PageSelector(by=By.TAG_NAME, value='body'))
            content_html_elements.append(body)

        result = await self.parse_content(
            url=url,
            links_html_elements=links_html_elements,
            content_html_elements=content_html_elements
        )
        return title, result[0], result[1]

    def _find_static_page_element(self, soup: BeautifulSoup, selector: PageSelector) -> str:
        if selector.by == By.TAG_NAME or selector.by == By.NAME:
            return soup.find(selector.value).text
        if selector.by == By.CSS_SELECTOR or selector.by == By.XPATH:
            return soup.select_one(selector=selector.value).text
        if selector.by == By.ID:
            return soup.select_one(selector=f'#{selector.value}').text
        if selector.by == By.CLASS_NAME:
            return soup.find(selector=f'class={selector.value}').text
        raise RuntimeError(f'Unsupported selector for static page. by={selector.by}')
