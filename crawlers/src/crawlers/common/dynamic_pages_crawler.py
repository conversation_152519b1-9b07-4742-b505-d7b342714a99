import time
from typing import <PERSON><PERSON>, List

from selenium.webdriver.support.wait import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By

from crawlers.base import get_web_driver
from common import tools
from .crawler_with_llm import BasicCrawlerWithLLM
from .schema import PageSelector

log = tools.get_logger()


class DynamicPagesCrawler(BasicCrawlerWithLLM):
    def __init__(
            self,
            wait_page_load_time: int = 10,
            speed_limit_page_keyword: str = None,
            **kwargs
    ):
        """
        动态网页爬虫，需要加载 ajax 等网页

        Args:
            wait_page_load_time: 等待网页加载的时间，默认是 10 秒
            speed_limit_page_keyword: 爬虫被限流了的过滤关键字
            kwargs: 父类参数等
        """
        super().__init__(**kwargs)
        self.wait_page_load_time = wait_page_load_time
        self.speed_limit_page_keyword = speed_limit_page_keyword

    async def start(self, max_page_limit: int = 10_000):
        await self._start_with_driver(max_page_limit=max_page_limit)

    async def _start_with_driver(self, max_page_limit: int = 10_000):
        """
        动态加载页面
        打开页面过多容易 oom，所以 browser 需要每爬几个页面关闭一次
        """
        page_count = 0
        browser = None
        for i in range(max_page_limit):
            url = self._next_url()
            if not url:
                break
            page_count += 1

            if page_count % 10 == 0:
                try:
                    tmp_browser = browser
                    browser = None
                    tmp_browser.close()
                except:
                    pass

            if browser is None:
                browser = get_web_driver()
            try:
                self.set_been_crawled(url)
                page_result = await self._get_dynamic_page(browser, url=url)
                self._process_page_result(url=url, page_result=page_result)
            except BaseException as e:
                log.warning('crawler error.', e)
                self._callback(f'crawler for page {url} occurs error: {e}')

    def _wait_browser_element(self, browser, selector: PageSelector) -> bool:
        try:
            WebDriverWait(browser, self.wait_page_load_time).until(
                EC.visibility_of_all_elements_located((selector.by, selector.value)))
            return True
        except BaseException as e:
            log.debug(f'wait selector failed.', e)
            return False

    async def _get_dynamic_page(self, browser, url: str) -> Tuple[str | None, str | None, List[str] | None]:
        log.info(f'start get page: {url}')
        browser.get(url)

        is_content_page = self._passed_content_url_filter(url)
        links_html_elements = []
        if self.links_selector:
            for selector in self.content_selector:
                if not self._wait_browser_element(browser, selector):
                    continue
                element = browser.find_element(by=selector.by, value=selector.value)
                if element is not None:
                    links_html_elements.append(browser.execute_script("return arguments[0].innerHTML;", element))

        # 如果链接元素已经提取到，并且该页面不在内容过滤器页面中，则不再解析页面内容
        need_parse_content = True
        if links_html_elements and (not is_content_page):
            need_parse_content = False

        content_html_elements = []
        if need_parse_content:
            if is_content_page and self.content_selector:
                for selector in self.content_selector:
                    if not self._wait_browser_element(browser, selector):
                        continue
                    element = browser.find_element(by=selector.by, value=selector.value)
                    if element is not None:
                        content_html_elements.append(browser.execute_script("return arguments[0].innerHTML;", element))

        if need_parse_content and (not content_html_elements):
            time.sleep(self.wait_page_load_time)
            body = browser.find_element(by=By.TAG_NAME, value='body')
            content_html_elements.append(browser.execute_script("return arguments[0].innerHTML;", body))

        title = browser.title
        result = await self.parse_content(
            url=url,
            links_html_elements=links_html_elements,
            content_html_elements=content_html_elements
        )
        return title, result[0], result[1]
