# 通用知识库爬虫
from abc import ABC
from typing import List, <PERSON><PERSON>
from tenacity import retry, stop_after_attempt, retry_if_exception_type, wait_fixed
import xml.etree.cElementTree as ET

from framework.models import BaseLLM, ModelsFactory
from .base import BasicCommonCrawler

import re
from framework.agents.assistant import AssistantAgent
from common import tools

log = tools.get_logger()

PAGE_CONTENT_PROMPT = """
# 职责
你是一个网页爬虫，你能读懂网页前端代码，给你一段网页内容提取需求和网页源码，你需要将网页内容按照需求整理成 markdown 格式，
并根据需求和网页源码识别出需要额外爬取的网页链接，并按照回复要求中的格式回复。
网页源码有可能不是完整的网页源码，而是局部源码。

内容提取需求说明：
{pages_instructions}

需要注意，你识别出来的网页链接是可能跟需求中相匹配的内容链接，不匹配的链接无需在回复中给出。

# 网页及回复要求
用户给出的网页会以 <|PAGE-CONTENT|> 开头和结尾。
你需要严格按照以下格式回复：
Thought: 你对于当前网页需要提取内容的思考
Response:
<xml>
    <page-content>
        your page content with markdown format
    </page-content>
    <link-urls>
        <link-url>https://your_urls.domain</link-url>
        ...
    </link-urls>
</xml>

你必须以 Thought: 开头，并且给出 Response:，在 Thought 中，你需要思考当前网页，并给出如何提取的建议
在 Response: 中，你需要按照 xml 的格式出，并且分别将内容和链接包含在 <page-content> 和 <link-urls> 标签内。
其中：
  - <page-content> 表示当前页面的内容（没有实质内容则不用回复该标签，举例：一些内容列表页、仅包含广告、课程内容的网站则不用回复该标签）
  - <link-urls> 表示需要额外爬取的网页链接。
如果当前网页没有对应的文章内容或者下级链接，则直接在对应标签内回复空列表即可，比如：
<xml>
    <page-content/>
    <link-urls/>
</xml>

在你使用 xml 格式回复完 Response: 中的内容以后，你需要立即结束你的回复（即不再回复任何内容）。

# 开始
网页源码：
***
<|PAGE-CONTENT|>
{page_content}
<|PAGE-CONTENT|>
"""


def _parse_xml(xml_doc: str) -> Tuple[str, List[str]]:
    tree = ET.fromstring(xml_doc)
    content = tree.find('page-content')
    if content is not None:
        content = content.text
    links_element = tree.find('link-urls')
    links = []
    if links_element is not None:
        links_elements = links_element.findall('link-url')
        if links_elements is not None:
            for e in links_elements:
                links.append(e.text)

    return content, links


@retry(
    stop=stop_after_attempt(5),
    retry=retry_if_exception_type(BaseException),
    wait=wait_fixed(60),
    reraise=True,
)
async def _get_page_content_by_llm(
        llm: BaseLLM,
        pages_instructions: str,
        page_content_source: str,
        links_source: str | None = None,
) -> Tuple[str | None, List[str] | None]:
    system_prompt = PAGE_CONTENT_PROMPT
    if (not page_content_source) and (not links_source):
        return None, None

    if links_source:
        links_source = f'待爬取链接源码：\n{links_source}'
    system_prompt = system_prompt.format(
        pages_instructions=pages_instructions,
        page_content=f'{page_content_source}\n{links_source}',
    )
    agent = AssistantAgent(llm=llm, system_prompt=system_prompt)
    user_prompt = '请你按照要求的格式回复：\nThought:'

    for i in range(5):
        response = await agent.achat_and_save(prompt=user_prompt)
        if 'Response:' in response:
            response = response.split('Response:')[1]
        else:
            log.warning(f'LLM response format error. response={response}')
            user_prompt = '你的回复格式错误，请严格按照格式回复：'
            continue
        # 回复解析，解析失败重试
        try:
            return _parse_xml(response)
        except BaseException as e:
            log.warning(f'LLM response parse error. response={response}', e)
            user_prompt = '你的回复格式错误，请严格按照格式回复：'
            continue
    raise RuntimeError('Can not get page content and urls from page source.')


class BasicCrawlerWithLLM(BasicCommonCrawler, ABC):
    def __init__(
            self,
            pages_instructions: str,
            use_llm_content_parse: bool = False,
            llm: BaseLLM = ModelsFactory.get_llm(),
            max_token_limit: int = 1000 * 64,
            **kwargs
    ):
        """
        基础带 LLM 的爬虫

        Args:
            pages_instructions: 页面获取说明，会根据此说明解析网页内容和下级网页链接
            use_llm_content_parse: 是否使用 llm 做内容解析
            llm: llm
            max_token_limit: llm input token limit
            kwargs: 父类参数
        """
        super().__init__(**kwargs)
        self.use_llm_content_parse = use_llm_content_parse
        self.llm = llm
        self.pages_instructions = pages_instructions
        self.max_token_limit = max_token_limit

    async def _get_page_content_by_llm(
            self,
            page_content_source: str,
            links_source: str | None = None,
    ) -> Tuple[str | None, List[str] | None]:
        """
        使用大模型解析网页内容

        Return:
            返回三元组：标题、内容、下级链接
        """
        return await _get_page_content_by_llm(
            llm=self.llm,
            pages_instructions=self.pages_instructions,
            page_content_source=page_content_source,
            links_source=links_source,
        )

    async def parse_title(self, html_content: str) -> str:
        title_match = re.search(r'<title>(.*?)</title>', html_content, re.IGNORECASE)
        return title_match.group(1).strip() if title_match else 'Unknown'

    async def parse_content(
            self,
            url: str,
            links_html_elements: List[str],
            content_html_elements: List[str],
    ) -> Tuple[str | None, List[str] | None]:
        is_content_page = self._passed_content_url_filter(url)
        # 去除多余标签等
        content_html_elements = [self._trim_html(content) for content in content_html_elements]
        links_html_elements = [self._trim_html(content) for content in links_html_elements]

        log.info(f'start parse page for url: {url}')
        rel_content = None
        rel_links = []

        if self.use_llm_content_parse:
            rel = await self._get_page_content_by_llm(
                page_content_source='\n'.join(content_html_elements),
                links_source='\n'.join(links_html_elements)
            )
            if rel is not None:
                rel_content = rel[0] if is_content_page else None
                rel_links = rel[1]
        else:
            # 手动解析
            links_html_elements.extend(content_html_elements)
            if is_content_page:
                rel_content = '\n'.join([self._html_to_markdown(html=h) for h in content_html_elements])
            for h in links_html_elements:
                rel_links.extend(self._get_links_from_html(html=h))
        return rel_content, rel_links
