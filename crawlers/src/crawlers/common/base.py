import json
import time
from abc import ABC, abstractmethod
from pathlib import Path
from typing import List, Callable, Any, Tuple
from langchain_community.document_transformers import MarkdownifyTransformer
from langchain_core.documents import Document

from bs4 import BeautifulSoup

from crawlers.data.document import SensorsDocMetadata, SensorsDocsMetadata
from common import tools
from .schema import PageSelector, UrlFilter

log = tools.get_logger()


class BasicCommonCrawler(ABC, object):

    def __init__(
            self,
            basic_urls: List[str],
            output_path: Path,
            content_page_url_filters: List[UrlFilter] | None = None,
            link_url_filters: List[UrlFilter] | None = None,
            content_selector: PageSelector | List[PageSelector] | None = None,
            links_selector: PageSelector | List[PageSelector] | None = None,
            callback: Callable[[Any], Any] | None = None,
            **kwargs
    ):
        """
        基础通用爬虫

        Args:
            basic_urls: 爬虫开始 url
            content_page_url_filters: 需要抓取内容的页面 url 过滤器，满足该过滤器的 url 才会保存内容
            link_url_filters: 需要解析链接的页面 url 过滤器，满足该过滤器的页面才会解析 url
            content_selector: 可以预先通过 selector 筛选网页源码中的内容，防止上下文超出，并且减少 token 消耗，增加爬虫爬取时间
            links_selector: 下级链接的 selector
            callback: 其他的日志等回显使用
            kwargs: 兼容其他参数不报错
        """
        self.content_page_url_filters = content_page_url_filters
        self.link_url_filters = link_url_filters
        self._crawled_cache = set()
        self._url_pool = []
        for url in basic_urls:
            self._add_url(url)

        self.callback = callback
        self.output_path = output_path
        if not self.output_path.exists():
            self.output_path.mkdir(parents=True, exist_ok=True)

        self.content_selector = []
        if content_selector is not None:
            if isinstance(content_selector, List):
                self.content_selector.extend(content_selector)
            else:
                self.content_selector.append(content_selector)

        self.links_selector = []
        if links_selector is not None:
            if isinstance(links_selector, List):
                self.links_selector.extend(links_selector)
            else:
                self.links_selector.append(links_selector)

    def _callback(self, content: Any):
        try:
            log.info(content)
            if self.callback:
                self.callback(content)
        except:
            pass

    def _add_url(self, url: str):
        if not url:
            return
        if url in self._crawled_cache or url in self._url_pool:
            return
        if not self._passed_links_url_filter(url=url):
            return

        self._callback(f'add {url} into crawler pool.')
        self._url_pool.append(url)

    def _next_url(self) -> str | None:
        if not self._url_pool:
            return None
        return self._url_pool.pop(0)

    def set_been_crawled(self, url: str):
        if not url:
            return
        self._crawled_cache.add(url)

    def _save_metadata(self, metadata: SensorsDocsMetadata):
        metadata.end_time = self._get_now_time()
        metadata_file = self.output_path.joinpath('metadata.json')
        json.dump(
            metadata.model_dump(),
            metadata_file.open('w', encoding='utf-8'),
            indent=4,
            ensure_ascii=False
        )

    def _passed_links_url_filter(self, url: str) -> bool:
        if not self.link_url_filters:
            return True
        for f in self.link_url_filters:
            if f.filter(url=url):
                return True
        return False

    def _passed_content_url_filter(self, url: str) -> bool:
        if not self.content_page_url_filters:
            return True
        for f in self.content_page_url_filters:
            if f.filter(url=url):
                return True
        return False

    def _process_page_result(self, url: str, page_result: Tuple[str | None, str | None, List[str] | None]):
        if page_result is not None:
            if page_result[2]:
                for more_url in page_result[2]:
                    self._add_url(more_url)

            if self._passed_content_url_filter(url):
                if page_result[1]:
                    self.add_as_document(
                        document_title=page_result[0], document_content=page_result[1], url=url)
                else:
                    self._callback(f'page {url} has no contents, skip.')

    def _trim_html(self, html: str) -> str:
        """
        去除 html 中不需要的样式等，减少 token 消耗
        去除内容包括：css / js
        """
        soup = BeautifulSoup(html, 'html5lib')
        for ele in soup(['style', 'script']):
            ele.extract()
        return str(soup)

    @property
    def metadata(self) -> SensorsDocsMetadata:
        metadata_file = self.output_path.joinpath('metadata.json')
        if metadata_file.exists():
            metadata = SensorsDocsMetadata.model_validate(json.load(metadata_file.open('r', encoding='utf-8')))
        else:
            metadata = SensorsDocsMetadata.model_validate({
                'batch_id': 0,  # 每次新爬取就 +1
                'documents': [],
            })
        metadata.start_time = self._get_now_time()
        return metadata

    def add_as_document(self, document_title: str | None, document_content: str, url: str):
        content_sign = tools.md5(document_content)
        file_name = tools.uuid4_no_underline() + '.txt'
        metadata = self.metadata
        exist_doc = metadata.get_doc(url=url)
        if exist_doc:
            if content_sign == exist_doc.content_sign:
                self._callback(f'no changes for page {url}.')
                return
        curr_document = SensorsDocMetadata(
            file_name=file_name,
            id=url,
            url=url,
            title=document_title.strip() if document_title else '',
            content_sign=content_sign,
            batch_id=metadata.batch_id,
        )
        metadata.upsert_doc(curr_document)
        Path(self.output_path, curr_document.file_name).write_text(document_content, encoding='utf-8')
        self._save_metadata(metadata)

    def _get_now_time(self) -> str:
        return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

    @abstractmethod
    async def start(self, max_page_limit: int = 10_000):
        """开始爬取

        Args:
            max_page_limit: 最大爬取页面个数（包含有内容和没有内容的）
        """
        ...

    def _html_to_markdown(self, html: str) -> str:
        # 内容转 markdown
        content = MarkdownifyTransformer().transform_documents(
            [Document(page_content=html)]
        )[0].page_content
        return content

    def _get_links_from_html(self, html: str) -> list[str]:
        # 获取链接
        soup = BeautifulSoup(html, 'html5lib')
        links = soup.find_all('a')
        result = []
        if links is not None:
            for link in links:
                try:
                    result.append(link.get('href'))
                except:
                    pass
        return result
