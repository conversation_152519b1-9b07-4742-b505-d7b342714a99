import os
from typing import Optional

from webdriver_manager.core.download_manager import Download<PERSON>anager
from webdriver_manager.core.driver_cache import Driver<PERSON>ache<PERSON>anager
from webdriver_manager.core.manager import DriverManager
from webdriver_manager.core.os_manager import OperationSystemManager
from webdriver_manager.drivers.firefox import GeckoDriver

driver_version = 'v0.35.0'


def _driver_get_latest_release_version(self) -> str:
    return driver_version


GeckoDriver.get_latest_release_version = _driver_get_latest_release_version


class SensorsGeckoDriverManager(DriverManager):
    """ 由于公开的很多资源在 github 上，国内下载困难，所以这里只能自己适配一个 """

    def __init__(
            self,
            version: Optional[str] = None,
            name: str = "geckodriver",
            url: str = "https://mirrors.huaweicloud.com/geckodriver",
            latest_release_url: str = "https://api.github.com/repos/mozilla/geckodriver/releases/latest",
            mozila_release_tag: str = "https://mirrors.huaweicloud.com/geckodriver/.index.json",
            download_manager: Optional[DownloadManager] = None,
            cache_manager: Optional[DriverCacheManager] = None,
            os_system_manager: Optional[OperationSystemManager] = None
    ):
        super(SensorsGeckoDriverManager, self).__init__(
            download_manager=download_manager,
            cache_manager=cache_manager
        )

        self.driver = GeckoDriver(
            driver_version=driver_version,
            name=name,
            url=url,
            latest_release_url=latest_release_url,
            mozila_release_tag=mozila_release_tag,
            http_client=self.http_client,
            os_system_manager=os_system_manager
        )

        self.url = url
        self.mozila_release_tag = mozila_release_tag
        self.driver_version = driver_version

    def _get_driver_binary_path(self, driver: GeckoDriver):
        binary_path = self._cache_manager.find_driver(driver)
        if binary_path:
            return binary_path

        os_type = self.get_os_type()

        download_url = f'{self.url}/{self.driver_version}/geckodriver-{self.driver_version}-{os_type}.tar.gz'
        file = self._download_manager.download_file(download_url)
        binary_path = self._cache_manager.save_file_to_cache(driver, file)
        return binary_path

    def install(self) -> str:
        driver_path = self._get_driver_binary_path(self.driver)
        os.chmod(driver_path, 0o755)
        return driver_path

    def get_os_type(self):
        os_type = super().get_os_type()
        if not self._os_system_manager.is_mac_os(os_type):
            return os_type

        macos = 'macos'
        if self._os_system_manager.is_arch(os_type):
            return f"{macos}-aarch64"
        return macos
