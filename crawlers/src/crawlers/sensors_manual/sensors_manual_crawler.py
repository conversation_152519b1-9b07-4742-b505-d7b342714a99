# 神策官网文档爬虫
import json, os, time
from pathlib import Path
from typing import Set, List, Any, Callable
from langchain_community.document_transformers import MarkdownifyTransformer
from langchain_core.documents import Document
from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from framework.data.document import SensorsDocMetadata, SensorsDocsMetadata
from crawlers.base import get_web_driver
from common import tools

log = tools.get_logger()

crawlers_start_points = {
    'sa': {
        'url_prefix': 'https://manual.sensorsdata.cn/sa',
        '3.0': {
            'start_page': 'https://manual.sensorsdata.cn/sa/docs/main/v0300'
        },
        '2.5': {
            'start_page': 'https://manual.sensorsdata.cn/sa/docs/main/v0205'
        }
    },
    'sf': {
        'url_prefix': 'https://manual.sensorsdata.cn/sf',
        '4.4': {
            'start_page': 'https://manual.sensorsdata.cn/sf/docs/user_guide/v0404'
        },
        '4.3': {
            'start_page': 'https://manual.sensorsdata.cn/sf/docs/user_guide/v0403'
        },
        '4.2': {
            'start_page': 'https://manual.sensorsdata.cn/sf/docs/user_guide/v0402'
        },
    }
}


class SensorsManualCrawler:
    def __init__(
            self,
            start_page: str,
            url_prefix: str,
            clean_dir: bool = False,
            storage_dir: Path = Path('/tmp/' + tools.uuid4_no_underline()),
            callback: Callable[[str], None] = None
    ):
        self.start_page = start_page
        self.url_prefix = url_prefix
        self.clean_dir = clean_dir
        self.callback = callback
        self.storage_dir = storage_dir
        p = Path(self.storage_dir)
        if not p.exists():
            p.mkdir(parents=True, exist_ok=True)

        self._init_url_pool()
        self._init_metadata()

    def _callback(self, content: str):
        log.info(content)
        if self.callback:
            try:
                self.callback(content)
            except:
                pass

    def _clean_dir(self):
        if self.clean_dir:
            for root, dirs, files in os.walk(self.storage_dir, topdown=False):
                for name in files:
                    os.remove(os.path.join(root, name))
                for name in dirs:
                    os.rmdir(os.path.join(root, name))

    def _init_metadata(self):
        metadata_file = self.storage_dir.joinpath('metadata.json')
        if metadata_file.exists():
            self.metadata = SensorsDocsMetadata.model_validate(json.load(metadata_file.open('r', encoding='utf-8')))
        else:
            self.metadata = SensorsDocsMetadata.model_validate({
                'batch_id': 0,  # 每次新爬取就 +1
                'documents': [],
            })
        self.metadata.start_time = self._get_now_time()

    def _init_url_pool(self):
        self._url_pool = [self.start_page]
        self._url_prefix = self.url_prefix

        # 已经抓取过的 url 列表
        self._crawled_urls: Set[str] = {''}

    def start_crawler(self, max_page_count: int = 100_000):
        """开始从 self._url_pool 中爬取"""
        self._callback('开始爬取神策使用文档')
        if self.clean_dir:
            self._clean_dir()

        # 遍历爬取
        self.metadata.batch_id = self.metadata.batch_id + 1
        new_docs = 0
        update_docs = 0

        page_count = 0

        # 打开页面过多容易 oom，所以 browser 需要每爬几个页面关闭一次
        browser = None
        for i in range(max_page_count):
            if not self._url_pool:
                break
            url = self._url_pool.pop(0)
            if url in self._crawled_urls:
                continue
            page_count += 1
            if page_count % 10 == 0:
                try:
                    tmp_browser = browser
                    browser = None
                    tmp_browser.close()
                except:
                    pass

            if browser is None:
                browser = get_web_driver()
            try:
                article_content, title, more_urls = self._get_page(browser, url, page_count)
                self._crawled_urls.add(url)
                if more_urls:
                    self._url_pool.extend([u.strip() for u in more_urls if u.startswith(self._url_prefix)])

                content_sign = tools.md5(article_content)
                file_name = tools.uuid4_no_underline() + '.txt'

                exist_doc = self.metadata.get_doc(url=url)
                if exist_doc:
                    if content_sign == exist_doc.content_sign:
                        continue
                    else:
                        update_docs += 1
                else:
                    new_docs += 1

                curr_document = SensorsDocMetadata(
                    file_name=file_name,
                    id=url,
                    url=url,
                    title=title,
                    content_sign=content_sign,
                    batch_id=self.metadata.batch_id
                )
                self.metadata.upsert_doc(curr_document)
                Path(self.storage_dir, curr_document.file_name).write_text(article_content, encoding='utf-8')
                self._save_metadata()
            except BaseException as e:
                log.error('get page failed.', e)
                self._callback(f'get page failed, now ignore. url={url}, error_detail={e}')

        if browser is not None:
            browser.close()
        self._callback(f'爬取完成：总共爬取页面 {page_count}个，其中新增页面 {new_docs}个，更新页面 {update_docs}个')

    def _get_page(self, browser, url: str, page_count):
        self._callback(f'get page content from {url}. page_count={page_count}')
        browser.get(url)

        article_xpath = '//*[@id="hl-doc"]'
        WebDriverWait(browser, 10).until(EC.visibility_of_element_located((By.XPATH, article_xpath)))
        article_element = self._find_element(
            element=browser,
            by=By.XPATH,
            value=article_xpath,
        )
        title_xpath = '//*[@id="hl-docLayoutScroll"]/div/div[1]/div[1]/div/div[1]'
        title_xpath = '//*[@id="hl-docLayoutScroll"]/div/div[1]/div[1]/div/div[1]'
        title_element = self._find_element(
            element=browser,
            by=By.XPATH,
            value=title_xpath
        )
        if not title_element or title_element.get_property('role') != 'navigation':
            title_element = self._find_element(
                element=article_element,
                by=By.TAG_NAME,
                value='h1'
            )
        if title_element:
            title = title_element.text.replace('\n', ' -> ')
        else:
            title = ''
        article_content = browser.execute_script("return arguments[0].innerHTML;", article_element)
        # 内容转 markdown
        article_content = MarkdownifyTransformer().transform_documents(
            [Document(page_content=article_content)]
        )[0].page_content

        # 只有首次才需要展开全部目录
        if page_count > 1:
            return article_content, title, None

        # 等待左边目录树加载完毕
        WebDriverWait(browser, 60).until(
            EC.visibility_of_element_located((
                By.XPATH,
                '//*[@id="hl-docLayoutScroll"]/div/div/div[2]/div[2]/div[1]/div'
            ))
        )

        # 展开文档目录
        element = self._find_element(
            element=browser,
            by=By.XPATH,
            value='//*[@id="contentMenu"]'
        )
        a_elements = self._find_elements(
            element=element,
            by=By.TAG_NAME,
            value='a',
        )

        more_urls = []
        for ele in a_elements:
            url = ele.get_attribute('href')
            more_urls.append(url)

        return article_content, title, more_urls

    def _expand_trees(self, element) -> List[str]:
        """展开目录并获取目录下面的页面 url 列表"""
        link = self._find_element(
            element=element,
            by=By.TAG_NAME,
            value='a'
        )
        content = link.accessible_name
        url = link.get_attribute('href')
        self._callback(f'Got page url tree. {content} : {url}')
        result = [url]
        expand_button = self._find_element(
            element=element,
            by=By.CLASS_NAME,
            value='submenu-toggle'
        )
        if not expand_button:
            return result

        expanded = expand_button.get_attribute('aria-expanded')
        if expanded.lower() == 'false':
            self._callback(f'展开菜单. {content} : {url}')
            expand_button.click()
            WebDriverWait(element, 60).until(
                EC.element_to_be_clickable((By.TAG_NAME, 'ul'))
            )

        ul_element = self._find_element(element=element, by=By.TAG_NAME, value='ul')
        li_elements = self._find_elements(element=ul_element, by=By.TAG_NAME, value='li')

        if li_elements:
            for ele in li_elements:
                child_tree = self._expand_trees(ele)
                result.extend(child_tree)

        return result

    def _find_element(self, element, by, value) -> Any:
        try:
            return element.find_element(by=by, value=value)
        except:
            return None

    def _find_elements(self, element, by, value) -> List[Any]:
        try:
            return element.find_elements(by=by, value=value)
        except:
            return []

    def _save_metadata(self):
        self.metadata.end_time = self._get_now_time()
        metadata_file = self.storage_dir.joinpath('metadata.json')
        json.dump(
            self.metadata.model_dump(),
            metadata_file.open('w', encoding='utf-8'),
            indent=4,
            ensure_ascii=False
        )

    def _get_now_time(self) -> str:
        return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
