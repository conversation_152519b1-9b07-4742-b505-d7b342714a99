[project]
name = "crawlers"
version = "1.0.0"
classifiers = ["Private :: Do Not Upload"]
description = ""
authors = [
    { name = "<EMAIL>" }
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "common",
    "framework",
    "requests>=2.32.3",
    "fake-useragent>=2.2.0",
    "beautifulsoup4>=4.13.4",
    "lxml>=5.4.0",
    "html5lib>=1.1",
    "selenium>=4.33.0",
    "webdriver-manager>=4.0.2",
    "markdownify>=1.1.0",
]

[tool.uv.sources]
common = { workspace = true }
framework = { workspace = true }

[dependency-groups]
dev = ["pytest"]
lint = ["ruff"]

[tool.setuptools]
packages = ["crawlers"]

[tool.setuptools.package-dir]
crawlers = "src/crawlers"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
