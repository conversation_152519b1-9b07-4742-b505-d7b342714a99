[project]
name = "prompt-server"
version = "1.0.0"
classifiers = ["Private :: Do Not Upload"]
description = ""
authors = [
    { name = "<EMAIL>" }
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "core",
    "streamlit>=1.45.1",
    "dotenv>=0.9.9",
    "stream-chat>=4.26.0",
]

[tool.uv.sources]
core = { workspace = true }

[tool.setuptools]
packages = ["prompt_server"]

[tool.setuptools.package-dir]
backend_server = "src/prompt_server"

[project.scripts]
# 封装系统命令
st_server = "prompt_server.server:start"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
