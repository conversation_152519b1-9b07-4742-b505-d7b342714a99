import streamlit as st
import sys

st.set_page_config(
    page_title="Sensors Prompts",
    page_icon="✨",
    layout='wide',
    initial_sidebar_state='collapsed',
)

prompt_manage_page = st.Page(
    "pages/prompt_manage.py", title="提示词管理", icon="✨", default=True
)

pg = st.navigation(
    {
        "PROMPT MANAGE": [prompt_manage_page],
    }
)


def start():
    file = __name__ + ".py"
    sys.exit(f"streamlit run {file}")


if __name__ == "__main__":
    pg.run()
