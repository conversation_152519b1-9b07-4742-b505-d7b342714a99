import streamlit as st
import asyncio
from core.service.prompt_service import PromptService
from core.models.prompt_model import PromptModel


class PromptManagePage:
    def __init__(self):
        self.prompt_service = PromptService()

    def _get_businesses(self):
        """获取业务列表（带缓存）"""
        try:
            return asyncio.run(self.prompt_service.get_all_businesses())
        except Exception as e:
            st.error(f"获取业务列表失败: {str(e)}")
            return []

    def _get_prompts_by_business(self, business: str):
        """获取指定业务的Prompt列表（带缓存）"""
        try:
            return asyncio.run(self.prompt_service.get_prompts_by_business(business))
        except Exception as e:
            st.error(f"获取Prompt列表失败: {str(e)}")
            return []

    def run(self):
        """运行页面"""
        st.title("📝 Prompt 管理")

        # 初始化session state
        if 'selected_business' not in st.session_state:
            st.session_state.selected_business = None
        if 'selected_prompt' not in st.session_state:
            st.session_state.selected_prompt = None
        if 'show_edit_drawer' not in st.session_state:
            st.session_state.show_edit_drawer = False
        if 'show_add_business_modal' not in st.session_state:
            st.session_state.show_add_business_modal = False
        if 'show_add_prompt_modal' not in st.session_state:
            st.session_state.show_add_prompt_modal = False
        if 'refresh_trigger' not in st.session_state:
            st.session_state.refresh_trigger = 0

        # 创建两列布局
        col1, col2 = st.columns([1, 2])

        with col1:
            self._render_business_sidebar()

        with col2:
            self._render_prompt_list()

        # 渲染模态框和抽屉
        self._render_modals_and_drawers()

    def _render_business_sidebar(self):
        """渲染左侧业务列表"""
        st.subheader("业务列表")

        # 添加业务按钮
        if st.button("➕ 添加业务", use_container_width=True):
            st.session_state.show_add_business_modal = True
            st.rerun()

        st.divider()

        # 获取业务列表（使用缓存）
        businesses = self._get_businesses()

        if not businesses:
            st.info("暂无业务数据")
            return

        # 渲染业务列表
        for business, business_cname in businesses:
            # 创建业务卡片
            with st.container():
                col1, col2 = st.columns([3, 1])

                with col1:
                    if st.button(
                            f"**{business}**\n{business_cname}",
                            key=f"business_{business}",
                            use_container_width=True,
                            type="primary" if st.session_state.selected_business == business else "secondary"
                    ):
                        st.session_state.selected_business = business
                        st.session_state.selected_prompt = None
                        st.session_state.show_edit_drawer = False
                        # 清除缓存以获取最新数据
                        self._get_prompts_by_business.clear()
                        st.rerun()

                with col2:
                    # 获取该业务下的prompt数量（使用缓存）
                    prompts = self._get_prompts_by_business(business)
                    st.caption(f"{len(prompts)} 个")

                st.divider()

    def _render_prompt_list(self):
        """渲染右侧prompt列表"""
        if not st.session_state.selected_business:
            st.info("请选择左侧的业务来查看相关的 Prompt")
            return

        # 获取选中业务的信息
        businesses = asyncio.run(self.prompt_service.get_all_businesses())
        selected_business_info = next(
            (b for b in businesses if b[0] == st.session_state.selected_business),
            (st.session_state.selected_business, "")
        )

        st.subheader(f"业务: {selected_business_info[0]} - {selected_business_info[1]}")

        # 添加prompt按钮
        if st.button("➕ 添加 Prompt", use_container_width=True):
            st.session_state.show_add_prompt_modal = True
            st.rerun()

        st.divider()

        # 获取prompt列表
        prompts = asyncio.run(self.prompt_service.get_prompts_by_business(st.session_state.selected_business))

        if not prompts:
            st.info("该业务下暂无 Prompt")
            return

        # 渲染prompt列表
        for prompt in prompts:
            with st.container():
                col1, col2 = st.columns([4, 1])

                with col1:
                    st.markdown(f"**{prompt.prompt_name}**")
                    if prompt.prompt_desc:
                        st.caption(prompt.prompt_desc)
                    st.caption(f"代码: {prompt.prompt_code}")

                with col2:
                    if st.button("编辑", key=f"edit_{prompt.id}"):
                        st.session_state.selected_prompt = prompt.id
                        st.session_state.show_edit_drawer = True
                        st.rerun()

                st.divider()

    def _render_modals_and_drawers(self):
        """渲染模态框和抽屉"""
        # 添加业务模态框
        if st.session_state.show_add_business_modal:
            self._render_add_business_modal()

        # 添加prompt模态框
        if st.session_state.show_add_prompt_modal:
            self._render_add_prompt_modal()

        # 编辑prompt抽屉
        if st.session_state.show_edit_drawer:
            self._render_edit_prompt_drawer()

    def _render_add_business_modal(self):
        """渲染添加业务模态框"""
        with st.container():
            st.markdown("### 添加新业务")

            with st.form("add_business_form"):
                business = st.text_input("业务标识", placeholder="例如: SF")
                business_cname = st.text_input("业务名称", placeholder="例如: 神策智能运营")

                col1, col2 = st.columns(2)
                with col1:
                    if st.form_submit_button("确认添加", use_container_width=True):
                        if business and business_cname:
                            # 检查业务是否已存在
                            existing_businesses = asyncio.run(self.prompt_service.get_all_businesses())
                            if any(b[0] == business for b in existing_businesses):
                                st.error("该业务标识已存在！")
                            else:
                                # 创建一个临时prompt来添加业务（因为业务信息存储在prompt表中）
                                temp_prompt = PromptModel(
                                    business=business,
                                    business_cname=business_cname,
                                    prompt_name="默认系统提示",
                                    prompt_desc="系统默认创建的提示模板",
                                    prompt_code="DEFAULT_SYSTEM",
                                    prompt_content="这是一个默认的系统提示模板，请根据需要修改。"
                                )
                                asyncio.run(self.prompt_service.create_prompt(temp_prompt))
                                st.success("业务添加成功！")
                                st.session_state.show_add_business_modal = False
                                st.session_state.refresh_trigger += 1
                                st.rerun()
                        else:
                            st.error("请填写完整信息！")

                with col2:
                    if st.form_submit_button("取消", use_container_width=True):
                        st.session_state.show_add_business_modal = False
                        st.rerun()

    def _render_add_prompt_modal(self):
        """渲染添加prompt模态框"""
        with st.container():
            st.markdown("### 添加新 Prompt")

            with st.form("add_prompt_form"):
                prompt_name = st.text_input("Prompt 名称", placeholder="例如: 系统提示语")
                prompt_code = st.text_input("Prompt 代码", placeholder="例如: SYSTEM_PROMPT")
                prompt_desc = st.text_area("Prompt 描述", placeholder="描述这个prompt的用途...")
                prompt_content = st.text_area("Prompt 内容", placeholder="输入prompt模板内容...", height=200)

                col1, col2 = st.columns(2)
                with col1:
                    if st.form_submit_button("确认添加", use_container_width=True):
                        if prompt_name and prompt_code and prompt_content:
                            # 检查prompt代码是否已存在
                            exists = asyncio.run(
                                self.prompt_service.check_prompt_code_exists(
                                    st.session_state.selected_business,
                                    prompt_code
                                )
                            )
                            if exists:
                                st.error("该 Prompt 代码在当前业务下已存在！")
                            else:
                                # 获取业务名称
                                businesses = asyncio.run(self.prompt_service.get_all_businesses())
                                business_cname = next(
                                    (b[1] for b in businesses if b[0] == st.session_state.selected_business),
                                    ""
                                )

                                new_prompt = PromptModel(
                                    business=st.session_state.selected_business,
                                    business_cname=business_cname,
                                    prompt_name=prompt_name,
                                    prompt_desc=prompt_desc,
                                    prompt_code=prompt_code,
                                    prompt_content=prompt_content
                                )
                                asyncio.run(self.prompt_service.create_prompt(new_prompt))
                                st.success("Prompt 添加成功！")
                                st.session_state.show_add_prompt_modal = False
                                st.session_state.refresh_trigger += 1
                                st.rerun()
                        else:
                            st.error("请填写必要信息（名称、代码、内容）！")

                with col2:
                    if st.form_submit_button("取消", use_container_width=True):
                        st.session_state.show_add_prompt_modal = False
                        st.rerun()

    def _render_edit_prompt_drawer(self):
        """渲染编辑prompt抽屉"""
        if not st.session_state.selected_prompt:
            return

        # 获取选中的prompt
        prompt = asyncio.run(self.prompt_service.get_prompt_by_id(st.session_state.selected_prompt))
        if not prompt:
            st.error("Prompt 不存在！")
            st.session_state.show_edit_drawer = False
            st.rerun()
            return

        with st.container():
            st.markdown("### 编辑 Prompt")

            # 关闭按钮
            if st.button("✖ 关闭", key="close_drawer"):
                st.session_state.show_edit_drawer = False
                st.session_state.selected_prompt = None
                st.rerun()

            st.divider()

            with st.form("edit_prompt_form"):
                prompt_name = st.text_input("Prompt 名称", value=prompt.prompt_name)
                prompt_code = st.text_input("Prompt 代码", value=prompt.prompt_code)
                prompt_desc = st.text_area("Prompt 描述", value=prompt.prompt_desc or "")
                prompt_content = st.text_area("Prompt 内容", value=prompt.prompt_content, height=300)

                col1, col2, col3 = st.columns(3)

                with col1:
                    if st.form_submit_button("保存修改", use_container_width=True):
                        if prompt_name and prompt_code and prompt_content:
                            # 检查prompt代码是否已存在（排除当前prompt）
                            exists = asyncio.run(
                                self.prompt_service.check_prompt_code_exists(
                                    prompt.business,
                                    prompt_code,
                                    exclude_id=prompt.id
                                )
                            )
                            if exists:
                                st.error("该 Prompt 代码在当前业务下已存在！")
                            else:
                                asyncio.run(
                                    self.prompt_service.update_prompt(
                                        prompt.id,
                                        prompt_name=prompt_name,
                                        prompt_code=prompt_code,
                                        prompt_desc=prompt_desc,
                                        prompt_content=prompt_content
                                    )
                                )
                                st.success("Prompt 更新成功！")
                                st.session_state.show_edit_drawer = False
                                st.session_state.selected_prompt = None
                                st.session_state.refresh_trigger += 1
                                st.rerun()
                        else:
                            st.error("请填写必要信息（名称、代码、内容）！")

                with col2:
                    if st.form_submit_button("删除", use_container_width=True, type="secondary"):
                        if st.session_state.get('confirm_delete', False):
                            asyncio.run(self.prompt_service.delete_prompt(prompt.id))
                            st.success("Prompt 删除成功！")
                            st.session_state.show_edit_drawer = False
                            st.session_state.selected_prompt = None
                            st.session_state.confirm_delete = False
                            st.session_state.refresh_trigger += 1
                            st.rerun()
                        else:
                            st.session_state.confirm_delete = True
                            st.warning("再次点击删除按钮确认删除操作")
                            st.rerun()

                with col3:
                    if st.form_submit_button("取消", use_container_width=True):
                        st.session_state.show_edit_drawer = False
                        st.session_state.selected_prompt = None
                        st.session_state.confirm_delete = False
                        st.rerun()


# 页面入口函数
def main():
    """页面主函数"""
    page = PromptManagePage()
    page.run()


main()
