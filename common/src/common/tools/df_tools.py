from typing import List

import pandas as pd


def batch_rows(
        batch_count: int,
        data: pd.DataFrame,
) -> List[pd.DataFrame]:
    if data is None:
        return []
    if batch_count <= 0:
        batch_count = 1
    if data.shape[0] <= batch_count:
        result = []
        count = 0
        for idx, row in data.iterrows():
            result.append(data.loc[count:count, :])
            count += 1
        return result
    batch_size = int((len(data) + batch_count - 1) / batch_count)
    return batch_rows_with_batch_size(batch_size=batch_size, data=data)


def batch_rows_with_batch_size(
        batch_size: int,
        data: pd.DataFrame,
) -> List[pd.DataFrame]:
    if data is None:
        return []
    if batch_size <= 0:
        batch_size = 1
    if data.shape[0] <= batch_size:
        return [data]

    result = []
    for i in range(int((data.shape[0] + batch_size - 1) / batch_size)):
        result.append(data.loc[batch_size * i:min(len(data), batch_size * (i + 1)), :])
    return result
