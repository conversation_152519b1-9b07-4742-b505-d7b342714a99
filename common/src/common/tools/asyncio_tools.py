import asyncio
from collections.abc import Callable
from typing import Any

from .logger import get_logger

log = get_logger()


def asyncio_run(func: Callable[..., Any], **kwargs) -> Any:
    """
    自动适配环境的事件循环调用器：
    - 若当前无运行中的事件循环，启动新循环并运行任务。
    - 若已有事件循环运行，将任务加入当前循环并同步等待结果。
    """

    # 定义内部协程以包装函数调用
    async def _async_wrapper():
        return await func(**kwargs)

    try:
        # 检查是否有正在运行的事件循环
        loop = asyncio.get_running_loop()
    except RuntimeError:
        # 场景1：无运行中的事件循环，启动新循环
        try:
            return asyncio.run(_async_wrapper())
        except:
            raise
    else:
        # 场景2：已有事件循环运行，将任务加入当前循环并同步等待
        try:
            future = asyncio.run_coroutine_threadsafe(_async_wrapper(), loop)
            return future.result()
        except:
            raise
