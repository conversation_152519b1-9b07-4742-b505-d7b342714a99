from typing import List


def split_as_batch(
        batch_count: int,
        data: List,
) -> List[List]:
    if not data:
        return []
    if batch_count <= 0:
        batch_count = 1
    if len(data) <= batch_count:
        return [[_] for _ in data]
    batch_size = int((len(data) + batch_count - 1) / batch_count)
    return split_list_with_batch_size(batch_size=batch_size, data=data)


def split_list_with_batch_size(
        batch_size: int,
        data: List,
) -> List[List]:
    if not data:
        return []
    if batch_size <= 0:
        batch_size = 1
    if len(data) <= batch_size:
        return [data]

    result = []
    for i in range(int((len(data) + batch_size - 1) / batch_size)):
        result.append(data[batch_size * i:min(len(data), batch_size * (i + 1))])
    return result
