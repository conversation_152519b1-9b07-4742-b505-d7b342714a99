import hashlib
import uuid
from typing import List

import nltk
from langchain_text_splitters import NLTKTextSplitter

from .logger import get_logger

log = get_logger()

# Download punkt_tab files
try:
    nltk.find('tokenizers/punkt_tab')
except:
    log.info('Start download nltk punkt_tab resource.')
    nltk.download('punkt_tab')


def sha256(content: str) -> str:
    return hashlib.sha256(content.encode()).hexdigest()


def md5(content: str) -> str:
    return hashlib.md5(content.encode()).hexdigest()


def uuid4_no_underline() -> str:
    return str(uuid.uuid4()).replace('-', '')


def uuid4_no_underline_to_uuid(s: str) -> str:
    """
    uuid4 sample: 00067f1b-2423-4af0-b35d-02bc1cfa18b5
    """
    return f'{s[0:8]}-{s[8:12]}-{s[12:16]}-{s[16:20]}-{s[20:]}'


def split_text(text: str, chunk_size: int = 1024 * 4, chunk_overlap: int = 200) -> List[str]:
    """文本分割"""
    if not text:
        return []

    if chunk_size <= 0:
        return [text]

    text_splitter = NLTKTextSplitter(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        language='english'
    )
    text_units = text_splitter.split_text(text)
    return text_units


def indent_multi_line(indent: int, multi_line_text: str) -> str:
    """对多行文本进行缩进, 空行不做处理"""
    indent_string = indent * ' '
    return "\n".join([indent_string + line if line else line for line in multi_line_text.splitlines()])
