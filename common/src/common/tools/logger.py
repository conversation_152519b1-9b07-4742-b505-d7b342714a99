import logging
from logging import StreamHandler
from typing import Any

import colorlog


class ConsumerStream:
    def __init__(self, max_size=1000):
        self.name = 'consumer_stream'
        self.data = []
        self.max_size = max_size

    def write(self, obj: Any):
        self.data.append(obj)
        if len(self.data) > self.max_size:
            self.data = self.data[-self.max_size:]

    def flush(self):
        pass


class LogConsumerHandler(StreamHandler):
    def __init__(self, stream=ConsumerStream()):
        super().__init__(stream=stream)


def get_logger(name=None, level=logging.INFO):
    if not name:
        return root_logger
    else:
        special_logger = logging.getLogger(name)
        special_logger.setLevel(level)
        return special_logger


def init_root_logger(level=logging.INFO):
    # 创建logger对象
    logger = logging.getLogger()
    logger.setLevel(level)
    # 创建控制台日志处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    # 定义颜色输出格式
    color_formatter = colorlog.ColoredFormatter(
        fmt='%(log_color)s[%(asctime)s] %(pathname)s:%(lineno)d %(levelname)s: %(message)s',
        log_colors={
            'DEBUG': 'cyan',
            'INFO': 'green',
            'WARNING': 'yellow',
            'ERROR': 'red',
            'CRITICAL': 'red,bg_white',
        }
    )
    # 将颜色输出格式添加到控制台日志处理器
    console_handler.setFormatter(color_formatter)
    # 移除默认的handler
    for handler in logger.handlers:
        logger.removeHandler(handler)
    # 将控制台日志处理器添加到logger对象
    logger.addHandler(console_handler)
    return logger


root_logger = init_root_logger()
