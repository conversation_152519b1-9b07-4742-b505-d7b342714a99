from pathlib import Path

from langchain_community.document_loaders import Docx2txtLoader, PyPDFLoader
from langchain_community.document_transformers import MarkdownifyTransformer

import shutil

from .logger import get_logger

log = get_logger()


def convert_file(file: Path, new_file: Path):
    supported_type = ['.txt', '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.csv', '.md']
    file_suffix = file.suffix.lower()
    if file_suffix not in supported_type:
        raise RuntimeError(f'Unsupported file type: {file_suffix}')

    if file_suffix == '.txt' or file_suffix == '.md':
        shutil.move(file, new_file)
    elif file_suffix == '.xls' or file_suffix == '.xlsx':
        convert_excel(file=file, new_file=new_file)
    elif file_suffix == '.csv':
        convert_csv(file=file, new_file=new_file)
    elif file_suffix == '.doc' or file_suffix == '.docx':
        convert_word(file=file, new_file=new_file)
    elif file_suffix == '.pdf':
        convert_pdf(file=file, new_file=new_file)


def convert_document_with_llm(content) -> str:
    from models import ModelsFactory
    llm = ModelsFactory.get_llm()
    prompt = """请将下列文档按照 markdown 格式重新组织：\n***\n"""
    log.info('convert document to markdown with llm.')
    return llm.complete(input={'role': 'system', 'content': f'{prompt}{content}'}).content


def convert_excel(file: Path, new_file: Path):
    import pandas as pd
    data = []
    for i in range(100):
        try:
            sheet_result = pd.read_excel(file, sheet_name=i)
            content = f'## Sheet{i}\n```csv\n_CONTENT_\n```'
            temp_file = str(file.absolute()) + f'.sheet{i}'
            sheet_result.to_csv(temp_file)
            content = content.replace('_CONTENT_', Path(temp_file).read_text(encoding='utf-8'))
            data.append(content)
        except ValueError as e:
            break
        except TypeError as e1:
            log.error('parse sheet error.', e1)
            continue
    new_file.write_text(data='\n'.join(data), encoding='utf-8')


def convert_csv(file: Path, new_file: Path):
    content = '```csv\n_CONTENT_\n```'
    content = content.replace('_CONTENT_', file.read_text(encoding='utf-8'))
    new_file.write_text(data=content, encoding='utf-8')


def convert_word(file: Path, new_file: Path):
    loader = Docx2txtLoader(file)
    docs = loader.load()
    content = docs[0].page_content
    new_file.write_text(data=content, encoding='utf-8')


def convert_pdf(file: Path, new_file: Path):
    loader = PyPDFLoader(file_path=str(file.absolute()), extraction_mode='layout')
    docs = loader.load()
    docs = MarkdownifyTransformer().transform_documents(docs)
    content = docs[0].page_content
    new_file.write_text(data=content, encoding='utf-8')
