[project]
name = "common"
version = "1.0.0"
classifiers = ["Private :: Do Not Upload"]
description = ""
authors = [
    { name = "<EMAIL>" }
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "colorlog>=6.9.0",
    "rich>=14.0.0",
]

[tool.setuptools]
packages = ["common"]

[tool.setuptools.package-dir]
common = "src/common"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
