[project]
name = "web"
version = "1.0.0"
classifiers = ["Private :: Do Not Upload"]
description = ""
authors = [
    { name = "<EMAIL>" }
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "apis",
    "business",
    "protobuf==4.25.5",
    "wsproto==1.2.0",
    "grpcio==1.62.3",
    "grpcio-tools==1.62.3",
    "black==25.1.0",
    "aiohttp>=3.12.2",
    "aiohttp-retry>=2.9.1",
    "pydantic>=2.11.5",
    "python-dateutil>=2.9.0.post0",
    "sse-starlette>=2.3.5",
    "typing-extensions>=4.13.2",
    "urllib3>=2.4.0",
    "uvicorn>=0.34.2",
    "fastapi>=0.115.12",
    "uvloop>=0.21.0",
    "httptools>=0.6.4",
]

[tool.uv.sources]
apis = { workspace = true }
business = { workspace = true }

[tool.setuptools]
packages = ["web"]

[tool.setuptools.package-dir]
web = "src/web"

# 封装系统命令
[project.scripts]
http_server = "web.http_server:start"
rpc_server = "web.rpc_server:start"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
