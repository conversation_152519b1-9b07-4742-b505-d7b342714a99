# 异步 grpc
from concurrent import futures

import grpc

from common import tools
from web.grpc_apis.knowledgebase_grpc import KnowledgeBaseGrpcService
from web.grpc_apis.crawler_grpc import CrawlerGrpcService
from web.grpc_apis.canvas_grpc import CanvasBotGrpcService
from web.grpc_apis.portal_grpc import PortalBotGrpcService
from apis.definition import (
    samples_pb2_grpc, samples_pb2,
    canvas_bot_pb2_grpc,
    knowledge_base_pb2_grpc,
    crawler_pb2_grpc,
)
from apis.definition.portal import portal_bot_pb2_grpc

log = tools.get_logger()


class DemoGrpcService(samples_pb2_grpc.DemoServiceServicer):
    """
    This is a demo service
    """

    async def DemoRequest(self, request, context):
        log.info(f'demo request: {request}')
        return samples_pb2.DemoMessage(
            role=f'assistant from {request.role}',
            content=f'I am an assistant.\n{request.content}'
        )


async def serve():
    server = grpc.aio.server(futures.ThreadPoolExecutor(max_workers=50))

    # add service to grpc server
    samples_pb2_grpc.add_DemoServiceServicer_to_server(DemoGrpcService(), server)
    canvas_bot_pb2_grpc.add_CanvasBotServiceServicer_to_server(CanvasBotGrpcService(), server)
    knowledge_base_pb2_grpc.add_KnowledgeBaseServiceServicer_to_server(KnowledgeBaseGrpcService(), server)
    crawler_pb2_grpc.add_CrawlerServiceServicer_to_server(CrawlerGrpcService(), server)
    portal_bot_pb2_grpc.add_PortalBotServiceServicer_to_server(PortalBotGrpcService(), server)

    server.add_insecure_port('[::]:8007')

    await server.start()
    log.info(f'Start GRPC Server on port 8007')
    await server.wait_for_termination()


def start():
    tools.asyncio_run(serve)
