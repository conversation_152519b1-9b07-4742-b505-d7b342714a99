from contextlib import asynccontextmanager
from fastapi import FastAPI
from starlette.requests import Request
from common import tools
from core.db.pgsql_session import auto_create_tables
from web.http_apis.routers.http_context import HttpContext
from .routers import focus_routers, portal_routers

log = tools.get_logger()


@asynccontextmanager
async def app_lifespan(app: FastAPI):
    # do something here on server startup
    log.info('Starting up...')
    await auto_create_tables()
    yield
    # do something here on server shutdown
    log.info("Shutting down...")


app = FastAPI(
    docs_url="/sensors-ai-docs",
    openapi_url="/sensors-ai-docs.json",
    redoc_url=None,
    lifespan=app_lifespan
)


@app.middleware("http")
async def http_context(request: Request, call_next):
    context = HttpContext()
    request.state.grpc_context = context
    try:
        response = await call_next(request)
    except BaseException as e:
        context.status = 'failed'
        raise e
    context.status = 'finished'
    return response


# add routers
app.include_router(focus_routers.api_router, prefix="/api/v3/focus/v1/ai")
app.include_router(portal_routers.api_router, prefix="/api/v3/focus/v1/ai")
