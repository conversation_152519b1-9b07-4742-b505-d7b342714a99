from typing import Type
from starlette.requests import Request
from google.protobuf.message import Message
from google.protobuf.json_format import ParseDict, ParseError
import json
from typing import AsyncGenerator
from google.protobuf.json_format import MessageToDict


async def convert_request_to_proto(
        request: Request,
        proto_class: Type[Message]
) -> Message:
    """
    将 Starlette 的 Request 对象转换为 Protobuf 消息类

    Args:
        request: Starlette 的 Request 对象
        proto_class: 目标 Protobuf 消息类（如 HttpRequest）

    Returns:
        填充后的 Protobuf 消息实例
    """
    try:
        body = await request.json()
        proto_instance = ParseDict(body, proto_class(), ignore_unknown_fields=True)
        return proto_instance
    except (ParseError, AttributeError, ValueError) as e:
        raise RuntimeError(f"字段映射失败: {e}") from e


async def convert_as_stream(request: Request, generator: AsyncGenerator):
    async for item in generator:
        if await request.is_disconnected():
            break
        item = MessageToDict(item)
        yield json.dumps(item, ensure_ascii=False)
