from fastapi import APIRouter
from google.protobuf.json_format import MessageToDict
from sse_starlette import EventSourceResponse
from starlette.requests import Request

from common import tools

from apis.definition.portal import portal_bot_pb2
from web.grpc_apis.canvas_grpc import CanvasBotGrpcService
from web.grpc_apis.crawler_grpc import CrawlerGrpcService
from web.grpc_apis.knowledgebase_grpc import KnowledgeBaseGrpcService
from web.grpc_apis.portal_grpc import PortalBotGrpcService

from .http_utils import convert_request_to_proto, convert_as_stream

log = tools.get_logger()

api_router = APIRouter()

canvas_bot_service = CanvasBotGrpcService()
knowledgebase_service = KnowledgeBaseGrpcService()
crawler_service = CrawlerGrpcService()
portal_bot_service = PortalBotGrpcService()


# ----------------------------------
# Portal 相关接口
# ----------------------------------
@api_router.post("/portal/idea/retrieval")
async def portal_idea_retrieval(request: Request):
    proto_request = await convert_request_to_proto(request, portal_bot_pb2.RetrievalIdeasRequest)
    response = await portal_bot_service.RetrievalIdeas(
        request=proto_request,
        context=request.state.grpc_context
    )
    return MessageToDict(response, preserving_proto_field_name=True)


@api_router.post("/portal/chat")
async def portal_chat(request: Request):
    proto_request = await convert_request_to_proto(request, portal_bot_pb2.GeneralPortalRequest)

    return EventSourceResponse(
        convert_as_stream(
            request=request,
            generator=portal_bot_service.Chat(request=proto_request, context=request.state.grpc_context)
        ),
        media_type="text/event-stream"
    )
