from typing import Union
from grpc import ServicerContext


class HttpContext(ServicerContext):
    """自定义从 http 调用的 context"""

    def __init__(self):
        self.status: Union['connected', 'finished', 'failed'] = 'connected'

    def is_active(self):
        return self.status == 'connected'

    def time_remaining(self):
        return 60.0 * 10

    def cancel(self):
        pass

    def add_callback(self, callback):
        pass

    def invocation_metadata(self):
        pass

    def peer(self):
        pass

    def peer_identities(self):
        pass

    def peer_identity_key(self):
        pass

    def auth_context(self):
        pass

    def send_initial_metadata(self, initial_metadata):
        pass

    def set_trailing_metadata(self, trailing_metadata):
        pass

    def abort(self, code, details):
        pass

    def abort_with_status(self, status):
        pass

    def set_code(self, code):
        pass

    def set_details(self, details):
        pass
