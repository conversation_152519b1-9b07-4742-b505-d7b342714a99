from fastapi import APIRouter
from sse_starlette import EventSourceResponse
from starlette.requests import Request
from common import tools
from google.protobuf.json_format import MessageToDict
from apis.definition import (
    canvas_bot_pb2, knowledge_base_pb2, crawler_pb2
)
from web.grpc_apis.canvas_grpc import CanvasBotGrpcService
from web.grpc_apis.crawler_grpc import CrawlerGrpcService
from web.grpc_apis.knowledgebase_grpc import KnowledgeBaseGrpcService
from web.grpc_apis.portal_grpc import PortalBotGrpcService

from .http_utils import convert_request_to_proto, convert_as_stream

log = tools.get_logger()

api_router = APIRouter()

canvas_bot_service = CanvasBotGrpcService()
knowledgebase_service = KnowledgeBaseGrpcService()
crawler_service = CrawlerGrpcService()
portal_bot_service = PortalBotGrpcService()


# ----------------------------------
# 画布相关接口
# ----------------------------------

@api_router.post("/canvas/dispatch")
async def dispatch(request: Request):
    log.info(f'dispatch request received.')
    proto_request = await convert_request_to_proto(request, canvas_bot_pb2.GeneralCanvasRequest)
    return EventSourceResponse(
        convert_as_stream(
            request=request,
            generator=canvas_bot_service.Dispatch(request=proto_request, context=request.state.grpc_context)
        ),
        media_type="text/event-stream"
    )


@api_router.post("/canvas/design")
async def design_canvas(request: Request):
    log.info(f'design canvas request received.')
    proto_request = await convert_request_to_proto(request, canvas_bot_pb2.GeneralCanvasRequest)
    return EventSourceResponse(
        convert_as_stream(
            request=request,
            generator=canvas_bot_service.DesignCanvas(request=proto_request, context=request.state.grpc_context)
        ),
        media_type="text/event-stream"
    )


@api_router.post("/canvas/adjust")
async def adjust_canvas(request: Request):
    proto_request = await convert_request_to_proto(request, canvas_bot_pb2.GeneralCanvasRequest)
    return EventSourceResponse(
        convert_as_stream(
            request=request,
            generator=canvas_bot_service.AdjustCanvas(request=proto_request, context=request.state.grpc_context)
        ),
        media_type="text/event-stream"
    )


@api_router.post("/canvas/generate")
async def generate_canvas(request: Request):
    proto_request = await convert_request_to_proto(request, canvas_bot_pb2.GeneralCanvasRequest)
    return EventSourceResponse(
        convert_as_stream(
            request=request,
            generator=canvas_bot_service.GenerateCanvas(request=proto_request, context=request.state.grpc_context)
        ),
        media_type="text/event-stream"
    )


@api_router.post("/canvas/optimize")
async def optimize_canvas(request: Request):
    proto_request = await convert_request_to_proto(request, canvas_bot_pb2.GeneralCanvasRequest)
    return EventSourceResponse(
        convert_as_stream(
            request=request,
            generator=canvas_bot_service.OptimizeCanvas(request=proto_request, context=request.state.grpc_context)
        ),
        media_type="text/event-stream"
    )


@api_router.post("/canvas/insight")
async def insight_canvas(request: Request):
    log.info(f'insight canvas request received.')
    proto_request = await convert_request_to_proto(request, canvas_bot_pb2.GeneralCanvasRequest)
    return EventSourceResponse(
        convert_as_stream(
            request=request,
            generator=canvas_bot_service.InsightCanvas(request=proto_request, context=request.state.grpc_context)
        ),
        media_type="text/event-stream"
    )


@api_router.post("/canvas/retrieve")
async def retrieve_canvas(request: Request):
    log.info(f'retrieve canvas request received.')
    proto_request = await convert_request_to_proto(request, canvas_bot_pb2.GeneralCanvasRequest)
    return EventSourceResponse(
        convert_as_stream(
            request=request,
            generator=canvas_bot_service.RetrieveCanvas(request=proto_request, context=request.state.grpc_context)
        ),
        media_type="text/event-stream"
    )


@api_router.post("/canvas/predict")
async def predict_canvas(request: Request):
    proto_request = await convert_request_to_proto(request, canvas_bot_pb2.GeneralCanvasRequest)
    return EventSourceResponse(
        convert_as_stream(
            request=request,
            generator=canvas_bot_service.PredictCanvas(request=proto_request, context=request.state.grpc_context)
        ),
        media_type="text/event-stream"
    )


# ----------------------------------
# 知识库相关接口
# ----------------------------------

@api_router.post("/knowledgebase/build")
async def build_knowledgebase(request: Request):
    proto_request = await convert_request_to_proto(request, knowledge_base_pb2.BuildKnowledgeBaseRequest)
    response = await knowledgebase_service.BuildKnowledgeBase(request=proto_request, context=request.state.grpc_context)
    return MessageToDict(response)


@api_router.post("/knowledgebase/update")
async def update_knowledgebase(request: Request):
    proto_request = await convert_request_to_proto(request, knowledge_base_pb2.BuildKnowledgeBaseRequest)
    response = await knowledgebase_service.UpdateKnowledgeBase(request=proto_request,
                                                               context=request.state.grpc_context)
    return MessageToDict(response)


@api_router.post("/knowledgebase/delete")
async def delete_knowledgebase(request: Request):
    proto_request = await convert_request_to_proto(request, knowledge_base_pb2.DeleteKnowledgeBaseRequest)
    response = await knowledgebase_service.DeleteKnowledgeBase(request=proto_request,
                                                               context=request.state.grpc_context)
    return MessageToDict(response)


@api_router.post("/knowledgebase/query")
async def query_knowledgebase(request: Request):
    proto_request = await convert_request_to_proto(request, knowledge_base_pb2.QueryKnowledgeBaseRequest)
    response = await knowledgebase_service.QueryKnowledgeBaseStatus(request=proto_request,
                                                                    context=request.state.grpc_context)
    return MessageToDict(response)


@api_router.post("/knowledgebase/upload")
async def knowledgebase_upload(request: Request):
    proto_request = await convert_request_to_proto(request, knowledge_base_pb2.UploadKnowledgeBaseFileRequest)

    # 暂时只上传一个 chunk
    async def request_stream():
        yield proto_request

    response = await knowledgebase_service.UploadKnowledgeBaseFile(request_iterator=request_stream(),
                                                                   context=request.state.grpc_context)
    return MessageToDict(response)


@api_router.post("/knowledgebase/resource/add")
async def add_knowledgebase_resource(request: Request):
    proto_request = await convert_request_to_proto(request, knowledge_base_pb2.OperateKnowledgeBaseResourceRequest)
    response = await knowledgebase_service.AddKnowledgeBaseResource(request=proto_request,
                                                                    context=request.state.grpc_context)
    return MessageToDict(response)


@api_router.post("/knowledgebase/resource/delete")
async def delete_knowledgebase_resource(request: Request):
    proto_request = await convert_request_to_proto(request, knowledge_base_pb2.OperateKnowledgeBaseResourceRequest)
    response = await knowledgebase_service.DeleteKnowledgeBaseResource(request=proto_request,
                                                                       context=request.state.grpc_context)
    return MessageToDict(response)


# ----------------------------------
# 爬虫相关接口
# ----------------------------------
@api_router.post("/crawler/analyze_urls")
async def analyze_urls(request: Request):
    proto_request = await convert_request_to_proto(request, crawler_pb2.AnalyzeUrlRequest)
    response = await  crawler_service.analyzeUrls(request=proto_request, context=request.state.grpc_context)
    return MessageToDict(response)


@api_router.post("/crawler/crawl_urls")
async def crawl_urls(request: Request):
    proto_request = await convert_request_to_proto(request, crawler_pb2.CrawlUrlRequest)
    response = await  crawler_service.crawlUrls(request=proto_request, context=request.state.grpc_context)
    return MessageToDict(response)
