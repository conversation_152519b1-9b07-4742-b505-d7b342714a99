import asyncio
import uvicorn


def config_app(port: int = 8005):
    config = uvicorn.Config(
        "web.http_apis.server:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        backlog=4096,
        loop="none"
    )
    return config


async def main():
    config = config_app()
    server = uvicorn.Server(config)
    await server.serve()


def start():
    import uvloop

    uvloop.install()
    asyncio.run(main())


if __name__ == '__main__':
    start()
