import asyncio
from typing import Any, Type

from google.protobuf import json_format
from framework.bot import <PERSON><PERSON>
from business.focus.bots.canvas import (
    CanvasCreateBot,
    CanvasDesignBot,
    CanvasDispatchBot,
    CanvasInsightBot,
    CanvasOptimizeBot,
    CanvasPredictBot,
    CanvasRetrieveBot
)
from framework.agents.schema import (
    Conversation, Role
)
from business.focus.tenant import ProjectInfo, CdpInfo
from apis.definition import canvas_bot_pb2_grpc
from apis.definition.canvas_bot_pb2 import ChatSessionCanvasMessage
from apis.definition.canvas_draft_pb2 import CanvasDraft
from apis.definition.chat_session_pb2 import ChatSessionInfo
from common import tools
from core.service.tenant_project_service import TenantProjectService

from .utils.stream_utils import output_simple_stream

log = tools.get_logger()
tenant_project_service = TenantProjectService()


def _convert_message(message: ChatSessionCanvasMessage) -> Conversation:
    role = Role.USER
    if message.generator == ChatSessionCanvasMessage.MessageGenerator.SYSTEM:
        role = Role.SYSTEM
    elif message.generator == ChatSessionCanvasMessage.MessageGenerator.BOT:
        role = Role.ASSISTANT
    content = message.text or ''
    if message.type == ChatSessionCanvasMessage.MessageType.DESIGN:
        design = message.design
        if design:
            design = '\n'.join(
                [json_format.MessageToJson(d, preserving_proto_field_name=True, ensure_ascii=False) for d in design])
        else:
            design = 'No design'
        content = f"{content}\n\n画布设计：\n```\n{design}\n```"
    elif message.type == ChatSessionCanvasMessage.MessageType.SUGGEST:
        content = f"{content}\n\n策略建议：\n```\n{json_format.MessageToJson(message.suggest, preserving_proto_field_name=True, ensure_ascii=False)}\n```"
    elif message.type == ChatSessionCanvasMessage.MessageType.CANVAS:
        content = f"{content}\n\n策略详情：\n```\n{json_format.MessageToJson(message.canvas, preserving_proto_field_name=True, ensure_ascii=False)}\n```"

    return Conversation(role=role, content=content)


async def _supplement_chat_session_info(pb_chat_session_info: ChatSessionInfo):
    chat_session_info = json_format.MessageToDict(pb_chat_session_info, preserving_proto_field_name=True)
    organization_id = pb_chat_session_info.organization_id
    project_id = pb_chat_session_info.project_id
    tenant_project = await tenant_project_service.get_tenant_project(
        organization_id=organization_id,
        project_id=project_id,
    )
    if not tenant_project:
        raise Exception('项目不存在')

    chat_session_info = chat_session_info | tenant_project.dict()

    from business.context import request_project_context, request_cdp_context
    request_cdp_context.set(CdpInfo(
        cdp_host=chat_session_info['cdp_host'],
        api_key=chat_session_info['api_key']
    ))
    request_project_context.set(ProjectInfo(
        org_id=chat_session_info['organization_id'],
        project_name=chat_session_info['project_name'],
        project_id=chat_session_info['project_id']
    ))
    if "chat_session_name" not in chat_session_info:
        chat_session_info["chat_session_name"] = ''
    return chat_session_info


class CanvasBotGrpcService(canvas_bot_pb2_grpc.CanvasBotServiceServicer):

    async def _handle_request(self, request, bot_class: Type[Bot], bot_name: str, **kwargs):
        """
        处理 gRPC 请求的通用方法。

        :param request: gRPC 请求对象
        :param bot_class: 要使用的机器人类
        :param bot_name: 机器人的名称
        :return: 流式响应数据
        """
        log.info(f'{bot_name} service: {request}')
        _queue = asyncio.Queue()

        async def _put_queue(obj: Any):
            if obj is not None:
                await _queue.put(obj)

        knowledge_base_infos = request.knowledge_base_infos
        try:
            if knowledge_base_infos:
                new_knowledge_base_infos = []
                for info in knowledge_base_infos:
                    new_knowledge_base_infos.append(
                        json_format.MessageToDict(
                            info,
                            including_default_value_fields=True,
                            preserving_proto_field_name=True
                        )
                    )
                knowledge_base_infos = new_knowledge_base_infos
            else:
                knowledge_base_infos = []
        except Exception as e:
            log.error("get knowledgebase infos error", e)
            knowledge_base_infos = []

        chat_session_info = await _supplement_chat_session_info(request.chat_session_info)
        log.info(f'got chat session info: {chat_session_info}')
        bot = bot_class(
            name=bot_name,
            stream=True,
            chat_session_info=chat_session_info,
            knowledge_base_infos=knowledge_base_infos,
            callback=_put_queue,
            **kwargs
        )
        history_messages = []
        try:
            for message in request.messages:
                if message:
                    history_messages.append(_convert_message(message))
        except BaseException as e:
            log.error(f'convert chat history error', e)
            history_messages = []

        log.info(f'---- start stream chat ----')
        async for chunk in output_simple_stream(
                chat_session_id=chat_session_info.get('chat_session_id', None),
                bot=bot,
                history=history_messages,
                message_queue=_queue
        ):
            yield chunk

    async def Dispatch(self, request, context):
        """ 识别意图 & 分发服务 """
        async for chunk in self._handle_request(request, CanvasDispatchBot, 'canvas_dispatch_bot'):
            yield chunk

    async def DesignCanvas(self, request, context):
        """画布设计"""
        async for chunk in self._handle_request(request, CanvasDesignBot, 'canvas_design_bot'):
            yield chunk

    async def AdjustCanvas(self, request, context):
        """ 调整画布 """
        message = request.messages[-1]
        prompt = f"{message.text}，请按照以下要求调整画布结构（component_id 表示要调整的组件id）：\n\n"
        message.text = prompt
        message.type = ChatSessionCanvasMessage.MessageType.SUGGEST
        async for chunk in self.DesignCanvas(request, context):
            yield chunk

    async def GenerateCanvas(self, request, context):
        """ 生成画布 """
        async for chunk in self._handle_request(
                request,
                CanvasCreateBot,
                bot_name='canvas_create_bot',
                canvas_draft_type=CanvasDraft
        ):
            yield chunk

    async def OptimizeCanvas(self, request, context):
        """ 优化画布 """
        async for chunk in self._handle_request(
                request,
                CanvasOptimizeBot,
                bot_name='canvas_optimize_bot'
        ):
            yield chunk

    async def InsightCanvas(self, request, context):
        """ 数据洞察 """
        async for chunk in self._handle_request(
                request,
                CanvasInsightBot,
                bot_name='canvas_insight_bot'
        ):
            yield chunk

    async def RetrieveCanvas(self, request, context):
        """ 画布检索 """
        async for chunk in self._handle_request(
                request,
                CanvasRetrieveBot,
                bot_name='canvas_retrieve_bot'
        ):
            yield chunk

    async def PredictCanvas(self, request, context):
        """ 效果预测 """
        async for chunk in self._handle_request(
                request,
                CanvasPredictBot,
                bot_name='canvas_predict_bot'
        ):
            yield chunk
