import asyncio
from urllib.robotparser import RobotFileParser

from apis.definition import crawler_pb2_grpc, crawler_pb2
from common import tools
from crawlers.base import get_web_driver

log = tools.get_logger()


async def _is_robots_allowed(url: str) -> bool:
    # 检测 robots.txt
    try:
        basic_host = url
        if url.startswith('http://'):
            basic_host = f"http://{url.split('/')[2]}"
        elif url.startswith('https://'):
            basic_host = f"https://{url.split('/')[2]}"

        basic_host = basic_host.strip().split(r'?')[0]
        rp = RobotFileParser()
        rp.set_url(f"{basic_host}/robots.txt")
        rp.read()
        user_agent = "Sensorsbot"
        return rp.can_fetch(user_agent, url)
    except:
        # 默认允许爬取
        return True


async def _get_page_title(url: str) -> str | None:
    """使用爬虫获取网页标题"""
    browser = get_web_driver()
    try:
        browser.get(url)
        await asyncio.sleep(3)
        return browser.title
    except BaseException as e:
        log.error(f'wait selector failed.', e)
        return ""
    finally:
        browser.quit()


class CrawlerGrpcService(crawler_pb2_grpc.CrawlerServiceServicer):

    async def analyzeUrls(self, request, context):
        """{_summary_} 识别网页地址"""
        analyzed_urls = []
        for url in request.urls:
            if not await _is_robots_allowed(url):
                analyzed_urls.append(crawler_pb2.AnalyzeUrlResponse.AnalyzeUrl(
                    url=url,
                    name='',
                    valid=False
                ))
                continue
            # 获取网页 title
            analyzed_urls.append(crawler_pb2.AnalyzeUrlResponse.AnalyzeUrl(
                url=url,
                name=await _get_page_title(url),
                valid=True
            ))
        return crawler_pb2.AnalyzeUrlResponse(urls=analyzed_urls)

    async def crawlUrls(self, request, context):
        """{_summary_} 触发爬取网页 (用于"不主动更新"的网页)"""
        crawled_urls = []
        for crawl_url in request.urls:
            crawled_urls.append(crawler_pb2.CrawlUrl(
                url=crawl_url.url,
                crawl_type=crawl_url.crawl_type,
                crawl_status=crawler_pb2.CrawlUrl.CrawlStatus.READY
            ))
        return crawler_pb2.CrawlUrlResponse(urls=crawled_urls)
