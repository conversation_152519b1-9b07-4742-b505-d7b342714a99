from pathlib import Path
from typing import List

from apis.definition import knowledge_base_pb2, knowledge_base_pb2_grpc
from business.knowledge_factory import KnowledgeFactory
from business.scenario.customer_rag_client import CustomerRagClient
from framework.knowledge import KnowledgeBase
from common import tools

log = tools.get_logger()

CDP_HOST = 'http://************:8107'
API_KEY = '#K-Aje0hQkGiK3kMeiRCD8P2r9eMa9CauOD'


def _create_customer_knowledge_base(
        project_info: knowledge_base_pb2.TenantProjectInfo,
        kb_infos: List[knowledge_base_pb2.KnowledgeBaseInfo],
) -> CustomerRagClient:
    if not kb_infos:
        kb_infos = []
    customer_kbs = dict(
        (kb.knowledge_base_id, f'{kb.remark}，应用场景：{kb.scenario}')
        for kb in kb_infos
        if kb.type == knowledge_base_pb2.KnowledgeBaseInfo.KnowledgeBaseType.CUSTOM
    )
    customer_knowledgebase = KnowledgeFactory.get_knowledge_base_client(
        knowledge_base=KnowledgeBase.CUSTOM,
        customer=project_info.organization_id,
        cdp_host=CDP_HOST,  # 暂时写死
        project_name=project_info.project_name,
        project_id=project_info.project_id,
        api_key=API_KEY,
        custom_knowledge=customer_kbs,
    )
    return customer_knowledgebase


class KnowledgeBaseGrpcService(knowledge_base_pb2_grpc.KnowledgeBaseServiceServicer):

    async def BuildKnowledgeBase(self, request, context):
        customer_knowledgebase = _create_customer_knowledge_base(
            project_info=request.project_info,
            kb_infos=request.base_infos,
        )

        async def _run():
            await customer_knowledgebase.reload_strategy_data()
            await customer_knowledgebase.reload_customer_metadata()
            await customer_knowledgebase.build_index(index_mode='reindex')

        # 需异步执行
        # tools.asyncio_run(_run)
        for kb in request.base_infos:
            kb.status = knowledge_base_pb2.KnowledgeBaseStatus.READY
        return knowledge_base_pb2.KnowledgeBaseResponse(base_infos=request.base_infos)

    async def UpdateKnowledgeBase(self, request, context):
        customer_knowledgebase = _create_customer_knowledge_base(
            project_info=request.project_info,
            kb_infos=request.base_infos,
        )

        async def _run():
            await customer_knowledgebase.reload_strategy_data()
            await customer_knowledgebase.reload_customer_metadata()
            await customer_knowledgebase.build_index(index_mode='update')

        # 需异步执行
        # tools.asyncio_run(_run)
        for kb in request.base_infos:
            kb.status = knowledge_base_pb2.KnowledgeBaseStatus.READY
        return knowledge_base_pb2.KnowledgeBaseResponse(base_infos=request.base_infos)

    async def UploadKnowledgeBaseFile(self, request_iterator, context):
        first_chunk = await anext(request_iterator)
        project_info = first_chunk.project_info
        kb_info = first_chunk.base_info
        filename = first_chunk.filename
        file = '/tmp/' + filename
        new_file = '/tmp/' + tools.uuid4_no_underline() + '.txt'
        with open(file, 'wb') as f:
            f.write(first_chunk.content)
            # 处理剩余的chunks
            async for chunk in request_iterator:
                f.write(chunk.content)

        customer_knowledgebase = _create_customer_knowledge_base(
            project_info=project_info,
            kb_infos=[kb_info],
        )
        sub_kb = customer_knowledgebase.custom_rag_clients[0]
        # 文件转换
        tools.convert_file(file=Path(file), new_file=Path(new_file))
        sub_kb.add_input_file(input_file=Path(new_file))
        await sub_kb.build_index(index_mode='update')
        kb_info.status = knowledge_base_pb2.KnowledgeBaseStatus.READY
        return knowledge_base_pb2.KnowledgeBaseResponse(base_infos=[kb_info])

    async def AddKnowledgeBaseResource(self, request, context):
        log.info(f'AddKnowledgeBaseResource: {request}')
        request.base_info.status = knowledge_base_pb2.KnowledgeBaseStatus.READY
        return knowledge_base_pb2.KnowledgeBaseResponse(base_infos=[request.base_info])

    async def DeleteKnowledgeBaseResource(self, request, context):
        log.info(f'DeleteKnowledgeBaseResource: {request}')
        request.base_info.status = knowledge_base_pb2.KnowledgeBaseStatus.READY
        return knowledge_base_pb2.KnowledgeBaseResponse(base_infos=[request.base_info])

    async def DeleteKnowledgeBase(self, request, context):
        raise RuntimeError('Not supported')

    async def QueryKnowledgeBaseStatus(self, request, context):
        log.info(f'QueryKnowledgeBaseStatus: {request}')
        for kb in request.base_infos:
            kb.status = knowledge_base_pb2.KnowledgeBaseStatus.READY
        return knowledge_base_pb2.KnowledgeBaseResponse(base_infos=request.base_infos)
