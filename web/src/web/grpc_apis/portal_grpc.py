import asyncio
import json
from typing import Any

from google.protobuf.json_format import MessageToDict, ParseDict

from apis.definition.portal import portal_bot_pb2, portal_bot_pb2_grpc
from apis.definition.portal.portal_bot_pb2 import RetrievalIdeasResponse, RetrievalIdeasRequest
from business.portal.portal_chat_bot import PortalChatBot
from business.portal.portal_ideas_retrieval_bot import PortalIdeasRetrievalBot
from common import tools
from framework.agents.schema import Conversation, Role
from .utils.stream_utils import output_simple_stream

log = tools.get_logger()


def _convert_insight_params_as_prompt(insight_params: portal_bot_pb2.InsightParams) -> str:
    if insight_params is None:
        return ""
    params_obj = MessageToDict(
        insight_params,
        including_default_value_fields=True,
        preserving_proto_field_name=True,
        use_integers_for_enums=False,
    )
    details = [
        f"统计开始时间: {params_obj.get('start_time')}",
        f"统计结束时间: {params_obj.get('end_time')}",
        f"分析层级: {params_obj.get('level')}",
        f"场景 id: {params_obj.get('scene_id')}",
        f"子场景 id: {params_obj.get('sub_scene_id')}",
        f"业务目标 id: {params_obj.get('business_goal_id')}",
        f"业务目标值: {params_obj.get('business_goal_value')}",
        f"策略用例 id: {params_obj.get('campaign_case_id')}",
        f"当前层级包含的所有策略列表: {params_obj.get('strategies')}"
    ]
    return "\n".join(details)


def convert_message_to_conversation(message: portal_bot_pb2.PortalMessage) -> Conversation | None:
    result = Conversation(role=Role.USER, content='')
    if message.generator != portal_bot_pb2.PortalMessage.MessageGenerator.USER:
        result.role = Role.ASSISTANT

    content = ""
    if message.type == portal_bot_pb2.PortalMessage.MessageType.INSIGHT_STRATEGY:
        insight_params_content = _convert_insight_params_as_prompt(message.insight_strategy)
        if insight_params_content:
            content = f"**策略洞察相关参数：**\n{insight_params_content}\n***\n{content}"
    elif message.type == portal_bot_pb2.PortalMessage.MessageType.INSIGHT_GOAL:
        insight_params_content = _convert_insight_params_as_prompt(message.insight_goal)
        if insight_params_content:
            content = f"**目标洞察相关参数：**\n{insight_params_content}\n***\n{content}"

    if message.analyse_idea:
        content = f"**执行计划（如 <execute-plain></execute-plain> 中所示）：**\n<execute-plain>\n{message.analyse_idea}\n</execute-plain>\n***\n{content}"
    if message.text:
        content = f"{content}\n***\n{message.text}"

    if not content.strip():
        content = "请继续回复"

    log.info("*" * 50 + f"\nportal ask input={content}\n" + "*" * 50)
    result.content = content
    return result


class PortalBotGrpcService(portal_bot_pb2_grpc.PortalBotServiceServicer):

    async def RetrievalIdeas(self, request, context):
        chat_session_info = MessageToDict(request.chat_session_info, preserving_proto_field_name=True)
        query = request.query
        limit = request.limit
        scenario = 'data_analyse'
        if request.scenario == RetrievalIdeasRequest.Scenario.INSIGHT_GOAL:
            scenario = 'insight_goal'
        elif request.scenario == RetrievalIdeasRequest.Scenario.INSIGHT_STRATEGY:
            scenario = 'insight_strategy'
        bot = PortalIdeasRetrievalBot(
            knowledge_base_infos=None,
            chat_session_info=chat_session_info,
            result_limit=limit,
            scenario=scenario,
            stream=False,
        )
        result = await bot.async_chat(history=[Conversation(role=Role.USER, content=query)])
        ideas = json.loads(result.content)

        log.info(f"portal bot retrieval ideas: {ideas}")

        return RetrievalIdeasResponse(
            ideas=[
                ParseDict(idea, portal_bot_pb2.RetrievalIdea(), ignore_unknown_fields=True)
                for idea in ideas
            ]
        )

    async def Chat(self, request, context):
        chat_session_info = MessageToDict(request.chat_session_info, preserving_proto_field_name=True)
        history: list[Conversation] = []

        for message in request.messages:
            history.append(convert_message_to_conversation(message))

        last_message = request.messages[-1]

        log.info(f'portal chat service: {request}')
        _queue = asyncio.Queue()

        async def _put_queue(obj: Any):
            if obj is not None:
                await _queue.put(obj)

        bot = PortalChatBot(
            knowledge_base_infos=None,
            chat_session_info=chat_session_info,
            insight_strategy=last_message.insight_strategy,
            insight_goal=last_message.insight_goal,
            capability=last_message.capability,
            stream=True,
            callback=_put_queue,
        )
        async for chunk in output_simple_stream(
                chat_session_id=chat_session_info.get('chat_session_id', None),
                bot=bot,
                history=history,
                message_queue=_queue,
                show_thoughts=False,
        ):
            yield chunk
