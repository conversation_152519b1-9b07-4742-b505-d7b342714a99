import asyncio
import time
import uuid
from typing import <PERSON><PERSON><PERSON><PERSON>, Union

from apis.definition.stream_event_pb2 import StreamEvent
from common import tools
from framework.agents.assistant import AssistantAgent
from framework.agents.schema import Conversation, ViewMessageStatus, ViewMessage
from framework.bot import Bo<PERSON>
from framework.models import ModelsFactory
from web.grpc_apis.prompts.chat_session import GENERATE_CHAT_SESSION_NAME, CHAT_SESSION_NAME_CONTEXT

log = tools.get_logger()

terminate_token = "__SENSORS_TERMINATE__"


async def generate_chat_session_name(history: list[Conversation]):
    try:
        agent = AssistantAgent(
            llm=ModelsFactory.get_llm(),
            system_prompt=GENERATE_CHAT_SESSION_NAME
        )

        trimmed_history = history[-10:] if len(history) > 10 else history
        contents = '\n'.join([f"{h.role.name}: {h.content}" for h in trimmed_history])
        user_prompt = CHAT_SESSION_NAME_CONTEXT.format(contents=contents)
        response = await agent.achat_and_save(prompt=user_prompt)
        log.info(f'generate chat session name: {response}')
        return response.strip()
    except Exception as e:
        log.error(f'generate chat session name error: {e}')
        return ''


async def _generator(_queue: asyncio.Queue, max_wait_second: int = 60 * 60):
    start_time = time.time()
    non_times = 1
    while True:
        try:
            if time.time() - start_time > max_wait_second:
                break
            try:
                msg = await _queue.get()
                non_times = 1
            except asyncio.QueueEmpty | asyncio.TimeoutError:
                if non_times > 10:
                    break
                await asyncio.sleep(0.2 * non_times)
                non_times += 1
                continue

            if msg == terminate_token:
                break
            if not isinstance(msg, ViewMessage) and not isinstance(msg, ViewMessageStatus):
                log.warning(f'queue get msg and will not output: {msg}')
            else:
                yield msg
        except Exception as e:
            log.error(f'generator error: {e}')
            break


MessageTypes: TypeAlias = Union[
    ViewMessage,
    ViewMessageStatus,
    str,
    None
]


def _convert_stream_event(message: MessageTypes) -> StreamEvent:
    if message is None:
        return StreamEvent()
    if isinstance(message, ViewMessageStatus):
        return StreamEvent(
            type=StreamEvent.EventType.STATUS,
            status=StreamEvent.StatusData(
                status=message.status,
                text=message.text,
                keeping=message.keeping
            )
        )

    if not isinstance(message, ViewMessage):
        log.warning(f'message is not ViewMessage: {message}')
        return StreamEvent()

    return StreamEvent(
        type=StreamEvent.EventType.MESSAGE,
        message=StreamEvent.MessageData(
            type=message.type,
            content=message.content
        )
    )


async def output_simple_stream(
        bot: Bot,
        history: list[Conversation],
        message_queue: asyncio.Queue,
        chat_session_id: str | None = None,
        chat_session_name: str | None = None,
        show_thoughts: bool = True
):
    if not chat_session_id:
        chat_session_id = str(uuid.uuid4())
    if not chat_session_name:
        chat_session_name = await generate_chat_session_name(history)

    log.info(">>>>>> begin to output event stream <<<<<<")
    yield StreamEvent(
        type=StreamEvent.EventType.MESSAGE_START,
        message_start=StreamEvent.MessageStartData(
            chat_session_id=chat_session_id,
            chat_session_name=chat_session_name
        )
    )

    async def _chat_task():
        try:
            await bot.async_chat(history)
        except BaseException as e:
            message_queue.put_nowait(ViewMessageStatus(status='ERROR', text=str(e)))
            log.error(f'chat task error.', e)
        finally:
            message_queue.put_nowait(terminate_token)  # 填充一个特殊结束符

    task = asyncio.create_task(_chat_task())
    await asyncio.sleep(0.5)

    # 消息体
    async for chunk in _generator(message_queue, max_wait_second=60 * 60):
        if not isinstance(chunk, ViewMessage) and not isinstance(chunk, ViewMessageStatus):
            log.info(f'queue get msg: {chunk}')
            continue

        if not show_thoughts and isinstance(chunk, ViewMessage) and chunk.type in ['THINKING', 'KNOWLEDGE']:
            # print(chunk.content)
            continue

        try:
            yield _convert_stream_event(chunk)
        except Exception as e:
            log.error(e)

    await asyncio.sleep(1)
    await task

    # 消息结束
    yield StreamEvent(
        type=StreamEvent.EventType.MESSAGE_END
    )
    log.info(">>>>>> end to output event stream <<<<<<")


__all__ = [
    'output_simple_stream',
    'generate_chat_session_name',
]
