[project]
name = "backend-server"
version = "1.0.0"
classifiers = ["Private :: Do Not Upload"]
description = ""
authors = [
    { name = "<EMAIL>" }
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "web",
    "streamlit>=1.45.1",
    "dotenv>=0.9.9",
    "stream-chat>=4.26.0",
]

[tool.uv.sources]
web = { workspace = true }

[tool.setuptools]
packages = ["backend_server"]

[tool.setuptools.package-dir]
backend_server = "src/backend_server"

[project.scripts]
# 封装系统命令
st_server = "backend_server.server:start"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
