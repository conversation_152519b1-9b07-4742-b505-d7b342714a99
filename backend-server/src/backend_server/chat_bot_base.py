import asyncio
import uuid
from typing import List, Callable

import streamlit as st

from backend_server.page_utils import SFUISession
from framework.agents.schema import Conversation, Role, ViewMessage, ViewMessageStatus
from common import tools
from framework.bot import Bot

log = tools.get_logger(__name__)

chat_style = """
<style>
.chat-box {
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 15px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    max-width: 100%;
    font-size: 14px;
    line-height: 1.6;
}

.user {
    background-color: #05a873;
    margin-left: auto;
    color: #ffffff;
}

.assistant {
    background-color: #f8f9fa;
    margin-right: auto;
    color: #333333;
}

.thinking {
    background-color: #e9e9e9;
    margin-right: auto;
    color: #333333;
    height: 300px;
    overflow-y: auto;
    border: 1px solid #ccc;
    padding: 10px;
    max-width: 100%;
}

.reference {
    background-color: #e9e9e9;
    margin-right: auto;
    color: #333333;
}
</style>
"""


class BaseChatBotPage:
    def __init__(self, bot: Bot, session: SFUISession, is_single_chat: bool = False, **kwargs):
        self.bot = bot
        self.mq = asyncio.Queue()
        self.terminate_token = str(uuid.uuid4())
        self.is_single_chat = is_single_chat
        self.session = session
        self.chat_history = []
        self.session.add_session_object("chat_history", self.chat_history)

    def produce_message(self, msg):
        self.mq.put_nowait(msg)

    def render(
            self,
            input_tip: str = "有什么可以帮助你吗？",
            input_prompt_processor: Callable[[str], str] | None = None
    ):
        """前端开始渲染"""
        st.markdown(chat_style, unsafe_allow_html=True)
        input_text = st.chat_input(input_tip)
        if not input_text:
            return
        if input_prompt_processor is not None:
            input_text = input_prompt_processor(input_text)
        self._render0(input_text)

    def _render0(self, input_text: str):
        if not input_text or (not input_text.strip()):
            return

        if self.is_single_chat:
            self.chat_history.clear()

        self.chat_history.append(Conversation(role=Role.USER, content=input_text))
        self.chat_history.append(Conversation(role=Role.ASSISTANT, content=""))

        chat_container = st.container()
        with chat_container:
            for idx, message in enumerate(self.chat_history):
                if message.role == Role.USER:
                    st.markdown(
                        f'<div class="chat-box user">{message.content}</div>',
                        unsafe_allow_html=True
                    )
                else:
                    placeholder = st.empty()
                    if idx == len(self.chat_history) - 1:
                        response = tools.asyncio_run(
                            self.chat,
                            chat_history=self.chat_history[:-1],
                            place_holder=placeholder
                        )
                        self.chat_history[-1].content = response
                    else:
                        st.markdown(
                            f'<div class="chat-box assistant">{message.content}</div>',
                            unsafe_allow_html=True
                        )

    async def chat(self, chat_history: List[Conversation], place_holder) -> str:
        async def _chat_task():
            await self.bot.async_chat(history=chat_history)
            self.mq.put_nowait(self.terminate_token)

        async def _display_task():
            non_times = 1
            while True:
                try:
                    try:
                        msg = await self.mq.get()
                        non_times = 1
                    except asyncio.QueueEmpty | asyncio.TimeoutError:
                        if non_times > 10:
                            break
                        await asyncio.sleep(0.2 * non_times)
                        non_times += 1
                        continue

                    if msg == self.terminate_token:
                        break
                    if not isinstance(msg, ViewMessage) and not isinstance(msg, ViewMessageStatus):
                        log.warning(f'queue get msg and will not output: {msg}')
                    else:
                        yield msg
                except Exception as e:
                    log.error(f'generator error.', e)
                    break

        chat_task = asyncio.create_task(_chat_task())

        thinking = ""
        response = ""
        references = []
        status = None
        async for chunk in _display_task():
            if isinstance(chunk, ViewMessageStatus):
                state = "running"
                if chunk.status == 'SUCCESS':
                    state = "complete"
                elif chunk.status != "PROCESSING":
                    state = "error"
                status = (chunk.text, state)
            elif isinstance(chunk, ViewMessage):
                if chunk.type == 'THINKING':
                    thinking += chunk.content
                elif chunk.type == 'MARKDOWN':
                    response += chunk.content
                elif chunk.type == 'KNOWLEDGE':
                    references.append(chunk.content)
                else:
                    response += f"\n```\n{chunk.content}\n```"

                with place_holder:
                    with st.container():
                        if status is not None:
                            st.status(status[0], state=status[1])
                        if thinking:
                            with st.expander("思考过程"):
                                st.markdown(
                                    f'<div class="chat-box thinking"><b>Thinking:</b>{thinking}</div>',
                                    unsafe_allow_html=True
                                )
                        if response:
                            st.markdown(
                                f'<div class="chat-box assistant">{response}</div>',
                                unsafe_allow_html=True
                            )
                        if references:
                            with st.expander("知识检索结果"):
                                for reference in references:
                                    with st.container(border=1):
                                        st.markdown(
                                            f'<div class="chat-box reference">{reference}</div>',
                                            unsafe_allow_html=True
                                        )
        await chat_task
        return response
