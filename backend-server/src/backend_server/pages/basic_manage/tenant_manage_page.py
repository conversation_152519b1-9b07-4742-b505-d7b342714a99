import streamlit as st
import pandas as pd
from core.models import TenantProjectModel
from core.service.tenant_project_service import TenantProjectService

st.markdown('### 租户项目管理')
st.divider()

from common import tools

logger = tools.get_logger()
tenant_project_service = TenantProjectService()


def reload_tenant_project() -> pd.DataFrame:
    all_data = tools.asyncio_run(tenant_project_service.get_all)
    df = pd.DataFrame([d.dict() for d in all_data])
    edited_df = st.data_editor(
        df,
        use_container_width=True,
        num_rows='dynamic',
        disabled=['id', 'version', 'create_time', 'update_time', 'is_deleted'],
    )
    return edited_df


def save_tenant_project(edited_df: pd.DataFrame):
    if edited_df is None:
        return
    all_data = tools.asyncio_run(tenant_project_service.get_all)
    edited_data = []
    for index, row in edited_df.iterrows():
        data = row.to_dict()
        new_data = dict()
        new_data['id'] = None if pd.isna(data.get('id')) or data.get('id', None) is None else int(data.get('id'))
        new_data['tenant_cname'] = data['tenant_cname'].strip()
        new_data['organization_id'] = data['organization_id'].strip()
        new_data['cdp_host'] = data['cdp_host'].strip()
        new_data['project_name'] = data['project_name'].strip()
        new_data['project_id'] = int(data['project_id'])
        new_data['api_key'] = data['api_key'].strip()
        new_data['product_versions'] = data['product_versions']
        new_data['industry'] = data['industry']
        edited_data.append(new_data)

    # deleted
    for data in all_data:
        if data.id not in [d['id'] for d in edited_data]:
            tools.asyncio_run(tenant_project_service.delete, id=int(data.id))

    # new
    for data in edited_data:
        logger.info(f"tenant_project id={data.get('id', None)}")
        if data.get('id', None) is None:
            tools.asyncio_run(tenant_project_service.save, model=TenantProjectModel(**data))

    # update
    for data in edited_data:
        for data1 in all_data:
            if data['id'] == data1.id:
                if data['tenant_cname'] != data1.tenant_cname or \
                        data['organization_id'] != data1.organization_id or \
                        data['cdp_host'] != data1.cdp_host or \
                        data['project_name'] != data1.project_name or \
                        data['project_id'] != data1.project_id or \
                        data['api_key'] != data1.api_key or \
                        data['industry'] != data1.industry or \
                        data['product_versions'] != data1.product_versions:
                    data = data1.dict() | data
                    tools.asyncio_run(tenant_project_service.save, model=TenantProjectModel(**data))
                    break
    st.text('保存成功')


def main():
    edited_df = reload_tenant_project()
    st.button('保存', on_click=save_tenant_project, kwargs={"edited_df": edited_df})


main()
