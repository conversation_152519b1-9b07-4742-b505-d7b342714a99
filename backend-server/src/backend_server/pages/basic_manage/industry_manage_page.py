import streamlit as st
import pandas as pd
from core.models import SensorsProductModel
from core.service.industry_service import IndustryService

st.markdown('### 行业管理')
st.divider()

from common import tools

logger = tools.get_logger()
_service = IndustryService()


def reload_data() -> pd.DataFrame:
    all_data = tools.asyncio_run(_service.list_all)
    df = pd.DataFrame([d.dict() for d in all_data])
    edited_df = st.data_editor(
        df,
        use_container_width=True,
        num_rows='dynamic',
        disabled=['id', 'version', 'create_time', 'update_time', 'is_deleted'],
    )
    return edited_df


def save_data(edited_df: pd.DataFrame):
    if edited_df is None:
        return
    all_data = tools.asyncio_run(_service.list_all)
    edited_data = []
    for index, row in edited_df.iterrows():
        data = row.to_dict()
        new_data = dict()
        new_data['id'] = None if pd.isna(data.get('id')) or data.get('id', None) is None else int(data.get('id'))
        new_data['industry'] = data['industry'].strip()
        new_data['industry_cname'] = data['industry_cname'].strip()
        edited_data.append(new_data)

    # deleted
    for data in all_data:
        if data.id not in [d['id'] for d in edited_data]:
            tools.asyncio_run(_service.delete, id=int(data.id))

    # new
    for data in edited_data:
        if data.get('id', None) is None:
            tools.asyncio_run(_service.save, model=SensorsProductModel(**data))

    # update
    for data in edited_data:
        for data1 in all_data:
            if data['id'] == data1.id:
                if data['industry'] != data1.industry or \
                        data['industry_cname'] != data1.industry_cname:
                    data = data1.dict() | data
                    tools.asyncio_run(_service.save, model=SensorsProductModel(**data))
                    break
    st.text('保存成功')


def main():
    edited_df = reload_data()
    st.button('保存', on_click=save_data, kwargs={"edited_df": edited_df})


main()
