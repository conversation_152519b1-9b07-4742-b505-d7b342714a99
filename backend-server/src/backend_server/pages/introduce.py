import os
from pathlib import Path

import streamlit as st
from streamlit.runtime.uploaded_file_manager import UploadedFile
import zipfile

from common import tools

log = tools.get_logger()

show_text = """
# 欢迎使用

## 快捷链接
"""

st.markdown(show_text)

# 一些常用工具
st.markdown("### 常用工具\n***\n")


# 压缩指定文件夹
def zipdir(path, ziph):
    # 循环遍历文件夹中的所有文件和子文件夹
    for root, dirs, files in os.walk(path):
        for file in files:
            # 将每个文件添加到zip文件中
            ziph.write(
                filename=os.path.join(root, file),
                arcname=Path(root, file).name,
            )


def convert_file_tool():
    # 文件转换
    uploaded_files = st.file_uploader(
        "文件转文本",
        key='file_convert_tool',
        accept_multiple_files=True,
        type=['txt', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'csv', 'md'],
        help='文件转成 markdown 文本'
    )

    if isinstance(uploaded_files, UploadedFile):
        uploaded_files = [uploaded_files]
    if len(uploaded_files) <= 0:
        st.text('请上传文件~')
        return
    else:
        st.text('文本转换中 ...')

    while True:
        base_path = Path('/tmp').joinpath(tools.uuid4_no_underline())
        if base_path.exists():
            continue
        base_path.mkdir(parents=True, exist_ok=True)
        break

    new_base_path = base_path.joinpath('downloads')
    new_base_path.mkdir(parents=True, exist_ok=True)

    for uploaded_file in uploaded_files:
        if uploaded_file is not None:
            file_name = uploaded_file.name
            saved_file_path = base_path.joinpath(file_name)
            saved_file_path.write_bytes(uploaded_file.getvalue())

            new_file_name = file_name.split(r'.')
            if len(new_file_name) > 1:
                new_file_name = new_file_name[:-1]
            new_file_name.append('txt')
            new_file = new_base_path.joinpath('.'.join(new_file_name))
            tools.convert_file(file=saved_file_path, new_file=new_file)

    # 压缩目录
    log.info('Start zip files.')
    zip_file = base_path.joinpath('documents.zip')
    with zipfile.ZipFile(zip_file, 'w') as zip_obj:
        zipdir(str(new_base_path.absolute()), zip_obj)

    with zip_file.open(mode='rb') as file:
        st.download_button(
            label="Download",
            data=file,
            file_name=zip_file.name,
            mime="application/octet-stream",
        )


convert_file_tool()
