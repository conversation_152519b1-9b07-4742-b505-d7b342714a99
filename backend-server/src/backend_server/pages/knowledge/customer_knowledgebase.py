from abc import ABC

import streamlit as st

from backend_server.page_utils import Session<PERSON><PERSON><PERSON><PERSON>ord<PERSON>, run_in_thread, SFUISession
from business.knowledge_factory import KnowledgeFactory
from business.scenario.customer_rag_client import CustomerRagClient
from business.scenario.service.preset_knowledgebase_service import PresetKnowledgebaseService
from common import tools

from core.service.sensors_product_service import SensorsProductService

st.markdown('### 客户知识库管理')
st.divider()

logger = tools.get_logger()

from backend_server.pages.knowledge.base import KnowledgeManagerPage


class AbstractCustomerKnowledgePage(KnowledgeManagerPage, ABC):
    def __init__(self, **kwargs):
        super().__init__(ui_session=SFUISession(), **kwargs)

    def _reindex_knowledge(self, knowledge_client, callback):
        self.log_and_callback(callback=callback, content="开始索引...")
        tools.asyncio_tools.asyncio_run(
            lambda: knowledge_client.build_index(index_mode='reindex')
        )
        self.log_and_callback(callback=callback, content="索引完成，尝试检索吧")

    @run_in_thread
    def _reindex_documents_knowledge_metadata(self, knowledgebase: dict, call_back):
        knowledge_client = KnowledgeFactory.get_knowledge_base_client(
            id=knowledgebase['id'],
            name=knowledgebase['name'],
            callback=call_back
        )
        if not isinstance(knowledge_client, CustomerRagClient):
            self.log_and_callback(callback=call_back, content=f'知识库类型错误')
            return
        self._reindex_knowledge(knowledge_client=knowledge_client.customer_metadata_rag_client, callback=call_back)

    @run_in_thread
    def _reindex_documents_knowledge_history_strategy(self, knowledgebase: dict, call_back):
        knowledge_client = KnowledgeFactory.get_knowledge_base_client(
            id=knowledgebase['id'],
            name=knowledgebase['name'],
            callback=call_back
        )
        if not isinstance(knowledge_client, CustomerRagClient):
            self.log_and_callback(callback=call_back, content=f'知识库类型错误')
            return
        self._reindex_knowledge(knowledge_client=knowledge_client.customer_strategy_rag_client, callback=call_back)

    @st.fragment
    def _knowledge_manager(self, knowledgebase: dict, call_back):
        st.markdown("#### 知识库操作")

        st.button(
            '重新索引 CDP 元数据',
            icon='⚠️',
            on_click=self._reindex_documents_knowledge,
            kwargs={
                'knowledgebase': knowledgebase,
                'index_mode': 'update',
                'call_back': call_back
            }
        )

        st.button(
            '重新索引 SF 策略数据',
            icon='⚠️',
            on_click=self._reindex_documents_knowledge,
            kwargs={
                'knowledgebase': knowledgebase,
                'index_mode': 'update',
                'call_back': call_back
            }
        )

        st.button(
            '全量重新索引',
            icon='⚠️',
            on_click=self._reindex_documents_knowledge,
            kwargs={
                'knowledgebase': knowledgebase,
                'index_mode': 'reindex',
                'call_back': call_back
            }
        )

    @st.fragment
    def knowledge_manager(self, index_type: str) -> dict:
        with st.container(border=1):
            knowledgebase = self._knowledgebase_selector(
                index_type=index_type,
                key=f"{self.ui_session.session_id}_isyg7dbuisdnjugsdv"
            )
        with st.container(border=1):
            self._knowledge_manager(knowledgebase, call_back=self.manager_callback)

        return knowledgebase


class CustomerKnowledgeListPage(AbstractCustomerKnowledgePage):
    def main(self):
        self.manage_knowledge_list(index_type='PRESET_CUSTOMER')


class CustomerKnowledgeManagerPage(AbstractCustomerKnowledgePage):
    def main(self):
        self.show_knowledge(index_type='PRESET_CUSTOMER')


class CustomerKnowledgeTestPage(AbstractCustomerKnowledgePage):
    def main(self):
        self.recall_test(index_type='PRESET_CUSTOMER')


_tab1_state_key_ = "iuhas7ty8w9eonjijhgvyausiuoadsasaiusyugt"
_tab2_state_key_ = "iuhas7ty8w9eonjijhgvyausiuoadsasaiusyugd"
_tab3_state_key_ = "iuhas7ty8w9eonjijhgvyausiuoadsasaiusyugv"


def main():
    tab1, tab2, tab3 = st.tabs(['知识库列表', '知识库操作', '召回测试'])
    with tab1:
        tab1_object = st.session_state.get(_tab1_state_key_, None)
        if tab1_object is None:
            tab1_object = CustomerKnowledgeListPage()
            st.session_state[_tab1_state_key_] = tab1_object

        tab1_object.main()
    with tab2:
        tab2_object = st.session_state.get(_tab2_state_key_, None)
        if tab2_object is None:
            tab2_object = CustomerKnowledgeManagerPage()
            st.session_state[_tab2_state_key_] = tab2_object

        tab2_object.main()
    with tab3:
        tab3_object = st.session_state.get(_tab3_state_key_, None)
        if tab3_object is None:
            tab3_object = CustomerKnowledgeManagerPage()
            st.session_state[_tab3_state_key_] = tab3_object

        tab3_object.main()


main()
