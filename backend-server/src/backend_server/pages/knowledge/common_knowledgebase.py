import streamlit as st
from common import tools
from backend_server.page_utils import SFUISession

st.markdown('### 通用知识库管理')
st.divider()

logger = tools.get_logger()

from backend_server.pages.knowledge.base import KnowledgeManagerPage


class CommonKnowledgeManagerPage(KnowledgeManagerPage):
    def __init__(self, **kwargs):
        super().__init__(ui_session=SFUISession(), **kwargs)

    def main(self):
        self.show_knowledge(index_type='PRESET_COMMON')


class CommonKnowledgeTestPage(KnowledgeManagerPage):
    def __init__(self, **kwargs):
        super().__init__(ui_session=SFUISession(), **kwargs)

    def main(self):
        self.recall_test(index_type='PRESET_COMMON')


_tab1_state_key_ = "loiuhygfastdghcjhujiskodpoewiur"
_tab2_state_key_ = "kisuygdhgbjshkjdhsgjhsbdqiwgui2"


def main():
    tab1, tab2 = st.tabs(['知识库操作', '召回测试'])
    with tab1:
        tab1_object = st.session_state.get(_tab1_state_key_, None)
        if tab1_object is None:
            tab1_object = CommonKnowledgeManagerPage()
            st.session_state[_tab1_state_key_] = tab1_object
        tab1_object.main()
    with tab2:
        tab2_object = st.session_state.get(_tab2_state_key_, None)
        if tab2_object is None:
            tab2_object = CommonKnowledgeTestPage()
            st.session_state[_tab2_state_key_] = tab2_object
        tab2_object.main()


main()
