import json
from abc import ABC

import streamlit as st

from backend_server.page_utils import run_in_thread, SFUISession
from business.knowledge_factory import KnowledgeFactory
from common import tools

from core.service.sensors_product_service import SensorsProductService
from crawlers.sensors_manual import SensorsManualCrawler

st.markdown('### 神策知识库管理')
st.divider()

logger = tools.get_logger()

from backend_server.pages.knowledge.base import KnowledgeManagerPage


class AbstractSensorsKnowledgePage(KnowledgeManagerPage, ABC):
    def __init__(self, **kwargs):
        super().__init__(ui_session=SFUISession(), **kwargs)
        self._sensors_product_service = SensorsProductService()

    @run_in_thread
    def _start_crawler(self, knowledgebase: dict, call_back):
        self.log_and_callback(callback=call_back, content=f'更新 {knowledgebase["cname"]} 知识库...')
        self.log_and_callback(callback=call_back, content="获取知识库信息...")

        knowledge_client = KnowledgeFactory.get_knowledge_base_client(
            id=knowledgebase['id'],
            name=knowledgebase['name'],
            callback=call_back
        )
        self.log_and_callback(callback=call_back, content="知识库信息已获取")
        self.log_and_callback(callback=call_back, content="获取官网数据...")
        kb_config = json.loads(knowledgebase['config'])
        sensors_product = tools.asyncio_run(
            self._sensors_product_service.get_sensors_product,
            product_name=kb_config['product'],
            product_manual_version=kb_config['version']
        )
        if sensors_product is None:
            self.log_and_callback(callback=call_back, content="产品版本不存在")
            return

        crawler = SensorsManualCrawler(
            start_page=sensors_product.product_manual_page_url,
            url_prefix=f"https://manual.sensorsdata.cn/{kb_config['product'].lower()}",
            clean_dir=False,
            storage_dir=knowledge_client.input_dir,
            callback=call_back
        )
        crawler.start_crawler(max_page_count=100_000)
        self.log_and_callback(callback=call_back, content="官网数据已获取，请使用索引重新索引数据")

    @st.fragment
    def show_knowledge(self, index_type: str):
        kg_manage, show_log = st.columns([0.3, 0.7])
        with kg_manage:
            st.markdown("#### 知识库管理")
            knowledgebase = self.knowledge_manager(index_type=index_type)
            st.button(
                "重新抓取文档",
                on_click=self._start_crawler,
                kwargs={
                    "knowledgebase": knowledgebase,
                    "call_back": self.manager_callback
                }
            )
        with show_log:
            _t1_c1, _t1_c2 = st.columns([0.9, 0.1])
            with _t1_c1:
                st.markdown("#### Show Logs")
            with _t1_c2:
                st.button('清除日志', on_click=lambda: self.manage_logs_cache.clear())
            with st.container(border=1, height=1000):
                self._show_manage_logs()


class SensorsKnowledgeListPage(AbstractSensorsKnowledgePage):
    def main(self):
        self.manage_knowledge_list(index_type='PRESET_SENSORS')


class SensorsKnowledgeManagePage(AbstractSensorsKnowledgePage):
    def main(self):
        self.show_knowledge(index_type='PRESET_SENSORS')


class SensorsKnowledgeTestPage(AbstractSensorsKnowledgePage):
    def main(self):
        self.recall_test(index_type='PRESET_SENSORS')


_tab1_state_key_ = "kauisyg6786798sudjiu9n3iurbg8isd"
_tab2_state_key_ = "kauisyg6786798sudjiu9n3iurbg8isf"
_tab3_state_key_ = "kauisyg6786798sudjiu9n3iurbg8isg"


def main():
    tab1, tab2, tab3 = st.tabs(['知识库列表', '知识库操作', '召回测试'])
    with tab1:
        tab1_object = st.session_state.get(_tab1_state_key_, None)
        if tab1_object is None:
            tab1_object = SensorsKnowledgeListPage()
            st.session_state[_tab1_state_key_] = tab1_object
        tab1_object.main()
    with tab2:
        tab1_object = st.session_state.get(_tab1_state_key_, None)
        if tab1_object is None:
            tab1_object = SensorsKnowledgeManagePage()
            st.session_state[_tab1_state_key_] = tab1_object
        tab1_object.main()
    with tab3:
        tab1_object = st.session_state.get(_tab1_state_key_, None)
        if tab1_object is None:
            tab1_object = SensorsKnowledgeTestPage()
            st.session_state[_tab1_state_key_] = tab1_object
        tab1_object.main()


main()
