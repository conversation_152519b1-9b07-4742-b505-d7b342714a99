import tempfile
from abc import ABC
from pathlib import Path

import streamlit as st

from backend_server.page_utils import SFUISession
from business.knowledge_factory import KnowledgeFactory
from common import tools

from core.service.sensors_product_service import SensorsProductService

st.markdown('### 行业知识库管理')
st.divider()

logger = tools.get_logger()

from backend_server.pages.knowledge.base import KnowledgeManagerPage


class AbstractIndustryKnowledgePage(KnowledgeManagerPage, ABC):
    def __init__(self, **kwargs):
        super().__init__(ui_session=SFUISession(), **kwargs)

        self._sensors_product_service = SensorsProductService()

    @st.fragment
    def show_knowledge(self, index_type: str):
        kg_manage, show_log = st.columns([0.3, 0.7])
        with kg_manage:
            st.markdown("#### 知识库管理")
            knowledgebase = self.knowledge_manager(index_type=index_type)
            self._upload_canvas_template(knowledgebase=knowledgebase)
        with show_log:
            _t1_c1, _t1_c2 = st.columns([0.9, 0.1])
            with _t1_c1:
                st.markdown("#### Show Logs")
            with _t1_c2:
                st.button('清除日志', on_click=lambda: self.manage_logs_cache.clear())
            with st.container(border=1, height=1000):
                self._show_manage_logs()

    def _upload_canvas_template(self, knowledgebase: dict):
        # 上传画布模板
        uploaded_canvas_templates = st.file_uploader(
            "上传画布模板文件", type=['templates'],
            help='仅支持 .templates 后缀'
        )
        if uploaded_canvas_templates is not None:
            file_name = uploaded_canvas_templates.name
            new_file_path = Path(tempfile.gettempdir()) / file_name
            new_file_path.write_bytes(uploaded_canvas_templates.getvalue())
            knowledge_client = KnowledgeFactory.get_knowledge_base_client(
                id=knowledgebase['id'],
                name=knowledgebase['name'],
                callback=self.manager_callback
            )
            knowledge_client.add_additional_file(input_file=new_file_path, new_file_name='canvas.templates')
            st.write('文件上传成功！')

    def main(self):
        tab1, tab2, tab3 = st.tabs(['知识库列表', '知识库操作', '召回测试'])
        with tab1:
            self.manage_knowledge_list(index_type='PRESET_INDUSTRY')
        with tab2:
            self.show_knowledge(index_type='PRESET_INDUSTRY')
        with tab3:
            self.recall_test(index_type='PRESET_INDUSTRY')


class IndustryKnowledgeListPage(AbstractIndustryKnowledgePage):
    def main(self):
        self.manage_knowledge_list(index_type='PRESET_INDUSTRY')


class IndustryKnowledgeManagePage(AbstractIndustryKnowledgePage):
    def main(self):
        self.show_knowledge(index_type='PRESET_INDUSTRY')


class IndustryKnowledgeTestPage(AbstractIndustryKnowledgePage):
    def main(self):
        self.recall_test(index_type='PRESET_INDUSTRY')


_tab1_state_key_ = "09872yugehjmkisugdyuisodkmsjd"
_tab2_state_key_ = "09872yugehjmkisugdyuisodkmsjr"
_tab3_state_key_ = "09872yugehjmkisugdyuisodkmsjy"


def main():
    tab1, tab2, tab3 = st.tabs(['知识库列表', '知识库操作', '召回测试'])
    with tab1:
        tab1_object = st.session_state.get(_tab1_state_key_, None)
        if tab1_object is None:
            tab1_object = IndustryKnowledgeListPage()
            st.session_state[_tab1_state_key_] = tab1_object
        tab1_object.main()
    with tab2:
        tab2_object = st.session_state.get(_tab2_state_key_, None)
        if tab2_object is None:
            tab2_object = IndustryKnowledgeManagePage()
            st.session_state[_tab2_state_key_] = tab2_object
        tab2_object.main()
    with tab3:
        tab3_object = st.session_state.get(_tab3_state_key_, None)
        if tab3_object is None:
            tab3_object = IndustryKnowledgeTestPage()
            st.session_state[_tab3_state_key_] = tab3_object
        tab3_object.main()


main()
