from abc import ABC
import streamlit as st
from backend_server.page_utils import SFUISession
from common import tools

st.markdown('### 自定义知识库管理')
st.divider()

logger = tools.get_logger()

from backend_server.pages.knowledge.base import KnowledgeManagerPage


class AbstractOtherKnowledgePage(KnowledgeManagerPage, ABC):
    def __init__(self, **kwargs):
        super().__init__(ui_session=SFUISession(), **kwargs)


class OtherKnowledgeListPage(AbstractOtherKnowledgePage):
    def main(self):
        self.manage_knowledge_list(index_type='SIMPLE')


class OtherKnowledgeManagePage(AbstractOtherKnowledgePage):
    def main(self):
        self.show_knowledge(index_type='SIMPLE')


class OtherKnowledgeTestPage(AbstractOtherKnowledgePage):
    def main(self):
        self.recall_test(index_type='SIMPLE')


_tab1_state_key_ = "089wyueghnjsdiuyg9asidjshdjb"
_tab2_state_key_ = "189wyueghnjsdiuyg9asidjshdjn"
_tab3_state_key_ = "289wyueghnjsdiuyg9asidjshdjm"

config_instruction = """
在你开始索引之前，你需要将 config 字段正确配置。

config 字段示例及说明如下：
```json
{
    "entity_types": {
        "product": "产品名称"
    },
    "pre_knowledge":"这里是知识库的前提知识",
    "chunk_size": 2000,
    "query_limit": 10,
    "max_talk_round": 5,
    "score_limit": 0.4,
    "additional_retrieve_requirement": "额外检索需求描述",
    "retrieve_documents_only": false
}
```
字段说明：
  - entity_types: 抽取的实体类型、实体类型描述
  - pre_knowledge: 检索该知识库的前提知识，在检索的时候会一并带上
  - chunk_size: 文档分段的最大段大小
  - query_limit: 每次检索最多返回的条数
  - max_talk_round: 检索推理的轮数
  - score_limit: 检索的分数限制，范围 0～1，低于这个分数的结果不返回
  - additional_retrieve_requirement: 对检索的额外需求描述
  - retrieve_documents_only: 是否只检索文档，不检索实体，设置为 true 表示只检索文档
"""


def main():
    tab1, tab2, tab3 = st.tabs(['知识库列表', '知识库操作', '召回测试'])
    with tab1:
        tab1_object = st.session_state.get(_tab1_state_key_, None)
        if tab1_object is None:
            tab1_object = OtherKnowledgeListPage()
            st.session_state[_tab1_state_key_] = tab1_object
        tab1_object.main()

        with st.container(border=1):
            st.markdown(f'#### config 字段常见配置项说明\n\n{config_instruction}')

    with tab2:
        tab2_object = st.session_state.get(_tab2_state_key_, None)
        if tab2_object is None:
            tab2_object = OtherKnowledgeManagePage()
            st.session_state[_tab2_state_key_] = tab2_object
        tab2_object.main()
    with tab3:
        tab3_object = st.session_state.get(_tab3_state_key_, None)
        if tab3_object is None:
            tab3_object = OtherKnowledgeTestPage()
            st.session_state[_tab3_state_key_] = tab3_object
        tab3_object.main()


main()
