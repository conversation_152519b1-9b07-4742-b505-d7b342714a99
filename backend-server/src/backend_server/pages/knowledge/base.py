import json
import tempfile
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any

import streamlit as st

from backend_server.page_utils import SessionLogRecords, run_in_thread, append_conversation, SFUISession
from business.knowledge_factory import KnowledgeFactory
from business.scenario.service.preset_knowledgebase_service import PresetKnowledgebaseService
from common import tools
import pandas as pd

from core.models import KnowledgebaseModel
from framework.agents.schema import Conversation, Role
from framework.data.document import SensorsDocsMetadata

logger = tools.get_logger()


class KnowledgeManagerPage(ABC):
    def __init__(self, ui_session: SFUISession, **kwargs):
        self.ui_session = ui_session
        self._service = PresetKnowledgebaseService()
        self.manage_logs_cache = SessionLogRecords(max_record_size=100)
        self.manager_callback = lambda x: self.add_manage_logs_cache(x)
        self.recall_logs_cache = SessionLogRecords(max_record_size=200)
        self.recall_callback = lambda x: self.add_recall_logs_cache(x)

    def reload_data(self, index_type: str) -> pd.DataFrame:
        all_data = tools.asyncio_run(self._service.get_all_knowledgebase_by_type, index_type=index_type)
        if all_data:
            df = pd.DataFrame([d.dict() for d in all_data])
        else:
            # 创建一个空的DataFrame但包含所有必要的列名
            df = pd.DataFrame(columns=[
                'id', 'cname', 'name', 'description', 'base_dir', 
                'index_type', 'config', 'custom_knowledge_class',
                'version', 'create_time', 'update_time', 'is_deleted'
            ])
        
        edited_df = st.data_editor(
            df,
            use_container_width=True,
            num_rows='dynamic',
            disabled=['index_type', 'id', 'version', 'create_time', 'update_time', 'is_deleted'],
            key=f"{self.ui_session.session_id}_oishudgygsd"
        )
        return edited_df

    def save_data(self, edited_df: pd.DataFrame, index_type: str):
        if edited_df is None:
            return
        all_data = tools.asyncio_run(self._service.get_all_knowledgebase_by_type, index_type=index_type)
        edited_data = []
        for index, row in edited_df.iterrows():
            data = row.to_dict()
            new_data = dict()
            new_data['id'] = None if pd.isna(data.get('id')) or data.get('id', None) is None else int(data.get('id'))
            new_data['cname'] = data['cname'].strip()
            new_data['name'] = data['name'].strip()
            new_data['description'] = data['description'].strip()
            new_data['base_dir'] = data['base_dir'].strip()
            new_data['index_type'] = index_type
            new_data['config'] = '{}' if pd.isna(data.get('config')) else data['config'].strip()
            new_data['custom_knowledge_class'] = None if pd.isna(data.get('custom_knowledge_class')) else data[
                'custom_knowledge_class'].strip()
            edited_data.append(new_data)

        # deleted
        for data in all_data:
            if data.id not in [d['id'] for d in edited_data]:
                tools.asyncio_run(self._service.delete, id=int(data.id))

        # new
        for data in edited_data:
            if data.get('id', None) is None:
                tools.asyncio_run(self._service.save_knowledgebase, knowledgebase=KnowledgebaseModel(**data))

        # update
        for data in edited_data:
            for data1 in all_data:
                if data['id'] == data1.id:
                    if data['cname'] != data1.cname or \
                            data['name'] != data1.name or \
                            data['base_dir'] != data1.base_dir or \
                            data['index_type'] != data1.index_type or \
                            data['config'] != data1.config or \
                            data['custom_knowledge_class'] != data1.custom_knowledge_class:
                        data = data1.dict() | data
                        tools.asyncio_run(self._service.save_knowledgebase, knowledgebase=KnowledgebaseModel(**data))
                        break
        st.text('保存成功')

    @st.fragment
    def manage_knowledge_list(self, index_type: str):
        edited_df = self.reload_data(index_type=index_type)
        st.button(
            '保存',
            on_click=self.save_data,
            kwargs={
                "edited_df": edited_df,
                "index_type": index_type
            },
            key=f"{self.ui_session.session_id}_opiuhbshdjnkjs"
        )

    def log_and_callback(self, callback, content, log_level: str = 'info'):
        getattr(logger, log_level)(content)
        if callback is not None:
            callback(content)

    def _init_common_knowledgebase(self, call_back):
        tools.asyncio_run(self._service.init_default_preset_knowledgebase)
        self.log_and_callback(callback=call_back, content='初始化完成')

    @run_in_thread
    def _show_knowledge_documents(self, knowledgebase: dict, call_back):
        knowledge_client = KnowledgeFactory.get_knowledge_base_client(
            id=knowledgebase['id'],
            name=knowledgebase['name'],
            callback=call_back
        )
        files_path = knowledge_client.input_dir
        metadata_file = files_path / 'metadata.json'
        if not metadata_file.exists():
            self.log_and_callback(callback=call_back, content='知识库文件不存在')
            return
        metadata = SensorsDocsMetadata.model_validate(json.load(metadata_file.open('r', encoding='utf-8')))
        self.log_and_callback(callback=call_back, content=f'数据批次ID：{metadata.batch_id}')
        self.log_and_callback(callback=call_back, content=f'当前已索引批次ID：{metadata.indexed_batch_id}')
        self.log_and_callback(
            callback=call_back,
            content=f'数据获取开始结束时间：{metadata.start_time} - {metadata.end_time}'
        )

        data_frame = pd.DataFrame([doc.model_dump() for doc in metadata.documents])
        self.log_and_callback(callback=call_back, content='数据及文件详情:')
        self.log_and_callback(callback=call_back, content=data_frame)

    def _upload_file(self, knowledgebase: dict, call_back):
        uploaded_file = st.file_uploader(
            "上传知识库文件", type=['txt'],
            help='仅支持 .txt 后缀，如果上传同名文件则可能会替换。',
            key=f"{self.ui_session.session_id}_97yuhwjdausytay"
        )
        if uploaded_file is not None:
            file_name = uploaded_file.name
            new_file_path = Path(tempfile.gettempdir()) / file_name
            new_file_path.write_bytes(uploaded_file.getvalue())
            knowledge_client = KnowledgeFactory.get_knowledge_base_client(
                id=knowledgebase['id'],
                name=knowledgebase['name'],
                callback=call_back
            )
            knowledge_client.add_input_file(input_file=new_file_path)
            st.write('文件上传成功！')

    def _delete_knowledge_file(self, knowledgebase: dict, file_id: str, call_back):
        if not file_id or not file_id.strip():
            return
        knowledge_client = KnowledgeFactory.get_knowledge_base_client(
            id=knowledgebase['id'],
            name=knowledgebase['name'],
            callback=call_back
        )
        try:
            knowledge_client.delete_file(file_id=file_id)
            self.log_and_callback(callback=call_back, content=f'删除文件成功')
        except Exception as e:
            self.log_and_callback(callback=call_back, content=f'删除文件失败：{e}')

    def _knowledgebase_selector(self, index_type: str, key: str | None = None) -> dict:
        st.markdown("#### 知识库选择")
        all_data = tools.asyncio_run(self._service.get_all_knowledgebase_by_type, index_type=index_type)
        all_data = [d.dict() for d in all_data]
        knowledgebase = st.selectbox(
            '选择知识库',
            key=key or f"{self.ui_session.session_id}_iubgsvdhiiuysdftyuh",
            options=all_data,
            format_func=lambda x: x['cname'],
            index=0
        )
        return knowledgebase

    @run_in_thread
    def _reindex_documents_knowledge(self, knowledgebase: dict, index_mode: str, call_back):
        self.log_and_callback(callback=call_back, content=f'重新索引 {knowledgebase["cname"]} 知识库...')
        self.log_and_callback(callback=call_back, content="获取知识库信息...")

        knowledge_client = KnowledgeFactory.get_knowledge_base_client(
            id=knowledgebase['id'],
            name=knowledgebase['name'],
            callback=call_back
        )
        self.log_and_callback(callback=call_back, content="知识库信息已获取")
        self.log_and_callback(callback=call_back, content="开始索引...")
        tools.asyncio_tools.asyncio_run(
            lambda: knowledge_client.build_index(index_mode=index_mode)
        )
        self.log_and_callback(callback=call_back, content="索引完成，尝试检索吧")

    @st.fragment
    def _knowledge_manager(self, knowledgebase: dict, call_back):
        st.markdown("#### 知识库操作")
        st.button(
            '全量重新索引',
            icon='⚠️',
            on_click=self._reindex_documents_knowledge,
            kwargs={
                'knowledgebase': knowledgebase,
                'index_mode': 'reindex',
                'call_back': call_back
            },
            key=f"{self.ui_session.session_id}_98wyuerhnuysdlapp"
        )

        st.button(
            '更新索引',
            on_click=self._reindex_documents_knowledge,
            kwargs={
                'knowledgebase': knowledgebase,
                'index_mode': 'update',
                'call_back': call_back
            },
            key=f"{self.ui_session.session_id}_9786t8i23jugtyauqiwzaskmas"
        )

    @st.fragment
    def knowledge_manager(self, index_type: str) -> dict:
        with st.container(border=1, key=f"{self.ui_session.session_id}_8a7s"):
            knowledgebase = self._knowledgebase_selector(
                index_type=index_type,
                key=f"{self.ui_session.session_id}_iaysgvyftgvuaisduhbusd"
            )
            st.button(
                "查看知识库文件",
                on_click=self._show_knowledge_documents,
                kwargs={
                    "knowledgebase": knowledgebase,
                    "call_back": self.manager_callback
                },
                key=f"{self.ui_session.session_id}_iyt6f27837iu8jikpqs"
            )
            # 展示上传按钮
            self._upload_file(knowledgebase, call_back=self.manager_callback)
            # 删除文件
            file_id = st.text_input("请输入要删除的文件ID")
            st.button(
                "删除文件",
                on_click=self._delete_knowledge_file,
                kwargs={
                    "knowledgebase": knowledgebase,
                    "file_id": file_id,
                    "call_back": self.manager_callback
                },
                key=f"{self.ui_session.session_id}_isygdtgbhioytw7egu"
            )
        with st.container(border=1, key=f"{self.ui_session.session_id}_8a7l"):
            self._knowledge_manager(knowledgebase, call_back=self.manager_callback)

        return knowledgebase

    def add_manage_logs_cache(self, log_content: Any):
        if log_content is None:
            return
        if isinstance(log_content, pd.DataFrame):
            self.manage_logs_cache.append(log_content)
        else:
            for l in str(log_content).split('\n'):
                self.manage_logs_cache.append(l)

    @st.fragment(run_every=1)
    def _show_manage_logs(self):
        logs = ''
        for log in self.manage_logs_cache.records():
            if isinstance(log, pd.DataFrame):
                if logs:
                    st.code(body=logs, language='log')
                st.dataframe(log)
                logs = ''
            else:
                logs = logs + str(log) + '\n'

        if logs:
            st.code(body=logs, language='log')

    @st.fragment
    def show_knowledge(self, index_type: str):
        kg_manage, show_log = st.columns([0.3, 0.7])
        with kg_manage:
            st.markdown("#### 知识库管理")
            self.knowledge_manager(index_type=index_type)
        with show_log:
            _t1_c1, _t1_c2 = st.columns([0.9, 0.1])
            with _t1_c1:
                st.markdown("#### Show Logs")
            with _t1_c2:
                st.button('清除日志', on_click=lambda: self.manage_logs_cache.clear())
            with st.container(border=1, height=1000, key=f"{self.ui_session.session_id}_8a7m"):
                self._show_manage_logs()

    def add_recall_logs_cache(self, log_content: Any):
        if log_content is None:
            return
        if isinstance(log_content, pd.DataFrame):
            self.recall_logs_cache.append(log_content)
        else:
            for l in str(log_content).split('\n'):
                self.recall_logs_cache.append(l)

    @run_in_thread
    def _generate_answer(self, knowledgebase: dict, history: list, call_back):
        knowledge_client = KnowledgeFactory.get_knowledge_base_client(
            id=knowledgebase['id'],
            name=knowledgebase['name'],
            callback=call_back
        )
        result = tools.asyncio_run(knowledge_client.simple_search, history=history)
        history.append(result)
        append_conversation(
            conversation=result,
            history=history,
        )

    @st.fragment
    def _chat(self, knowledgebase: dict, prompt: str):
        if not prompt:
            return
        prompt = prompt.strip()
        # get answer
        history = [Conversation(role=Role.USER, content=prompt)]
        self._generate_answer(
            knowledgebase=knowledgebase,
            history=history,
            call_back=self.recall_callback
        )

    @st.fragment(run_every=1)
    def _show_recall_logs(self):
        logs = ''
        for log in self.recall_logs_cache.records():
            if isinstance(log, pd.DataFrame):
                if logs:
                    st.code(body=logs, language='log')
                st.dataframe(log)
                logs = ''
            else:
                logs = logs + str(log) + '\n'

        if logs:
            st.code(body=logs, language='log')

    @st.fragment
    def recall_test(self, index_type: str):
        recall_result_col, chat_middle_col = st.columns([0.5, 0.5])
        with recall_result_col:
            st.markdown("#### Recall")
            knowledgebase = self._knowledgebase_selector(
                index_type=index_type,
                key=f"{self.ui_session.session_id}_iseuyghknjljshfgsd"
            )
            input_text = st.chat_input("输入问题开始召回测试", key=f"{self.ui_session.session_id}_iuwtefjhbsd")

            with st.container(border=1, height=808, key=f"{self.ui_session.session_id}_8aut"):
                self._chat(prompt=input_text, knowledgebase=knowledgebase)
        with chat_middle_col:
            st.markdown("#### Logs")
            with st.container(border=1, height=1000, key=f"{self.ui_session.session_id}_8auv"):
                self._show_recall_logs()

    @abstractmethod
    def main(self):
        """程序页面入口"""
        ...
