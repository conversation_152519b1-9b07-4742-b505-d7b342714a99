import streamlit as st

from backend_server.page_utils import (
    SessionLogRecords,
    run_in_thread,
    append_conversation,
    show_all_history,
    SFUISession
)
from business.portal.portal_chat_bot import PortalChatBot
from framework.agents.schema import Conversation, Role
from backend_server.env import SENSORS_PROJECT_INFO

st.markdown('### 策略 Case 分析')
st.divider()

import json
from common import tools

logger = tools.get_logger()


class TestPageState:
    def __init__(
            self,
            session_id: str = tools.uuid4_no_underline(),
    ):
        self.history = []
        self.chat_middle_cache = SessionLogRecords(max_record_size=100)
        self.bot = PortalChatBot(
            name=session_id,
            chat_session_info=SENSORS_PROJECT_INFO,
            callback=lambda x: self.chat_middle_cache.append(x),
        )


_session_state_key = "_campaign_execution_analyse_"


@run_in_thread
def _get_answer(chat_state: TestPageState, prompt: str):
    bot = chat_state.bot
    chat_state.history.append(Conversation(role=Role.USER, content=prompt))
    answer = bot.chat(history=chat_state.history)
    logger.info(f"got answer: {answer.content}")
    chat_state.history.append(answer)


@st.fragment(run_every=1)
def _show_chat_middles(recall_state: TestPageState):
    history = []
    for h in recall_state.chat_middle_cache.records():
        try:
            msg_displayed = False
            d = h
            if isinstance(h, str):
                d = json.loads(h)

            if isinstance(d, dict):
                if 'content' in d:
                    append_conversation(conversation=Conversation(
                        role=Role.ASSISTANT,
                        name=d.get('name', 'Unknown'),
                        content=str(d['content'])
                    ), history=history)
                    msg_displayed = True
            if not msg_displayed:
                raise RuntimeError()
        except:
            st.caption(str(h))


@st.fragment(run_every=1)
def _show_chat_result(recall_state: TestPageState):
    history: list[Conversation] = recall_state.history
    if history:
        show_all_history(history=history)


@st.fragment
def _chat(chat_state: TestPageState, prompt: str):
    if not prompt:
        return
    prompt = prompt.strip()
    # get answer
    _get_answer(prompt=prompt, chat_state=chat_state)


def _init_or_get_session_state() -> TestPageState:
    if 'state' not in st.session_state:
        st.session_state.state = SFUISession()

    session = st.session_state.state
    state_key = _session_state_key
    chat_state = session.get_session_object(state_key)
    if not chat_state:
        chat_state = TestPageState()
        session.add_session_object(state_key, chat_state)
    return chat_state


def _clean_records(state: TestPageState):
    state.chat_middle_cache.clear()
    state.history.clear()


# start
def main():
    input_text = st.chat_input("输入策略 CASE ID 开始测试~")
    _recall_state = _init_or_get_session_state()

    recall_result_col, chat_middle_col = st.columns([0.5, 0.5])
    with recall_result_col:
        st.markdown("#### 聊天")
        with st.container(border=1, height=1000):
            _chat(prompt=input_text, chat_state=_recall_state)
            _show_chat_result(_recall_state)
    with chat_middle_col:
        _t2_c1, _t2_c2 = st.columns([0.85, 0.15])
        with _t2_c1:
            st.markdown("#### 推理")
        with _t2_c2:
            st.button('清除记录', on_click=_clean_records, kwargs={'state': _recall_state})

        with st.container(border=1, height=1000):
            _show_chat_middles(_recall_state)


main()
