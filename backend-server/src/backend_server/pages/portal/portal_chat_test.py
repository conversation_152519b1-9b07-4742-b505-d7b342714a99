import json
import uuid
from datetime import datetime, timedelta

import streamlit as st

from apis.definition.portal.portal_bot_pb2 import InsightParams
from backend_server.chat_bot_base import BaseChatBotPage
from backend_server.page_utils import SFUISession
from business.portal.portal_chat_bot import PortalChatBot
from common import tools
from core.service.tenant_project_service import TenantProjectService
from google.protobuf import json_format

log = tools.get_logger(__name__)

st.markdown("### Portal AI 测试")

tenant_project_service = TenantProjectService()

default_insight_params = {
    "insight_strategy": {
        "start_time": (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d %H:%M:%S"),
        "end_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "level": "SCENE",
        "scene_id": 1,
        "strategies": [
            {"type": "CANVAS", "ids": ["1", "2", "3"]}
        ]
    },
    "insight_goal": {}
}


class ChatBot(BaseChatBotPage):

    def __init__(
            self,
            chat_session_info: dict,
            insight_strategy: InsightParams | None = None,
            insight_goal: InsightParams | None = None,
            **kwargs
    ):
        bot = PortalChatBot(
            name=str(uuid.uuid4()),
            chat_session_info=chat_session_info,
            insight_strategy=insight_strategy,
            insight_goal=insight_goal,
            callback=lambda x: self.produce_message(x)
        )
        super().__init__(bot=bot, session=SFUISession(), is_single_chat=False, **kwargs)


@st.fragment
def selectors() -> dict:
    all_data = tools.asyncio_run(tenant_project_service.get_all)
    if not all_data:
        st.error("请先创建项目")
        return {}
    all_data = [d.dict() for d in all_data]
    st.markdown("#### 选择租户&项目")
    tenant_project = st.selectbox(
        '选择租户&项目',
        options=all_data,
        format_func=lambda x: f"{x['tenant_cname']}@{x['project_name']}",
        index=0
    )
    result = {
        'chat_session_info': tenant_project,
    }
    other_params = st.text_area(
        "其他参数(PortalChatBot 入参)",
        value=json.dumps(default_insight_params, indent=4, ensure_ascii=False)
    )
    ideas = st.text_area("请填入思路", value="")
    if other_params:
        try:
            _p = json.loads(other_params)
            if 'insight_strategy' in _p:
                _p['insight_strategy'] = json_format.ParseDict(_p['insight_strategy'], InsightParams())
            if 'insight_goal' in _p:
                _p['insight_goal'] = json_format.ParseDict(_p['insight_goal'], InsightParams())
            result = result | _p
        except BaseException as e:
            log.error("参数格式化错误", e)
            st.error("参数 json 格式化错误!")
    if ideas:
        result['ideas'] = ideas
    return result


def _convert_insight_params_as_prompt(insight_params: InsightParams) -> str:
    if insight_params is None:
        return ""
    params_obj = json_format.MessageToDict(
        insight_params,
        including_default_value_fields=True,
        preserving_proto_field_name=True,
        use_integers_for_enums=False,
    )
    details = [
        f"统计开始时间: {params_obj.get('start_time')}",
        f"统计结束时间: {params_obj.get('end_time')}",
        f"分析层级: {params_obj.get('level')}",
        f"场景 id: {params_obj.get('scene_id')}",
        f"子场景 id: {params_obj.get('sub_scene_id')}",
        f"业务目标 id: {params_obj.get('business_goal_id')}",
        f"业务目标值: {params_obj.get('business_goal_value')}",
        f"策略用例 id: {params_obj.get('campaign_case_id')}",
        f"当前层级包含的所有策略列表: {params_obj.get('strategies')}"
    ]
    return "\n".join(details)


def main():
    col1, col2 = st.columns([0.3, 0.7])
    with col1:
        with st.container(border=1):
            params = selectors()
    with col2:
        all_prompt = ""
        if 'insight_strategy' in params:
            insight_params_content = _convert_insight_params_as_prompt(params['insight_strategy'])
            if insight_params_content:
                all_prompt = f"**策略洞察相关参数：**\n{insight_params_content}\n***\n{all_prompt}"
        if 'insight_goal' in params:
            insight_params_content = _convert_insight_params_as_prompt(params['insight_goal'])
            if insight_params_content:
                all_prompt = f"**目标洞察相关参数：**\n{insight_params_content}\n***\n{all_prompt}"
        if params.get('ideas'):
            all_prompt = f"{all_prompt}\n***\n**分析思路：**\n\n{params['ideas']}"

        chat_bot = ChatBot(**params)
        input_prompt_processor = lambda x: f"{all_prompt}\n***\n{x}"
        chat_bot.render(
            input_tip="输入开始测试～",
            input_prompt_processor=input_prompt_processor
        )


main()
