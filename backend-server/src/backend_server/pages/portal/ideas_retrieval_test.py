import uuid

import streamlit as st

from backend_server.chat_bot_base import BaseChatBotPage
from backend_server.page_utils import SFUISession
from business.portal.portal_ideas_retrieval_bot import PortalIdeasRetrievalBot
from common import tools
from core.service.tenant_project_service import TenantProjectService

log = tools.get_logger(__name__)

st.markdown("### 思路检索")

tenant_project_service = TenantProjectService()


class ChatBot(BaseChatBotPage):

    def __init__(
            self,
            chat_session_info: dict,
            **kwargs
    ):
        bot = PortalIdeasRetrievalBot(
            name=str(uuid.uuid4()),
            chat_session_info=chat_session_info,
            callback=lambda x: self.produce_message(x)
        )
        super().__init__(bot=bot, session=SFUISession(), is_single_chat=True, **kwargs)


@st.fragment
def selectors() -> dict:
    all_data = tools.asyncio_run(tenant_project_service.get_all)
    if not all_data:
        st.error("请先创建项目")
        return {}
    all_data = [d.dict() for d in all_data]
    st.markdown("#### 选择租户&项目")
    tenant_project = st.selectbox(
        '选择租户&项目',
        options=all_data,
        format_func=lambda x: f"{x['tenant_cname']}@{x['project_name']}",
        index=0
    )
    result = {
        'chat_session_info': tenant_project,
    }
    return result


def main():
    col1, col2 = st.columns([0.3, 0.7])
    with col1:
        with st.container(border=1):
            params = selectors()
    with col2:
        chat_bot = ChatBot(**params)
        chat_bot.render(input_tip="输入开始检索～")


main()
