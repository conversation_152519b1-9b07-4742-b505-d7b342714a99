import streamlit as st
import sys

st.set_page_config(
    page_title="Sensors Agents",
    page_icon="✨",
    layout='wide',
    initial_sidebar_state='auto',
)

introduce_page = st.Page(
    "pages/introduce.py", title="介绍", icon="✨", default=True
)


def basic_manage_pages() -> list:
    return [
        st.Page(
            "pages/basic_manage/sensors_product_manage_page.py", title="神策产品管理", icon="🏢", default=False
        ),
        st.Page(
            "pages/basic_manage/industry_manage_page.py", title="行业管理", icon="🏢", default=False
        ),
        st.Page(
            "pages/basic_manage/tenant_manage_page.py", title="租户/项目管理", icon="🏢", default=False
        ),
    ]


def knowledge_pages() -> list:
    return [
        st.Page(
            "pages/knowledge/sensors_knowledgebase.py",
            title="神策知识库", icon="📚", default=False
        ),
        st.Page(
            "pages/knowledge/common_knowledgebase.py",
            title="通用知识库", icon="📚", default=False
        ),
        st.Page(
            "pages/knowledge/industry_knowledgebase.py",
            title="行业知识库", icon="📚", default=False
        ),
        st.Page(
            "pages/knowledge/customer_knowledgebase.py",
            title="客户知识库", icon="📚", default=False
        ),
        st.Page(
            "pages/knowledge/other_knowledgebase.py",
            title="自定义知识库", icon="📚", default=False
        ),
    ]


def portal_pages() -> list:
    return [
        st.Page(
            "pages/portal/ideas_retrieval_test.py",
            title="DEMO 思路检索", icon="🏕️", default=False
        ),
        st.Page(
            "pages/portal/portal_chat_test.py",
            title="DEMO 聊天", icon="🏕️", default=False
        ),
    ]


pg = st.navigation(
    {
        "START": [introduce_page],
        "BASIC MANAGE": basic_manage_pages(),
        "KNOWLEDGE": knowledge_pages(),
        "PORTAL": portal_pages(),
    }
)


def start():
    file = __name__ + ".py"
    sys.exit(f"streamlit run {file}")


if __name__ == "__main__":
    pg.run()
