import time
from dataclasses import dataclass
from typing import List, Any, Literal
import streamlit as st
from streamlit.delta_generator import DeltaG<PERSON><PERSON>
from threading import Thread
from functools import wraps
from streamlit.runtime.scriptrunner import add_script_run_ctx, get_script_run_ctx

from framework.agents.schema import Conversation, Role


def new_chat_message(role: Role):
    match role.value:
        case Role.SYSTEM.value:
            return
        case Role.USER.value:
            return st.chat_message(name="user", avatar="user")
        case Role.ASSISTANT.value:
            return st.chat_message(name="assistant", avatar="assistant")
        case Role.TOOL.value:
            return st.chat_message(name="tool", avatar="assistant")
        case Role.INTERPRETER.value:
            return st.chat_message(name="interpreter", avatar="assistant")
        case Role.OBSERVATION.value:
            return st.chat_message(name="observation", avatar="user")
        case _:
            st.error(f'Unexpected role: {role}')


def get_text(conversation: Conversation) -> str:
    text = postprocess_text(conversation.content)
    match conversation.role.value:
        case Role.TOOL.value:
            text = f'Calling tool `{conversation.tool}`:\n{text}'
        case Role.INTERPRETER.value:
            text = f'{text}'
        case Role.OBSERVATION.value:
            text = f'Observation:\n```\n{text}\n```'
    return text


def postprocess_text(text: str) -> str:
    if not text:
        return "NO TEXT"
    text = text.replace("\(", "$")
    text = text.replace("\)", "$")
    text = text.replace("\[", "$$")
    text = text.replace("\]", "$$")
    text = text.replace("assistant", "")
    text = text.replace("observation", "")
    text = text.replace("system", "")
    text = text.replace("user", "")
    return text.strip()


def show_conversation_message(
        conversation: Conversation,
        placeholder: DeltaGenerator | None = None,
        text_format: Literal["markdown", "log", "text"] = "markdown"
):
    if placeholder:
        message = placeholder
    else:
        message = new_chat_message(conversation.role)

    if conversation.image:
        message.image(conversation.image)
    else:
        text = get_text(conversation)
        if conversation.name and len(conversation.name.strip()) > 0:
            message.markdown(conversation.name)
        if text_format == "log":
            message.code(text, language='log')
        elif text_format == "text":
            message.write(text)
        else:
            message.markdown(text)


def show_all_history(
        history: list[Conversation],
        placeholder: DeltaGenerator | None = None,
        text_format: Literal["markdown", "log", "text"] = "markdown"
) -> None:
    for conversation in history:
        show_conversation_message(conversation=conversation, placeholder=placeholder, text_format=text_format)


def append_conversation(
        conversation: Conversation,
        history: list[Conversation],
        placeholder: DeltaGenerator | None = None,
        text_format: Literal["markdown", "log", "text"] = "markdown",
) -> None:
    history.append(conversation)
    show_conversation_message(conversation=conversation, placeholder=placeholder, text_format=text_format)


# 存储多个对话上下文
import string
import random


class SFUISession:
    def __init__(self):
        self.session_id = self._random_string(str_len=20)
        self.sessions_objs = {}

    def get_session_object(self, key: str):
        if key not in self.sessions_objs:
            return None
        return self.sessions_objs[key]

    def add_session_object(self, key: str, obj):
        self.sessions_objs[key] = obj

    def _random_string(self, str_len: int) -> str:
        return ''.join(random.sample(string.ascii_lowercase + string.ascii_uppercase + string.digits, str_len))


## function wrapper
def run_in_thread(func):
    # per https://stackoverflow.com/questions/76472211/multithreading-for-streamlit-and-kite-connect
    @wraps(func)
    def wrapper(*args, **kwargs):
        thread = Thread(target=func, args=args, kwargs=kwargs)
        ctx = get_script_run_ctx()
        add_script_run_ctx(thread, ctx)
        thread.start()

    return wrapper


@dataclass
class SessionLogRecord:
    record: Any
    append_time: int = int(time.time())


class SessionLogRecords:
    """记录一些临时中间输出，暂不考虑并发"""

    def __init__(
            self,
            max_record_size=50,
            max_expired_seconds=30 * 60
    ):
        self.expired_seconds = max_expired_seconds
        self.max_record_size = max_record_size
        self.history: List[SessionLogRecord] = []

    def _clean_expired(self):
        if not self.history:
            return
        try:
            curr_time = int(time.time())
            index = 0
            for i in range(len(self.history)):
                if self.history[i].append_time + self.expired_seconds < curr_time:
                    index = i + 1
                else:
                    break
            if index > 0:
                self.history = self.history[index:]
        except:
            pass

    def append(self, obj: Any):
        self.history.append(SessionLogRecord(record=obj))
        """
        if len(self.history) > self.max_record_size:
            self._clean_expired()
        """
        if len(self.history) > self.max_record_size:
            self.history = self.history[-self.max_record_size:]

    def records(self) -> List[Any]:
        # self._clean_expired()
        return [h.record for h in self.history]

    def clear(self):
        self.history.clear()
